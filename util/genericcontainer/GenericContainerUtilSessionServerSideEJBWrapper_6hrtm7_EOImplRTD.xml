<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.util.genericcontainer.GenericContainerUtilSessionServerSideEJBWrapper_6hrtm7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="load(glog.util.jdbc.Pk,glog.util.genericcontainer.DataSource,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="load(glog.util.genericcontainer.BusinessObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="load(glog.util.jdbc.Pk,glog.util.genericcontainer.DataSource,java.lang.String,java.util.LinkedList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="store(glog.util.genericcontainer.BusinessObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="storeNoTrack(glog.util.genericcontainer.BusinessObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getUIMetaData(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="compareAndStore(glog.util.genericcontainer.BusinessObject,glog.util.genericcontainer.GenericContainer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="compareAndStore(glog.util.genericcontainer.BusinessObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="compareAndStore(glog.util.genericcontainer.BusinessObject,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="compareAndStore(glog.util.genericcontainer.BusinessObject,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
