<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.util.report.BIPSessionHome_9wq2lz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="report(glog.util.report.BIPReportSpecification,glog.util.report.BIPDistribution$ReportLog$Specification)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="report(glog.util.report.BIPReportSpecification,glog.util.report.BIPDistribution$Print$Specification)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="report(glog.util.report.BIPReportSpecification,glog.util.report.BIPDistribution$Mail$Specification)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="report(glog.util.report.BIPReportSpecification)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="retrieveTemplates(glog.util.report.BIPReportSpecification)"
    timeout="600000"
>
</method>
<method
    name="generateContent(glog.util.report.BIPReportSpecification,glog.util.report.BIPTemplates)"
    timeout="600000"
>
</method>
<method
    name="transformData(glog.util.report.BIPReportSpecification,glog.util.report.BIPTemplates,glog.util.report.BIPData)"
    timeout="600000"
>
</method>
<method
    name="distribute(glog.util.report.BIPReportSpecification,glog.util.report.BIPData,glog.util.report.BIPDistribution$Print$Specification)"
    timeout="600000"
>
</method>
<method
    name="distribute(glog.util.report.BIPReportSpecification,glog.util.report.BIPData,glog.util.report.BIPDistribution$ReportLog$Specification)"
    timeout="600000"
>
</method>
<method
    name="distribute(glog.util.report.BIPReportSpecification,glog.util.report.BIPData,glog.util.report.BIPDistribution$Mail$Specification)"
    timeout="600000"
>
</method>
<method
    name="getImageContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getReportContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeContent(glog.util.report.BIPData)"
    timeout="600000"
>
</method>
<method
    name="removeReport(glog.util.report.BIPData,glog.util.report.BIPDistribution$Type)"
    timeout="600000"
>
</method>
<method
    name="generateData(java.lang.String,java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="generateIntegration(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="diagnoseXML(java.lang.String,int)"
    timeout="600000"
>
</method>
<method
    name="resetStatistics(java.lang.String,int)"
    timeout="600000"
>
</method>
<method
    name="resetStatistics()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
