<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.util.uom.util_UOM_jclkqf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAllUOMs()"
    timeout="600000"
>
</method>
<method
    name="convert(glog.util.TypeUnit,java.lang.Object)"
    timeout="600000"
>
</method>
<method
    name="getConversionData()"
    timeout="600000"
>
</method>
<method
    name="convertWithInfo(glog.util.TypeUnit,java.lang.Object)"
    timeout="600000"
>
</method>
<method
    name="getExchangeRatesByGid()"
    timeout="600000"
>
</method>
<method
    name="getExchangeRatesByDate(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="loadDistinctCode()"
    timeout="600000"
>
</method>
<method
    name="loadDistinctType()"
    timeout="600000"
>
</method>
<method
    name="setStorageDefault(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setDisplayDefault(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
