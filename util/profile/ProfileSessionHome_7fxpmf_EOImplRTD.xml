<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.util.profile.ProfileSessionHome_7fxpmf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getFiles()"
    timeout="600000"
>
</method>
<method
    name="addFile(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeFile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteFile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="convertFile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="shutdown()"
    timeout="600000"
>
</method>
<method
    name="start()"
    timeout="600000"
>
</method>
<method
    name="getFile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
