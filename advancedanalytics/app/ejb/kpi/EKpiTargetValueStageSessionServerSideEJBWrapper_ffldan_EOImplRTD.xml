<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.advancedanalytics.app.ejb.kpi.EKpiTargetValueStageSessionServerSideEJBWrapper_ffldan_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="queryProvince()"
    timeout="600000"
>
</method>
<method
    name="getLocationGeography()"
    timeout="600000"
>
</method>
<method
    name="queryCountryName()"
    timeout="600000"
>
</method>
<method
    name="executeSnapshot()"
    timeout="600000"
>
</method>
<method
    name="getLastSuccessfulLoadTime()"
    timeout="600000"
>
</method>
<method
    name="getRODLastSuccessfulLoadTime()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
