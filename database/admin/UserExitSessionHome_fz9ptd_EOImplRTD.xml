<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.database.admin.UserExitSessionHome_fz9ptd_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getBackUp()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getUserExit()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="displayWithLineNo()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="put(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="compile(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDefault()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="backup()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
