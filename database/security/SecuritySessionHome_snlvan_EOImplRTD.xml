<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.database.security.SecuritySessionHome_snlvan_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="resetPolicyUsers(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getChildDomains(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getGrantedDomains(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getLevel(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDomain(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="checkAccess(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUser(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createUser(glog.database.security.SecurityUser)"
    timeout="600000"
>
</method>
<method
    name="checkAndUpdatePassword(java.lang.String,glog.database.security.crypto.Password,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="getUserAuths(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveUserAuth(glog.database.security.UserLogin)"
    timeout="600000"
>
</method>
<method
    name="removeUserAuth(glog.database.security.UserLogin)"
    timeout="600000"
>
</method>
<method
    name="removeUserAuths(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createDomain(glog.database.security.SecurityDomain)"
    timeout="600000"
>
</method>
<method
    name="modifyDomain(glog.database.security.SecurityDomain)"
    timeout="600000"
>
</method>
<method
    name="removeDomain(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeDomain(glog.database.security.SecurityDomain)"
    timeout="600000"
>
</method>
<method
    name="getDomainEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listDomains(glog.database.security.SecurityDomain$Filter)"
    timeout="600000"
>
</method>
<method
    name="createAutoGrantee(glog.database.security.SecurityAutoGrantee)"
    timeout="600000"
>
</method>
<method
    name="removeAutoGrantee(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeAutoGrantee(glog.database.security.SecurityAutoGrantee)"
    timeout="600000"
>
</method>
<method
    name="modifyAutoGrantee(glog.database.security.SecurityAutoGrantee)"
    timeout="600000"
>
</method>
<method
    name="getAutoGrantee(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAutoGranteeEx(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listAutoGrantees(glog.database.security.SecurityAutoGrantee$Filter)"
    timeout="600000"
>
</method>
<method
    name="createAutoGrantor(glog.database.security.SecurityAutoGrantor)"
    timeout="600000"
>
</method>
<method
    name="removeAutoGrantor(glog.database.security.SecurityAutoGrantor)"
    timeout="600000"
>
</method>
<method
    name="removeAutoGrantor(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="modifyAutoGrantor(glog.database.security.SecurityAutoGrantor)"
    timeout="600000"
>
</method>
<method
    name="getAutoGrantor(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAutoGrantorEx(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listAutoGrantors(glog.database.security.SecurityAutoGrantor$Filter)"
    timeout="600000"
>
</method>
<method
    name="createDomainGrant(glog.database.security.SecurityDomainGrant)"
    timeout="600000"
>
</method>
<method
    name="removeDomainGrant(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeDomainGrant(glog.database.security.SecurityDomainGrant)"
    timeout="600000"
>
</method>
<method
    name="getDomainGrant(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDomainGrantEx(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listDomainGrants(glog.database.security.SecurityDomainGrant$Filter,boolean)"
    timeout="600000"
>
</method>
<method
    name="listTableSets(glog.database.security.SecurityTableSet$Filter)"
    timeout="600000"
>
</method>
<method
    name="createTableSet(glog.database.security.SecurityTableSet)"
    timeout="600000"
>
</method>
<method
    name="removeTableSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="addTableToSet(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeTableFromSet(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getTableSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createAcrRole(glog.database.security.SecurityAcrRole)"
    timeout="600000"
>
</method>
<method
    name="modifyAcrRole(glog.database.security.SecurityAcrRole)"
    timeout="600000"
>
</method>
<method
    name="deleteAcrRole(glog.database.security.SecurityAcrRole)"
    timeout="600000"
>
</method>
<method
    name="createAcrEntryPoint(glog.database.security.SecurityAcrEntryPoint)"
    timeout="600000"
>
</method>
<method
    name="createGlUserAcrRole(glog.database.security.SecurityGlUserAcrRole)"
    timeout="600000"
>
</method>
<method
    name="removeGlUserAcrRole(glog.database.security.SecurityGlUserAcrRole)"
    timeout="600000"
>
</method>
<method
    name="modifyGlUserAcrRoles(glog.database.security.SecurityGlUserAcrRoles)"
    timeout="600000"
>
</method>
<method
    name="createGlUserBiApp(glog.database.security.SecurityGlUserBiApp)"
    timeout="600000"
>
</method>
<method
    name="getUserLevelForRole(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserLevel(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDefaultUserRole(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAuthenticatedUser(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAuthenticatedUserEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="authenticateUser(java.lang.String,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="resetPassword(java.lang.String,glog.database.security.crypto.Password,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="changePassword(java.lang.String,glog.database.security.crypto.Password,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="checkAndUpdateAccount(glog.database.security.UserLogin)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkAccountStatus(glog.database.security.UserLogin)"
    timeout="600000"
>
</method>
<method
    name="listUsers(glog.database.security.SecurityUser$Filter)"
    timeout="600000"
>
</method>
<method
    name="modifyUser(glog.database.security.SecurityUser)"
    timeout="600000"
>
</method>
<method
    name="removeUser(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeUser(glog.database.security.SecurityUser)"
    timeout="600000"
>
</method>
<method
    name="getUserExternal(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserWithReservedFlags(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserDomain(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeGlUserBiApp(glog.database.security.SecurityGlUserBiApp)"
    timeout="600000"
>
</method>
<method
    name="modifyGlUserBiApps(glog.database.security.SecurityGlUserBiApps)"
    timeout="600000"
>
</method>
<method
    name="createGlUserBiRole(glog.database.security.SecurityGlUserBiRole)"
    timeout="600000"
>
</method>
<method
    name="removeGlUserBiRole(glog.database.security.SecurityGlUserBiRole)"
    timeout="600000"
>
</method>
<method
    name="modifyGlUserBiRoles(glog.database.security.SecurityGlUserBiRoles)"
    timeout="600000"
>
</method>
<method
    name="createUserRoleAcrRole(glog.database.security.SecurityUserRoleAcrRole)"
    timeout="600000"
>
</method>
<method
    name="removeUserRoleAcrRole(glog.database.security.SecurityUserRoleAcrRole)"
    timeout="600000"
>
</method>
<method
    name="modifyUserRoleAcrRoles(glog.database.security.SecurityUserRoleAcrRoles)"
    timeout="600000"
>
</method>
<method
    name="createLevel(glog.database.security.SecurityLevel)"
    timeout="600000"
>
</method>
<method
    name="modifyLevel(glog.database.security.SecurityLevel)"
    timeout="600000"
>
</method>
<method
    name="removeLevel(glog.database.security.SecurityLevel)"
    timeout="600000"
>
</method>
<method
    name="removeLevel(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLevelEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listLevels(glog.database.security.SecurityLevel$Filter)"
    timeout="600000"
>
</method>
<method
    name="createUserGroup(glog.database.security.SecurityUserGroup)"
    timeout="600000"
>
</method>
<method
    name="modifyUserGroup(glog.database.security.SecurityUserGroup)"
    timeout="600000"
>
</method>
<method
    name="removeUserGroup(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeUserGroup(glog.database.security.SecurityUserGroup)"
    timeout="600000"
>
</method>
<method
    name="getUserGroup(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserGroupEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listUserGroups(glog.database.security.SecurityUserGroup$Filter)"
    timeout="600000"
>
</method>
<method
    name="assignRoleToLevel(glog.database.security.SecurityRole,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="unassignRoleFromLevel(glog.database.security.SecurityRole,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listRoles(glog.database.security.SecurityRole$Filter)"
    timeout="600000"
>
</method>
<method
    name="createUserAssociation(glog.database.security.SecurityUserAssociation)"
    timeout="600000"
>
</method>
<method
    name="modifyUserAssociation(glog.database.security.SecurityUserAssociation)"
    timeout="600000"
>
</method>
<method
    name="removeUserAssociation(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeUserAssociation(glog.database.security.SecurityUserAssociation)"
    timeout="600000"
>
</method>
<method
    name="getUserAssociation(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserAssociationEx(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listUserAssociations(glog.database.security.SecurityUserAssociation$Filter)"
    timeout="600000"
>
</method>
<method
    name="postServprovCreate(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="postServprovCreate(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createExternalPredicate(glog.database.security.SecurityExternalPredicate)"
    timeout="600000"
>
</method>
<method
    name="modifyExternalPredicate(glog.database.security.SecurityExternalPredicate)"
    timeout="600000"
>
</method>
<method
    name="removeExternalPredicate(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeExternalPredicate(glog.database.security.SecurityExternalPredicate)"
    timeout="600000"
>
</method>
<method
    name="getExternalPredicate(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getExternalPredicateEx(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listExternalPredicates(glog.database.security.SecurityExternalPredicate$Filter)"
    timeout="600000"
>
</method>
<method
    name="createVpdProfile(glog.database.security.SecurityVpdProfile)"
    timeout="600000"
>
</method>
<method
    name="modifyVpdProfile(glog.database.security.SecurityVpdProfile)"
    timeout="600000"
>
</method>
<method
    name="removeVpdProfile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeVpdProfile(glog.database.security.SecurityVpdProfile)"
    timeout="600000"
>
</method>
<method
    name="getVpdProfile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getVpdProfileEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listVpdProfiles(glog.database.security.SecurityVpdProfile$Filter)"
    timeout="600000"
>
</method>
<method
    name="getFunctionsByLevel(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getFunctionsByUser(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createUserRole(glog.database.security.SecurityUserRole)"
    timeout="600000"
>
</method>
<method
    name="modifyUserRole(glog.database.security.SecurityUserRole)"
    timeout="600000"
>
</method>
<method
    name="removeUserRole(glog.database.security.SecurityUserRole)"
    timeout="600000"
>
</method>
<method
    name="removeUserRole(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserRole(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserRoleEx(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listUserRoles(glog.database.security.SecurityUserRole$Filter)"
    timeout="600000"
>
</method>
<method
    name="getCurrentUserRoleGid()"
    timeout="600000"
>
</method>
<method
    name="setCurrentUserRoleGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="resetCurrentUserRoleGid()"
    timeout="600000"
>
</method>
<method
    name="modifyUserRoleGrants(glog.database.security.SecurityUserRoleGrants)"
    timeout="600000"
>
</method>
<method
    name="getUserRoleGrants(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="modifyRoleRoleGrants(glog.database.security.SecurityRoleRoleGrants)"
    timeout="600000"
>
</method>
<method
    name="getRoleRoleGrants(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listUserUserRoles(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listUserUserRoles(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="modifyUserRoleUserGrants(glog.database.security.SecurityUserRoleUserGrants)"
    timeout="600000"
>
</method>
<method
    name="getUserRoleUserGrants(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="authenticateLDAPUser(glog.database.security.ldap.NameSpace[],java.lang.String,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="reinitializeRealm()"
    timeout="600000"
>
</method>
<method
    name="isDomainReadOnly(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isRowReadOnly(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getExecAsUserElements()"
    timeout="600000"
>
</method>
<method
    name="validateOAMUser(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="validateAuthUser(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="userHasProcessScheduled(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="userIsContact(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="userIsExpired(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="userIsEffective(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createServprovUser(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isCurrentUserSuperAdmin()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
