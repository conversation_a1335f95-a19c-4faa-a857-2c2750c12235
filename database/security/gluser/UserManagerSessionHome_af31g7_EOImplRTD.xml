<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.database.security.gluser.UserManagerSessionHome_af31g7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="updateValues(java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="removeValue(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeValue(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateValue(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateValue(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setUsername(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeValues(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getDefaultUserPreferences()"
    timeout="600000"
>
</method>
<method
    name="getQueryTable()"
    timeout="600000"
>
</method>
<method
    name="getQueryTableByName(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserFavorites(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteUserFavorites(java.util.Vector)"
    timeout="600000"
>
</method>
<method
    name="update_GL_USER_Approve_Rule_Profile(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateUserShippingAgentContactJoin(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteUserShippingAgentContactJoin(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getValue(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getValue(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getValues(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
