<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.database.purge.PurgeSessionHome_dqcjgl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="markForPurge(boolean,int,java.util.List)"
    timeout="600000"
>
</method>
<method
    name="markForPurgeQuery(boolean,int,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="checkPurgeStatus(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getJobStatus(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getJobProperties()"
    timeout="600000"
>
</method>
<method
    name="getServerType()"
    timeout="600000"
>
</method>
<method
    name="stopPurgeJob(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getJobSchedule()"
    timeout="600000"
>
</method>
<method
    name="startPurgeJob(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="alterJobSchedule(glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
