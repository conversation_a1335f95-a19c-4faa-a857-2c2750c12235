<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.coroute.app.business.route.CrDeleteSessionHome_9xxrbl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="deleteCrProject(glog.coroute.app.ejb.project.db.CrProjectPK)"
    timeout="600000"
>
</method>
<method
    name="deletePShipmentSet(glog.procurement.app.ejb.shipment.db.PShipmentSetPK)"
    timeout="600000"
>
</method>
<method
    name="deleteLvAggregate(glog.coroute.app.ejb.lanevolume.db.CrLvAggregatePK)"
    timeout="600000"
>
</method>
<method
    name="deleteForecast(glog.coroute.app.ejb.lanevolume.db.CrForecastPK)"
    timeout="600000"
>
</method>
<method
    name="deleteScenario(glog.coroute.app.ejb.scenario.db.CrScenarioPK)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
