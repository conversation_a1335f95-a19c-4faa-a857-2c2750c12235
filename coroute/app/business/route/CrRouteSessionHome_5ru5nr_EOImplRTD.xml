<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.coroute.app.business.route.CrRouteSessionHome_5ru5nr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="commit(glog.coroute.app.ejb.scenario.db.CrRoutePK[])"
    timeout="600000"
>
</method>
<method
    name="uncommit(glog.coroute.app.ejb.scenario.db.CrRoutePK[])"
    timeout="600000"
>
</method>
<method
    name="retrieveMapData(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="similarRoutes(glog.coroute.app.ejb.scenario.db.CrRoutePK)"
    timeout="600000"
>
</method>
<method
    name="processEdits(glog.coroute.app.ejb.scenario.db.CrRoutePK[])"
    timeout="600000"
>
</method>
<method
    name="processConvertToRouteTemplate(glog.coroute.app.ejb.scenario.db.CrRoutePK[],java.lang.String,java.lang.Boolean)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
