<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.device.DeviceSessionHome_redi6p_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAssociatedDeviceData(glog.util.jdbc.Pk)"
    timeout="600000"
>
</method>
<method
    name="getDeviceGidUsingExternalDeviceID(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getCurrentDevicesForObjectGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDeviceGidListForObjectGid(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
