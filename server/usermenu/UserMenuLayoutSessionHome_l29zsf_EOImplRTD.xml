<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.usermenu.UserMenuLayoutSessionHome_l29zsf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="addUserMenuLayout(glog.ejb.usermenu.db.UserMenuLayoutData)"
    timeout="600000"
>
</method>
<method
    name="addUserMenu(glog.ejb.usermenu.db.UserMenuData[])"
    timeout="600000"
>
</method>
<method
    name="addOrUpdatePowerdataLayout(glog.ejb.usermenu.db.PowerdataLayoutData)"
    timeout="600000"
>
</method>
<method
    name="getPowerdataLayout(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateUserMenuLayout(glog.ejb.usermenu.db.UserMenuLayoutData)"
    timeout="600000"
>
</method>
<method
    name="getUserMenuLayout(java.lang.String,glog.server.useraccess.UserMenuAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getUserMenuLayouts(glog.server.useraccess.UserMenuAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="deleteUserMenuLayout(glog.ejb.usermenu.db.UserMenuLayoutPK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
