<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.synch.object.ObjectLockSessionHome_yo2kwh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="unlock(java.lang.String,java.lang.String,long)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(int,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseTableXML(java.lang.String,java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="unlockTable(java.lang.String,java.lang.String,long)"
    timeout="10000"
>
</method>
<method
    name="resetStats()"
    timeout="10000"
>
</method>
<method
    name="resetCache()"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
