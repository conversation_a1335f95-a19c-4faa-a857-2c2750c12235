<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.cache.CacheSessionHome_rp9jbr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>
<method
    name="setCapacity(java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="setTimeout(java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="clear(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(java.lang.String,java.lang.String,int,java.util.Set,java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="resetStats(java.lang.String,java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="clearAll(java.lang.String,java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="clearItem(java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="expireAll(java.lang.String,java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="expire(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="setSynch(java.lang.String,boolean)"
    timeout="10000"
>
</method>
<method
    name="setLogging(java.lang.String,boolean)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
