<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.producer.ProducerSessionHome_oq43gx_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getProducerStatsXML()"
    timeout="10000"
>
</method>
<method
    name="getProducerStats()"
    timeout="10000"
>
</method>
<method
    name="clearProducerCache()"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
