<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.screenlayout.ScreenLayoutSessionHome_wzgmlr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(glog.ejb.screenlayout.db.ScreenLayoutDefData)"
    timeout="600000"
>
</method>
<method
    name="update(glog.ejb.screenlayout.db.ScreenLayoutData)"
    timeout="600000"
>
</method>
<method
    name="update(glog.ejb.workinglist.db.WorkspaceData)"
    timeout="600000"
>
</method>
<method
    name="getDefaultScreenLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getScreenLayout(java.lang.String,glog.server.useraccess.ScreenLayoutAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="notifyJMS(glog.server.screenlayout.ScreenLayoutAlert)"
    timeout="600000"
>
</method>
<method
    name="getAllScreenLayoutsNoXml(java.lang.String[],glog.server.useraccess.ScreenLayoutAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="deleteScreenLayouts(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getScreenLayoutDef(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getScreenLayouts(java.lang.String[],glog.server.useraccess.ScreenLayoutAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="deleteScreenLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteWorkspace(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getWorkspace(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getIconPath(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.screenlayout.db.ScreenLayoutData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.workinglist.db.WorkspaceData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.screenlayout.db.ScreenLayoutDefData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
