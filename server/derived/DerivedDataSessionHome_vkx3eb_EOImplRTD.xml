<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.derived.DerivedDataSessionHome_vkx3eb_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getData(java.lang.Object[],java.lang.String,java.util.List)"
    timeout="600000"
>
</method>
<method
    name="filter(glog.webserver.umt.UserManager,java.util.List,java.lang.String,java.lang.String[],glog.ejb.savedquery.SavedQueryDef)"
    timeout="600000"
>
</method>
<method
    name="getQueryMap()"
    timeout="600000"
>
</method>
<method
    name="loadFieldDisplays()"
    timeout="600000"
>
</method>
<method
    name="getPkData(java.lang.Object[],java.lang.String,java.util.List)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
