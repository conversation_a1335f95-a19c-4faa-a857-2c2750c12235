<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.document.DocumentSessionHome_z01ru9_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="sendDocuments(java.lang.String[],glog.server.document.ContactRequest[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="sendDocuments(java.lang.String[],glog.server.document.ContactRequest[],java.lang.String[],glog.util.report.IppPrintRequest[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="sendDocuments(java.lang.String[],java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDocumentContent(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getDocumentContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="emailDocuments(java.lang.String[],java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDocumentUseProfile()"
    timeout="600000"
>
</method>
<method
    name="getDocumentDefData(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDocumentData(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setDocumentParameter(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertUpdateDocumentContent(java.lang.String,java.lang.String,glog.server.document.DocumentContent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setDocumentCmsId(java.lang.String,glog.server.document.cms.CmsDocumentId)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="attachDocumentContent(java.lang.String,java.lang.String,glog.server.document.DocumentContent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="attachDocumentContent(java.lang.String,java.lang.String,glog.server.document.DocumentContent,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setDocumentsOwner(java.util.Collection,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isBrowserVirusProtected()"
    timeout="600000"
>
</method>
<method
    name="checkUseProfile(java.lang.String,long,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="verifyDocumentSafety(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="printIppDocuments(java.lang.String[],glog.util.report.IppPrintRequest[])"
    timeout="600000"
>
</method>
<method
    name="setDocumentContent(java.lang.String,glog.server.document.DocumentContent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setDocumentContent(java.lang.String,glog.server.document.DocumentContent,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setDocumentContent(java.lang.String,glog.server.document.DocumentContent,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="clearDocumentContent(java.lang.String,glog.server.document.RemoveCase)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeDocuments(java.lang.String[],glog.server.document.RemoveCase)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeDocument(java.lang.String,glog.server.document.RemoveCase)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateDocumentParameters(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateDocumentParameters(java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
