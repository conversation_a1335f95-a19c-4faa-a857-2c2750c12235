<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.document.ReqdocSessionHome_crpaz5_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="validate(glog.server.document.Reqdoc,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReqdoc(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLatestRevision(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewRevision(glog.server.document.Reqdoc,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewRevision(glog.server.document.Reqdoc,glog.server.document.ReqdocRevision,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRevision(glog.server.document.Reqdoc,glog.server.document.ReqdocRevision,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeRevision(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkValidationRuleSavedCondition(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkValidationRuleAutoAssign(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRevisions(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRevision(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkValidationRuleXPath(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="acceptRevision(glog.server.document.Reqdoc,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="denyRevision(glog.server.document.Reqdoc,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="finalizeDocument(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unfinalizeDocument(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReviewers(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReviewer(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCurrentReviewer(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReviews(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setReviewersAsPendingReview(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setReviewState(glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setRevisionCount(glog.server.document.Reqdoc)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkForModified(glog.ejb.document.db.DocumentData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkForRemoved(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="publishLifetime(java.lang.String,glog.server.document.Reqdoc,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="notifyReviewers(glog.server.document.Reqdoc,java.lang.String,glog.server.workflow.notify.NotifyContext)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="notifySubscribers(glog.server.document.Reqdoc,java.lang.String,glog.server.workflow.notify.NotifyContext)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
