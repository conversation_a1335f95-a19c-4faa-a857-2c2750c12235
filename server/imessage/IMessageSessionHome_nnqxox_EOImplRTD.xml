<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.imessage.IMessageSessionHome_nnqxox_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getRecipientContactInfo(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="notifyIMessageRecipient(glog.integration.schema.generate.msg.Message,java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="getNextIMessageXid()"
    timeout="600000"
>
</method>
<method
    name="getNextTransmissionNo()"
    timeout="600000"
>
</method>
<method
    name="transformMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveTransmission(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.jdbc.T2SharedConnection)"
    timeout="600000"
>
</method>
<method
    name="saveIMessage(glog.integration.tools.schemawrappers.msg.MessageInstance)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveIMessage(java.lang.String,glog.ejb.message.db.IMessageData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="receiveSynchronousMessageAck(java.lang.String,glog.integration.tools.schemawrappers.msg.MessageAckWrapper)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getViewIMessageContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEditIMessageContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateIMessageArguments(java.lang.String,java.lang.String,java.util.Map)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyIMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRawIMessageContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getIMessage(java.lang.String,java.lang.String,long)"
    timeout="600000"
>
</method>
<method
    name="processIMessageContent(glog.integration.schema.generate.msg.Message,java.lang.String,glog.database.security.crypto.Password,long)"
    timeout="600000"
>
</method>
<method
    name="getIMessagePk(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="receiveAsynchronousMessageAck(glog.integration.tools.schemawrappers.msg.MessageAckWrapper)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processPendingMessage(glog.ejb.message.db.IMessageData)"
    timeout="600000"
>
</method>
<method
    name="updateMessageStatus(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTransmissionNumberUsingMessageGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isActionable(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateAckSpecForMessage(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
