<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.diag.DiagSessionHome_migzwv_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="pauseSchedule(long)"
    timeout="30000"
>
</method>
<method
    name="resumeSchedule(long)"
    timeout="30000"
>
</method>
<method
    name="abortSchedule(long)"
    timeout="30000"
>
</method>
<method
    name="getNTierResponse(java.lang.Long)"
    timeout="30000"
>
</method>
<method
    name="capture(glog.webserver.diag.DiagSettings)"
    timeout="30000"
>
</method>
<method
    name="remove()"
    timeout="30000"
>
</method>
<method
    name="getHandle()"
    timeout="30000"
>
</method>
<method
    name="getEJBHome()"
    timeout="30000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="30000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="30000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
