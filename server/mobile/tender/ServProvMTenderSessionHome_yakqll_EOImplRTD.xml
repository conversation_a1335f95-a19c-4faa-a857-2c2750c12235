<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.mobile.tender.ServProvMTenderSessionHome_yakqll_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getTenderCollabXMLStrByIds(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getTenderCollabXMLByIds(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getTransIdsByServProv(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="persistTenderResponse(glog.integration.schema.generate.TenderResponse)"
    timeout="600000"
>
</method>
<method
    name="acceptShipmentTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="declineShipmentTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,boolean,boolean,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="addFavoriteShipment(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeFavoriteShipment(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
