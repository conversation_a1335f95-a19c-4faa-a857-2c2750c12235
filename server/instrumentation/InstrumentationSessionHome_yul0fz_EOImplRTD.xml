<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.instrumentation.InstrumentationSessionHome_yul0fz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="diagnoseObjectsXML(java.lang.String,java.lang.String,int,java.util.Set,java.util.Set,java.util.Set,int)"
    timeout="600000"
>
</method>
<method
    name="resetObjects(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="instrument(java.lang.String,java.lang.Class)"
    timeout="600000"
>
</method>
<method
    name="instrument(java.lang.String,java.lang.String,java.lang.Class)"
    timeout="600000"
>
</method>
<method
    name="setInterval(java.lang.String,long)"
    timeout="600000"
>
</method>
<method
    name="diagnoseObjects(java.lang.String,java.lang.String,int,java.util.Set,java.util.Set,java.util.Set,int)"
    timeout="600000"
>
</method>
<method
    name="profile(java.lang.String,java.lang.Class)"
    timeout="600000"
>
</method>
<method
    name="profile(java.lang.String,java.lang.String,int,double,java.lang.Class)"
    timeout="600000"
>
</method>
<method
    name="diagnoseProfiles(java.lang.String,java.lang.String,int,java.util.Set,java.util.Set,double,int,int)"
    timeout="600000"
>
</method>
<method
    name="diagnoseProfilesXML(java.lang.String,java.lang.String,int,java.util.Set,java.util.Set,double,int,int)"
    timeout="600000"
>
</method>
<method
    name="resetProfiles(java.lang.String,java.lang.String,int,double)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
