<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.migrationproject.MigrationProjectSessionServerSideEJBWrapper_l0virz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="sequenceFinderSets(java.util.Collection,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="importProject(java.lang.String,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="validateObjectGroups(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createOrReplaceProject(java.lang.String,glog.integration.schema.jaxb.migrationproject.Project,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyPackageFile(java.lang.String,byte[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="sequenceTables(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setProjectStatus(java.lang.String,glog.server.migrationproject.MigrationProjectConstants$PROJECT_STATUS,java.io.File)"
    timeout="600000"
>
</method>
<method
    name="setProjectStatus(java.lang.String,glog.server.migrationproject.MigrationProjectConstants$PROJECT_STATUS)"
    timeout="600000"
>
</method>
<method
    name="exportProject(java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
