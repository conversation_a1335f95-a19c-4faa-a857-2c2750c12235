<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.datasource.CPDiagSessionHome_gug3qn_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="test(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnose(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="resetStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="shrink(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="shutdown(java.lang.String,boolean)"
    timeout="10000"
>
</method>
<method
    name="reset(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="verify(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
