<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.data.DataAccessSessionHome_qu2wnl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getData(java.lang.Class,java.lang.Class,java.lang.String,glog.util.jdbc.PkCallback)"
    timeout="600000"
>
</method>
<method
    name="getData(java.lang.Class,java.lang.Class,glog.util.jdbc.Pk,int,glog.util.jdbc.PkCallback,glog.util.jdbc.PkCallback)"
    timeout="600000"
>
</method>
<method
    name="getData(java.lang.Class,java.lang.Class,glog.util.jdbc.Pk[],glog.util.jdbc.PkCallback)"
    timeout="600000"
>
</method>
<method
    name="getData(java.lang.Class,java.lang.Class,glog.util.jdbc.Pk,glog.util.jdbc.PkCallback)"
    timeout="600000"
>
</method>
<method
    name="getOrderRelease(java.lang.String,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getOrderBase(java.lang.String,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
