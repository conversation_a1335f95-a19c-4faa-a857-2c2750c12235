<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.action.ActionSessionHome_4ij5in_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAction(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getActions(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getActions(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getActionsForParameter(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllActions()"
    timeout="600000"
>
</method>
<method
    name="deleteAction(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.server.action.ActionContainer)"
    timeout="600000"
>
</method>
<method
    name="deleteActions(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteActions(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="checkAction(glog.server.action.AppActionCheck,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkReason(glog.server.action.AppActionReason,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="getAppActionCheck(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDataQueryTypes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryDataQueryType(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="supportsPostCheck(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAssociatedManagerLayouts(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAppActionGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
