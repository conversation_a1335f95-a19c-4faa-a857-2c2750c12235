<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.finderset.FinderSetSessionHome_a5gnkz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(glog.ejb.finderset.db.FinderSetData)"
    timeout="600000"
>
</method>
<method
    name="deleteFinderSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDefaultFinderSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getFinderSet(java.lang.String,glog.server.useraccess.FinderSetAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getFinderSets(java.lang.String[],glog.server.useraccess.FinderSetAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.finderset.db.FinderSetData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="notifyJMS(glog.server.finderset.FinderSetAlert)"
    timeout="600000"
>
</method>
<method
    name="getAllFinderSetsNoXml(java.lang.String[],glog.server.useraccess.FinderSetAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="deleteFinderSets(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
