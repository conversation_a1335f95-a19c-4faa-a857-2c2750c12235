<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.report.ReportSessionHome_9gsajz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(java.util.Collection,glog.ejb.report.db.ReportEmailPK)"
    timeout="600000"
>
</method>
<method
    name="update(java.util.Collection,glog.ejb.report.db.ReportPrintPK)"
    timeout="600000"
>
</method>
<method
    name="getReport(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getRecurringReportHistoryRequests(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String[],java.lang.String[],int)"
    timeout="600000"
>
</method>
<method
    name="getManagerReports()"
    timeout="600000"
>
</method>
<method
    name="getManagerReports(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getManagerReport(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getTransactionalParam(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deduceDataQueryTypes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deduceDataQueryType(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clearReportCaches(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBulkReportingGid(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBulkReportingMachine(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="startBulkReporting(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateBulkReporting(java.lang.String,int,int,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="completeBulkReporting(java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="abortBulkReporting(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateBulkReporting(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllReportsInGroup(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEmailSequenceNumber()"
    timeout="600000"
>
</method>
<method
    name="getReportRedirect(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getReportRedirectFromLog(glog.ejb.report.db.ReportLogPK)"
    timeout="600000"
>
</method>
<method
    name="createNewReportEmail(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="createNewReportPrint(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="getRecurringReportRequests(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String[],java.lang.String[],int)"
    timeout="600000"
>
</method>
<method
    name="getReportData(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllReports()"
    timeout="600000"
>
</method>
<method
    name="getAllReportGroups()"
    timeout="600000"
>
</method>
<method
    name="getAllReportEmails(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllReportPrints(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllReportParameters(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.report.db.ReportEmailData)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.report.db.ReportPrintData)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
