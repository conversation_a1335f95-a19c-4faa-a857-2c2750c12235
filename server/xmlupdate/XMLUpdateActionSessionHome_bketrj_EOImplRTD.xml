<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.xmlupdate.XMLUpdateActionSessionHome_bketrj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="orderReleaseCreated(glog.ejb.order.db.OrderReleasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderBaseCreated(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderReleaseRequest(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderBaseReleased(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderReleaseModified(glog.ejb.order.db.OrderReleasePK,glog.ejb.order.db.OrderReleaseData,glog.ejb.order.db.OrderReleaseLinePK[],glog.ejb.order.db.OrderReleaseLineData[],glog.ejb.order.db.ShipUnitPK[],glog.ejb.order.db.ShipUnitData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderReleaseDatesModified(glog.ejb.order.db.OrderReleasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderBaseModified(glog.ejb.orderbase.db.ObOrderBasePK,glog.ejb.orderbase.db.ObLinePK[],glog.ejb.orderbase.db.ObLineData[],glog.ejb.orderbase.db.ObShipUnitPK[],glog.ejb.orderbase.db.ObShipUnitData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderBaseQuantityModified(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderBaseDateModified(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="orderReleaseQuantityModified(glog.ejb.order.db.OrderReleasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
