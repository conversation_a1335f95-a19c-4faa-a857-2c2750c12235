<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.xmlupdate.UIDataContainerUpdateSessionServerSideEJBWrapper_7lq64j_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(glog.webserver.util.UIDataContainer,glog.webserver.umt.UserPreference,glog.server.wrapper.WrapperEventInfo)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAndTrack(glog.webserver.util.UIDataContainer,glog.webserver.umt.UserPreference,glog.server.wrapper.WrapperEventInfo)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAndTrack(glog.webserver.util.UIDataContainer,glog.webserver.umt.UserPreference,glog.server.wrapper.WrapperEventInfo,glog.util.persistence.PersistenceHandler)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
