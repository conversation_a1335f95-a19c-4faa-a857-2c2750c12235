<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.wallet.WalletSessionHome_eo7ggf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getPassword(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="reload(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="reload()"
    timeout="600000"
>
</method>
<method
    name="setPassword(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="deletePassword(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getWallet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
