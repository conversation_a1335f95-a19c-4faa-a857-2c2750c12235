<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.transmission.ITransmissionSessionHome_xotjlt_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getTransmissionXML(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getTransactionXML(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="changeExternalStatus(java.lang.String,java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="reprocess(long,boolean)"
    timeout="600000"
>
</method>
<method
    name="reprocess(long)"
    timeout="600000"
>
</method>
<method
    name="getTransactions(long)"
    timeout="600000"
>
</method>
<method
    name="getITransmissionDetails(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
