<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.logview.logview_SystemLogSession_f0kmk8_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="suppressAllIds(boolean)"
    timeout="600000"
>
</method>
<method
    name="getAllEnabledIds()"
    timeout="600000"
>
</method>
<method
    name="getEnabledId(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="suppressId(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getWebLogSettings()"
    timeout="600000"
>
</method>
<method
    name="getLogEntries(java.lang.String,glog.util.log.LogFileIndex,java.sql.Timestamp,java.lang.Long,java.util.Collection,java.util.Collection,java.lang.String,boolean,int)"
    timeout="600000"
>
</method>
<method
    name="getLogType(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
