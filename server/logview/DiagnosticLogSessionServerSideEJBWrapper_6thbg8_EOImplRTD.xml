<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.logview.DiagnosticLogSessionServerSideEJBWrapper_6thbg8_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getDescription(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLogEntries(java.util.Date,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getLogEntries(glog.ejb.diagprocessconfig.db.DiagLogFilePK)"
    timeout="600000"
>
</method>
<method
    name="getLogEntries(java.util.Date,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPosssibleSolutions(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAvailableDiagnosticFiles(java.util.Date)"
    timeout="600000"
>
</method>
<method
    name="isDiagnosticLogFileExists(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="clearDiagnosticCache()"
    timeout="600000"
>
</method>
<method
    name="getAppMachineGID(glog.ejb.diagprocessconfig.db.DiagLogFilePK)"
    timeout="600000"
>
</method>
<method
    name="getDiagLogEntriesToExport(glog.ejb.diagprocessconfig.db.DiagLogFilePK)"
    timeout="600000"
>
</method>
<method
    name="getFileToExport(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getReasonCodeDescription(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
