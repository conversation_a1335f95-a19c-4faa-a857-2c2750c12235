<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.notify.StylesheetContentSessionServerSideEJBWrapper_j5hzgb_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getStylesheetContent(glog.ejb.notify.db.StylesheetProfilePK)"
    timeout="600000"
>
</method>
<method
    name="getStylesheetContent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateStylesheetContent(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyStylesheetToAppServer(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getStylesheetContentGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
