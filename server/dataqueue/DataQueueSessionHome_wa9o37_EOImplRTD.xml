<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.dataqueue.DataQueueSessionHome_wa9o37_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getExceptionFinderSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getFinderSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getQEvent(java.lang.Long,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEventTree(long,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="preemptQEvents(java.util.Collection,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="cancelQEvents(java.util.Collection,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resubmitQEvents(java.util.Collection,java.lang.String,glog.util.LocalTimestamp,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="upload(java.lang.String,int,long,int)"
    timeout="600000"
>
</method>
<method
    name="stopUploads()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
