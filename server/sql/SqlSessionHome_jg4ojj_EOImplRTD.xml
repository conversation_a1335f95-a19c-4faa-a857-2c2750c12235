<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.sql.SqlSessionHome_jg4ojj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="select(java.lang.String,java.lang.Object[],int,int,int,boolean,glog.util.uom.UOMPreferences,boolean)"
    timeout="600000"
>
</method>
<method
    name="execute(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="dmlReturningSession(java.lang.String,java.lang.Object[],java.lang.String,boolean,long)"
    timeout="600000"
>
</method>
<method
    name="call(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="callSession(java.lang.String,java.lang.Object[],long)"
    timeout="600000"
>
</method>
<method
    name="explain(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="explainSession(java.lang.String,java.lang.Object[],long)"
    timeout="600000"
>
</method>
<method
    name="commitRollbackSession(boolean,long)"
    timeout="600000"
>
</method>
<method
    name="describe(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="bounce(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="releaseConnection(long)"
    timeout="600000"
>
</method>
<method
    name="getSequences()"
    timeout="600000"
>
</method>
<method
    name="selectUI(java.lang.String,java.lang.Object[],int,int,int,boolean,glog.util.uom.UOMPreferences,boolean)"
    timeout="600000"
>
</method>
<method
    name="selectSession(java.lang.String,java.lang.Object[],java.lang.String,int,int,int,boolean,glog.util.uom.UOMPreferences,boolean,long)"
    timeout="600000"
>
</method>
<method
    name="executeSession(java.lang.String,java.lang.Object[],java.lang.String,long)"
    timeout="600000"
>
</method>
<method
    name="dmlReturning(java.lang.String,java.lang.Object[],boolean)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
