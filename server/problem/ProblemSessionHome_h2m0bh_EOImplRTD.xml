<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.problem.ProblemSessionHome_h2m0bh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAllProbs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNewProbs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNewProbCount(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changeGlUser(java.lang.String[],java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addComment(java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="manuallyActOnMessage(java.lang.String[],glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMessageDetails(java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getProb(java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteProb(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteProb(java.lang.String,java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllProbCount(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.problem.db.ProblemData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNewProbPKs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllProbPKs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="notifyJMS(glog.webserver.problem.ProblemAlert)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllProbsNoBody(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getComments(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
