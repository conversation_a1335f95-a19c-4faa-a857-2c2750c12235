<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appserver.PingSessionHome_p3dfkh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="appServerAlive(java.lang.String,java.lang.String)"
    timeout="5000"
>
</method>
<method
    name="isReady()"
    timeout="5000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="5000"
>
</method>
<method
    name="getEJBHome()"
    timeout="5000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="5000"
>
</method>
<method
    name="remove()"
    timeout="5000"
>
</method>
<method
    name="getHandle()"
    timeout="5000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
