<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appserver.AppServerSessionHome_wx65il_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="loadWebServerMap()"
    timeout="600000"
>
</method>
<method
    name="stopServers(glog.ejb.appserver.db.AppMachinePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="stopMachines(glog.ejb.appserver.db.AppServerPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="listServers(glog.ejb.appserver.db.AppMachinePK)"
    timeout="600000"
>
</method>
<method
    name="setMachineURL(glog.ejb.appserver.db.AppMachinePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="redoDataQueues(glog.ejb.appserver.db.AppMachinePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBeanCaches(java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBeanCache(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBeanDetails(java.lang.String,glog.util.jdbc.Pk)"
    timeout="600000"
>
</method>
<method
    name="unloadBean(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unloadBeanCache(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unloadBeanCaches(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reloadBean(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="refreshBean(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="verifyBean(java.lang.String,glog.util.jdbc.Pk)"
    timeout="600000"
>
</method>
<method
    name="verifyBeanCache(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="verifyBeanCaches(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLocks(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLockDetails(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="stopServer(glog.ejb.appserver.db.AppMachinePK,glog.ejb.appserver.db.AppServerPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="startServer(glog.ejb.appserver.db.AppMachinePK,glog.ejb.appserver.db.AppServerPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unlockBean(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unlockBeanCache(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateMaxCacheSize(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTxDatas()"
    timeout="600000"
>
</method>
<method
    name="diagnoseTransactions()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="diagnoseTransactionsXML()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getScaSupport(glog.ejb.appserver.db.AppFunctionPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateScaSupport(glog.ejb.appserver.db.AppFunctionPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getWebFunctions()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getApplicationMode()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadAppServerMap()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
