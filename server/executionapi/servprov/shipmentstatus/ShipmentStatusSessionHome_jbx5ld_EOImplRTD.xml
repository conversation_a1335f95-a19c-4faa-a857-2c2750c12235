<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusSessionHome_jbx5ld_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="notify(java.lang.Long,java.lang.String,java.util.List,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addContainerBasedShipmentStatus(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEquipmentBasedEventHistory(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.util.uom.data.Duration,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTimeZoneAtStop(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getStatusGroups(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReasonGroups(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentStopLocationInfo(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentStopLocationInfo(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentRefnums(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSEquipmentDetails(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentStatus(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="validateEventGroups(java.lang.String,int,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addShipmentStatusLocationBasedMultipleShipment(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="queryInTransitShipments(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    timeout="600000"
>
</method>
<method
    name="copyShipmentEventActualValuesReceived(glog.ejb.shipment.db.ShipmentPK,java.lang.Long,boolean,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isServProvUser(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    timeout="600000"
>
</method>
<method
    name="getShipmentEvents(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMatchingShipments(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSShipUnits(java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSavedQueryResults(glog.util.jdbc.Pk,glog.ejb.savedquery.db.SavedQueryPK,glog.server.workflow.SavedQuerySqlAdapter$Factory)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getInTransitShipment(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    timeout="600000"
>
</method>
<method
    name="getInTransitShipments(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentStatusHistory(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    timeout="600000"
>
</method>
<method
    name="getShipmentEventHistory(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addShipmentStatus(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addShipmentStatus(glog.server.eventreason.EventContext,glog.server.eventreason.EventReason,java.lang.Long,java.util.Set,java.lang.String,java.util.Collection,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addShipmentStatus(glog.server.eventreason.EventContext,glog.server.eventreason.EventReason,java.lang.Long,java.util.Set,java.lang.String,java.util.Collection,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addShipmentStatusByQualifiers(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addForwarderShipmentStatus(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addForwarderShipmentStatusByQualifiers(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="hideShipmentStatus(glog.server.executionapi.servprov.shipmentstatus.ShipmentStatusRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyBuySideShipmentEventToSellSide(glog.server.workflow.shipmentstatus.SSEventForAgentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
