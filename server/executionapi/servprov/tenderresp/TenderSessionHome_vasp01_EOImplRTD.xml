<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.executionapi.servprov.tenderresp.TenderSessionHome_vasp01_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getTenderedShipment(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    timeout="600000"
>
</method>
<method
    name="getTenderShipmentList(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    timeout="600000"
>
</method>
<method
    name="respondToTender(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="awardOpenTender(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processConditionalBooking(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTenderStatusAndTypeValues()"
    timeout="600000"
>
</method>
<method
    name="isServiceProvider(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    timeout="600000"
>
</method>
<method
    name="getTenderCollabId(java.lang.String,glog.server.executionapi.servprov.tenderresp.TenderOfferStatus)"
    timeout="600000"
>
</method>
<method
    name="getMaxITransactionNumber(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTenderCollaborationDetails(glog.server.executionapi.servprov.tenderresp.TenderRequest)"
    timeout="600000"
>
</method>
<method
    name="getLocationTimezoneDetails(java.lang.Object[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentExternalPredicate(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
