<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.mediator.MediatorSessionHome_x19q6n_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="check(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="release(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnose(int,java.lang.String,java.lang.String,int,int)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(int,java.lang.String,java.lang.String,int,int)"
    timeout="10000"
>
</method>
<method
    name="resetStatistics()"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
