<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.process.ProcessControlSessionServerSideEJBWrapper_uw0g7k_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="resume(long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRequest(long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="request(java.lang.Long,java.lang.String,glog.util.CommandLine,int,glog.util.uom.data.Duration,glog.util.LocalTimestamp,glog.ejb.workflow.db.WorkflowTimeWindowPK,glog.ejb.appserver.db.AppServerPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unschedule(long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="schedule(glog.server.workflow.process.ProcessControlRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateServerAssignments()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTopicAliasData()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="hold(long,glog.util.LocalTimestamp,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRecurringRequests(glog.comm.query.Query$Criterion,glog.comm.query.Query$Criterion,glog.comm.query.Query$Criterion)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
