<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.notify.NotifySessionHome_1f287j_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getNotifyData(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="sendConsolidatedNotifyNow(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reSendConsolidatedNotify(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="sendTestMessage(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="receiveNotificationError(java.util.Collection,java.util.Date,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMailFile(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getOutboundStatsXML()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetOutboundStats()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getInboundStatsXML()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetInboundStats()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
