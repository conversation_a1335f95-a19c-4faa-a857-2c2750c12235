<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.TopicPublishSessionServerSideEJBWrapper_g37fhs_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="publish(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="publish(glog.server.workflow.Topic,glog.server.workflow.Workflow)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="abort(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="execute(glog.server.workflow.Topic,glog.server.workflow.Workflow)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="execute(glog.server.workflow.Topic,java.lang.Object)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="execute(glog.server.workflow.Topic,glog.server.workflow.Workflow,java.lang.Object)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="execute(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remote(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="onError(glog.server.workflow.Topic,glog.server.process.ProcessError)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="closeRemoteTopic(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="closeAllRemoteTopics(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="diagnoseRemoteTopics(java.lang.String,int,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="diagnoseRemoteTopicXML(java.lang.String,int,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetRemoteTopicStatistics(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processTimeout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="publishNoWait(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unschedule(long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeFromQueue(glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="onCompletion(glog.server.workflow.Topic,glog.server.process.ProcessResults)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="publishWait(glog.server.workflow.Topic,java.lang.Object,long,glog.server.workflow.OnPublishWaitTimeout)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="publishWait(glog.server.workflow.Topic,long,glog.server.workflow.OnPublishWaitTimeout)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="schedule(glog.server.workflow.Topic,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="schedule(glog.server.workflow.Topic,int,glog.util.uom.data.Duration,glog.util.LocalTimestamp,glog.ejb.appserver.db.AppServerPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
