<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.WorkflowSessionServerSideEJBWrapper_61kh96_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="execute(glog.server.workflow.WorkflowFactory,glog.server.workflow.Trigger,glog.server.workflow.Topic)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reloadStatus()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRegisteredTopics()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getQueueAssignments(int)"
    timeout="600000"
>
</method>
<method
    name="reload()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
