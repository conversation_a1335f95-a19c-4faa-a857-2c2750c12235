<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.workflow.WorkflowSessionNonTransServerSideEJBWrapper_t6tuwu_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="execute(glog.server.workflow.WorkflowFactory,glog.server.workflow.Trigger,glog.server.workflow.Topic)"
    timeout="600000"
>
</method>
<method
    name="getRegisteredTopics()"
    timeout="600000"
>
</method>
<method
    name="reloadStatus()"
    timeout="600000"
>
</method>
<method
    name="getQueueAssignments(int)"
    timeout="600000"
>
</method>
<method
    name="reload()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
