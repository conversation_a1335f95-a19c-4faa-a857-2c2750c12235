<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.scem.MonitorAgentLinkSessionServerSideEJBWrapper_pnl0gf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getMonitorAgentLink(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorAgentLink(glog.business.scem.MonitorAgentLink)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorAgentLink(glog.business.scem.MonitorAgentLink)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorAgentLink(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSavedQueryForMAL(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTimeUsingSQL(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadOutOfSequenceMALForSuccGid(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
