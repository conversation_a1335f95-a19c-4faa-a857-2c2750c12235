<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.scem.MonitorAgentSessionHome_uy1d0x_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getMonitorAgentType(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorAgentAll(glog.business.scem.MonitorAgent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorAgent(glog.business.scem.MonitorAgent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorAgent(glog.business.scem.MonitorAgent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorAgent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getProcessControlRequestIds(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="buildQuery(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="testQuery(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getFirstMPAforProcessing(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCriteria(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitorAgent(java.lang.String,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitorAgent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorAgent(glog.business.scem.MonitorAgent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
