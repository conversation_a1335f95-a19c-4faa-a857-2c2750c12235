<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.scem.MonitorAgentToleranceSessionServerSideEJBWrapper_6oy02x_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="addSavedCondition(glog.business.scem.MonitorToleranceRule)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorAgentTolerance(glog.business.scem.MonitorAgentTolerance)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorAgentTolerance(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isAssignedToMilestone(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitorToleranceRule(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSavedQueryForSavedCondition(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="evaluateSavedCondition(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifySavedCondition(glog.business.scem.MonitorToleranceRule)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteSavedCondition(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorAgentTolerance(glog.business.scem.MonitorAgentTolerance)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
