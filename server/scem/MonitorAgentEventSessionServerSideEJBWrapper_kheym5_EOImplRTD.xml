<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.scem.MonitorAgentEventSessionServerSideEJBWrapper_kheym5_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getMonitorAgentEvent(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorAgentEvent(glog.business.scem.MonitorAgentEvent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEventRemarks(java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSEquipmentXids(java.lang.String,java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setAgentEventsAsNotExceptions(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getExceptionEventsForShipments(java.util.Map,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitoredEventsForObjectGid(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorAgentEvent(glog.business.scem.MonitorAgentEvent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorAgentEvent(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitoredEventsForShipmentGid(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEventGroups(java.lang.String,java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
