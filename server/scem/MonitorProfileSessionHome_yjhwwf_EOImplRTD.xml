<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.scem.MonitorProfileSessionHome_yjhwwf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="plannedMilestoneExpirationDateChanged(java.lang.String,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDataQueryType(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="activateProfiles(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deactivateProfiles(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="disableSelectedMPAs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="recordDeletedProfile(glog.business.scem.MonitorProfileDeleted)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorProfileSelected(glog.business.scem.MonitorProfile,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTenderWarningTime(java.lang.String,glog.util.uom.data.Duration,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="activateDeactivateTimeBasedMPAs(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipmentGraph(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadMPAssignedToShipmentForMPT(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="evaluateSavedCondition(java.lang.String,glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unsubscribeDeactivatedTimeBasedMPAs(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSavedQueryForMPA(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadStopInfo(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getMonitorProfile(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBusinessObjDomain(glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="maaIsPerformAgentActionChangedTo(glog.business.scem.MonitorProfile,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="mxaIsPerformAgentActionChangedTo(glog.business.scem.MonitorProfile,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="subscribeMXA(glog.business.scem.MonitorProfile,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deactivateMilestone(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="activateTimeBasedMPAs(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deactivatePlannedMilestone(glog.business.scem.MonitorProfile,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteSuccLinks(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="nextExpectedMilestone(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="allMilestones(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="allRemainingMilestones(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="lastAchievedMilestones(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="allAchievedMilestones(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isPreviouslyAssigned(glog.comm.agent.DataQueryType,glog.util.jdbc.Pk,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="scheduleResetExpirationDate(glog.business.scem.MonitorProfile,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyMonitorProfileTemplate(glog.business.scem.MPTCopyObjectParam)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorProfile(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createMonitorProfileAll(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="assignMonitorProfile(java.lang.String,glog.util.jdbc.Pk[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorProfile(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyMonitorProfileAll(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorProfile(glog.business.scem.MonitorProfile)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteMonitorProfile(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
