<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.account.AccountSessionHome_ilv4zl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="addAccount(glog.server.account.AccountDetails)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAccount(glog.server.account.AccountDetails)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="activateAccount(glog.server.account.AccountDetails)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deactivateAccount(glog.server.account.AccountDetails)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addNotificationOverrides(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addAdHocNotification(glog.ejb.notify.db.AdhocNotifyData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="hasNotificationActivity(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deactivateUser(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addContact(glog.ejb.location.db.ContactData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
