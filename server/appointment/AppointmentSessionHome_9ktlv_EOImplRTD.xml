<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appointment.AppointmentSessionHome_9ktlv_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAppointmentBaseInfo(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentBaseInfos(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentBaseInfos(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="blockOrUnblockAppointmentTime(java.lang.String,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAppointments(java.lang.String,java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationResource(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isAppointmentScheduled(java.lang.String,int,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isAppointmentScheduled(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationInfo(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentBaseInfoByConfirmation(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="findPrevAppointment(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="findNextAppointment(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAppointment(glog.ejb.location.db.AppointmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAppointment(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAppointment(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppointment(java.lang.String,java.lang.String,java.lang.String,int,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isServprovUser()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setAppointmentFixed(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setAppointmentFixed(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDockRelatedShipments(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentOptions(java.lang.String,java.lang.String,int,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getObjectAppointmentsOnStop(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderByUserAssociation()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isAppointmentOnShipment(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationResourcesIdNameAtLocation(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getUnmatchedPref(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String[][])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changeAppointmentActivityType(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changeAppointmentActivityType(java.lang.String,java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentStartTime(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentTimeWindow(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="scheduleAppointment(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="scheduleAppointment(java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rescheduleAppointment(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="scheduleAppointmentsDragDrop(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationResources(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationResourcesWithCalendar(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAppointmentBaseInfosForNoAppointment(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
