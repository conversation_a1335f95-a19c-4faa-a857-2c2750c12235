<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appointment.ShipmentAppointmentSessionServerSideEJBWrapper_mpj1u9_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="removeAppointments(java.lang.String,java.util.Set)"
    timeout="600000"
>
</method>
<method
    name="blockOrUnblockAppointmentTime(java.lang.String,java.util.Set)"
    timeout="600000"
>
</method>
<method
    name="findPrevAppointment(java.lang.String,java.lang.String,int)"
    timeout="600000"
>
</method>
<method
    name="findNextAppointment(java.lang.String,java.lang.String,int)"
    timeout="600000"
>
</method>
<method
    name="updateAppointment(java.lang.String,java.lang.String,java.lang.String,int,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="rescheduleAppointment(java.lang.String,java.util.Set,glog.server.appointment.ScheduleMethod)"
    timeout="600000"
>
</method>
<method
    name="getAppointmentOptions(java.lang.String,java.lang.String,int,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="removeAppointment(java.lang.String,java.lang.String,int)"
    timeout="600000"
>
</method>
<method
    name="scheduleAppointment(java.lang.String,java.lang.String,int,glog.server.appointment.ScheduleMethod)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
