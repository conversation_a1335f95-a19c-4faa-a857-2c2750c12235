<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appointment.YardSessionHome_4ehx4f_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getYards(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="moveToYard(java.lang.String,int,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="blockYard(glog.business.appointment.Yard)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeFromYard(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="moveToYardManually(glog.business.appointment.Yard)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isObjectInYard(java.lang.String,java.lang.String,int,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
