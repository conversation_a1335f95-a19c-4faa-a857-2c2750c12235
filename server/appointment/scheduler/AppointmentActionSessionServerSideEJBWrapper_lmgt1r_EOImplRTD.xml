<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.appointment.scheduler.AppointmentActionSessionServerSideEJBWrapper_lmgt1r_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="search(glog.server.appointment.scheduler.ResourceRequest)"
    timeout="600000"
>
</method>
<method
    name="schedule(glog.server.appointment.scheduler.ResourceRequest)"
    timeout="600000"
>
</method>
<method
    name="persistInternal(glog.server.appointment.scheduler.ShipmentAppointment,int,glog.server.appointment.scheduler.ResourceRequest)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reschedule(glog.server.appointment.scheduler.ResourceRequest,java.util.Collection,glog.server.appointment.scheduler.ShipmentAppointment)"
    timeout="600000"
>
</method>
<method
    name="removeAppointments(java.lang.String,glog.server.appointment.scheduler.ShipmentAppointment,glog.server.appointment.scheduler.ResourceRequest)"
    timeout="600000"
>
</method>
<method
    name="removeAppointments(java.lang.String,java.lang.String,int,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateAppointment(java.lang.String,java.lang.String,int,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
