<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.process.walker.ProcessWalkerSessionHome_k5k1g1_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="release(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="rollup(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="resolveDeadlocks()"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(java.lang.Long,java.lang.String,java.lang.String,int,glog.util.uom.data.Duration,java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
