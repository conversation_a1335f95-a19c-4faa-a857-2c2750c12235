<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.event.EventSessionHome_bo59uh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="abort(java.lang.String,int,int)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(java.lang.String,java.lang.String,int,int)"
    timeout="10000"
>
</method>
<method
    name="diagnose(java.lang.String,java.lang.String,int,int)"
    timeout="10000"
>
</method>
<method
    name="kill(java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="resetStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="increase(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="preempt(java.lang.String,int)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
