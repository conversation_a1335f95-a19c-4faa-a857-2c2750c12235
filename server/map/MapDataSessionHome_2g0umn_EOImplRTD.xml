<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.map.MapDataSessionHome_2g0umn_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getShipmentMap(java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getDriverMap(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getLocationMap(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getMapOrderMovements(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getShipmentMapFromLocation(java.lang.String,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getShipmentMapFromOrder(java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getMapOrderReleases(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="lookupDriveDirections(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getORShipmentRoutes(java.lang.String[],java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getDriver(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
