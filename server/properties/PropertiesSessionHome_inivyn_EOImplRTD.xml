<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.properties.PropertiesSessionHome_inivyn_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="setProperty(java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="getProperty(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="getProperties(java.lang.String,boolean)"
    timeout="10000"
>
</method>
<method
    name="removeProperty(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="removeProperty(java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="addProperty(java.lang.String,java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="revertToInitialValues()"
    timeout="10000"
>
</method>
<method
    name="listProperties(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="readPropertySet(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="validatePropertySet(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="getPendingRestart()"
    timeout="10000"
>
</method>
<method
    name="clearPropertyLog(glog.ejb.property.db.PropInstructionSetPK[],glog.util.uom.data.Duration)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
