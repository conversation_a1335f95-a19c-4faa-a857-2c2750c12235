<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.agent.AgentSessionHome_lkwtkf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAgent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createAgent(glog.comm.agent.Agent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildEventSet(java.lang.Class)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getQueryClassMap()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEventSetsByNotifySubjects(java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAssociatedDataQueryTypes(glog.comm.agent.DataQueryType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllowableAssociationActions(glog.comm.agent.DataQueryType,glog.comm.agent.ActionBlockType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDataTypeAssociationsByFromType(glog.comm.agent.DataQueryType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDataQueryTypeAssociation(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getActionType(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyAgent(glog.comm.agent.Agent)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAgent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyAllDomainAgents(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyAgent(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEvent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllowableActions(glog.comm.agent.DataQueryType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllowableActions(java.util.Set)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
