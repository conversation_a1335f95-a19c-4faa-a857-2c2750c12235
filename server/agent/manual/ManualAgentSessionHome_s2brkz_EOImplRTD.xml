<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.agent.manual.ManualAgentSessionHome_s2brkz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="runManualAgent(java.lang.String,glog.util.jdbc.Pk,glog.server.workflow.variables.Variables)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="runManualAgent(java.lang.String,glog.util.jdbc.Pk)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="runManualAgent(java.lang.String,java.util.Collection,glog.server.workflow.variables.Variables)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="runManualAgent(java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
