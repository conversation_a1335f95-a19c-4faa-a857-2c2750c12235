<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.sku.SkuActionHome_55cor3_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getSumPackagedItems(glog.ejb.sku.db.SkuData,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSkuDescriptorXmlMap(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getSkuTransactionDescriptorXmlMap(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getShipmentGids(java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="getQuantityOnHand(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
