<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.ilm.SkuServiceSessionHome_ta9xu9_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="decrementBucketCount(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setBucketCount(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="linkAssetInstanceToBucket(java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="linkAssetInstanceToBucket(java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.LocalTimestamp,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unlinkAssetFromBucket(java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unlinkAssetFromBucket(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createBucket(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createSku(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuQuantityAssetSequence(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBucketLevelsValues(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAvailableAssetsMapAtLocation(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAvailableAssetsAtLocation(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAvailableAssetsTypesAtLocation(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAssetLocation(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllAssetLocations(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBucketAndLocnForAsset(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAssetSightingLocation(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAssociatedBuckets(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildBucketTypes(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isTotalBucket(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllTopLevelParentBuckets()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isBucketTreesCacheInstanceAlive()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBucketTreesCacheInstance()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rebuildBucketTreesCacheInstance()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildLocationList(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildLocnList(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getInstanceSkuBuckets(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTotalSkuBuckets(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAggregateInstanceSkuBuckets(java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reserveAssetType(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="releaseAssetType(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createdOrModifiedBucket(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removedBucket(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="assetDwellTime(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuShipmentLinks(glog.ejb.shipment.db.ShipmentPK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuShipmentLinks(glog.ejb.sku.db.SkuPK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuOrderBaseLinks(glog.ejb.sku.db.SkuPK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuOrderBaseLinks(glog.ejb.orderbase.db.ObOrderBasePK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateSkuQuantities(glog.ejb.orderbase.db.ObOrderBasePK,glog.ejb.scem.db.DataTypeAssociationPK,glog.ejb.sku.db.SkuQuantityTypePK,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateSkuQuantities(glog.ejb.shipment.db.ShipmentPK,glog.ejb.scem.db.DataTypeAssociationPK,glog.ejb.sku.db.SkuQuantityTypePK,java.lang.Long,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateSkuQuantities(glog.ejb.shipment.db.ShipmentPK,glog.ejb.scem.db.DataTypeAssociationPK,glog.ejb.sku.db.SkuQuantityTypePK,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateSkuQuantities(glog.ejb.order.db.OrderReleasePK,glog.ejb.scem.db.DataTypeAssociationPK,glog.ejb.sku.db.SkuQuantityTypePK,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildSkus(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getChildSkusMap(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAssetInventory(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuOrderReleaseLinks(glog.ejb.sku.db.SkuPK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSkuOrderReleaseLinks(glog.ejb.order.db.OrderReleasePK,glog.ejb.scem.db.DataTypeAssociationPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBucketContents(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="incrementBucketCount(java.lang.String,java.lang.String,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
