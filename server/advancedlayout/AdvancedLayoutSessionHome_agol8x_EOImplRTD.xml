<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.advancedlayout.AdvancedLayoutSessionHome_agol8x_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(glog.ejb.advancedlayout.db.AdvancedLayoutData,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.advancedlayout.db.AdvancedLayoutData)"
    timeout="600000"
>
</method>
<method
    name="getAdvancedLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAdvancedLayoutNoClob(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAdvancedLayouts(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="deleteAdvancedLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteAdvancedLayouts(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
