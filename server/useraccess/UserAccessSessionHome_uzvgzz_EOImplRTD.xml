<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.useraccess.UserAccessSessionHome_uzvgzz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getUserAccessData(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserAccess(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNewUserWsReportAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewSavedQueryAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewMobileActionAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewAskOtmSavedQueryAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewFinderSetAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewDefaultFinderSetAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewUserPreferenceAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewAppActionCheckAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewAppPowerActionAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewAppActionReasonAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewAppActionMorgAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewStatusTypeFilterAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateUserMenuAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateUserWsReportAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateUserPreferenceAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateSavedQueryAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateMobileActionAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAskOtmSavedQueryAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateFinderSetAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateDefaultFinderSetAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppActionCheckAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppPowerActionAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppActionReasonAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppActionMorgAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateStatusTypeFilterAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getUserAccesses(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="deleteUserAccess(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteUserAccessIfUnused(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createNewUserMenuAccess(glog.ejb.useraccess.db.UserAccessData,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setUserPreference(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setUserMenuLayout(java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
