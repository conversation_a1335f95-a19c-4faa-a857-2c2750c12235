<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.rateload.RateUploadTrxnSessionHome_jtxggf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="processBDT(glog.util.beandata.BDT,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeRMRrReferences(java.lang.String,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="registerUploadinRLError(java.lang.Integer,org.apache.poi.xssf.usermodel.XSSFWorkbook)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertRateBaseDef(glog.ejb.rates.db.RateBaseDefData,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateBaseDef(glog.ejb.rates.db.RateBaseDefData,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRLSpreadSheetinRLHistory(org.apache.poi.xssf.usermodel.XSSFWorkbook,java.lang.Integer,java.lang.Integer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertXLane(glog.ejb.xlane.db.XLaneData,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertRateVersion(glog.ejb.rates.db.RateVersionData,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="registerUploadinRLHistory(java.lang.Integer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="registerUploadinRLSummary(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTotalUploadedROCount(java.lang.Integer,int,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTotalUploadedRRCount(java.lang.Integer,int,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateApprovedROCount(java.lang.Integer,int,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateApprovedRRCount(java.lang.Integer,int,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadExcel(java.lang.Integer,byte[],glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadErrorExcel(java.lang.Integer,byte[],glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="retrieveRateLoadExcel(java.lang.Integer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="retrieveErrorRates(java.lang.Integer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isRateLoadGroupPresent(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertRateLoadGroup(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeRMRoReferences(java.lang.String,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadRoD(java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadRrD(java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,glog.util.jdbc.T2SharedConnection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
