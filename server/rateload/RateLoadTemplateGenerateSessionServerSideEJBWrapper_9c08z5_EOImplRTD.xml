<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.rateload.RateLoadTemplateGenerateSessionServerSideEJBWrapper_9c08z5_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="generateRateLoadTemplate(java.lang.String,java.util.Map,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateRateLoadTemplate(java.lang.String,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateRateLoadTemplate(java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="createRateLoadDefinition(java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="loadRateLoadFieldData(java.util.List,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateRateLoadDefinition(java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="loadStatusValueDesc(java.util.List,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="isValidRateLoadDefinitionToDelete(java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
