<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.rateload.RateLoadApprovalActionSessionServerSideEJBWrapper_2ltxkh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="approveRates(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectRates(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveRateOfferingOnly(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveRateOfferingAndAssRecord(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveNewROAndAssNewRRs(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectRateOfferingOnly(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectRateOfferingAndAssRecords(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="undoRateOfferingOnly(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="undoRateOfferingAndAssRecords(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveRateRecord(int[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveRateRecord(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectRateRecord(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectRateRecord(int[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="undoRateRecord(int[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="undoRateRecord(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveNewRates(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="undoRates(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="finalizeRates(int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadSummary(int,int,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
