<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.rateload.RateUploadSessionHome_hozfnz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getRateLoadTemplateData(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateUploadedSpreadSheet(byte[],java.lang.String,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="loadRateLoadHistoryData(int)"
    timeout="600000"
>
</method>
<method
    name="loadRateLoadSummaryData(int)"
    timeout="600000"
>
</method>
<method
    name="getEmailIdsOfInvolvedParties(int)"
    timeout="600000"
>
</method>
<method
    name="setRateLoadHistoryStatus(int,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateAndPersistRates(java.util.HashMap,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="doesRLDHasEmailContact(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="populateUploadRatesBaseTables(boolean,java.util.HashMap)"
    timeout="600000"
>
</method>
<method
    name="validateRateData(org.apache.poi.xssf.usermodel.XSSFWorkbook,java.lang.String,glog.server.rateload.RateLoadTemplateMetaData,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="processRateData(java.lang.String,java.lang.String,org.apache.poi.xssf.usermodel.XSSFWorkbook,int,glog.server.rateload.RateLoadTemplateMetaData,java.util.Map,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
