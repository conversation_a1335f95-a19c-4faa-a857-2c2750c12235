<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.bngenerator.BNSessionHome_7sdgcx_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="generate(glog.server.bngenerator.BNType,glog.server.bngenerator.BNTypeContext)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="generate(glog.server.bngenerator.BNType,glog.server.bngenerator.BNTypeContext,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetTestRule(glog.server.bngenerator.BNType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetTestRule(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetTestRange(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="manualCloseBNRange(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="testBNG(glog.server.bngenerator.BNType,glog.server.bngenerator.BNTypeContext[],int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="testBNG(java.lang.String,java.lang.String,glog.server.bngenerator.BNTypeContext[],int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="reset(glog.server.bngenerator.BNType)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
