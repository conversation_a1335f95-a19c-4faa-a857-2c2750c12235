<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.fusion.cil.CILServiceSessionHome_jwvcj5_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="find(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="delete(glog.fusion.cil.DataObject)"
    timeout="600000"
>
</method>
<method
    name="delete(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="insert(glog.fusion.cil.DataObject)"
    timeout="600000"
>
</method>
<method
    name="insert(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="create(glog.fusion.cil.MetaData)"
    timeout="600000"
>
</method>
<method
    name="update(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="update(glog.fusion.cil.DataObject)"
    timeout="600000"
>
</method>
<method
    name="findAll(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="produceMetaDataSchema(glog.fusion.cil.MetaData)"
    timeout="600000"
>
</method>
<method
    name="findAllPks(glog.fusion.cil.CILServiceParameters)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
