<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.trackingevent.TrackingEventSessionHome_6297kl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="reprocess(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="markAsError(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPackageEvents(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateOrderEventData(glog.ejb.shipmentstatus.db.IeSsRemarkData,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyITNNumberToTransaction(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rollbackSighting(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createEquipmentLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createDriverLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createPowerUnitLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createShipmentLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createRelatedEventLink(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,glog.ejb.shipmentstatus.db.IeShipmentstatusPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeEquipmentLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeDriverLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removePowerUnitLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeShipmentLinks(glog.ejb.shipmentstatus.db.IeShipmentstatusPK,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeRelatedEventLink(glog.ejb.shipmentstatus.db.IeShipmentstatusPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
