<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.managerlayout.ManagerLayoutSessionHome_xue07n_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="update(glog.ejb.managerlayout.db.ManagerLayoutData,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getManagerLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getManagerLayoutNoClob(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getManagerLayouts(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="deleteManagerLayout(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNew(glog.ejb.managerlayout.db.ManagerLayoutData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteManagerLayouts(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
