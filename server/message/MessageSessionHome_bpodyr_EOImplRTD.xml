<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.server.message.MessageSessionHome_bpodyr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="resetTopicStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseTopic(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseBeanUpdateXML(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseBeanUpdate(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseTopicXML(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="resetCacheRefreshStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseQueryUpdate(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseQueryUpdateXML(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="resetQueryUpdateStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="addCachedQuery(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="reconnectSubscriptions(boolean)"
    timeout="10000"
>
</method>
<method
    name="resetBeanUpdateStatistics(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseCacheRefresh(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="diagnoseCacheRefreshXML(java.lang.String)"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
