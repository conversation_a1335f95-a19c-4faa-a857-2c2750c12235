<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.resourceschedule.ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="generateResSchInstance(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getResSchInstancesForGivenTimes(java.lang.String[],glog.util.LocalTimestamp[],glog.util.LocalTimestamp[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getExistingResSchInstances(java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getAllResSchInstances(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
