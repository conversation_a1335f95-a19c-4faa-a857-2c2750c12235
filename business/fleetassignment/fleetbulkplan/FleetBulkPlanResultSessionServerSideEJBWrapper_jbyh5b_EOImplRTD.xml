<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.fleetassignment.fleetbulkplan.FleetBulkPlanResultSessionServerSideEJBWrapper_jbyh5b_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="executeFleetBulkPlan(glog.ejb.fleetbulkplan.db.FleetBulkPlanPK,glog.util.LocalTimestamp,glog.ejb.shipment.db.ShipmentPK[],java.lang.String,glog.ejb.driver.db.DriverPK[],glog.ejb.equipment.db.EquipmentPK[],long)"
    timeout="600000"
>
</method>
<method
    name="updateFleetBulkPlan(glog.ejb.fleetbulkplan.db.FleetBulkPlanData,glog.ejb.fleetbulkplan.db.FleetBulkPlanCostData[],glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="generateFleetBulkPlanPK(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
