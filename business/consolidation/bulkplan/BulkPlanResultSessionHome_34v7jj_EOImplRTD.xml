<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.consolidation.bulkplan.BulkPlanResultSessionHome_34v7jj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="updateBulkPlanMilestone(glog.business.consolidation.bulkplan.TBulkPlanMilestone,int)"
    timeout="600000"
>
</method>
<method
    name="createBulkPlanMilestoneError(glog.business.consolidation.bulkplan.TBulkPlanMilestoneError)"
    timeout="600000"
>
</method>
<method
    name="generateBulkPlanPK(java.lang.String,java.lang.String,glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="generateBulkPlanPK(java.lang.String,java.lang.String,glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getBulkPlanMachines(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="terminateBulkPlan(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateBulkPlanResult(glog.business.consolidation.bulkplan.TBulkPlanResult)"
    timeout="600000"
>
</method>
<method
    name="updateBulkPlanPartitionResult(glog.business.consolidation.bulkplan.TBulkPlanPartitionResult)"
    timeout="600000"
>
</method>
<method
    name="getBulkPlanMilestones(glog.ejb.bulkplan.db.BulkPlanPK)"
    timeout="600000"
>
</method>
<method
    name="getBulkPlanMilestoneErrors(glog.ejb.bulkplan.db.BulkPlanMilestonePK)"
    timeout="600000"
>
</method>
<method
    name="getBulkPlanMilestoneErrors(glog.ejb.bulkplan.db.BulkPlanPK)"
    timeout="600000"
>
</method>
<method
    name="createBulkPlanMilestone(glog.business.consolidation.bulkplan.TBulkPlanMilestone)"
    timeout="600000"
>
</method>
<method
    name="startBulkPlan(java.lang.String,glog.util.LocalTimestamp,long)"
    timeout="600000"
>
</method>
<method
    name="endBulkPlan(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="endBulkPlanPartition(glog.ejb.bulkplan.db.BulkPlanPartitionPK)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
