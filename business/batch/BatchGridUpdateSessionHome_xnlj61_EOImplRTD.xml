<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.batch.BatchGridUpdateSessionHome_xnlj61_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="saveSchedule(glog.business.batch.TSchedule)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveBatch(glog.business.batch.TBatch)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveBatchGrid(glog.business.batch.TBatchGrid)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteBatchGrid(glog.business.batch.TBatchGrid)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateBatchNames(java.lang.String,java.lang.String[],java.lang.String[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveScheduleDoors(glog.business.batch.TSchedule)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
