<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.logisticsguide.LogisticsGuideTransactionSessionServerSideEJBWrapper_2znqfj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="updateLgAddress(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="finishLogisticsGuide(glog.ejb.logisticsguide.db.LogisticsGuidePK,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="createDetailRecords(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="createAggregateRecords(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="createExchangeRateRecords(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="generateLogisticsGuidePK(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="startLogisticsGuide(glog.ejb.logisticsguide.db.LogisticsGuidePK,glog.util.LocalTimestamp,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
