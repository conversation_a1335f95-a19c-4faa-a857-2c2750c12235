<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.shipment.ShipmentCalcSessionHome_9fniof_HomeImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
     name="*"
    idempotent="true"
>
</method>
<method
    name="create()"
    requires-transaction="true"
    idempotent="true"
    timeout="600000"
>
</method>
<method
    name="getHomeHandle()"
    requires-transaction="true"
    idempotent="true"
    timeout="600000"
>
</method>
<method
    name="remove(java.lang.Object)"
    requires-transaction="true"
    idempotent="true"
    timeout="600000"
>
</method>
<method
    name="remove(javax.ejb.Handle)"
    requires-transaction="true"
    idempotent="true"
    timeout="600000"
>
</method>
<method
    name="getEJBMetaData()"
    requires-transaction="true"
    idempotent="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
