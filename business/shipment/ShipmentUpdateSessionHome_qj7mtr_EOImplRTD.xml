<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.shipment.ShipmentUpdateSessionHome_qj7mtr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="commitSShipUnitChanges(glog.ejb.shipment.db.ShipmentStopDData[],glog.ejb.shipment.db.SEquipmentSShipUnitJoinData[],glog.ejb.order.db.OrderMovementDData[],glog.ejb.shipment.db.ShipmentStopDPK[],glog.ejb.shipment.db.SEquipmentSShipUnitJoinPK[],glog.ejb.shipment.db.SShipUnitPK[],glog.ejb.order.db.OrderMovementDPK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitSplitOrderChanges(java.util.List,glog.business.order.TOrderRelease[],java.util.List,java.util.List,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitOrderModChanges(glog.business.order.TOrderRelease,glog.util.genericcontainer.BusinessObject,java.util.Map,glog.business.planningstructure.PlanningObjectGraph[],java.util.Map)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataChanges(glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataChanges(glog.util.remote.BeanData[],glog.business.util.RecalcSuppressible)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteByPK(glog.util.jdbc.Pk[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentTimesManually(glog.ejb.shipment.db.ShipmentPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentStopTimesManually(glog.ejb.shipment.db.ShipmentStopPK,glog.business.shipment.TShipmentStop)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateEShipment(glog.business.action.ServerActionEShipment)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteXDockHandlingAndDownstreamShipments(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.order.OrderRelease[],glog.business.consolidation.bulkplan.XDock,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentStopDebriefDatas(glog.ejb.shipment.db.ShipmentStopDebriefData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentOrderReleaseJoinDatas(glog.ejb.shipment.db.ShipmentOrderReleaseJoinData[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createShipmentSSSecondaryShipmentJoin(glog.business.shipmentstructure.ShipmentGraph,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteShipmentSSSecondaryShipmentJoin(glog.business.shipmentstructure.ShipmentGraph,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setCompletionState(glog.ejb.shipment.db.ShipmentSpecialServicePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentSpecialService(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="appendActivity(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTShipmentSpecialServices(glog.business.shipment.TShipment,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentRefnumData(glog.ejb.shipment.db.ShipmentRefnumData,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentRefnumData(java.util.List,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTShipmentGroup(glog.business.shipmentgroup.TShipmentGroup,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentGraph(glog.business.shipmentstructure.ShipmentGraph,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentGraph(glog.business.shipmentstructure.ShipmentGraph)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitShipmentGraphCollection(glog.business.shipmentstructure.ShipmentGraphCollection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTSShipUnits(glog.util.genericcontainer.GenericContainer,glog.business.shipment.TSShipUnit[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTWorkAssignments(glog.business.workassignment.TWorkAssignment[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateJobMemoBlStatus(glog.ejb.job.db.JobPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
