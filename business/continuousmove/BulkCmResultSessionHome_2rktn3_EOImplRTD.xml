<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.continuousmove.BulkCmResultSessionHome_2rktn3_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="generateBulkCmPK(java.lang.String,java.lang.String,glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="executeBulkCm(glog.ejb.bulkcm.db.BulkCmPK,glog.util.LocalTimestamp,glog.ejb.shipment.db.ShipmentPK[],long,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="startBulkCm(glog.ejb.bulkcm.db.BulkCmPK,glog.util.LocalTimestamp,int,long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="endBulkCm(glog.ejb.bulkcm.db.BulkCmPK,glog.util.LocalTimestamp)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateBulkCmData(glog.ejb.bulkcm.db.BulkCmData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
