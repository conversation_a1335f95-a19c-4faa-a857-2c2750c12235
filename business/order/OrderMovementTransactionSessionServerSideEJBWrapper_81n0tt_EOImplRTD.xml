<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.order.OrderMovementTransactionSessionServerSideEJBWrapper_81n0tt_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="updateOrderMovementData(glog.ejb.order.db.OrderMovementData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveOrderMovements(glog.business.order.TOrderMovement[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveOrderMovements(glog.business.order.TOrderMovement[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteOrderMovementDs(glog.business.order.TOrderMovement)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveOrderMovementDs(glog.business.order.TOrderMovement,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveOrderMovementDs(glog.business.order.TOrderMovement,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteOrderMovements(glog.business.order.TOrderMovement[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteOrderMovements(glog.business.order.TOrderMovement[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateOrderMovementsAndContainerGroup(glog.business.order.TOrderMovement[],glog.business.unitization.TContainerGroup)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateOrderMovementsToORDestination(glog.ejb.order.db.OrderMovementPK[],glog.ejb.order.db.OrderReleasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="manuallyCreateOrderMovements(glog.ejb.order.db.OrderMovementData[],glog.ejb.shipment.db.SShipUnitData[],java.util.ArrayList,glog.ejb.shipment.db.SEquipmentData[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="manuallyCreateOrderMovements(glog.ejb.order.db.OrderMovementData[],glog.ejb.shipment.db.SShipUnitData[],java.util.ArrayList,glog.ejb.shipment.db.SEquipmentData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
