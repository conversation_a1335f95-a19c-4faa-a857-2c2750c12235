<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.order.ObLinkSessionHome_m7rybz_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="addObLinkData(glog.ejb.orderbase.db.ObLinkData)"
    timeout="600000"
>
</method>
<method
    name="getPrevObLinkInfo(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getNextObLinkInfo(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteObLinkData(java.util.ArrayList,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteAllObLinkData(java.util.ArrayList)"
    timeout="600000"
>
</method>
<method
    name="getAllLinksInChain(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
