<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.order.OrderTransactionSessionServerSideEJBWrapper_czbqgf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="resetIsProcessedOnObSuReleaseInstr(glog.ejb.orderbase.db.ObSuReleaseInstructionPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetIsProcessedOnObSuReleaseInstr(glog.ejb.orderbase.db.ObOrderBasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateOrderReleaseServices(glog.ejb.order.db.OrderReleaseServiceData[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeOrderReleaseServices(glog.ejb.order.db.OrderReleasePK,glog.ejb.reference.db.CustomerServicePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setOrderReleaseStatus(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setObShipUnitStatus(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="generateBill(glog.ejb.order.db.OrderReleasePK[],glog.business.invoice.TInvoice,glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertLineReleaseInstruction(glog.ejb.orderbase.db.ObReleaseInstructionData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="insertSUReleaseInstruction(glog.ejb.orderbase.db.ObSuReleaseInstructionData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateORLineTransData(glog.ejb.order.db.OrderReleaseLineData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateORLineTransData(glog.ejb.order.db.OrderReleaseLineData[],glog.ejb.order.db.OrderReleaseLineRemarkData[],glog.ejb.order.db.OrderReleaseLineTextData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataNewAndChanged(glog.util.remote.BeanData[],glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataNew(glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataChanges(glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeOrderRelease(glog.ejb.order.db.OrderReleasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetIsProcessedOnObReleaseInstr(glog.ejb.orderbase.db.ObReleaseInstructionPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="resetIsProcessedOnObReleaseInstr(glog.ejb.orderbase.db.ObOrderBasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
