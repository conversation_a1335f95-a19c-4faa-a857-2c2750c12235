<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.orderbase.OrderBaseUpdateSessionHome_todvgr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="deleteObSuReleaseInstruction(glog.ejb.orderbase.db.ObSuReleaseInstructionPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteObReleaseInstruction(glog.ejb.orderbase.db.ObReleaseInstructionPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTObReleaseInstructions(glog.business.orderbase.TObReleaseInstruction[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTObSuReleaseInstructions(glog.business.orderbase.TObSuReleaseInstruction[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
