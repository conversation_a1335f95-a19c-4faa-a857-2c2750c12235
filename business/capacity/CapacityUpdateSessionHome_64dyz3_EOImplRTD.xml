<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.capacity.CapacityUpdateSessionHome_64dyz3_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="overrideCapacityLimit(glog.ejb.capacity.db.CapacityUsagePK,java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyDailyCapacityLimit(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="expireCapacityLimitManually(glog.ejb.capacity.db.CapacityLimitPK,glog.util.LocalTimestamp,glog.ejb.capacity.db.CapacityUsageData[],glog.util.LocalTimestamp,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="modifyCapacityLimit(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
