<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.job.JobServerActionSessionHome_v3ni7j_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="deleteJobs(glog.ejb.job.db.JobPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="addOrdersToJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderReleasePK[])"
    timeout="600000"
>
</method>
<method
    name="addOrderMovementsToJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderMovementPK[])"
    timeout="600000"
>
</method>
<method
    name="removeOrdersFromJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderReleasePK[])"
    timeout="600000"
>
</method>
<method
    name="removeOrderMovementsFromJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderMovementPK[])"
    timeout="600000"
>
</method>
<method
    name="deleteJobBills(glog.ejb.job.db.JobPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="applyNFRCRules(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="applyNFRCRuleManually(glog.ejb.job.db.JobPK,glog.ejb.job.db.NfrcRulePK,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="applyPaymentTerms(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="assignSpecificAgentToJob(glog.ejb.job.db.JobPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="autoAssignAgentToJob(glog.ejb.job.db.JobPK,glog.ejb.job.db.ShippingAgentTypePK)"
    timeout="600000"
>
</method>
<method
    name="setExchangeRate(glog.ejb.job.db.JobPK[],glog.util.LocalDate,glog.ejb.currency.db.ExchangeRatePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="autoAssignShippingAgentRsp(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="removeShippingAgentContactsFromJob(glog.ejb.job.db.ShippingAgentContactPK[],glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="removeShippingAgentContactsFromJob(glog.ejb.job.db.ShippingAgentTypePK,glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="manualAssignShippingAgentRsp(glog.ejb.job.db.ShippingAgentContactPK,glog.ejb.job.db.JobPK,glog.ejb.shipment.db.ShipmentPK[],glog.ejb.job.db.ShipmentAgentRspPK[])"
    timeout="600000"
>
</method>
<method
    name="updateShipmentAgentRsps(glog.ejb.job.db.ShippingAgentContactPK,glog.ejb.job.db.JobPK,java.util.HashMap)"
    timeout="600000"
>
</method>
<method
    name="notifyShippingAgent(glog.ejb.job.db.JobPK[])"
    timeout="600000"
>
</method>
<method
    name="notifyShippingAgent(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="getShippingAgentRuleTypes()"
    timeout="600000"
>
</method>
<method
    name="getJobQueryType(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="getJobIdsForOrderReleases(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[])"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.consol.db.ConsolPK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.consol.db.ConsolPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[])"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getJobIdsForOrderReleasesWithJobType(java.lang.String[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
