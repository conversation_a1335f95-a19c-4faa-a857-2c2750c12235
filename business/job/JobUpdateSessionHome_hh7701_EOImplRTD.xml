<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.job.JobUpdateSessionHome_hh7701_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[],java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderMovementPK[],java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.business.job.TJob,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.business.job.TJob)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createJob(glog.ejb.order.db.OrderReleasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteJobs(glog.ejb.job.db.JobPK[],glog.ejb.invoice.db.InvoicePK[],glog.ejb.shipment.db.ShipmentPK[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addOrdersToJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addOrdersToJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderReleasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeOrdersFromJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderReleasePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeOrderMovementsFromJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderMovementPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addInvolvedPartiesToJob(glog.ejb.job.db.JobInvolvedPartyData[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addInvolvedPartiesToJob(glog.ejb.job.db.JobInvolvedPartyData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addRefnumsToJobs(glog.ejb.job.db.JobRefnumData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateExchangeRateInfo(java.util.List,java.util.List,glog.business.util.RecalcSuppressible,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addToShipmentAgentRsp(glog.ejb.job.db.ShipmentAgentRspData[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeInvolvedPartyToJob(glog.ejb.job.db.JobInvolvedPartyPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeInvolvedPartyToJob(glog.ejb.job.db.JobPK,glog.ejb.job.db.ShippingAgentContactPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeShipmentAgentRsp(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeShipmentAgentRsp(glog.ejb.shipment.db.ShipmentPK,glog.ejb.job.db.ShippingAgentContactPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeShipmentAgentRsp(glog.ejb.job.db.JobPK,glog.ejb.job.db.ShippingAgentContactPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeShipmentAgentRsp(glog.ejb.job.db.ShipmentAgentRspPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentAgentRsp(glog.ejb.job.db.ShipmentAgentRspData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addOrderMovementsToJob(glog.ejb.job.db.JobPK,glog.ejb.order.db.OrderMovementPK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteJob(glog.ejb.job.db.JobPK,glog.ejb.invoice.db.InvoicePK[],glog.ejb.shipment.db.ShipmentPK[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
