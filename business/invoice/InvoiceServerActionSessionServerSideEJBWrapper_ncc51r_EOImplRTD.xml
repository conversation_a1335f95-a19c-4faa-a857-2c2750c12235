<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.invoice.InvoiceServerActionSessionServerSideEJBWrapper_ncc51r_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="autoApproveAdjustedCosts(glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="autoApproveAdjustedCosts(glog.ejb.invoice.db.InvoicePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="unApprovePayment(glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustPayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustLinePayment(glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustInvoiceForShipments(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="adjustInvoiceForShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="billAdjustedJobCosts(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="rejectInvoice(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="unRejectInvoice(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findPotentialMatchesForInvoice(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="approveBill(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="approveBill(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="unApproveBill(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="deleteBill(glog.ejb.invoice.db.InvoicePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="deleteInvoice(glog.ejb.invoice.db.InvoicePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="generateInvoice(glog.ejb.shipment.db.ShipmentPK,glog.ejb.invoice.db.InvoiceSplitRulePK)"
    timeout="600000"
>
</method>
<method
    name="generateInvoice(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="generateChildInvoices(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="generateParentInvoiceReturnResult(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="generateParentInvoice(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="addInvoicesToConsolidation(glog.ejb.invoice.db.InvoicePK[],glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="removeInvoicesFromConsolidation(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="createBalanceDueBill(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency)"
    timeout="600000"
>
</method>
<method
    name="createBalanceDueBillByLine(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.ApproveLineInput[])"
    timeout="600000"
>
</method>
<method
    name="generateCustomerBill(glog.ejb.job.db.JobPK)"
    timeout="600000"
>
</method>
<method
    name="generateCustomerBill(glog.ejb.shipment.db.ShipmentPK,glog.ejb.invoice.db.InvoiceSplitRulePK)"
    timeout="600000"
>
</method>
<method
    name="generateCustomerBill(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="createCreditNote(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="changeInvoiceStatuses(glog.ejb.invoice.db.InvoicePK[],java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="changeInvoiceStatus(glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="modificationAcknowledged(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="generateChildCustomerBills(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="calculateVATForInvoice(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="matchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="matchInvoice(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.matchrule.MatchOutcome,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="approvePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="autoApprovePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="approveLinePayment(glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAutoApproveInvoiceErrorNote(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="getLineApproveInfo(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="getLineApproveInfo(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="canPerformApproval(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="matchInvoices(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="unMatchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateMatch(glog.ejb.invoice.db.InvoicePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="calculateVATManuallyForInvoice(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="generateBNInvoiceNumber(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="determineOrderReleasesForRecharge(glog.ejb.invoice.db.VoucherPK,glog.ejb.reference.db.InvolvedPartyQualPK,glog.ejb.reference.db.InvolvedPartyQualPK,glog.ejb.order.db.OrderReleaseRefnumQualPK,glog.ejb.invoice.db.InvoiceRefnumQualPK)"
    timeout="600000"
>
</method>
<method
    name="createRechargeShipments(glog.ejb.invoice.db.VoucherPK,glog.ejb.savedquery.db.SavedQueryPK,java.lang.Character,java.lang.Character)"
    timeout="600000"
>
</method>
<method
    name="setVoucherStatus(glog.ejb.invoice.db.VoucherPK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calculateProvincialVATForInvoice(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="setExchangeRate(glog.ejb.invoice.db.VoucherPK[],glog.util.LocalDate,glog.ejb.currency.db.ExchangeRatePK)"
    timeout="600000"
>
</method>
<method
    name="setExchangeRate(glog.ejb.invoice.db.InvoicePK[],glog.util.LocalDate,glog.ejb.currency.db.ExchangeRatePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="updateShipmentEquipmentFromInvoice(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="recoverMatchFailedInvoices(glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="setGlDate(glog.ejb.invoice.db.InvoicePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getObjectCostTotal(java.lang.String,java.lang.String,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getShipmentBillDataList(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="sendVoucherTransmission(glog.ejb.invoice.db.VoucherPK)"
    timeout="600000"
>
</method>
<method
    name="sendBillTransmission(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="sendWorkInvoiceTransmission(glog.ejb.workinvoice.db.WorkInvoicePK)"
    timeout="600000"
>
</method>
<method
    name="getInvoiceShipement(glog.ejb.invoice.db.InvoiceShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getShipemntCost(glog.ejb.shipment.db.ShipmentCostPK)"
    timeout="600000"
>
</method>
<method
    name="getInvoiceShipmentbyGid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentCostsForParentInvoice(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateInvoiceGeneration(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="canPerfromBillGeneration(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getInvoiceData(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
