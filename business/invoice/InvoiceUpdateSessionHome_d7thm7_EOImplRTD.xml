<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.invoice.InvoiceUpdateSessionHome_d7thm7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="autoApprovePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="autoApprovePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,glog.business.invoice.ApproveLineInput[],java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unApprovePayment(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="adjustPayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="adjustLinePayment(glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,boolean,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="adjustPaymentForUnApprove(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,glog.business.invoice.ApproveLineInput[],java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectInvoices(glog.ejb.invoice.db.InvoicePK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="rejectInvoice(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unRejectInvoices(glog.ejb.invoice.db.InvoicePK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unRejectInvoice(glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveBill(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unApproveBill(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteBills(glog.ejb.invoice.db.InvoicePK[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteBills(glog.ejb.invoice.db.InvoicePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteBill(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteBill(glog.ejb.invoice.db.InvoicePK,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteInvoice(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTInvoices(glog.business.invoice.TInvoice[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTInvoices(glog.business.invoice.TInvoice[],glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTInvoice(glog.business.invoice.TInvoice,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTInvoice(glog.business.invoice.TInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitParentTInvoice(glog.business.invoice.TInvoice,glog.ejb.invoice.db.InvoicePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="makeInvoice(glog.business.invoice.TInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="makeVoucher(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,boolean,java.lang.String,java.lang.String,glog.business.invoice.ApproveLineInput[],java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="makeVoucher(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,boolean,java.lang.String,java.lang.String,glog.business.invoice.ApproveLineInput[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveUserInputNote(glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBillAdjustmentForShipmentCosts(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoiceLineitemCostRefData[],glog.ejb.invoice.db.InvoiceLineitemRemarkData[],glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBillAdjustmentForShipmentCostsNewBill(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoiceLineitemCostRefData[],glog.ejb.invoice.db.InvoiceLineitemRemarkData[],glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitInvoiceAdjustmentForShipmentCosts(glog.ejb.invoice.db.InvoicePK,glog.ejb.invoice.db.InvoiceLineitemData[],glog.ejb.invoice.db.InvoiceLineitemCostRefData[],glog.ejb.invoice.db.InvoiceLineitemRemarkData[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBillForJob(glog.business.invoice.TInvoice,glog.ejb.job.db.JobPK,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="matchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="matchInvoice(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.matchrule.MatchOutcome,java.lang.String,glog.ejb.matchrule.db.MatchRulePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="matchInvoice(glog.ejb.invoice.db.InvoicePK,glog.business.invoice.matchrule.MatchOutcome,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitInvoiceChangeForMatchValidationResult(glog.ejb.invoice.db.InvoicePK,boolean,glog.business.invoice.matchrule.MatchValidationResult)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approvePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approvePayment(glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,glog.business.invoice.ApproveLineInput[],java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="approveLinePayment(glog.business.invoice.ApproveLineInput[],glog.ejb.invoice.db.InvoicePK,glog.util.currency.Currency,java.lang.String,java.lang.String,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="autoMatchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="autoMatchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="unMatchInvoice(glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBillsForEachShipmentOnJob(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addInvoicesToConsolidation(glog.ejb.invoice.db.InvoicePK[],glog.ejb.invoice.db.InvoicePK,glog.ejb.invoice.db.InvoiceInvolvedPartyData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addInvoiceNote(glog.ejb.invoice.db.InvoicePK,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeInvoicesFromConsolidation(glog.ejb.invoice.db.InvoicePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitInvoiceAdjustmentForShipmentCostsToNewInvoice(java.lang.String,java.lang.String,glog.ejb.invoice.db.InvoiceLineitemData[],glog.ejb.invoice.db.InvoiceLineitemCostRefData[],glog.ejb.invoice.db.InvoiceLineitemRemarkData[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.invoice.db.InvoicePK,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitConsolidatedCreditNote(glog.business.invoice.TInvoice,glog.ejb.invoice.db.InvoicePK,glog.business.invoice.TInvoice[],glog.ejb.invoice.db.InvoicePK[],glog.ejb.shipment.db.ShipmentPK[][])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitCreditNote(glog.business.invoice.TInvoice,glog.ejb.invoice.db.InvoicePK,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setVATStatusToFailed(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setVATStatusToFailed(glog.ejb.invoice.db.InvoicePK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setApproveStatusToFailed(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setMatchStatusToFailed(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="generateAndSetInvoiceNumber(glog.ejb.invoice.db.InvoicePK,glog.server.bngenerator.contexts.invoice.BNInvoiceBillNumberContext,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="generateAndSetInvoiceNumber(glog.ejb.invoice.db.InvoicePK,glog.ejb.job.db.JobPK,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createVATDataForTInvoice(glog.business.invoice.TInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createManualVATDataForTInvoice(glog.business.invoice.TInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTInvoices(glog.business.invoice.TInvoice[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTInvoice(glog.business.invoice.TInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTVouchers(glog.business.invoice.TVoucher[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateTVoucher(glog.business.invoice.TVoucher)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataChanges(glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentDataAndTInvoices(java.util.List,glog.business.util.RecalcSuppressible,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateShipmentInvoiceEquipmentInfo(java.util.List,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteVoucher(glog.ejb.invoice.db.InvoicePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
