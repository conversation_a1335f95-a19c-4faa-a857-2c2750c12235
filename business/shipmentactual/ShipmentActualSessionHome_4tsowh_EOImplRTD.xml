<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.shipmentactual.ShipmentActualSessionHome_4tsowh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="executeShipmentActual(glog.util.genericcontainer.BusinessObject,glog.business.shipmentactual.ModViaOrderLine[],glog.business.shipmentactual.ModViaOrderShipUnit[],glog.business.shipmentactual.ShipmentActualOptions)"
    timeout="600000"
>
</method>
<method
    name="executeShipmentActual(glog.util.genericcontainer.GenericContainer,glog.business.order.TOrderReleaseLine[],glog.business.order.TShipUnit[],boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
