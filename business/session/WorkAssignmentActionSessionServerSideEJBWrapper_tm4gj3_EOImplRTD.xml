<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.WorkAssignmentActionSessionServerSideEJBWrapper_tm4gj3_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getExistingShipmentsOnWA(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="UpdateWorkAssignment(glog.ejb.workassignment.db.WorkAssignmentPK,glog.ejb.shipment.db.ShipmentPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getWorkAssignmentShipmentsForUI(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="createWorkAssignment(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.resourceschedule.db.ResourceScheduleInstancePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getWorkAssignmentShipmentsForRecalculateUI(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="disbandWorkAssignment(glog.ejb.workassignment.db.WorkAssignmentPK,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getCalculatedData(java.lang.String,java.lang.String,glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
