<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.UtilActionSessionHome_g6mve7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getHazmatItemGid(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="addItemsToSelectForList(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="formulateIataTactRates(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.LocalDate,glog.util.LocalDate)"
    timeout="600000"
>
</method>
<method
    name="getDomainSettingData(java.lang.String[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getFlexFieldDefData(java.lang.String[],java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getFlexFieldDefData(java.lang.String[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPlanningParameterValue(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentPlanningParameterSet(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getPrimaryContactsForLocations(java.util.Set)"
    timeout="600000"
>
</method>
<method
    name="getStatusTypeFromObjType(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getStatusValueFromStatusType(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="composeAndSendMessage(java.lang.String,java.lang.String[],java.lang.String,java.lang.String,java.lang.String[],java.lang.String[],java.lang.String[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="composeAndSendMessagePreviewContent(java.lang.String,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findFlights(glog.business.airschedule.AirScheduleSearchCriteria,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateAddress(glog.business.location.Address,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateAddress(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="validateAddress(glog.externalapi.distanceengine.ExtEngineAddress,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="formComposeAndSendMessageContacts(java.lang.String[],java.lang.String,java.lang.String[],java.lang.String[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isAmericanRegion(glog.externalapi.distanceengine.ExtEngineAddress)"
    timeout="600000"
>
</method>
<method
    name="locationUpdate(glog.externalapi.distanceengine.ExtEngineAddress,java.lang.String,java.lang.Boolean,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="locationUpdateLatLonOnly(glog.externalapi.distanceengine.ExtEngineAddress,java.lang.String,java.lang.Boolean,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLogicConfigValue(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateChildEquipmentGroup(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLogicConfigShipmentModeColorMap(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLogicConfigFleetShipmentColorMap(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getOrderReleaseGidsFromShipmentStop(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteCombEquipGrpStructRecords(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getGanttLogicConfig(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLogicConfig(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setIndicator(java.lang.String,glog.util.jdbc.Pk[])"
    timeout="600000"
>
</method>
<method
    name="setIndicator(java.lang.String,glog.util.jdbc.Pk)"
    timeout="600000"
>
</method>
<method
    name="queryProvinces(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changeExternalStatus(java.lang.String,java.lang.String,java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="findRepetitionSchedules(glog.business.repetitionschedule.RepSchedSearchCriteria,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getORShipmentGIVDetails(glog.business.action.ORShipmentGIV[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="expireRateOfferingById(java.lang.String,glog.util.LocalDate)"
    timeout="600000"
>
</method>
<method
    name="expireRateGeoById(java.lang.String,glog.util.LocalDate)"
    timeout="600000"
>
</method>
<method
    name="setRateGeoExpireDate(java.lang.String,glog.util.LocalDate,glog.ejb.rates.db.RateGeoPK[])"
    timeout="600000"
>
</method>
<method
    name="setRateOfferingExpireDate(java.lang.String,glog.util.LocalDate,glog.ejb.rates.db.RateOfferingPK[])"
    timeout="600000"
>
</method>
<method
    name="setRateOfferingExpireVersion(java.lang.String,java.lang.String,glog.ejb.rates.db.RateOfferingPK[])"
    timeout="600000"
>
</method>
<method
    name="getShipmentsForOrderBases(glog.ejb.orderbase.db.ObOrderBasePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="callStoredProcedure(java.lang.String,java.util.ArrayList,java.lang.Class)"
    timeout="600000"
>
</method>
<method
    name="setUserDefinedImages(glog.util.jdbc.Pk,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setUserDefinedImages(glog.util.jdbc.Pk[],java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="matchTimeZone(glog.business.location.Address)"
    timeout="600000"
>
</method>
<method
    name="matchTimeZone(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setExpireId(java.lang.String,glog.ejb.rates.db.RateOfferingPK[])"
    timeout="600000"
>
</method>
<method
    name="setExpireId(java.lang.String,glog.ejb.rates.db.RateGeoPK[])"
    timeout="600000"
>
</method>
<method
    name="setPriority(java.lang.String,glog.util.jdbc.Pk,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
