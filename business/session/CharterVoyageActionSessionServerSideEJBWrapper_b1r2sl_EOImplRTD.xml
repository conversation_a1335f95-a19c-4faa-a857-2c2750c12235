<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.CharterVoyageActionSessionServerSideEJBWrapper_b1r2sl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="makeConsol(glog.ejb.chartervoyage.db.CharterVoyageStowagePK,glog.business.Capacity,glog.business.Capacity,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="preCommitConsolCapacity(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="commitConsolCapacity(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="unCommitConsolCapacity(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="openConsols(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="unPreCommitConsolCapacity(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="closeConsols(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="changeConsolStatusAction(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolsCapacitySummary(glog.ejb.chartervoyage.db.CharterVoyagePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
