<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.LocationActionSessionHome_du7c5r_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="calcLocationCapacityUsage(glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationCapacityGroupDPK,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="calcLocationCapacityUsages(glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationCapacityGroupDPK[],java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="createBuyerForLocation(glog.ejb.location.db.LocationPK[])"
    timeout="600000"
>
</method>
<method
    name="createCustomerForLocation(glog.ejb.location.db.LocationPK[])"
    timeout="600000"
>
</method>
<method
    name="processLocationLongituteLatitude(glog.ejb.location.db.LocationPK[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
