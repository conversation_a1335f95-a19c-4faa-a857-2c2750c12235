<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ItineraryActionSessionHome_p1b76h_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="findValidItineraries(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.business.action.BSConstraints,glog.business.action.helper.BuyFromSellConstraints,java.lang.String,boolean,java.util.HashMap,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findValidItineraries(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.business.action.BSConstraints,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findValidItineraries(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findValidItineraries(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="findValidItinerariesForCreatingOMs(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
