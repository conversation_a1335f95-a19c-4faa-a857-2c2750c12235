<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.RateActionSessionHome_a6s4b5_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="calcRateServiceTime(glog.business.location.Address,glog.business.location.Address,glog.ejb.rates.db.RateServicePK,glog.ejb.rates.db.RateDistancePK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.business.rate.ratedistance.DistanceTimeInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="processRateFactorSource(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="processRateFactorRule(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="processRateFactors()"
    timeout="600000"
>
</method>
<method
    name="validateRoutingOption(glog.business.rate.rateinquiry.RateInquiryInput,glog.business.action.datastructure.RoutingOption,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateSourceLocationProfile(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getRates_SimpleType(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates_SimpleType(glog.business.rate.rateinquiry.RateInquiryInput,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRiqUiLogicConfigParameters(glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates(glog.business.rate.rateinquiry.RateInquiryInput,glog.business.rate.ratefinder.RateFinderConfig,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates_Route(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRatesShowOptions(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates_Simple(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference,boolean)"
    timeout="600000"
>
</method>
<method
    name="getRates_Simple(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRates_CustomerOrder(glog.business.rate.rateinquiry.RateInquiryInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="calcRateDistance(glog.business.location.Address,glog.business.location.Address,glog.ejb.rates.db.RateDistancePK,glog.business.rate.ratedistance.DistanceTimeInput,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
