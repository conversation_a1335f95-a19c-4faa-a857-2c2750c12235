<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.PerfTestActionSessionHome_98f4sv_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="testDistanceAndTime(int,boolean)"
    timeout="600000"
>
</method>
<method
    name="runAndCompareBulkPlan(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runAndCompareBulkPlan(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clearRatingCaches()"
    timeout="600000"
>
</method>
<method
    name="clearAllBusinessCaches()"
    timeout="600000"
>
</method>
<method
    name="testBusinessActions(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="compareBulkPlanResults(glog.ejb.bulkplan.db.BulkPlanPK,glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="compareShipments(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="compareShipmentsForOrders(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runBaselineBulkPlan(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runBaselineBulkPlan(glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clearCache(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
