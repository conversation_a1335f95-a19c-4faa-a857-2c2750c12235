<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.CommitmentCountSessionHome_tpoktj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createUsagesForNewCount(glog.ejb.capacity.db.CapacityCommitmentPK)"
    timeout="600000"
>
</method>
<method
    name="expireCommitmentCountsManually(glog.ejb.capacity.db.CapacityCommitmentPK[],glog.util.LocalTimestamp,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="deleteCommitmentCount(glog.ejb.capacity.db.CapacityCommitmentPK[])"
    timeout="600000"
>
</method>
<method
    name="setIsActive(glog.ejb.capacity.db.CommitUsagePK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="overrideCount(glog.ejb.capacity.db.CommitUsageData[])"
    timeout="600000"
>
</method>
<method
    name="validateForAddNewCount(glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
