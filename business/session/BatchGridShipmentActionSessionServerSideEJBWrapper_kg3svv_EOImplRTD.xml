<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.BatchGridShipmentActionSessionServerSideEJBWrapper_kg3svv_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="assignSpecificServiceProvider(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignSpecificServiceProviderUsingRateGeo(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentRate(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignLeastCostServiceProvider(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="manualEquipmentChange(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignSpecificEquipmentGroup(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentPlannedStopTimes(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,boolean,glog.util.LocalTimestamp,boolean,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignServiceProviderManually(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.util.currency.Currency,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
