<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ShipmentGroupActionSessionServerSideEJBWrapper_9x5sd7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="combineShipmentGroups(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.ejb.shipmentgroup.db.ShipGroupPK)"
    timeout="600000"
>
</method>
<method
    name="bulkTrailerBuild(glog.ejb.bulktrailerbuild.db.BulkTrailerBuildPK,glog.ejb.shipment.db.ShipmentPK[],glog.ejb.shipmentgroup.db.ShipGroupRulePK)"
    timeout="600000"
>
</method>
<method
    name="sendTrailerBuildPickup(glog.ejb.shipmentgroup.db.ShipGroupPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="resendTrailerBuildPickup(glog.ejb.shipmentgroup.db.ShipGroupPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="modifyTrailerBuildPickup(glog.ejb.shipmentgroup.db.ShipGroupPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="cancelTrailerBuildPickup(glog.ejb.shipmentgroup.db.ShipGroupPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="setAppointmentPriority(glog.ejb.shipmentgroup.db.ShipGroupPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createShipmentGroup(glog.ejb.shipment.db.ShipmentPK[],glog.business.action.ShipmentGroupTemplate)"
    timeout="600000"
>
</method>
<method
    name="deleteShipmentGroups(glog.ejb.shipmentgroup.db.ShipGroupPK[])"
    timeout="600000"
>
</method>
<method
    name="removeShipmentsFromGroup(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removePartialShipmentsFromGroup(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removePartialShipmentsFromGroup(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.ejb.shipmentgroup.db.ShipGroupDOrData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changeShipmentGroupStatus(glog.ejb.shipmentgroup.db.ShipGroupPK[],java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calculateGroupCosts(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="secureResources(glog.ejb.shipmentgroup.db.ShipGroupPK)"
    timeout="600000"
>
</method>
<method
    name="addShipmentsToShipmentGroups(glog.ejb.shipmentgroup.db.ShipGroupPK[],glog.ejb.shipment.db.ShipmentPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addPartialShipmentsToShipmentGroups(glog.ejb.shipmentgroup.db.ShipGroupPK[],glog.ejb.shipmentgroup.db.ShipGroupDOrData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
