<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ShipmentActualActionSessionServerSideEJBWrapper_fr264x_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="determineOrderReleaseQuantityStatus(glog.ejb.shipment.db.ShipmentPK,char)"
    timeout="600000"
>
</method>
<method
    name="determineOrderReleaseQuantityStatus(glog.ejb.order.db.OrderReleasePK,char)"
    timeout="600000"
>
</method>
<method
    name="determineOrderBaseQuantityStatus(glog.ejb.orderbase.db.ObOrderBasePK,char)"
    timeout="600000"
>
</method>
<method
    name="deleteRemainingQuantity(glog.ejb.order.db.OrderMovementPK[])"
    timeout="600000"
>
</method>
<method
    name="getShipments(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
