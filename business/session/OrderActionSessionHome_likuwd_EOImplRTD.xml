<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.OrderActionSessionHome_likuwd_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="unassignOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="replanOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="replanLastLegOfOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="simulateMoveOrdersToShipment(glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,java.lang.Integer,java.lang.Integer,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="moveOrdersToShipment(glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,java.lang.Integer,java.lang.Integer,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="moveOrdersToScheduleInstanceShipment(glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,glog.business.action.order.StopInfo,glog.business.action.order.StopInfo,boolean,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findPotentialShipmentsForOrder(glog.ejb.order.db.OrderReleasePK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.ShipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipmentGraph(glog.ejb.order.db.OrderReleasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentsForOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentStopsForOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changeOrderReleaseStatuses(glog.ejb.order.db.OrderReleasePK[],java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="changeOrderReleaseStatus(glog.ejb.order.db.OrderReleasePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildBulkOrderReleases(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="orderReleaseCreated(glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="adjustOrderReleaseTimes(glog.ejb.order.db.OrderReleasePK[],glog.util.LocalTimestamp,glog.util.uom.data.Duration,glog.util.LocalTimestamp,glog.util.uom.data.Duration,glog.util.LocalTimestamp,glog.util.uom.data.Duration,glog.util.LocalTimestamp,glog.util.uom.data.Duration)"
    timeout="600000"
>
</method>
<method
    name="planOrders(glog.ejb.bulkplan.db.BulkPlanPK,glog.ejb.order.db.OrderReleasePK[],boolean,boolean,java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="planOrders(glog.ejb.bulkplan.db.BulkPlanPartitionPK,glog.ejb.order.db.OrderReleasePK[],boolean,java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="planOrders(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentOnPrimaryLeg(glog.ejb.order.db.OrderReleasePK,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentOnPrimaryLegWithConsolShipment(glog.ejb.order.db.OrderReleasePK,glog.business.action.consol.ConsolShipmentResult,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="unassignOrders(glog.ejb.order.db.OrderReleasePK[],java.lang.String,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignOrders(glog.ejb.bulkplan.db.BulkPlanPK,glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildShipments(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.BSConstraints,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildMultiStopShipments(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentsShowOptions(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.BSConstraints,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildMultiStopSellShipments(glog.ejb.order.db.OrderReleasePK[],boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildSellShipments(glog.ejb.order.db.OrderReleasePK[],boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="calculateDirectCost(glog.ejb.order.db.OrderReleasePK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="statusAllowsBuildShipments(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildAndExecuteShipments(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.ejb.shipment.db.ShipmentRefnumData[],glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildBillsFromSellShipments(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentManually(java.lang.String[],java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.currency.Currency,glog.util.uom.data.Duration,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="setOrderReleaseExecutionStatus(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="modificationAcknowledged(glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="insertOrder(glog.ejb.order.db.OrderReleasePK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignGeneralLedgerCodes(glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="manuallyAssignOrdersToShipment(glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="setOrderReleaseType(java.util.Collection,glog.ejb.order.db.OrderReleaseTypePK)"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipmentGGraph(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="makeOrderMovements(glog.ejb.order.db.OrderReleasePK[],java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="makeOrderMovements(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="deleteOrderMovements(glog.ejb.order.db.OrderReleasePK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateShipUnits(glog.ejb.order.db.OrderReleasePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="calculateCommericalInvoiceAmount(glog.business.invoice.TCommercialInvoice)"
    timeout="600000"
>
</method>
<method
    name="calculateAndSaveCommericalInvoices(glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="deleteCommercialInvoice(glog.ejb.invoice.db.CommercialInvoicePK)"
    timeout="600000"
>
</method>
<method
    name="assignTransHazmatData(glog.ejb.order.db.OrderReleasePK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignTransProductData(glog.ejb.order.db.OrderReleasePK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignPartitionKey(glog.business.action.order.OrderPartition[])"
    timeout="600000"
>
</method>
<method
    name="buildRoutingOptions(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.BSConstraints,glog.business.action.helper.BuyFromSellConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildRoutingOptions(glog.ejb.order.db.OrderReleasePK[],glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.BSConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateRoutingOption(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.RoutingOption,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentGraphFromRoutingConstraints(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.BSRoutingConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignServiceProviderFurthestBehindCommitment(glog.ejb.order.db.OrderReleasePK,java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="compareOrderSellChargesToQuoted(glog.ejb.order.db.OrderReleasePK,double,double,int)"
    timeout="600000"
>
</method>
<method
    name="setStatus(glog.server.status.StatusFunction,glog.ejb.order.db.OrderReleasePK,java.lang.Object)"
    timeout="600000"
>
</method>
<method
    name="assignOrderRoutingRule(glog.ejb.order.db.OrderReleasePK[])"
    timeout="600000"
>
</method>
<method
    name="findOrderRoutingRule(glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.util.uom.data.Weight,glog.util.uom.data.Volume,java.lang.String,glog.business.location.Address,java.lang.String,glog.business.location.Address,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="splitUnplannedOrder(glog.ejb.order.db.OrderReleasePK,glog.business.action.helper.SplitOffORShipUnitStruct[])"
    timeout="600000"
>
</method>
<method
    name="splitPlannedOrder(glog.ejb.order.db.OrderReleasePK,java.util.Map,java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="setOrderReleaseDeliveryDates(java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="setOrderSchedule(java.util.Collection,glog.util.genericcontainer.DataSource)"
    timeout="600000"
>
</method>
<method
    name="autoApplyServices(glog.ejb.order.db.OrderReleasePK[])"
    timeout="600000"
>
</method>
<method
    name="assignServices(glog.ejb.order.db.OrderReleasePK,glog.util.genericcontainer.DataSource)"
    timeout="600000"
>
</method>
<method
    name="applyServices(glog.ejb.order.db.OrderReleasePK,glog.util.genericcontainer.DataSource)"
    timeout="600000"
>
</method>
<method
    name="applyBuyerTemplate(glog.ejb.order.db.OrderReleasePK,glog.ejb.useraccess.db.BuyerPK[],glog.ejb.order.db.MovePerspectivePK,glog.ejb.transportmode.db.TransportModePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="applyDomainCustomerTemplate(glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="getScheduleInstanceAddStopCode(glog.ejb.shipment.db.ShipmentPK,glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getScheduleInstanceAddStopCode(glog.ejb.shipment.db.ShipmentPK,glog.ejb.order.db.OrderReleasePK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="refreshOrderCache(glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="validateBuildShipmentsUsingNRO(glog.ejb.order.db.OrderReleasePK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findNRORoutes(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createNetworkRoutingOptions(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.bsnro.Route[],glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="createTopNetworkRoutingOptions(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateNetworkRoutingOption(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.RoutingOption,glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentsUsingNRO(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.RoutingOption,glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentsUsingNRO(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.bsnro.Route[],glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentsUsingNRO_Legacy(glog.ejb.order.db.OrderReleasePK[],glog.business.action.datastructure.RoutingOption,glog.business.action.datastructure.bsnro.BSNROConstraints,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getOrderReleases(glog.ejb.invoice.db.InvoicePK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
