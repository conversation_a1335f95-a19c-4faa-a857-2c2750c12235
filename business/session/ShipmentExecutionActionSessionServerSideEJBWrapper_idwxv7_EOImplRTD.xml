<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ShipmentExecutionActionSessionServerSideEJBWrapper_idwxv7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="releaseScheduleInstances(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="approveForExecution(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="secureResources(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="secureResources(java.util.Collection,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="secureResources(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="tenderShipment(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="tenderShipment(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="setAsTendered(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="withdrawShipmentTender(java.util.Collection,boolean,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="withdrawShipmentTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="withdrawShipmentTender(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.ServprovPK,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="setAsWithdrawShipmentTender(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.ServprovPK,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="declineShipmentTender(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.ServprovPK,boolean,boolean,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="declineShipmentTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,boolean,boolean,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="acceptShipmentTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="reviewServProvUpdates(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="updateEShipment(glog.business.action.ServerActionEShipment)"
    timeout="600000"
>
</method>
<method
    name="reTransmitTender(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="modifyTender(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="copyActualFieldsFromBuyToSell(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyReceivedFieldsFromBuyToSell(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="copyMasterBookingDataToOtherPerspective(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="updateCommitmentFinalUsage(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="clearNumberOfAttemptedTender(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="assignGeneralLedgerCodes(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="broadcastTender(glog.ejb.shipment.db.ShipmentPK[],java.util.Set,glog.util.LocalTimestamp,boolean)"
    timeout="600000"
>
</method>
<method
    name="spotBidTender(glog.ejb.shipment.db.ShipmentPK[],java.util.Set,glog.util.LocalTimestamp,boolean)"
    timeout="600000"
>
</method>
<method
    name="modifyOpenTender(glog.ejb.shipment.db.ShipmentPK[],glog.util.LocalTimestamp,boolean)"
    timeout="600000"
>
</method>
<method
    name="withdrawSpotBidTender(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="withdrawBroadcastTender(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="getMatchedServprovForOpenTender(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="getSelectedServProvForReTransmitOpenTender(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="evaluateOpenTenderBid(glog.server.workflow.tender.TenderType,glog.ejb.shipment.db.ShipmentPK[],java.lang.Float,boolean)"
    timeout="600000"
>
</method>
<method
    name="copyPlannedStopTimeToActualStopTime(glog.ejb.shipment.db.ShipmentPK,int)"
    timeout="600000"
>
</method>
<method
    name="createRechargeShipments(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,java.lang.Character,java.lang.Character)"
    timeout="600000"
>
</method>
<method
    name="setRechargeBillTo(glog.ejb.shipment.db.ShipmentPK,glog.ejb.reference.db.InvolvedPartyQualPK,glog.ejb.reference.db.InvolvedPartyQualPK,glog.ejb.order.db.OrderReleaseRefnumQualPK,glog.ejb.shipment.db.ShipmentRefnumQualPK)"
    timeout="600000"
>
</method>
<method
    name="assignProviderForRetender(glog.ejb.shipment.db.ShipmentPK,java.lang.String[],glog.business.serviceprovider.ServprovStruct[],glog.business.serviceprovider.SpotBidServprovInfo,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentEstimatedStopTimes(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,boolean,glog.webserver.umt.UserPreference,java.lang.Integer)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentEstimatedStopTimesTransactionally(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,boolean,glog.webserver.umt.UserPreference,java.lang.Integer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAppointmentTimes(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="markShipmentLocation(glog.ejb.shipment.db.ShipmentPK,glog.business.location.Address,glog.util.LocalTimestamp,boolean,int,java.lang.Integer)"
    timeout="600000"
>
</method>
<method
    name="markShipmentLocation(glog.ejb.shipment.db.ShipmentPK,double,double,glog.util.LocalTimestamp,boolean,int,java.lang.Integer)"
    timeout="600000"
>
</method>
<method
    name="calculateDistanceFromNextStop(glog.ejb.shipment.db.ShipmentPK,glog.business.location.Address,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="setShipmentActualStopTimes(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,boolean,glog.webserver.umt.UserPreference,java.lang.Integer)"
    timeout="600000"
>
</method>
<method
    name="createShipmentStopDebriefs(glog.ejb.shipment.db.ShipmentStopPK)"
    timeout="600000"
>
</method>
<method
    name="bookSellShipmentToBuyConsol(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="bookSellShipmentToBuyConsolShowOptions(glog.ejb.shipment.db.ShipmentPK,glog.business.action.consol.ConsolShipmentResult)"
    timeout="600000"
>
</method>
<method
    name="buildBuySideFromSellShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="buildSellShipmentFromBuyShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="bookWithInternalNVOCC(glog.ejb.shipment.db.ShipmentPK,glog.business.action.datastructure.BSRoutingConstraints)"
    timeout="600000"
>
</method>
<method
    name="bookWithInternalNVOCC(glog.ejb.shipment.db.ShipmentPK,glog.ejb.itinerary.db.ItineraryPK[],glog.business.action.BSConstraints)"
    timeout="600000"
>
</method>
<method
    name="bookWithInternalNVOCC(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getAllRelatedBuyShipmentPKsForLSPModel(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getSellSideOrderMovement(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getAllRelatedBuyShipmentPKsForForwarderModel(glog.ejb.order.db.OrderMovementPK)"
    timeout="600000"
>
</method>
<method
    name="getAllRelatedBuyNFRCShipmentPKsForForwarderModel(glog.ejb.order.db.OrderMovementPK)"
    timeout="600000"
>
</method>
<method
    name="markupShipmentCosts(glog.ejb.shipment.db.ShipmentPK,glog.business.action.markup.CostWithMarkupInstructions[])"
    timeout="600000"
>
</method>
<method
    name="getTenderComMethod(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="applySpotCost(glog.ejb.shipment.db.ShipmentPK,int,java.lang.String,java.lang.String,java.lang.String,glog.util.currency.Currency,java.lang.String,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="versionShipmentCost(glog.ejb.shipment.db.ShipmentPK,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="applySpotRate(glog.ejb.shipment.db.ShipmentPK,glog.ejb.rates.db.RateGeoPK)"
    timeout="600000"
>
</method>
<method
    name="validateServprovForSpotRate(glog.ejb.location.db.ServprovPK)"
    timeout="600000"
>
</method>
<method
    name="ProcessPartiallyAcceptedShipment(glog.ejb.shipment.db.ShipmentPK,java.lang.String,boolean,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="reTransmitOpenTender(glog.ejb.shipment.db.ShipmentPK,java.util.Set)"
    timeout="600000"
>
</method>
<method
    name="getTenderContactInfo(java.lang.String[],boolean)"
    timeout="600000"
>
</method>
<method
    name="setExchangeRate(glog.ejb.shipment.db.ShipmentPK[],glog.util.LocalDate,glog.ejb.currency.db.ExchangeRatePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="selectRelatedPrimaryShipments(glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="selectRelatedJobs(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="selectRelatedEquipmentGroupsAndCounts(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="setAdvancedCharge(glog.ejb.shipment.db.ShipmentPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="generateMAWB(glog.ejb.shipment.db.ShipmentPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="selectTenderContact(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="setAppointmentPriority(glog.ejb.shipment.db.ShipmentPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentCosts(glog.ejb.shipment.db.ShipmentPK,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="getServprovPK(glog.ejb.shipment.db.ShipmentPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="getOrderReleasePKs(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="recordShipmentForTieredRating(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="removeShipmentTieredRatingRecord(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="stepTender(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="isStepTender(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="isStepTender(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="getServProvsWithStepTender(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getServProvsForStepTenderWithdraw(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="assignProviderForStepTender(glog.ejb.shipment.db.ShipmentPK,java.lang.String[],glog.business.serviceprovider.ServprovStruct[])"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderOnShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="acceptStepTender(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.location.db.ServprovPK,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="withdrawStepTenders(glog.ejb.shipment.db.ShipmentPK,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="withdrawStepTenders(glog.ejb.shipment.db.ShipmentPK[],java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPlannedServprovOfShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="declineStepTender(glog.ejb.shipment.db.ShipmentPK,java.util.Set,boolean)"
    timeout="600000"
>
</method>
<method
    name="getServiceProvider(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getShipmentPKsFromTenderCollaborationPKs(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderUsingSavedCondition(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="sendWorkAssignment(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="reTransmitWorkAssignment(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="modifyWorkAssignment(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="confirmWorkAssignment(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="withdrawWorkAssignment(java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderHelper(java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderHelper(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderHelper(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="assignLastTenderedServiceProvider(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentTrackingLocations(java.lang.String,java.util.List)"
    timeout="600000"
>
</method>
<method
    name="removeShipmentTrackingLocations(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calculateTransitTime(java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="updateShipmentSpecialService(glog.ejb.shipment.db.ShipmentSpecialServiceData,java.util.List,boolean)"
    timeout="600000"
>
</method>
<method
    name="getFirstLegShipments(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="getFirstEquipment(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getNextDriverCalendarEvent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentEquipments(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getDriverNextCalendarEvent(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calcSShipUnitAndCommit(glog.util.genericcontainer.GenericContainer)"
    timeout="600000"
>
</method>
<method
    name="getMaxITransactionNumber(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getAcceptanceCode(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getTenderStatus(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getloadCBData()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
