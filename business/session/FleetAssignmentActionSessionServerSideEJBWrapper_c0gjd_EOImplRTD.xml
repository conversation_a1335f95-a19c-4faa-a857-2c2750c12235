<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.FleetAssignmentActionSessionServerSideEJBWrapper_c0gjd_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="assignBestDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK[],glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.DriverAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.DriverAdvancedFilterRequest,glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findDriverAssignmentsForInsertion(glog.ejb.driver.db.DriverPK)"
    timeout="600000"
>
</method>
<method
    name="assignDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignDriverToShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.DriverAssignmentOption,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="switchDriver(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.DriverAssignmentOption,boolean,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateShipmentsForDriver(glog.ejb.driver.db.DriverPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.ShipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateShipmentsForDriver(glog.ejb.driver.db.DriverPK,glog.ejb.shipment.db.ShipmentPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateDriverHomeLocn(glog.ejb.driver.db.DriverPK,glog.ejb.shipment.db.ShipmentPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="addJustGetTheDriverHomeStop(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestShipmentToDriver(glog.ejb.driver.db.DriverPK,glog.ejb.shipment.db.ShipmentPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestShipmentToDriver(glog.ejb.driver.db.DriverPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.ShipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateDriversForShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateDriversForShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.driver.db.DriverPK[],glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateDriversForShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.DriverAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateDriversForShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.DriverAdvancedFilterRequest,glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentTypeAssignmentInputForShipment(glog.ejb.shipment.db.ShipmentPK,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="switchDriverAssignmentSequence(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.DriverAssignmentOption,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="validateUnassignDriverFromShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignDriverFromShipment(glog.ejb.shipment.db.ShipmentPK,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="optimizeDriverAssignment(glog.ejb.fleetbulkplan.db.FleetBulkPlanPK,glog.ejb.shipment.db.ShipmentPK[],glog.ejb.driver.db.DriverPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignPowerUnitToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.powerunit.db.PowerUnitPK,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignPowerUnitFromShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findSEquipmentsForShipment(glog.ejb.shipment.db.ShipmentPK,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="getSEquipmentAssignmentInputForShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="evaluateEquipmentsForShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestEquipmentToShipment(glog.ejb.shipment.db.ShipmentPK,boolean,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.EquipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignEquipmentsToShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInput,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignEquipmentFromShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="optimizeEquipmentAssignment(glog.ejb.fleetbulkplan.db.FleetBulkPlanPK,glog.ejb.shipment.db.ShipmentPK[],glog.ejb.equipment.db.EquipmentPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateEquipmentTypesForShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestEquipmentTypeToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.EquipmentTypeAdvancedFilterRequest,java.util.List,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored,boolean)"
    timeout="600000"
>
</method>
<method
    name="assignBestEquipmentTypeToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.business.fleetassignment.EquipmentTypeAdvancedFilterRequest,java.util.List,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="positionEquipmentTypesForShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignEquipmentsFromShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInputForUI,glog.business.fleetassignment.EquipmentTypeAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignEquipmentTypesFromShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInputForUI,glog.business.fleetassignment.EquipmentTypeAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignAllFromShipment(glog.ejb.shipment.db.ShipmentPK[],int,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentLocationGid(glog.ejb.equipment.db.EquipmentPK)"
    timeout="600000"
>
</method>
<method
    name="relayShipmentBreakdown(glog.ejb.shipment.db.ShipmentPK,int,boolean,glog.ejb.location.db.LocationPK,boolean,boolean,boolean,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="relayShipmentInTransit(glog.ejb.shipment.db.ShipmentPK,int,boolean,glog.ejb.location.db.LocationPK,glog.ejb.driver.db.DriverPK,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="relayShipmentSwap(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,int,boolean,int,boolean,glog.ejb.location.db.LocationPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findAppendableShipments(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="retrieveAppendableShipments(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="appendShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findSwappedShipments(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="revertSwap(glog.ejb.shipment.db.ShipmentPK[],glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="canRemoveRelayStop(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="checkShipmentFeasibility(glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="commitDriverAssignment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="uncommitDriverAssignment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="overrideDriverNatNal(glog.ejb.driver.db.DriverPK,glog.ejb.location.db.LocationPK,glog.util.LocalTimestamp,glog.ejb.shipment.db.ShipmentPK,glog.business.fleetassignment.HOSRuleStateInputSet,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="repositionDriver(glog.ejb.driver.db.DriverPK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.ejb.location.db.ServprovPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="repositionCombinationEquipment(glog.ejb.equipment.db.EquipmentPK[],glog.ejb.equipment.db.EquipmentGroupPK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.ejb.location.db.ServprovPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="repositionEquipment(glog.ejb.equipment.db.EquipmentPK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.ejb.location.db.ServprovPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="repositionEquipmentType(glog.ejb.equipment.db.EquipmentTypePK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.ejb.location.db.ServprovPK,int,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getAvailableEquipmentTypeCount(glog.ejb.equipment.db.EquipmentTypePK,glog.ejb.location.db.LocationPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getSplitableStopPksForShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="splitRepositioningShipment(glog.ejb.shipment.db.ShipmentPK,int,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="mergeRepositioningShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="insertNonFreightRelatedStop(glog.ejb.shipment.db.ShipmentPK,int,boolean,glog.ejb.location.db.LocationPK,java.lang.Boolean,java.lang.String,java.lang.String,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored,boolean)"
    timeout="600000"
>
</method>
<method
    name="removeNonFreightRelatedStop(glog.ejb.shipment.db.ShipmentPK,int,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored,boolean)"
    timeout="600000"
>
</method>
<method
    name="assignBackhaulToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,boolean,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestBackhaulToShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.ejb.location.db.ServprovProfilePK,glog.business.fleetassignment.ShipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="evaluateShipmentsForBackhaul(glog.ejb.shipment.db.ShipmentPK,glog.ejb.savedquery.db.SavedQueryPK,glog.ejb.location.db.ServprovProfilePK,glog.business.fleetassignment.ShipmentAdvancedFilterRequest,glog.business.service.fleetassignment.FleetAssignmentIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getCompatibiltyRules(boolean,boolean,boolean,boolean,glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="getCompatibiltyRules(boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="getDriverLocationGid(glog.ejb.driver.db.DriverPK)"
    timeout="600000"
>
</method>
<method
    name="getDriverTimePhasedDetails(glog.ejb.driver.db.DriverPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getDriverTimePhasedDetailsData(glog.ejb.driver.db.DriverPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getAdvancedFilterResults(glog.business.fleetassignment.DispatchBoardAdvancedFilterRequest)"
    timeout="600000"
>
</method>
<method
    name="isBobtailMove(glog.ejb.shipment.db.ShipmentPK,int,glog.ejb.location.db.LocationPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="isTransportMove(glog.ejb.shipment.db.ShipmentPK,int,glog.ejb.location.db.LocationPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="isDeadheadMove(glog.ejb.shipment.db.ShipmentPK,int,glog.ejb.location.db.LocationPK,boolean)"
    timeout="600000"
>
</method>
<method
    name="getDisplayFleetIgnoreCriteria(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setDisplayFleetIgnoreCriteria(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getLatestDriverAssignment(glog.ejb.driver.db.DriverPK)"
    timeout="600000"
>
</method>
<method
    name="getHOSRuleSetPK(glog.ejb.driver.db.DriverPK)"
    timeout="600000"
>
</method>
<method
    name="getDriverAssignments(glog.ejb.driver.db.DriverPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="processDriverCATCALAndHOSState(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.LocationPK,double,double,glog.util.LocalTimestamp,glog.business.fleetassignment.HOSRuleStateInputSet,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateDriverEstimatedTime(glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,boolean,glog.business.fleetassignment.HOSRuleStateInputSet,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="updateDriverActualTime(glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,boolean,glog.business.fleetassignment.HOSRuleStateInputSet,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeDriverAssignmentRecord(glog.ejb.fleetassignment.db.DriverAssignmentPK)"
    timeout="600000"
>
</method>
<method
    name="getDriverLast7DayMiles(java.util.List,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getShipmentsSuccessFailForEquipmentDPO(java.lang.String,glog.ejb.fleetbulkplan.db.FleetBulkPlanPK)"
    timeout="600000"
>
</method>
<method
    name="canAddDriverPowerUnitJoinData(java.lang.String,java.lang.String,java.lang.String,java.lang.Long,glog.util.LocalTimestamp,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="getDriverPk(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getStraightTruckDriverPK(glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentInput,glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getStraightTruckDriverPK(glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInputForUI)"
    timeout="600000"
>
</method>
<method
    name="getStraightTruckDriverPK(glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInput,glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getSEquipmentIfFreightLegMove(glog.business.service.fleetassignment.datastructure.SEquipmentAssignmentInput)"
    timeout="600000"
>
</method>
<method
    name="getStraightTruckShipmentPks(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="isStraightTruck(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="populateEquipmentGroupDetails(glog.business.service.fleetassignment.datastructure.EquipmentTypeAssignmentOption)"
    timeout="600000"
>
</method>
<method
    name="populateEquipmentGroupDetails(java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="assignDriverToReleasedGrndScheduleShipment(glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentLocationGidAndCombEquipGroups(java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
