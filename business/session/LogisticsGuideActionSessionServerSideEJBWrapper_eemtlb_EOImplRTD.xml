<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.LogisticsGuideActionSessionServerSideEJBWrapper_eemtlb_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="startLogisticsGuide(java.lang.String,glog.util.LocalTimestamp,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="generateLogisticsGuideForPoolXDock(glog.ejb.logisticsguide.db.LogisticsGuidePK,glog.ejb.logisticsguide.db.LogisticsGuideTemplatePK,glog.ejb.itinerary.db.ItineraryPK,glog.ejb.order.db.OrderReleasePK[],java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="generateLogisticsGuideGid(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="generateLogisticsGuide(glog.ejb.logisticsguide.db.LogisticsGuidePK,glog.ejb.logisticsguide.db.LogisticsGuideTemplatePK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
