<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.CapacityActionSessionHome_44ngup_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="setIsActive(glog.ejb.capacity.db.CapacityUsagePK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="overrideCapacityLimit(glog.ejb.capacity.db.CapacityUsagePK,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="isCapacityLimitRemovable(glog.ejb.capacity.db.CapacityLimitPK)"
    timeout="600000"
>
</method>
<method
    name="validateDateForManualExpire(glog.ejb.capacity.db.CapacityLimitPK,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="expireCapacityLimitsManually(glog.ejb.capacity.db.CapacityLimitPK[],glog.util.LocalTimestamp,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="validateDatesForAddNewLimit(glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="modifyCapacityLimit(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="modifyDailyCapacityLimit(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long[])"
    timeout="600000"
>
</method>
<method
    name="isCapacityLimitUpdatable(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="isDailyCapacityLimitUpdatable(glog.ejb.capacity.db.CapacityLimitPK,java.lang.Long[])"
    timeout="600000"
>
</method>
<method
    name="getActualCapacityUsage(glog.ejb.capacity.db.CapacityUsagePK)"
    timeout="600000"
>
</method>
<method
    name="getPlannedCapacityUsage(glog.ejb.capacity.db.CapacityUsagePK)"
    timeout="600000"
>
</method>
<method
    name="getCapacityUsages(glog.ejb.capacity.db.CapacityLimitPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
