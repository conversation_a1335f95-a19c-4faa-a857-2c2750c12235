<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.RouteExecutionActionSessionServerSideEJBWrapper_b3rp81_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="findPotentialLegsForShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.action.routeexecution.RouteExecutionIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentToLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK,glog.ejb.shipment.db.ShipmentPK,glog.business.action.routeexecution.RouteExecutionIgnoredCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findAndAssignShipmentToLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK,glog.business.action.routeexecution.RouteExecutionIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="disbandShipmentFromLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="disbandShipmentFromLeg(glog.ejb.shipment.db.ShipmentPK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="batchDisbandShipmentFromLeg(glog.ejb.shipment.db.ShipmentPK[],glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="batchDisbandShipmentFromLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK[],glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="deactivateRouteTemplates(glog.ejb.routeexecution.db.RouteTemplatePK[])"
    timeout="600000"
>
</method>
<method
    name="getEligibleWeeksForExpiration(glog.ejb.routeexecution.db.RouteTemplatePK)"
    timeout="600000"
>
</method>
<method
    name="expireRouteTemplate(glog.ejb.routeexecution.db.RouteTemplatePK,glog.util.LocalDate)"
    timeout="600000"
>
</method>
<method
    name="getEligibleWeeksForNewRouteInstances(glog.ejb.routeexecution.db.RouteTemplatePK)"
    timeout="600000"
>
</method>
<method
    name="createRouteInstances(glog.ejb.routeexecution.db.RouteTemplatePK,glog.business.action.routeexecution.EligibleWeeks)"
    timeout="600000"
>
</method>
<method
    name="createRouteInstances(glog.ejb.routeexecution.db.RouteTemplatePK,int)"
    timeout="600000"
>
</method>
<method
    name="activateRouteInstances(glog.ejb.routeexecution.db.RouteInstancePK[])"
    timeout="600000"
>
</method>
<method
    name="deactivateRouteInstances(glog.ejb.routeexecution.db.RouteInstancePK[])"
    timeout="600000"
>
</method>
<method
    name="recalculatePlannedStatistics(glog.ejb.routeexecution.db.RouteInstancePK[])"
    timeout="600000"
>
</method>
<method
    name="recalculateActualStatistics(glog.ejb.routeexecution.db.RouteInstancePK[])"
    timeout="600000"
>
</method>
<method
    name="findPotentialShipmentsForLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK,glog.business.action.routeexecution.RouteExecutionIgnoredCriteria,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="markLegAsMissed(glog.ejb.routeexecution.db.RouteInstanceLegPK)"
    timeout="600000"
>
</method>
<method
    name="markLegAsNotAssigned(glog.ejb.routeexecution.db.RouteInstanceLegPK)"
    timeout="600000"
>
</method>
<method
    name="findSimilarRouteTemplates(glog.ejb.routeexecution.db.RouteTemplatePK)"
    timeout="600000"
>
</method>
<method
    name="findSimilarRouteInstances(glog.ejb.routeexecution.db.RouteInstancePK)"
    timeout="600000"
>
</method>
<method
    name="changeServiceProvider(glog.ejb.routeexecution.db.RouteInstancePK,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="validateRouteInstanceLeg(glog.ejb.routeexecution.db.RouteInstanceLegPK)"
    timeout="600000"
>
</method>
<method
    name="activateRouteTemplates(glog.ejb.routeexecution.db.RouteTemplatePK[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
