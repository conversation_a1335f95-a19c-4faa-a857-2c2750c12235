<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.OMDProcessorSessionHome_f09cq7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="editShipments(glog.util.genericcontainer.BusinessObject,glog.ejb.orderbase.db.ObOrderBasePK,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="editShipments(glog.util.genericcontainer.BusinessObject,glog.ejb.order.db.OrderReleasePK,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="regenerateInvoices(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="regenerateBills(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="propagateObSuReleaseInstructionsChange(glog.ejb.orderbase.db.ObSuReleaseInstructionPK,boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="propagateObReleaseInstructionChange(glog.ejb.orderbase.db.ObReleaseInstructionPK,boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="propagateOrderBaseShipUnitChange(glog.ejb.orderbase.db.ObShipUnitPK,boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="propagateOrderBaseLineChange(glog.ejb.orderbase.db.ObLinePK,boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="propagateORModChange(glog.ejb.order.db.OrderReleasePK,boolean,boolean,boolean,boolean,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
