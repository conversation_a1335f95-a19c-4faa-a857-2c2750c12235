<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.CommitmentAllocSessionHome_718c2j_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="expireCommitmentAllocsManually(glog.ejb.capacity.db.CapacityCommitmentAllocPK[],glog.util.LocalTimestamp,boolean,boolean)"
    timeout="600000"
>
</method>
<method
    name="deleteCommitmentAlloc(glog.ejb.capacity.db.CapacityCommitmentAllocPK)"
    timeout="600000"
>
</method>
<method
    name="getAllocUsageDsForOverride(glog.ejb.capacity.db.CommitAllocUsagePK[])"
    timeout="600000"
>
</method>
<method
    name="overrideAllocation(glog.ejb.capacity.db.CommitAllocUsageDData[])"
    timeout="600000"
>
</method>
<method
    name="setIsActive(glog.ejb.capacity.db.CommitAllocUsagePK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="validateForAddNewAlloc(glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="createUsagesForNewAlloc(glog.ejb.capacity.db.CapacityCommitmentAllocPK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
