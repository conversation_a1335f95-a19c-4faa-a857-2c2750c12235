<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.OrderBaseShipmentLinkSessionServerSideEJBWrapper_arcs3v_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="copyInvolvedPartyFromOBToShipment(glog.business.action.ObShipmentCopyContext)"
    timeout="600000"
>
</method>
<method
    name="updateOrderBase(glog.business.action.ObShipmentLinkContext)"
    timeout="600000"
>
</method>
<method
    name="copyOBRemarkToShipment(glog.business.action.ObShipmentCopyContext)"
    timeout="600000"
>
</method>
<method
    name="linkShipmentAndOrderBase(glog.business.action.ObShipmentLinkContext)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
