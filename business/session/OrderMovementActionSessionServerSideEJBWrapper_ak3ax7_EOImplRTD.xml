<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.OrderMovementActionSessionServerSideEJBWrapper_ak3ax7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="buildShipmentGraphFromLegConstraints(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.BSLegConstraints,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildMultiStopShipment(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.OMBSConstraints,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="bulkPlanOrderMovements(glog.ejb.bulkplan.db.BulkPlanPK,glog.ejb.order.db.OrderMovementPK[],boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="bulkPlanOrderMovements(glog.ejb.bulkplan.db.BulkPlanPartitionPK,glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignOrderMovements(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unassignOrderMovements(glog.ejb.bulkplan.db.BulkPlanPK,glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildShipments(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildDirectShipments(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildRoutingLegOptions(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.OMBSConstraints,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveOrderMovementsToShipment(glog.ejb.order.db.OrderMovementPK[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveOrderMovementsToScheduleInstanceShipment(glog.ejb.order.db.OrderMovementPK[],glog.ejb.shipment.db.ShipmentPK,glog.business.action.order.StopInfo,glog.business.action.order.StopInfo,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveOrderMovementsToConsolShipment(glog.ejb.order.db.OrderMovementPK[],glog.business.action.consol.ConsolShipmentResult,glog.ejb.shipment.db.SEquipmentPK,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="statusAllowsBuildShipments(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="deleteOrderMovements(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="splitOrderMovement(glog.ejb.order.db.OrderMovementPK,glog.business.action.helper.SplitOffShipUnitStruct[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="createRelayOrderMovement(glog.ejb.order.db.OrderMovementPK,glog.business.service.order.datastructure.RelayOrderMovementInput,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="collapseOrderMovementsGetInput(glog.ejb.order.db.OrderMovementPK)"
    timeout="600000"
>
</method>
<method
    name="collapseOrderMovements(glog.ejb.order.db.OrderMovementPK,glog.business.service.order.datastructure.CollapseOrderMovementsInput,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="setRoutingFixed(glog.ejb.order.db.OrderMovementPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="validateOrderMovementsForTemplates(java.lang.String[],glog.webserver.uic.order.OrderMovementHelper$ValidationFlags)"
    timeout="600000"
>
</method>
<method
    name="createTopNetworkRoutingOptions(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.bsnro.BSNROConstraints,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentsUsingNRO(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.RoutingOption,glog.business.action.datastructure.bsnro.BSNROConstraints,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateBuildShipmentsUsingNRO(glog.ejb.order.db.OrderMovementPK[],glog.business.action.datastructure.bsnro.BSNROConstraints,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
