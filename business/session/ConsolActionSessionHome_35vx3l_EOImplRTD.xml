<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ConsolActionSessionHome_35vx3l_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="close(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="open(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="preCommitConsolCapacity(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="commitConsolCapacity(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="makeConsolShipment(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unCommitConsolCapacity(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="unPreCommitConsolCapacity(glog.ejb.consol.db.ConsolPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="changeStowageMode(glog.ejb.consol.db.ConsolPK,glog.ejb.consol.db.StowageModePK,glog.ejb.equipment.db.EquipmentGroupProfilePK)"
    timeout="600000"
>
</method>
<method
    name="getFlightInstanceId(glog.ejb.flight.db.FlightPK,glog.util.LocalTimestamp)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
