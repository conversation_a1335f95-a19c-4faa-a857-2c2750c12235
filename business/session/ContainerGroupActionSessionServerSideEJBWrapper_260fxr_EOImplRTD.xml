<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ContainerGroupActionSessionServerSideEJBWrapper_260fxr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="openContainerGroup(glog.ejb.unitization.db.ContainerGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="moveContainers(glog.ejb.shipment.db.ShipmentPK,glog.ejb.unitization.db.ContainerGroupPK,glog.ejb.shipment.db.SEquipmentPK[],glog.ejb.unitization.db.ContainerGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="moveContainerGroup(glog.ejb.unitization.db.ContainerGroupPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="changeStuffDestuffLocations(glog.ejb.unitization.db.ContainerGroupPK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipments(glog.ejb.unitization.db.ContainerGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="closeContainerGroup(glog.ejb.unitization.db.ContainerGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
