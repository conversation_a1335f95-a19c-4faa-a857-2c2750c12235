<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.OrderBaseActionSessionHome_lsyxxr_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="reReleaseOrderBase(glog.ejb.orderbase.db.ObOrderBasePK[])"
    timeout="600000"
>
</method>
<method
    name="reReleaseObSuReleaseInstruction(glog.ejb.orderbase.db.ObSuReleaseInstructionPK)"
    timeout="600000"
>
</method>
<method
    name="deleteObSuReleaseInstruction(glog.ejb.orderbase.db.ObSuReleaseInstructionPK)"
    timeout="600000"
>
</method>
<method
    name="reReleaseObReleaseInstruction(glog.ejb.orderbase.db.ObReleaseInstructionPK)"
    timeout="600000"
>
</method>
<method
    name="deleteObReleaseInstruction(glog.ejb.orderbase.db.ObReleaseInstructionPK)"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObOrderBasePK,boolean)"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObShipUnitPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObOrderBasePK)"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObLinePK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObLinePK[])"
    timeout="600000"
>
</method>
<method
    name="buildOrderReleases(glog.ejb.orderbase.db.ObShipUnitPK[])"
    timeout="600000"
>
</method>
<method
    name="changeOrderBaseStatuses(glog.ejb.orderbase.db.ObOrderBasePK[],java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="changeOrderBaseStatus(glog.ejb.orderbase.db.ObOrderBasePK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changeReleasingStatus(glog.ejb.orderbase.db.ObOrderBasePK[])"
    timeout="600000"
>
</method>
<method
    name="ROECancelAction(glog.ejb.orderbase.db.ObOrderBasePK,glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="setDefaultInvolvedParties(glog.ejb.orderbase.db.ObOrderBasePK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentsForOrderBase(glog.ejb.orderbase.db.ObOrderBasePK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="generateBillsFromBase(glog.ejb.orderbase.db.ObOrderBasePK[])"
    timeout="600000"
>
</method>
<method
    name="generateBillFromBase(glog.ejb.orderbase.db.ObOrderBasePK)"
    timeout="600000"
>
</method>
<method
    name="modificationAcknowledged(glog.ejb.orderbase.db.ObOrderBasePK)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
