<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ConsolRetrievingSessionServerSideEJBWrapper_n8ipu7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="retrieveConsolShipmentResults(glog.ejb.shipment.db.ShipmentPK,glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.shipment.db.ShipmentPK,glog.business.action.helper.SplitOffShipUnitStruct[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.shipment.db.ShipmentPK,glog.ejb.order.db.OrderReleasePK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.order.db.OrderMovementPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="retrieveConsolShipmentResults(glog.ejb.order.db.OrderReleasePK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
