<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.BatchBalanceActionSessionServerSideEJBWrapper_4ici9b_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="splitPartOfOrderToDifferentBatch(glog.ejb.batch.db.SchedulePK,int,java.lang.String,glog.ejb.order.db.OrderReleasePK,glog.ejb.shipment.db.SShipUnitPK,int,int,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="splitPartOfOrderOffShipmentToCell(glog.ejb.batch.db.SchedulePK,int,java.lang.String,glog.ejb.order.db.OrderReleasePK,glog.ejb.shipment.db.SShipUnitPK,int,int,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="scheduleUnScheduledOrdersToCell(glog.ejb.batch.db.SchedulePK,glog.ejb.order.db.OrderReleasePK[],int,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="mergeShipmentFromCellToCell(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,int,java.lang.String,int,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="unscheduledOrders(glog.ejb.batch.db.SchedulePK,glog.ejb.order.db.OrderReleasePK[],glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="buildMultistopShipment(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK[],boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="disbandMultistopShipment(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="recoverScheduleStatusFromInProgress(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="approveSchedule(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="buildBatchGrid(glog.ejb.batch.db.SchedulePK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="moveSingleCell(glog.ejb.batch.db.SchedulePK,int,java.lang.String,int,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="clearCell(glog.ejb.batch.db.SchedulePK,int,java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveUnAllocatedShipmentsToCell(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK[],java.lang.String,int,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="moveUnAllocatedMultistopShipmentToCells(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.business.action.BatchGridCellTemplate[],boolean)"
    timeout="600000"
>
</method>
<method
    name="splitOrderToDifferentBatch(glog.ejb.batch.db.SchedulePK,int,java.lang.String,glog.ejb.order.db.OrderReleasePK[],int,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="splitOrderOffShipmentToCell(glog.ejb.batch.db.SchedulePK,int,java.lang.String,glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,int,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="disbandBatchGrid(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="addBatch(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="moveShipments(glog.ejb.batch.db.SchedulePK,int,java.lang.String,int,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="sendIntegration(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="updateBatchNames(glog.ejb.batch.db.SchedulePK,glog.business.batch.BatchNumberNamePair[])"
    timeout="600000"
>
</method>
<method
    name="refreshBatchGrid(glog.ejb.batch.db.SchedulePK)"
    timeout="600000"
>
</method>
<method
    name="mergePreloadShipmentToCell(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,int,java.lang.String,boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="multiStopPreloadAndOtherShipments(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK[],boolean,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="flagShipmentsAsToBeHeld(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="clearCellOffShipment(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK,int,java.lang.String,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="getNumberOfCellsNeededForShipment(glog.ejb.batch.db.SchedulePK,glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getBBChangeHistorySince(glog.ejb.batch.db.SchedulePK,long,glog.business.batch.BBWorkArea,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
