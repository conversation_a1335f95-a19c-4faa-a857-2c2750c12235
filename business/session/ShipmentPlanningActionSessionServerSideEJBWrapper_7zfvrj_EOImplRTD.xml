<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.ShipmentPlanningActionSessionServerSideEJBWrapper_7zfvrj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAllServiceProviderOption(glog.ejb.shipment.db.ShipmentPK,boolean,boolean,boolean,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentPlannedStopTimes(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,boolean,glog.util.LocalTimestamp,boolean,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentPlannedStopTimes(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,boolean,glog.util.LocalTimestamp,boolean,boolean,boolean,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignSpecificServiceProvider(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignServiceProviderUsingRateGeo(glog.ejb.shipment.db.ShipmentPK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignSpecificEquipmentGroup(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignBestEquipmentGroup(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentRate(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentRates(glog.ejb.shipment.db.ShipmentPK[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignSellShipmentRates(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignSellShipmentRatesByVoucher(glog.ejb.invoice.db.VoucherPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentPlannedStopTimesManually(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentStopPK,glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="setShipmentStopAppointments(glog.business.shipment.ShipmentStopAppointment[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getLoadUnloadPointsAtStop(glog.ejb.shipment.db.ShipmentStopPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="resequenceShipmentStops(glog.ejb.shipment.db.ShipmentPK,int[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="deleteShipments(glog.ejb.shipment.db.ShipmentPK[],glog.business.action.CriteriaToBeIgnored)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="manualEquipmentChange(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="changeShipmentStatuses(glog.ejb.shipment.db.ShipmentPK[],java.lang.String[],java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="changeShipmentStatus(glog.ejb.shipment.db.ShipmentPK,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getOrdersForShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipments(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="splitShipmentByPercentage(glog.ejb.shipment.db.ShipmentPK,java.lang.Double,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitShipmentByPercentage(glog.ejb.shipment.db.ShipmentPK,java.lang.Double,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitShipmentBySShipUnits(glog.ejb.shipment.db.ShipmentPK,glog.business.action.helper.SplitOffShipUnitStruct[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,boolean,boolean,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitShipmentBySShipUnits(glog.ejb.shipment.db.ShipmentPK,glog.business.action.helper.SplitOffShipUnitStruct[],boolean,java.lang.String,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="mergeOntoShipment(glog.ejb.shipment.db.ShipmentPK[],glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignLeastCostServiceProvider(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignTenderContact(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignServiceProviderManually(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.util.currency.Currency,glog.util.currency.Currency,glog.business.shipment.TShipmentCost[],glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignServiceProviderManually(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.util.currency.Currency,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitBookingBySShipUnits(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.business.action.helper.SplitOffShipUnitStruct[],java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitBookingBySEquipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitBookingBySEquipments(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK[],java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitBookingByEquipmentGroup(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,java.util.HashMap,java.lang.String,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="divertShipment(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.LocationPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveSShipUnitsFromShipmentToShipment(glog.ejb.shipment.db.SShipUnitPK[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="moveOrdersFromShipmentToShipment(glog.ejb.order.db.OrderReleasePK[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findFlights(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="combineShipments(glog.ejb.shipment.db.ShipmentPK[],boolean,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="combineShipments(java.lang.String[],boolean,java.lang.String,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="reCalculateShipmentCost(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignFlightInstance(glog.ejb.shipment.db.ShipmentPK,glog.business.airschedule.TFlightInstance,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findRepSchedules(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateScheduleInstances(glog.ejb.repetitionschedule.db.GroundSchedGenPK,glog.ejb.repetitionschedule.db.RepetitionSchedulePK[],glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.util.uom.data.Duration,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateScheduleInstances(glog.ejb.repetitionschedule.db.RepetitionSchedulePK,glog.util.LocalTimestamp,glog.util.uom.data.Duration,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="generateScheduleInstances(glog.ejb.repetitionschedule.db.RepetitionSchedulePK,int,int,int,int,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="assignRepSchedule(glog.ejb.shipment.db.ShipmentPK,glog.business.repetitionschedule.RepetitionScheduleResult,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getVoyageInput(glog.ejb.shipment.db.ShipmentPK,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="findVoyages(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.ServprovPK,boolean,glog.ejb.location.db.ServprovProfilePK,glog.ejb.location.db.LocationPK[],glog.ejb.location.db.LocationPK[],glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.ejb.itinerary.db.VoyagePK,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="assignVoyage(glog.ejb.shipment.db.ShipmentPK,glog.ejb.itinerary.db.VoyagePK,glog.ejb.location.db.ServprovPK,glog.ejb.location.db.LocationPK,glog.ejb.location.db.LocationPK,boolean,java.lang.String,boolean,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="ROEApproveAction(java.util.Hashtable,glog.ejb.invoice.db.InvoicePK[])"
    timeout="600000"
>
</method>
<method
    name="splitShipmentByOrders(glog.ejb.shipment.db.ShipmentPK,java.lang.String[],boolean,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,boolean,boolean,java.lang.String,java.lang.String,java.lang.String,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="splitShipmentByOrderMovements(glog.ejb.shipment.db.ShipmentPK,java.lang.String[],glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.SEquipmentPK,boolean,boolean,java.lang.String,java.lang.String,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="convertToStopoff(glog.ejb.shipment.db.ShipmentPK,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="convertToDropoff(glog.ejb.shipment.db.ShipmentPK,java.lang.Integer,java.lang.Integer,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="createContinuousMove(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,boolean,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="manualCreateContinuousMove(glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="disbandContinuousMove(glog.ejb.shipment.db.ShipmentPK,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="disbandContinuousMove(java.lang.String,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findContinuousMoveShipments(glog.ejb.shipment.db.ShipmentPK,glog.util.uom.data.Distance,glog.util.uom.data.Duration,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findAndCreateContinuousMove(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="rerateCmTour(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="bulkContinuousMove(glog.ejb.bulkcm.db.BulkCmPK,glog.ejb.shipment.db.ShipmentPK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changeTransportMode(glog.ejb.shipment.db.ShipmentPK,java.lang.String,java.lang.String,boolean,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="moveToReviewed(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="calculateShipmentDistances(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="setOperationalLocations(glog.ejb.shipment.db.ShipmentPK,glog.ejb.location.db.LocationPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipmentGGraph(glog.ejb.shipment.db.ShipmentPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="modificationAcknowledged(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="moveEquipmentFromShipmentToShipment(glog.ejb.shipment.db.SEquipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="viewSEquipments(glog.ejb.shipment.db.SEquipmentPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="validateShipmentHasNoArbitraries(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="findRelatedCommitmentCounts(glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,glog.ejb.order.db.OrderReleasePK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findRelatedCommitmentCounts(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="findRelatedCommitmentAllocations(glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.ejb.order.db.OrderReleasePK,java.lang.Boolean,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findRelatedCommitmentAllocations(glog.util.LocalTimestamp,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.ejb.order.db.OrderReleasePK)"
    timeout="600000"
>
</method>
<method
    name="findRelatedCommitmentAllocations(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="retrieveLoadingConfig(glog.ejb.shipment.db.SEquipmentPK,glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="getRelatedOrderMovements(glog.ejb.shipment.db.ShipmentPK[])"
    timeout="600000"
>
</method>
<method
    name="generateGroundScheduleGenPK(java.lang.String,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.Long,java.lang.Boolean,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="startGroundSchedulePlan(glog.ejb.repetitionschedule.db.GroundSchedGenPK,glog.util.LocalTimestamp,long)"
    timeout="600000"
>
</method>
<method
    name="endGroundSchedulePlan(glog.ejb.repetitionschedule.db.GroundSchedGenPK)"
    timeout="600000"
>
</method>
<method
    name="getNoOfShipmentsExcepted(glog.ejb.repetitionschedule.db.RepetitionSchedulePK[],glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.util.uom.data.Duration,boolean,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="repackExistingEquipment(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="repackSShipUnit(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="setShipmentStopRefnums(java.lang.String,java.lang.String,java.util.Set)"
    timeout="600000"
>
</method>
<method
    name="createContinuousMoveForShipments(glog.ejb.shipment.db.ShipmentPK[],glog.business.continuousmove.CmInputCriteria,glog.webserver.umt.UserPreference,glog.business.action.CriteriaToBeIgnored)"
    timeout="600000"
>
</method>
<method
    name="linkShipmentsForUI(java.lang.String[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="rerateAndRedrive(glog.ejb.shipment.db.ShipmentPK,boolean,boolean,boolean,boolean,java.lang.String,boolean,java.lang.String,glog.business.action.CriteriaToBeIgnored,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
