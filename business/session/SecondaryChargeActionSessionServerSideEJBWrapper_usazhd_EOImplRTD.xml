<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.session.SecondaryChargeActionSessionServerSideEJBWrapper_usazhd_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createSCShipment(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.business.action.SecondaryChargeTemplate)"
    timeout="600000"
>
</method>
<method
    name="createSCShipment(glog.ejb.shipment.db.ShipmentPK,glog.business.action.SecondaryChargeTemplate)"
    timeout="600000"
>
</method>
<method
    name="deleteAllSCShipmentsForShipmentGroup(glog.ejb.shipmentgroup.db.ShipGroupPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="deleteAllSCShipmentsForShipment(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="recalculateSCShipmentsCostForShipmentGroup(glog.ejb.shipmentgroup.db.ShipGroupPK)"
    timeout="600000"
>
</method>
<method
    name="reassociateSCShipmentsForShipmentGroup(glog.ejb.shipmentgroup.db.ShipGroupPK)"
    timeout="600000"
>
</method>
<method
    name="recalculateSCShipmentsCostForShipment(glog.ejb.shipment.db.ShipmentPK)"
    timeout="600000"
>
</method>
<method
    name="deleteSCShipments(glog.ejb.shipment.db.ShipmentPK[],glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="deleteSCShipment(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildSecondaryChargeShipments(glog.ejb.shipment.db.ShipmentPK,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="buildSecondaryChargeShipments(glog.ejb.shipment.db.ShipmentPK,java.lang.String,glog.webserver.umt.UserPreference)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
