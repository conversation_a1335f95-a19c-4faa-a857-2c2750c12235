<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.workinvoice.WorkInvoiceUpdateSessionServerSideEJBWrapper_d01l4f_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="deleteTWorkInvoice(glog.business.workinvoice.TWorkInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTWorkInvoiceWithNotReceivedStatus(glog.business.workinvoice.TWorkInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTWorkInvoiceWithReceivedStatus(glog.business.workinvoice.TWorkInvoice,glog.ejb.workinvoice.WorkInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="cancelTWorkInvoice(glog.business.workinvoice.TWorkInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitTWorkInvoice(glog.business.workinvoice.TWorkInvoice)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
