<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.demurrage.DmTransactionServerActionSessionServerSideEJBWrapper_km0o01_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="UpdateDmTransactionRefnums(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.demurrage.db.DmTransactionRefnumData[])"
    timeout="600000"
>
</method>
<method
    name="calculateCharges(glog.ejb.demurrage.db.DmTransactionPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="recalculateCharges(glog.ejb.demurrage.db.DmTransactionPK[],boolean)"
    timeout="600000"
>
</method>
<method
    name="setDmTransactionEvent(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.businessobjectstatus.db.BsStatusCodePK,glog.util.LocalTimestamp,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveDmTransactionRefnums(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.demurrage.db.DmTransactionRefnumPK[])"
    timeout="600000"
>
</method>
<method
    name="getRelatedShipments(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="UpdateDmTransactionRemarks(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.demurrage.db.DmTransactionRemarkData[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
