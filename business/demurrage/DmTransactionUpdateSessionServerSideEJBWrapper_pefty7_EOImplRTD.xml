<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.demurrage.DmTransactionUpdateSessionServerSideEJBWrapper_pefty7_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="saveTDmTransaction(glog.business.demurrage.TDmTransaction,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveTDmTransactionWithCharges(glog.business.demurrage.TDmTransaction,int)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveDmTransactionEvent(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.businessobjectstatus.db.BsStatusCodePK,glog.util.LocalTimestamp,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveDmTransactionRefNum(glog.ejb.demurrage.db.DmTransactionPK[],glog.ejb.demurrage.db.DmTransactionRefnumPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
