<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.location.LocationUpdateSessionHome_o38rzj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createBuyer(glog.ejb.useraccess.db.BuyerData)"
    timeout="600000"
>
</method>
<method
    name="createBuyers(glog.ejb.useraccess.db.BuyerData[])"
    timeout="600000"
>
</method>
<method
    name="createCustomer(glog.ejb.customer.db.CustomerData)"
    timeout="600000"
>
</method>
<method
    name="createCustomers(glog.ejb.customer.db.CustomerData[])"
    timeout="600000"
>
</method>
<method
    name="applyLocationTemplate(java.util.Collection,glog.util.genericcontainer.DataSource,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
