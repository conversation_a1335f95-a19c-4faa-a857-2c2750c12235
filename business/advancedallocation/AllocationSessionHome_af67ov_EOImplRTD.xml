<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.advancedallocation.AllocationSessionHome_af67ov_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="allocate(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="allocateShipment(java.util.Vector,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="allocateVoucher(java.util.Vector,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="allocateCustomerBills(java.util.Vector,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="allocateByInvoice(java.util.Vector,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="allocateByOrderRelease(java.util.Vector)"
    timeout="600000"
>
</method>
<method
    name="setAllocationIssuedStatus(java.util.Vector)"
    timeout="600000"
>
</method>
<method
    name="closeAccruals(java.util.Vector)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
