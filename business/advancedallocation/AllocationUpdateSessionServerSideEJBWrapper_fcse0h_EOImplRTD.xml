<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.advancedallocation.AllocationUpdateSessionServerSideEJBWrapper_fcse0h_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="commitTAllocationBase(glog.business.advancedallocation.TAllocationBase[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAccruals(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="closeAccruals(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createAndDeleteAccruals(java.util.Collection,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="commitBeanDataChanges(glog.util.remote.BeanData[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAllocationsForShipments(java.util.Collection,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAllocationsForVouchers(java.util.Collection,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAllocationsForInvoices(java.util.Collection,java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeAllocations(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createAccruals(java.util.Collection)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createAccrual(glog.ejb.order.db.OrderReleaseAccrualData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateAccrualCost(glog.ejb.order.db.OrderReleaseAccrualPK,glog.util.currency.Currency)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
