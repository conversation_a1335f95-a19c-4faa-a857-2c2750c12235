<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.voyage.VoyageSessionHome_le8wfl_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="findVoyageServprovGids(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findCharterVoyages(java.lang.String,java.lang.String,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findVoyages(java.lang.String[],java.lang.String[],glog.util.LocalTimestamp,glog.util.LocalTimestamp,glog.business.util.PkProfile,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findVoyages(java.lang.String,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findVoyages(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
