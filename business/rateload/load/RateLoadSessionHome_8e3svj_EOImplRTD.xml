<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.rateload.load.RateLoadSessionHome_8e3svj_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="processRateLoad(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadProcessStatus(java.lang.String,java.lang.Long,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistCRTLoadProcessStatus(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadSourcingRates(java.lang.String,java.util.HashMap,java.util.HashMap,java.lang.String,java.lang.String,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.util.HashMap,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
