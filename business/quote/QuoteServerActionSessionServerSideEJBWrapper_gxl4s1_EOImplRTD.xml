<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.quote.QuoteServerActionSessionServerSideEJBWrapper_gxl4s1_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="applyNFRCRuleManually(glog.ejb.quote.db.QuoteCostOptionPK,glog.ejb.job.db.NfrcRulePK)"
    timeout="600000"
>
</method>
<method
    name="applyNFRCRules(glog.business.action.datastructure.RoutingOption,glog.business.rate.rateinquiry.RateInquiryInput)"
    timeout="600000"
>
</method>
<method
    name="applyNFRCRules(glog.business.action.datastructure.RoutingOption,glog.business.rate.rateinquiry.RateInquiryInput,glog.ejb.quote.db.QuotePK)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
