<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.askotmsavedquery.AskOtmSavedQuerySessionServerSideEJBWrapper_wtlzyp_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createQuery(java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createQuery(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createQuery(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationTimezone(glog.business.location.Address)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="retriveQueries(java.lang.String,glog.server.useraccess.AskOtmSavedQueryAccessContainer,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="retriveQueries(java.lang.String,glog.server.useraccess.AskOtmSavedQueryAccessContainer)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAskOtmSavedQuery(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAskOtmSavedQuery(java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
