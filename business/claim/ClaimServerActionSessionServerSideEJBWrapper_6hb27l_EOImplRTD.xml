<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.business.claim.ClaimServerActionSessionServerSideEJBWrapper_6hb27l_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="createClaimLineItems(java.lang.String,glog.ejb.shipment.db.ShipmentPK,java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="createClaimLineItemsFromExternalSystem(glog.business.claim.ClaimLineFromExtSystemInput)"
    timeout="600000"
>
</method>
<method
    name="updateClaimLineItemsToExternalSystem(glog.ejb.claim.db.ClaimLineItemPK,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="estimateDamageValue(glog.ejb.claim.db.ClaimPK[])"
    timeout="600000"
>
</method>
<method
    name="calculateSizeOfLoss(glog.ejb.claim.db.ClaimPK[])"
    timeout="600000"
>
</method>
<method
    name="markReviewed(glog.ejb.claim.db.ClaimPK[])"
    timeout="600000"
>
</method>
<method
    name="markResolved(glog.ejb.claim.db.ClaimPK[])"
    timeout="600000"
>
</method>
<method
    name="markSustained(glog.ejb.claim.db.ClaimPK[])"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
