<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.ejb.savedquery.SavedQuerySessionHome_lyjr4f_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="runBusinessMonitor(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runBusinessMonitorByGroup(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteQuery(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteQuery(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteQueries(glog.ejb.savedquery.db.SavedQueryPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateQuery(java.lang.String,java.lang.String,java.lang.String,glog.ejb.savedquery.SavedQueryDef)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateQuery(java.lang.String,java.lang.String,glog.ejb.savedquery.SavedQueryDef)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSaveQueryNames(java.lang.String,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getSaveQueryNames(java.lang.String,glog.server.useraccess.SavedQueryAccessContainer,boolean)"
    timeout="600000"
>
</method>
<method
    name="getSavedQueriesForNames(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSavedQueries(java.lang.String,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getSaveQueryNamesForType(java.lang.String,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getSavedQuery(glog.ejb.savedquery.db.SavedQueryPK,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getSavedQuery(glog.ejb.savedquery.db.SavedQueryPK)"
    timeout="600000"
>
</method>
<method
    name="getSavedQuery(java.lang.String,java.lang.String,java.lang.String,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="getSavedQuery(java.lang.String,java.lang.String,glog.server.useraccess.SavedQueryAccessContainer)"
    timeout="600000"
>
</method>
<method
    name="createQuery(java.lang.String,java.lang.String,glog.ejb.savedquery.SavedQueryDef)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="createQuery(java.lang.String,java.lang.String,java.lang.String,glog.ejb.savedquery.SavedQueryDef)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
