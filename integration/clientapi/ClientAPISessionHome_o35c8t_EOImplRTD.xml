<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.integration.clientapi.ClientAPISessionHome_o35c8t_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="close()"
    timeout="600000"
>
</method>
<method
    name="delete(glog.integration.clientapi.ValuesObject)"
    timeout="600000"
>
</method>
<method
    name="insert(glog.integration.clientapi.ValuesObject)"
    timeout="600000"
>
</method>
<method
    name="update(glog.integration.clientapi.ValuesObject)"
    timeout="600000"
>
</method>
<method
    name="findByPrimaryKey(glog.integration.clientapi.ValuesObject)"
    timeout="600000"
>
</method>
<method
    name="insertUpdate(glog.integration.clientapi.ValuesObject)"
    timeout="600000"
>
</method>
<method
    name="getEntityNames()"
    timeout="600000"
>
</method>
<method
    name="describeEntity(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="execMany(glog.integration.clientapi.ValuesObject[])"
    timeout="600000"
>
</method>
<method
    name="findAll(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
