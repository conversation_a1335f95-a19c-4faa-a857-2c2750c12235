<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.integration.application.CommandLineSessionHome_12qkd9_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="setTransmissionStatusDatesCount(long,java.lang.String,glog.util.LocalTimestamp,glog.util.LocalTimestamp,java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="setTransactionStatus(long,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="sendMail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runPerformance(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="schedulePerformanceExecution(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="stopPerformanceExecution()"
    timeout="600000"
>
</method>
<method
    name="saveTransmissionWithDS(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTemplate(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="processQuery(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="listDir(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="catFile(java.lang.String,long)"
    timeout="600000"
>
</method>
<method
    name="markAllTransmissionsForRedo(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveTransmissionHeader(java.lang.String,long,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveGLogXMLElement(java.lang.String,long)"
    timeout="600000"
>
</method>
<method
    name="saveGLogXMLElement(java.lang.String[],long)"
    timeout="600000"
>
</method>
<method
    name="getNextTransmissionNo()"
    timeout="600000"
>
</method>
<method
    name="raiseNewXMLTopic(long,java.lang.String,glog.database.security.crypto.Password)"
    timeout="600000"
>
</method>
<method
    name="createInboundQueue(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clientAPIExecMany(glog.integration.clientapi.ValuesObject[])"
    timeout="600000"
>
</method>
<method
    name="setTransmissionStatus(long,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="runCommand(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="saveTransmission(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveTransmission(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="saveTransmission(java.lang.String,java.lang.String,java.lang.String,glog.integration.tools.GLCredential)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
