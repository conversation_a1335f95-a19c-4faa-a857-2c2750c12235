/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.EJBObject
 *  weblogic.ejb.container.interfaces.Invokable
 *  weblogic.ejb.container.internal.BaseRemoteObject
 *  weblogic.ejb.container.internal.InvocationWrapper
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.SessionRemoteMethodInvoker
 *  weblogic.ejb.container.internal.StatelessEJBObject
 */
package glog.server.dbxml;

import glog.integration.tools.dbxml.DBXMLExportRequest;
import glog.integration.tools.dbxml.DBXMLExportResponse;
import glog.integration.tools.dbxml.DBXMLImportRequest;
import glog.integration.tools.dbxml.DBXMLImportResponse;
import glog.server.dbxml.DBXMLSession;
import glog.server.dbxml.DBXMLSessionHome_cbifft_Intf;
import glog.util.exception.GLException;
import glog.util.genericcontainer.DataSource;
import java.rmi.RemoteException;
import javax.ejb.EJBHome;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.ejb.EJBObject;
import weblogic.ejb.container.interfaces.Invokable;
import weblogic.ejb.container.internal.BaseRemoteObject;
import weblogic.ejb.container.internal.InvocationWrapper;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.SessionRemoteMethodInvoker;
import weblogic.ejb.container.internal.StatelessEJBObject;

public final class DBXMLSessionHome_cbifft_EOImpl
extends StatelessEJBObject
implements DBXMLSession,
EJBObject,
Invokable {
    public static MethodDescriptor md_eo_xmlImport_glog_integration_tools_dbxml_DBXMLImportRequestglog_util_genericcontainer_DataSource;
    public static MethodDescriptor md_eo_xmlImport_glog_integration_tools_dbxml_DBXMLImportRequest;
    public static MethodDescriptor md_eo_xmlExport_glog_integration_tools_dbxml_DBXMLExportRequest;
    public static MethodDescriptor md_eo_remove;
    public static MethodDescriptor md_eo_getEJBHome;
    public static MethodDescriptor md_eo_getHandle;
    public static MethodDescriptor md_eo_getPrimaryKey;
    public static MethodDescriptor md_eo_isIdentical_javax_ejb_EJBObject;

    public DBXMLExportResponse xmlExport(DBXMLExportRequest dBXMLExportRequest) throws RemoteException, GLException {
        return (DBXMLExportResponse)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_xmlExport_glog_integration_tools_dbxml_DBXMLExportRequest), (Object[])new Object[]{dBXMLExportRequest}, (int)0);
    }

    public DBXMLImportResponse xmlImport(DBXMLImportRequest dBXMLImportRequest, DataSource dataSource) throws RemoteException, GLException {
        return (DBXMLImportResponse)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_xmlImport_glog_integration_tools_dbxml_DBXMLImportRequestglog_util_genericcontainer_DataSource), (Object[])new Object[]{dBXMLImportRequest, dataSource}, (int)1);
    }

    public DBXMLImportResponse xmlImport(DBXMLImportRequest dBXMLImportRequest) throws RemoteException, GLException {
        return (DBXMLImportResponse)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_xmlImport_glog_integration_tools_dbxml_DBXMLImportRequest), (Object[])new Object[]{dBXMLImportRequest}, (int)2);
    }

    public Object __WL_invoke(Object object, Object[] objectArray, int n) throws Throwable {
        switch (n) {
            case 0: {
                return ((DBXMLSessionHome_cbifft_Intf)object).xmlExport((DBXMLExportRequest)objectArray[0]);
            }
            case 1: {
                return ((DBXMLSessionHome_cbifft_Intf)object).xmlImport((DBXMLImportRequest)objectArray[0], (DataSource)objectArray[1]);
            }
            case 2: {
                return ((DBXMLSessionHome_cbifft_Intf)object).xmlImport((DBXMLImportRequest)objectArray[0]);
            }
        }
        throw new IllegalArgumentException("No method found for index : " + n);
    }

    public void __WL_handleException(int n, Throwable throwable) throws Throwable {
        switch (n) {
            case 0: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 1: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 2: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            default: {
                throw new IllegalArgumentException("No method found for index : " + n);
            }
        }
    }

    public void remove() throws RemoveException, RemoteException {
        super.remove(md_eo_remove);
    }

    public EJBHome getEJBHome() throws RemoteException {
        return super.getEJBHome(md_eo_getEJBHome);
    }

    public Handle getHandle() throws RemoteException {
        return super.getHandle(md_eo_getHandle);
    }

    public Object getPrimaryKey() throws RemoteException {
        return super.getPrimaryKey(md_eo_getPrimaryKey);
    }

    public boolean isIdentical(javax.ejb.EJBObject eJBObject) throws RemoteException {
        return super.isIdentical(md_eo_isIdentical_javax_ejb_EJBObject, eJBObject);
    }

    public void operationsComplete() {
    }
}
