/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBMetaData
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 */
package glog.server.dbxml;

import glog.server.dbxml.DBXMLSession;
import glog.server.dbxml.DBXMLSessionHome;
import glog.server.dbxml.DBXMLSessionStub;
import javax.ejb.EJBMetaData;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;

public class DBXMLSessionHomeStub
implements DBXMLSessionHome {
    @Override
    public DBXMLSession create() {
        return new DBXMLSessionStub(this);
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }

    public void remove(Object o) {
    }
}
