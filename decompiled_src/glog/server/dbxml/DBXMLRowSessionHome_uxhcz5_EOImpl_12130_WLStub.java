/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.rmi.extensions.RemoteRuntimeException
 *  weblogic.rmi.extensions.server.RemoteReference
 *  weblogic.rmi.extensions.server.RuntimeMethodDescriptor
 *  weblogic.rmi.extensions.server.StubReference
 *  weblogic.rmi.internal.MethodDescriptor
 *  weblogic.rmi.internal.Stub
 *  weblogic.rmi.internal.StubInfo
 *  weblogic.rmi.internal.StubInfoIntf
 *  weblogic.rmi.utils.Utilities
 */
package glog.server.dbxml;

import glog.integration.tools.dbxml.DBXMLImportRequest;
import glog.integration.tools.dbxml.DBXMLImportResponse;
import glog.integration.tools.dbxml.DBXMLRow;
import glog.server.dbxml.DBXMLRowSession;
import glog.util.exception.GLException;
import glog.util.genericcontainer.DataSource;
import java.lang.reflect.Method;
import java.rmi.Remote;
import java.rmi.RemoteException;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.rmi.extensions.RemoteRuntimeException;
import weblogic.rmi.extensions.server.RemoteReference;
import weblogic.rmi.extensions.server.RuntimeMethodDescriptor;
import weblogic.rmi.extensions.server.StubReference;
import weblogic.rmi.internal.MethodDescriptor;
import weblogic.rmi.internal.Stub;
import weblogic.rmi.internal.StubInfo;
import weblogic.rmi.internal.StubInfoIntf;
import weblogic.rmi.utils.Utilities;

public final class DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub
extends Stub
implements StubInfoIntf,
DBXMLRowSession,
EJBObject {
    private static /* synthetic */ Class class$glog$server$dbxml$DBXMLRowSession;
    private static RuntimeMethodDescriptor md5;
    private static Method[] m;
    private static RuntimeMethodDescriptor md3;
    private static RuntimeMethodDescriptor md2;
    private final StubInfo stubinfo;
    private static RuntimeMethodDescriptor md0;
    private static RuntimeMethodDescriptor md4;
    private final RemoteReference ror;
    private static RuntimeMethodDescriptor md1;
    private static boolean initialized;

    public DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub(StubInfo stubInfo) {
        super((StubReference)stubInfo);
        this.stubinfo = stubInfo;
        this.ror = this.stubinfo.getRemoteRef();
        DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.ensureInitialized(this.stubinfo);
    }

    public StubInfo getStubInfo() {
        return this.stubinfo;
    }

    private static synchronized void ensureInitialized(StubInfo stubInfo) {
        if (initialized) {
            return;
        }
        m = Utilities.getRemoteRMIMethods((Class[])stubInfo.getInterfaces());
        md0 = new MethodDescriptor(m[0], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[0]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[0]));
        md1 = new MethodDescriptor(m[1], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[1]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[1]));
        md2 = new MethodDescriptor(m[2], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[2]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[2]));
        md3 = new MethodDescriptor(m[3], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[3]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[3]));
        md4 = new MethodDescriptor(m[4], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[4]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[4]));
        md5 = new MethodDescriptor(m[5], class$glog$server$dbxml$DBXMLRowSession == null ? (class$glog$server$dbxml$DBXMLRowSession = DBXMLRowSessionHome_uxhcz5_EOImpl_12130_WLStub.class$("glog.server.dbxml.DBXMLRowSession")) : class$glog$server$dbxml$DBXMLRowSession, false, true, false, false, stubInfo.getTimeOut(m[5]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[5]));
        initialized = true;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public final EJBHome getEJBHome() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (EJBHome)this.ror.invoke((Remote)((Object)this), md0, objectArray, m[0]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final Handle getHandle() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (Handle)this.ror.invoke((Remote)((Object)this), md1, objectArray, m[1]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final Object getPrimaryKey() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return this.ror.invoke((Remote)((Object)this), md2, objectArray, m[2]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final DBXMLImportResponse importRow(DBXMLRow dBXMLRow, DBXMLImportRequest dBXMLImportRequest, DataSource dataSource) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{dBXMLRow, dBXMLImportRequest, dataSource};
            return (DBXMLImportResponse)this.ror.invoke((Remote)((Object)this), md3, objectArray, m[3]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final boolean isIdentical(EJBObject eJBObject) throws RemoteException {
        try {
            Object[] objectArray = new Object[]{eJBObject};
            return (Boolean)this.ror.invoke((Remote)((Object)this), md4, objectArray, m[4]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final void remove() throws RemoteException, RemoveException {
        try {
            Object[] objectArray = new Object[]{};
            this.ror.invoke((Remote)((Object)this), md5, objectArray, m[5]);
            return;
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (RemoveException removeException) {
            throw removeException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }
}
