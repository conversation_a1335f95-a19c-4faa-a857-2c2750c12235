/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.SessionContext
 *  weblogic.ejb.container.interfaces.WLEnterpriseBean
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.CreateException;
import javax.ejb.SessionContext;
import weblogic.ejb.container.interfaces.WLEnterpriseBean;

public interface CILServiceSessionHome_jwvcj5_Intf
extends WLEnterpriseBean {
    public void ejbCreate() throws CreateException;

    public void ejbRemove() throws RemoteException;

    public void ejbActivate() throws RemoteException;

    public SessionContext getSessionContext();

    public List findAllPks(CILServiceParameters var1) throws GLException;

    public CILServiceResult delete(DataObject var1) throws GLException;

    public CILServiceResult delete(CILServiceParameters var1) throws GLException;

    public String produceMetaDataSchema(MetaData var1) throws GLException;

    public CILServiceResult insert(CILServiceParameters var1) throws GLException;

    public CILServiceResult insert(DataObject var1) throws GLException;

    public void setSessionContext(SessionContext var1);

    public DataObject find(CILServiceParameters var1) throws GLException;

    public CILServiceResult update(CILServiceParameters var1) throws GLException;

    public CILServiceResult update(DataObject var1) throws GLException;

    public T2SharedConnection getConnection() throws GLException;

    public List findAll(CILServiceParameters var1) throws GLException;

    public T2SharedConnection getConnectionRemote() throws RemoteException;

    public void ejbPassivate() throws RemoteException;

    public DataObject create(MetaData var1) throws GLException;
}
