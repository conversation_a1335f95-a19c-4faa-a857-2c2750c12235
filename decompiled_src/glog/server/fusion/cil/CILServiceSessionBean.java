/*
 * Decompiled with CFR 0.152.
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceConstants;
import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.fusion.cil.common.sdo.DataObjectFactory;
import glog.fusion.cil.common.sdo.DataObjectFinder;
import glog.fusion.cil.common.sdo.DataObjectPersister;
import glog.fusion.cil.common.sdo.PersistenceResult;
import glog.fusion.cil.server.sdo.MetaDataHelper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.T2SharedConnection;
import glog.util.log.Log;
import glog.util.remote.BaseStatelessBean;
import java.rmi.RemoteException;
import java.util.List;

public class CILServiceSessionBean
extends BaseStatelessBean
implements CILServiceConstants {
    public static final long serialVersionUID = 1L;

    public String produceMetaDataSchema(MetaData ref) throws RemoteException, GLException {
        String result = null;
        try {
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: produceMetaDataSchema: %s", this.getDebugInfo(), ref.toString()));
            }
            result = MetaDataHelper.get().produceXSD(ref);
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS_D.index]) {
                Log.logID(LOG_ALIAS_D, Log.DEBUG, String.format("%s: Schema:\n %s", this.getDebugInfo(), result));
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
        return result;
    }

    public CILServiceResult update(DataObject dataObject) throws GLException {
        DataObjectPersister updater = DataObjectFactory.get().newUpdater(dataObject.metaData());
        return this.persist(updater, dataObject);
    }

    public CILServiceResult insert(DataObject dataObject) throws GLException {
        DataObjectPersister inserter = DataObjectFactory.get().newInserter(dataObject.metaData());
        return this.persist(inserter, dataObject);
    }

    public CILServiceResult delete(DataObject dataObject) throws GLException {
        DataObjectPersister remover = DataObjectFactory.get().newRemover(dataObject.metaData());
        return this.persist(remover, dataObject);
    }

    public DataObject create(MetaData ref) throws GLException {
        try {
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: create: Class=%s", this.getDebugInfo(), ref));
            }
            Object object = DataObjectFactory.get().newDataObject(MetaDataHelper.get().lookupMetaData(ref));
            object.setInserted();
            return object;
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public CILServiceResult update(CILServiceParameters params) throws GLException {
        DataObjectPersister updater = DataObjectFactory.get().newUpdater(params.getMetaData());
        return this.persist(updater, params);
    }

    public CILServiceResult insert(CILServiceParameters params) throws GLException {
        DataObjectPersister inserter = DataObjectFactory.get().newInserter(params.getMetaData());
        return this.persist(inserter, params);
    }

    public CILServiceResult delete(CILServiceParameters params) throws GLException {
        DataObjectPersister remover = DataObjectFactory.get().newRemover(params.getMetaData());
        return this.persist(remover, params);
    }

    private CILServiceResult persist(DataObjectPersister persister, DataObject dataObject) throws GLException {
        CILServiceResult result = new CILServiceResult();
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: %s", this.getDebugInfo(), persister.getName()));
        }
        PersistenceResult presult = persister.persist(dataObject);
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Completed: %s", this.getDebugInfo(), persister.getName()));
        }
        result.addPk(presult.getPk());
        return result;
    }

    private CILServiceResult persist(DataObjectPersister persister, CILServiceParameters params) throws GLException {
        CILServiceResult results = new CILServiceResult();
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: %s", this.getDebugInfo(), persister.getName()));
        }
        if (params.getCriteria() != null && params.getCriteria().getPkList() != null) {
            for (Pk pk : params.getCriteria().getPkList()) {
                Object dataObject = DataObjectFactory.get().newDataObject(params.getMetaData(), pk);
                PersistenceResult presult = persister.persist((DataObject)dataObject);
                results.addPk(presult.getPk());
            }
        } else if (params.getDataObjects() != null) {
            for (DataObject dataObject : params.getDataObjects()) {
                PersistenceResult presult = persister.persist(dataObject);
                results.addPk(presult.getPk());
            }
        } else if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.WARNING, String.format("%s: Persistence requested but no data received", this.getDebugInfo()));
        }
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Completed: %s", this.getDebugInfo(), persister.getName()));
        }
        return results;
    }

    public DataObject find(CILServiceParameters params) throws GLException {
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: find", this.getDebugInfo()));
        }
        MetaData ref = params.getCriteria().getMetaData();
        DataObject object = null;
        t2conn.open();
        try (T2SharedConnection t2conn = this.getConnection();){
            DataObjectFinder finder = DataObjectFactory.get().newFinder(ref);
            List<DataObject> objects = finder.findAll(params.getCriteria(), t2conn);
            if (!objects.isEmpty()) {
                object = objects.get(0);
            } else if (Log.idOn[CILServiceSessionBean.LOG_ALIAS_D.index]) {
                Log.logID(LOG_ALIAS_D, Log.DEBUG, String.format("%s: Found No matching records", this.getDebugInfo()));
            }
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Completed: find", this.getDebugInfo()));
            }
            DataObject dataObject = object;
            return dataObject;
        }
    }

    public List<Pk> findAllPks(CILServiceParameters params) throws GLException {
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: findAllPks", this.getDebugInfo()));
        }
        MetaData ref = params.getCriteria().getMetaData();
        t2conn.open();
        try (T2SharedConnection t2conn = this.getConnection();){
            DataObjectFinder finder = DataObjectFactory.get().newFinder(ref);
            List<Pk> results = finder.findAllPks(params.getCriteria(), t2conn);
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Completed: findAllPks", this.getDebugInfo()));
            }
            List<Pk> list = results;
            return list;
        }
    }

    public List<DataObject> findAll(CILServiceParameters params) throws GLException {
        if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Starting: findAll", this.getDebugInfo()));
        }
        MetaData ref = params.getCriteria().getMetaData();
        t2conn.open();
        try (T2SharedConnection t2conn = this.getConnection();){
            DataObjectFinder finder = DataObjectFactory.get().newFinder(ref);
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS_D.index]) {
                Log.logID(LOG_ALIAS_D, Log.DEBUG, String.format("%s: Finding: criteria:%s", this.getDebugInfo(), params.getCriteria()));
            }
            List<DataObject> results = finder.findAll(params.getCriteria(), t2conn);
            if (Log.idOn[CILServiceSessionBean.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, Log.DEBUG, String.format("%s: Completed: findAll: results=%d records", this.getDebugInfo(), results == null ? 0 : results.size()));
            }
            List<DataObject> list = results;
            return list;
        }
    }

    private String getDebugInfo() {
        return "CILServiceSessionBean";
    }
}
