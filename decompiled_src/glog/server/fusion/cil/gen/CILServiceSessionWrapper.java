/*
 * Decompiled with CFR 0.152.
 */
package glog.server.fusion.cil.gen;

import glog.database.security.crypto.Password;
import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.appserver.AppFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.webserver.wrapper.BaseWrapper;
import java.rmi.RemoteException;
import java.util.List;

public class CILServiceSessionWrapper
extends BaseWrapper {
    private static final String wrappedClassName = "glog.server.fusion.cil.CILServiceSession";
    private static final String[] methodNames = new String[]{"produceMetaDataSchema", "find", "findAll", "findAllPks", "create", "update", "insert", "delete", "update", "insert", "delete"};

    public CILServiceSessionWrapper() {
    }

    public CILServiceSessionWrapper(AppFunction appFunction) {
        super(appFunction);
    }

    public CILServiceSessionWrapper(String machine) {
        super(machine);
    }

    public CILServiceSessionWrapper(String machine, String remoteUser, Password remotePassword) {
        super(machine, remoteUser, remotePassword);
    }

    public CILServiceSessionWrapper(boolean handle) {
        super(handle);
    }

    public CILServiceSessionWrapper(int flags) {
        super(flags);
    }

    public String produceMetaDataSchema(MetaData p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (String)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 1, args);
    }

    public DataObject find(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (DataObject)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 2, args);
    }

    public List<DataObject> findAll(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (List)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 3, args);
    }

    public List<Pk> findAllPks(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (List)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 4, args);
    }

    public DataObject create(MetaData p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (DataObject)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 5, args);
    }

    public CILServiceResult update(DataObject p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 6, args);
    }

    public CILServiceResult insert(DataObject p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 7, args);
    }

    public CILServiceResult delete(DataObject p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 8, args);
    }

    public CILServiceResult update(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 9, args);
    }

    public CILServiceResult insert(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 10, args);
    }

    public CILServiceResult delete(CILServiceParameters p1) throws RemoteException, GLException {
        Object[] args = new Object[]{p1};
        return (CILServiceResult)this.execDispatch("glog.server.fusion.cil.gen.CILServiceSessionSkel", 11, args);
    }

    @Override
    protected String getWrappedClassName() {
        return wrappedClassName;
    }

    @Override
    protected String getMethodName(int methodIndex) {
        return methodNames[methodIndex - 1];
    }
}
