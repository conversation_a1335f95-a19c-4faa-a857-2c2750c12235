/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 *  javax.ejb.RemoveException
 */
package glog.server.fusion.cil.gen;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSession;
import glog.server.fusion.cil.CILServiceSessionHome;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.NamingDirectory;
import glog.webserver.wrapper.SkelImpl;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.FinderException;
import javax.ejb.RemoveException;

public class CILServiceSessionSkel
extends SkelImpl {
    @Override
    public Object exec(int methodIndex, Object[] args) throws GLEx<PERSON>, RemoteException, FinderException {
        switch (methodIndex) {
            case 1: {
                return this.produceMetaDataSchema((MetaData)args[0]);
            }
            case 2: {
                return this.find((CILServiceParameters)args[0]);
            }
            case 3: {
                return this.findAll((CILServiceParameters)args[0]);
            }
            case 4: {
                return this.findAllPks((CILServiceParameters)args[0]);
            }
            case 5: {
                return this.create((MetaData)args[0]);
            }
            case 6: {
                return this.update((DataObject)args[0]);
            }
            case 7: {
                return this.insert((DataObject)args[0]);
            }
            case 8: {
                return this.delete((DataObject)args[0]);
            }
            case 9: {
                return this.update((CILServiceParameters)args[0]);
            }
            case 10: {
                return this.insert((CILServiceParameters)args[0]);
            }
            case 11: {
                return this.delete((CILServiceParameters)args[0]);
            }
        }
        throw GLException.factory(new GLException.CausedBy("cause.QueryHelper.0003", null, new Object[][]{new Object[0]}));
    }

    public String produceMetaDataSchema(MetaData p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            String string = server.produceMetaDataSchema(p1);
            return string;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public DataObject find(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            DataObject dataObject = server.find(p1);
            return dataObject;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public List<DataObject> findAll(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            List<DataObject> list = server.findAll(p1);
            return list;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public List<Pk> findAllPks(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            List<Pk> list = server.findAllPks(p1);
            return list;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public DataObject create(MetaData p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            DataObject dataObject = server.create(p1);
            return dataObject;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult update(DataObject p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.update(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult insert(DataObject p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.insert(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult delete(DataObject p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.delete(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult update(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.update(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult insert(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.insert(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }

    public CILServiceResult delete(CILServiceParameters p1) throws RemoteException, GLException {
        NamingDirectory nd = null;
        CILServiceSession server = null;
        try {
            nd = NamingDirectory.get();
            CILServiceSessionHome home = (CILServiceSessionHome)nd.lookup("CILServiceSessionHome");
            server = home.create();
            CILServiceResult cILServiceResult = server.delete(p1);
            return cILServiceResult;
        }
        catch (Exception ex) {
            throw GLException.factory(ex);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (server != null) {
                try {
                    server.remove();
                }
                catch (RemoveException ignore) {}
            }
        }
    }
}
