/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.SessionContext
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSession;
import glog.server.fusion.cil.CILServiceSessionBean;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.LocalSessionContext;
import glog.util.local.SessionEnterExit;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.SessionContext;

public class CILServiceSessionStub
implements CILServiceSession {
    private SessionContext context;

    public CILServiceSessionStub(EJBHome home) {
        this.context = new LocalSessionContext(home, this);
    }

    @Override
    public String produceMetaDataSchema(MetaData p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "produceMetaDataSchema");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            String string = bean.produceMetaDataSchema(p1);
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public DataObject find(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "find");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            DataObject dataObject = bean.find(p1);
            return dataObject;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public List<DataObject> findAll(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "findAll");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            List<DataObject> list = bean.findAll(p1);
            return list;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public List<Pk> findAllPks(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "findAllPks");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            List<Pk> list = bean.findAllPks(p1);
            return list;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public DataObject create(MetaData p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "create");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            DataObject dataObject = bean.create(p1);
            return dataObject;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult update(DataObject p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "update");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.update(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult insert(DataObject p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "insert");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.insert(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult delete(DataObject p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "delete");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.delete(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult update(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "update");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.update(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult insert(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "insert");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.insert(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public CILServiceResult delete(CILServiceParameters p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("CILServiceSession", "delete");
            CILServiceSessionBean bean = new CILServiceSessionBean();
            bean.setSessionContext(this.context);
            CILServiceResult cILServiceResult = bean.delete(p1);
            return cILServiceResult;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() {
        return null;
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    public void remove() {
    }
}
