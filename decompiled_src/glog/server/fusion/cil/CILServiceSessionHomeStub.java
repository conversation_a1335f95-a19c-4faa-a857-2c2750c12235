/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBMetaData
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 */
package glog.server.fusion.cil;

import glog.server.fusion.cil.CILServiceSession;
import glog.server.fusion.cil.CILServiceSessionHome;
import glog.server.fusion.cil.CILServiceSessionStub;
import javax.ejb.EJBMetaData;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;

public class CILServiceSessionHomeStub
implements CILServiceSessionHome {
    @Override
    public CILServiceSession create() {
        return new CILServiceSessionStub(this);
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }

    public void remove(Object o) {
    }
}
