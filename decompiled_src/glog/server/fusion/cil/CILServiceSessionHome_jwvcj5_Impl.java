/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 *  weblogic.ejb.container.interfaces.EJBCreateInvoker
 *  weblogic.ejb.container.interfaces.WLEnterpriseBean
 */
package glog.server.fusion.cil;

import glog.server.fusion.cil.CILServiceSessionHome_jwvcj5_Intf;
import glog.server.fusion.cil.CILServiceSessionServerSideEJBWrapper;
import glog.util.exception.GLException;
import javax.ejb.EJBContext;
import weblogic.ejb.container.interfaces.EJBCreateInvoker;
import weblogic.ejb.container.interfaces.WLEnterpriseBean;

public class CILServiceSessionHome_jwvcj5_Impl
extends CILServiceSessionServerSideEJBWrapper
implements CILServiceSessionHome_jwvcj5_Intf,
WLEnterpriseBean,
EJBCreateInvoker {
    private int __WL_method_state;
    private EJBContext __WL_EJBContext;

    public int __WL_getMethodState() {
        return this.__WL_method_state;
    }

    public void __WL_setMethodState(int n) {
        this.__WL_method_state = n;
    }

    public EJBContext __WL_getEJBContext() {
        return this.__WL_EJBContext;
    }

    public void __WL_setEJBContext(EJBContext eJBContext) {
        this.__WL_EJBContext = eJBContext;
    }
}
