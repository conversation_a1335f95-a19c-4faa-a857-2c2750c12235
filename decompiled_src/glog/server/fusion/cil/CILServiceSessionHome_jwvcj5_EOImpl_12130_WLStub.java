/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.rmi.extensions.RemoteRuntimeException
 *  weblogic.rmi.extensions.server.RemoteReference
 *  weblogic.rmi.extensions.server.RuntimeMethodDescriptor
 *  weblogic.rmi.extensions.server.StubReference
 *  weblogic.rmi.internal.MethodDescriptor
 *  weblogic.rmi.internal.Stub
 *  weblogic.rmi.internal.StubInfo
 *  weblogic.rmi.internal.StubInfoIntf
 *  weblogic.rmi.utils.Utilities
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSession;
import glog.util.exception.GLException;
import java.lang.reflect.Method;
import java.rmi.Remote;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.rmi.extensions.RemoteRuntimeException;
import weblogic.rmi.extensions.server.RemoteReference;
import weblogic.rmi.extensions.server.RuntimeMethodDescriptor;
import weblogic.rmi.extensions.server.StubReference;
import weblogic.rmi.internal.MethodDescriptor;
import weblogic.rmi.internal.Stub;
import weblogic.rmi.internal.StubInfo;
import weblogic.rmi.internal.StubInfoIntf;
import weblogic.rmi.utils.Utilities;

public final class CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub
extends Stub
implements StubInfoIntf,
CILServiceSession,
EJBObject {
    private static RuntimeMethodDescriptor md6;
    private static RuntimeMethodDescriptor md5;
    private static boolean initialized;
    private static RuntimeMethodDescriptor md3;
    private static RuntimeMethodDescriptor md2;
    private static RuntimeMethodDescriptor md1;
    private static RuntimeMethodDescriptor md0;
    private static RuntimeMethodDescriptor md12;
    private static RuntimeMethodDescriptor md11;
    private static RuntimeMethodDescriptor md10;
    private final RemoteReference ror;
    private static RuntimeMethodDescriptor md14;
    private static /* synthetic */ Class class$glog$server$fusion$cil$CILServiceSession;
    private final StubInfo stubinfo;
    private static RuntimeMethodDescriptor md4;
    private static RuntimeMethodDescriptor md15;
    private static Method[] m;
    private static RuntimeMethodDescriptor md13;
    private static RuntimeMethodDescriptor md9;
    private static RuntimeMethodDescriptor md8;
    private static RuntimeMethodDescriptor md7;

    public CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub(StubInfo stubInfo) {
        super((StubReference)stubInfo);
        this.stubinfo = stubInfo;
        this.ror = this.stubinfo.getRemoteRef();
        CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.ensureInitialized(this.stubinfo);
    }

    public StubInfo getStubInfo() {
        return this.stubinfo;
    }

    private static synchronized void ensureInitialized(StubInfo stubInfo) {
        if (initialized) {
            return;
        }
        m = Utilities.getRemoteRMIMethods((Class[])stubInfo.getInterfaces());
        md0 = new MethodDescriptor(m[0], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[0]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[0]));
        md1 = new MethodDescriptor(m[1], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[1]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[1]));
        md2 = new MethodDescriptor(m[2], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[2]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[2]));
        md3 = new MethodDescriptor(m[3], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[3]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[3]));
        md4 = new MethodDescriptor(m[4], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[4]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[4]));
        md5 = new MethodDescriptor(m[5], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[5]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[5]));
        md6 = new MethodDescriptor(m[6], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[6]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[6]));
        md7 = new MethodDescriptor(m[7], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[7]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[7]));
        md8 = new MethodDescriptor(m[8], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[8]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[8]));
        md9 = new MethodDescriptor(m[9], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[9]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[9]));
        md10 = new MethodDescriptor(m[10], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[10]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[10]));
        md11 = new MethodDescriptor(m[11], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[11]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[11]));
        md12 = new MethodDescriptor(m[12], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[12]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[12]));
        md13 = new MethodDescriptor(m[13], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[13]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[13]));
        md14 = new MethodDescriptor(m[14], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[14]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[14]));
        md15 = new MethodDescriptor(m[15], class$glog$server$fusion$cil$CILServiceSession == null ? (class$glog$server$fusion$cil$CILServiceSession = CILServiceSessionHome_jwvcj5_EOImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSession")) : class$glog$server$fusion$cil$CILServiceSession, false, true, false, false, stubInfo.getTimeOut(m[15]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[15]));
        initialized = true;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public final DataObject create(MetaData metaData) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{metaData};
            return (DataObject)this.ror.invoke((Remote)((Object)this), md0, objectArray, m[0]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult delete(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md1, objectArray, m[1]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult delete(DataObject dataObject) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{dataObject};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md2, objectArray, m[2]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final DataObject find(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (DataObject)this.ror.invoke((Remote)((Object)this), md3, objectArray, m[3]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final List findAll(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (List)this.ror.invoke((Remote)((Object)this), md4, objectArray, m[4]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final List findAllPks(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (List)this.ror.invoke((Remote)((Object)this), md5, objectArray, m[5]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final EJBHome getEJBHome() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (EJBHome)this.ror.invoke((Remote)((Object)this), md6, objectArray, m[6]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final Handle getHandle() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (Handle)this.ror.invoke((Remote)((Object)this), md7, objectArray, m[7]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final Object getPrimaryKey() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return this.ror.invoke((Remote)((Object)this), md8, objectArray, m[8]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult insert(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md9, objectArray, m[9]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult insert(DataObject dataObject) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{dataObject};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md10, objectArray, m[10]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final boolean isIdentical(EJBObject eJBObject) throws RemoteException {
        try {
            Object[] objectArray = new Object[]{eJBObject};
            return (Boolean)this.ror.invoke((Remote)((Object)this), md11, objectArray, m[11]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final String produceMetaDataSchema(MetaData metaData) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{metaData};
            return (String)this.ror.invoke((Remote)((Object)this), md12, objectArray, m[12]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final void remove() throws RemoteException, RemoveException {
        try {
            Object[] objectArray = new Object[]{};
            this.ror.invoke((Remote)((Object)this), md13, objectArray, m[13]);
            return;
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (RemoveException removeException) {
            throw removeException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult update(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{cILServiceParameters};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md14, objectArray, m[14]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceResult update(DataObject dataObject) throws RemoteException, GLException {
        try {
            Object[] objectArray = new Object[]{dataObject};
            return (CILServiceResult)this.ror.invoke((Remote)((Object)this), md15, objectArray, m[15]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (GLException gLException) {
            throw gLException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }
}
