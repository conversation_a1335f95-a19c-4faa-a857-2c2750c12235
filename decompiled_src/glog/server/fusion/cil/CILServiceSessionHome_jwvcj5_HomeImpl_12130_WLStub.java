/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 *  weblogic.ejb20.interfaces.RemoteHome
 *  weblogic.rmi.extensions.RemoteRuntimeException
 *  weblogic.rmi.extensions.server.RemoteReference
 *  weblogic.rmi.extensions.server.RuntimeMethodDescriptor
 *  weblogic.rmi.extensions.server.StubReference
 *  weblogic.rmi.internal.MethodDescriptor
 *  weblogic.rmi.internal.Stub
 *  weblogic.rmi.internal.StubInfo
 *  weblogic.rmi.internal.StubInfoIntf
 *  weblogic.rmi.utils.Utilities
 */
package glog.server.fusion.cil;

import glog.server.fusion.cil.CILServiceSession;
import glog.server.fusion.cil.CILServiceSessionHome;
import java.lang.reflect.Method;
import java.rmi.Remote;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;
import weblogic.ejb20.interfaces.RemoteHome;
import weblogic.rmi.extensions.RemoteRuntimeException;
import weblogic.rmi.extensions.server.RemoteReference;
import weblogic.rmi.extensions.server.RuntimeMethodDescriptor;
import weblogic.rmi.extensions.server.StubReference;
import weblogic.rmi.internal.MethodDescriptor;
import weblogic.rmi.internal.Stub;
import weblogic.rmi.internal.StubInfo;
import weblogic.rmi.internal.StubInfoIntf;
import weblogic.rmi.utils.Utilities;

public final class CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub
extends Stub
implements StubInfoIntf,
CILServiceSessionHome,
EJBHome,
RemoteHome {
    private static RuntimeMethodDescriptor md6;
    private static RuntimeMethodDescriptor md5;
    private static boolean initialized;
    private static RuntimeMethodDescriptor md3;
    private static RuntimeMethodDescriptor md2;
    private static RuntimeMethodDescriptor md1;
    private static RuntimeMethodDescriptor md0;
    private final RemoteReference ror;
    private static /* synthetic */ Class class$glog$server$fusion$cil$CILServiceSessionHome;
    private final StubInfo stubinfo;
    private static RuntimeMethodDescriptor md4;
    private static Method[] m;
    private static RuntimeMethodDescriptor md9;
    private static RuntimeMethodDescriptor md8;
    private static RuntimeMethodDescriptor md7;

    public CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub(StubInfo stubInfo) {
        super((StubReference)stubInfo);
        this.stubinfo = stubInfo;
        this.ror = this.stubinfo.getRemoteRef();
        CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.ensureInitialized(this.stubinfo);
    }

    public StubInfo getStubInfo() {
        return this.stubinfo;
    }

    private static synchronized void ensureInitialized(StubInfo stubInfo) {
        if (initialized) {
            return;
        }
        m = Utilities.getRemoteRMIMethods((Class[])stubInfo.getInterfaces());
        md0 = new MethodDescriptor(m[0], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[0]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[0]));
        md1 = new MethodDescriptor(m[1], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[1]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[1]));
        md2 = new MethodDescriptor(m[2], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[2]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[2]));
        md3 = new MethodDescriptor(m[3], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[3]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[3]));
        md4 = new MethodDescriptor(m[4], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[4]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[4]));
        md5 = new MethodDescriptor(m[5], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[5]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[5]));
        md6 = new MethodDescriptor(m[6], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[6]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[6]));
        md7 = new MethodDescriptor(m[7], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[7]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[7]));
        md8 = new MethodDescriptor(m[8], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[8]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[8]));
        md9 = new MethodDescriptor(m[9], class$glog$server$fusion$cil$CILServiceSessionHome == null ? (class$glog$server$fusion$cil$CILServiceSessionHome = CILServiceSessionHome_jwvcj5_HomeImpl_12130_WLStub.class$("glog.server.fusion.cil.CILServiceSessionHome")) : class$glog$server$fusion$cil$CILServiceSessionHome, false, true, false, true, stubInfo.getTimeOut(m[9]), stubInfo.getRemoteRef().getObjectID(), false, stubInfo.getRemoteExceptionWrapperClassName(m[9]));
        initialized = true;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public final EJBObject allocateEJBObject() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (EJBObject)this.ror.invoke((Remote)((Object)this), md0, objectArray, m[0]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final EJBObject allocateEJBObject(Object object) throws RemoteException {
        try {
            Object[] objectArray = new Object[]{object};
            return (EJBObject)this.ror.invoke((Remote)((Object)this), md1, objectArray, m[1]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final CILServiceSession create() throws CreateException, RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (CILServiceSession)this.ror.invoke((Remote)((Object)this), md2, objectArray, m[2]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (CreateException createException) {
            throw createException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final EJBMetaData getEJBMetaData() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (EJBMetaData)this.ror.invoke((Remote)((Object)this), md3, objectArray, m[3]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final HomeHandle getHomeHandle() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (HomeHandle)this.ror.invoke((Remote)((Object)this), md4, objectArray, m[4]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final String getIsIdenticalKey() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (String)this.ror.invoke((Remote)((Object)this), md5, objectArray, m[5]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final void remove(Object object) throws RemoteException, RemoveException {
        try {
            Object[] objectArray = new Object[]{object};
            this.ror.invoke((Remote)((Object)this), md6, objectArray, m[6]);
            return;
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (RemoveException removeException) {
            throw removeException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final void remove(Handle handle) throws RemoteException, RemoveException {
        try {
            Object[] objectArray = new Object[]{handle};
            this.ror.invoke((Remote)((Object)this), md7, objectArray, m[7]);
            return;
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (RemoveException removeException) {
            throw removeException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final void undeploy() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            this.ror.invoke((Remote)((Object)this), md8, objectArray, m[8]);
            return;
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }

    public final boolean usesBeanManagedTx() throws RemoteException {
        try {
            Object[] objectArray = new Object[]{};
            return (Boolean)this.ror.invoke((Remote)((Object)this), md9, objectArray, m[9]);
        }
        catch (Error error) {
            throw error;
        }
        catch (RuntimeException runtimeException) {
            throw runtimeException;
        }
        catch (RemoteException remoteException) {
            throw remoteException;
        }
        catch (Throwable throwable) {
            throw new RemoteRuntimeException("Unexpected Exception", throwable);
        }
    }
}
