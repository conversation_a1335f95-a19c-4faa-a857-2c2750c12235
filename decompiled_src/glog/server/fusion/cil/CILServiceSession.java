/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.EJBObject;

public interface CILServiceSession
extends EJBObject {
    public String produceMetaDataSchema(MetaData var1) throws RemoteException, GLException;

    public DataObject find(CILServiceParameters var1) throws RemoteException, GLException;

    public List<DataObject> findAll(CILServiceParameters var1) throws RemoteException, GLException;

    public List<Pk> findAllPks(CILServiceParameters var1) throws RemoteException, GLException;

    public DataObject create(MetaData var1) throws RemoteException, GLException;

    public CILServiceResult update(DataObject var1) throws RemoteException, GLException;

    public CILServiceResult insert(DataObject var1) throws RemoteException, GLException;

    public CILServiceResult delete(DataObject var1) throws RemoteException, GLException;

    public CILServiceResult update(CILServiceParameters var1) throws RemoteException, GLException;

    public CILServiceResult insert(CILServiceParameters var1) throws RemoteException, GLException;

    public CILServiceResult delete(CILServiceParameters var1) throws RemoteException, GLException;
}
