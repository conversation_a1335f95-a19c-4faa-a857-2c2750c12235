/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  weblogic.rmi.internal.Skeleton
 *  weblogic.rmi.spi.InboundRequest
 *  weblogic.rmi.spi.MsgInput
 *  weblogic.rmi.spi.OutboundResponse
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSession;
import glog.util.jdbc.Pk;
import java.io.IOException;
import java.rmi.MarshalException;
import java.rmi.UnmarshalException;
import java.util.List;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import weblogic.rmi.internal.Skeleton;
import weblogic.rmi.spi.InboundRequest;
import weblogic.rmi.spi.MsgInput;
import weblogic.rmi.spi.OutboundResponse;

public final class CILServiceSessionHome_jwvcj5_EOImpl_WLSkel
extends Skeleton {
    private static /* synthetic */ Class class$java$lang$Object;
    private static /* synthetic */ Class class$java$util$List;
    private static /* synthetic */ Class class$glog$fusion$cil$CILServiceParameters;
    private static /* synthetic */ Class class$javax$ejb$EJBHome;
    private static /* synthetic */ Class class$glog$fusion$cil$MetaData;
    private static /* synthetic */ Class class$javax$ejb$Handle;
    private static /* synthetic */ Class class$glog$fusion$cil$CILServiceResult;
    private static /* synthetic */ Class class$glog$fusion$cil$DataObject;
    private static /* synthetic */ Class class$java$lang$String;
    private static /* synthetic */ Class class$javax$ejb$EJBObject;

    public OutboundResponse invoke(int n, InboundRequest inboundRequest, OutboundResponse outboundResponse, Object object) throws Exception {
        switch (n) {
            case 0: {
                MetaData metaData;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    metaData = (MetaData)msgInput.readObject(class$glog$fusion$cil$MetaData == null ? (class$glog$fusion$cil$MetaData = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.MetaData")) : class$glog$fusion$cil$MetaData);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                DataObject dataObject = ((CILServiceSession)object).create(metaData);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)dataObject, class$glog$fusion$cil$DataObject == null ? (class$glog$fusion$cil$DataObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.DataObject")) : class$glog$fusion$cil$DataObject);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 1: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).delete(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 2: {
                DataObject dataObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    dataObject = (DataObject)msgInput.readObject(class$glog$fusion$cil$DataObject == null ? (class$glog$fusion$cil$DataObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.DataObject")) : class$glog$fusion$cil$DataObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).delete(dataObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 3: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                DataObject dataObject = ((CILServiceSession)object).find(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)dataObject, class$glog$fusion$cil$DataObject == null ? (class$glog$fusion$cil$DataObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.DataObject")) : class$glog$fusion$cil$DataObject);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 4: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                List<DataObject> list = ((CILServiceSession)object).findAll(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(list, class$java$util$List == null ? (class$java$util$List = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("java.util.List")) : class$java$util$List);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 5: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                List<Pk> list = ((CILServiceSession)object).findAllPks(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(list, class$java$util$List == null ? (class$java$util$List = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("java.util.List")) : class$java$util$List);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 6: {
                EJBHome eJBHome = ((EJBObject)object).getEJBHome();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBHome, class$javax$ejb$EJBHome == null ? (class$javax$ejb$EJBHome = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("javax.ejb.EJBHome")) : class$javax$ejb$EJBHome);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 7: {
                Handle handle = ((EJBObject)object).getHandle();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)handle, class$javax$ejb$Handle == null ? (class$javax$ejb$Handle = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("javax.ejb.Handle")) : class$javax$ejb$Handle);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 8: {
                Object object2 = ((EJBObject)object).getPrimaryKey();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(object2, class$java$lang$Object == null ? (class$java$lang$Object = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("java.lang.Object")) : class$java$lang$Object);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 9: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).insert(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 10: {
                DataObject dataObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    dataObject = (DataObject)msgInput.readObject(class$glog$fusion$cil$DataObject == null ? (class$glog$fusion$cil$DataObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.DataObject")) : class$glog$fusion$cil$DataObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).insert(dataObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 11: {
                EJBObject eJBObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    eJBObject = (EJBObject)msgInput.readObject(class$javax$ejb$EJBObject == null ? (class$javax$ejb$EJBObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("javax.ejb.EJBObject")) : class$javax$ejb$EJBObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                boolean bl = ((EJBObject)object).isIdentical(eJBObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeBoolean(bl);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 12: {
                MetaData metaData;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    metaData = (MetaData)msgInput.readObject(class$glog$fusion$cil$MetaData == null ? (class$glog$fusion$cil$MetaData = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.MetaData")) : class$glog$fusion$cil$MetaData);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                String string = ((CILServiceSession)object).produceMetaDataSchema(metaData);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)string, class$java$lang$String == null ? (class$java$lang$String = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 13: {
                ((EJBObject)object).remove();
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            case 14: {
                CILServiceParameters cILServiceParameters;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    cILServiceParameters = (CILServiceParameters)msgInput.readObject(class$glog$fusion$cil$CILServiceParameters == null ? (class$glog$fusion$cil$CILServiceParameters = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceParameters")) : class$glog$fusion$cil$CILServiceParameters);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).update(cILServiceParameters);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 15: {
                DataObject dataObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    dataObject = (DataObject)msgInput.readObject(class$glog$fusion$cil$DataObject == null ? (class$glog$fusion$cil$DataObject = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.DataObject")) : class$glog$fusion$cil$DataObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                CILServiceResult cILServiceResult = ((CILServiceSession)object).update(dataObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)cILServiceResult, class$glog$fusion$cil$CILServiceResult == null ? (class$glog$fusion$cil$CILServiceResult = CILServiceSessionHome_jwvcj5_EOImpl_WLSkel.class$("glog.fusion.cil.CILServiceResult")) : class$glog$fusion$cil$CILServiceResult);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            default: {
                throw new UnmarshalException("Method identifier [" + n + "] out of range");
            }
        }
        return outboundResponse;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public Object invoke(int n, Object[] objectArray, Object object) throws Exception {
        switch (n) {
            case 0: {
                return ((CILServiceSession)object).create((MetaData)objectArray[0]);
            }
            case 1: {
                return ((CILServiceSession)object).delete((CILServiceParameters)objectArray[0]);
            }
            case 2: {
                return ((CILServiceSession)object).delete((DataObject)objectArray[0]);
            }
            case 3: {
                return ((CILServiceSession)object).find((CILServiceParameters)objectArray[0]);
            }
            case 4: {
                return ((CILServiceSession)object).findAll((CILServiceParameters)objectArray[0]);
            }
            case 5: {
                return ((CILServiceSession)object).findAllPks((CILServiceParameters)objectArray[0]);
            }
            case 6: {
                return ((EJBObject)object).getEJBHome();
            }
            case 7: {
                return ((EJBObject)object).getHandle();
            }
            case 8: {
                return ((EJBObject)object).getPrimaryKey();
            }
            case 9: {
                return ((CILServiceSession)object).insert((CILServiceParameters)objectArray[0]);
            }
            case 10: {
                return ((CILServiceSession)object).insert((DataObject)objectArray[0]);
            }
            case 11: {
                return new Boolean(((EJBObject)object).isIdentical((EJBObject)objectArray[0]));
            }
            case 12: {
                return ((CILServiceSession)object).produceMetaDataSchema((MetaData)objectArray[0]);
            }
            case 13: {
                ((EJBObject)object).remove();
                return null;
            }
            case 14: {
                return ((CILServiceSession)object).update((CILServiceParameters)objectArray[0]);
            }
            case 15: {
                return ((CILServiceSession)object).update((DataObject)objectArray[0]);
            }
        }
        throw new UnmarshalException("Method identifier [" + n + "] out of range");
    }
}
