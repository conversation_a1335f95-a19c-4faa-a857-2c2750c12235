/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSessionBean;
import glog.server.sessionperf.SessionPerf;
import glog.util.NTierResponse;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.NamingDirectory;
import java.util.List;
import javax.ejb.EJBContext;

public class CILServiceSessionServerSideEJBWrapper
extends CILServiceSessionBean {
    @Override
    public String produceMetaDataSchema(MetaData p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.produceMetaDataSchema")) != null ? System.currentTimeMillis() : 0L;
        try {
            String string = super.produceMetaDataSchema(p1);
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public DataObject find(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.find")) != null ? System.currentTimeMillis() : 0L;
        try {
            DataObject dataObject = super.find(p1);
            return dataObject;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public List<DataObject> findAll(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.findAll")) != null ? System.currentTimeMillis() : 0L;
        try {
            List<DataObject> list = super.findAll(p1);
            return list;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public List<Pk> findAllPks(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.findAllPks")) != null ? System.currentTimeMillis() : 0L;
        try {
            List<Pk> list = super.findAllPks(p1);
            return list;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public DataObject create(MetaData p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.create")) != null ? System.currentTimeMillis() : 0L;
        try {
            DataObject dataObject = super.create(p1);
            return dataObject;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult update(DataObject p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.update")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.update(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult insert(DataObject p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.insert")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.insert(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult delete(DataObject p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.delete")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.delete(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult update(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.update")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.update(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult insert(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.insert")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.insert(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public CILServiceResult delete(CILServiceParameters p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("CILServiceSession.delete")) != null ? System.currentTimeMillis() : 0L;
        try {
            CILServiceResult cILServiceResult = super.delete(p1);
            return cILServiceResult;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }
}
