/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.EJBObject
 *  weblogic.ejb.container.interfaces.Invokable
 *  weblogic.ejb.container.internal.BaseRemoteObject
 *  weblogic.ejb.container.internal.InvocationWrapper
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.SessionRemoteMethodInvoker
 *  weblogic.ejb.container.internal.StatelessEJBObject
 */
package glog.server.fusion.cil;

import glog.fusion.cil.CILServiceParameters;
import glog.fusion.cil.CILServiceResult;
import glog.fusion.cil.DataObject;
import glog.fusion.cil.MetaData;
import glog.server.fusion.cil.CILServiceSession;
import glog.server.fusion.cil.CILServiceSessionHome_jwvcj5_Intf;
import glog.util.exception.GLException;
import java.rmi.RemoteException;
import java.util.List;
import javax.ejb.EJBHome;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.ejb.EJBObject;
import weblogic.ejb.container.interfaces.Invokable;
import weblogic.ejb.container.internal.BaseRemoteObject;
import weblogic.ejb.container.internal.InvocationWrapper;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.SessionRemoteMethodInvoker;
import weblogic.ejb.container.internal.StatelessEJBObject;

public final class CILServiceSessionHome_jwvcj5_EOImpl
extends StatelessEJBObject
implements CILServiceSession,
EJBObject,
Invokable {
    public static MethodDescriptor md_eo_update_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_update_glog_fusion_cil_DataObject;
    public static MethodDescriptor md_eo_findAllPks_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_findAll_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_find_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_delete_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_delete_glog_fusion_cil_DataObject;
    public static MethodDescriptor md_eo_insert_glog_fusion_cil_CILServiceParameters;
    public static MethodDescriptor md_eo_insert_glog_fusion_cil_DataObject;
    public static MethodDescriptor md_eo_produceMetaDataSchema_glog_fusion_cil_MetaData;
    public static MethodDescriptor md_eo_create_glog_fusion_cil_MetaData;
    public static MethodDescriptor md_eo_remove;
    public static MethodDescriptor md_eo_getEJBHome;
    public static MethodDescriptor md_eo_getHandle;
    public static MethodDescriptor md_eo_getPrimaryKey;
    public static MethodDescriptor md_eo_isIdentical_javax_ejb_EJBObject;

    public String produceMetaDataSchema(MetaData metaData) throws RemoteException, GLException {
        return (String)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_produceMetaDataSchema_glog_fusion_cil_MetaData), (Object[])new Object[]{metaData}, (int)0);
    }

    public List findAllPks(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (List)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_findAllPks_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)1);
    }

    public List findAll(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (List)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_findAll_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)2);
    }

    public CILServiceResult update(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_update_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)3);
    }

    public CILServiceResult delete(DataObject dataObject) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_delete_glog_fusion_cil_DataObject), (Object[])new Object[]{dataObject}, (int)4);
    }

    public DataObject create(MetaData metaData) throws RemoteException, GLException {
        return (DataObject)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_create_glog_fusion_cil_MetaData), (Object[])new Object[]{metaData}, (int)5);
    }

    public CILServiceResult insert(DataObject dataObject) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_insert_glog_fusion_cil_DataObject), (Object[])new Object[]{dataObject}, (int)6);
    }

    public CILServiceResult delete(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_delete_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)7);
    }

    public DataObject find(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (DataObject)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_find_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)8);
    }

    public CILServiceResult update(DataObject dataObject) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_update_glog_fusion_cil_DataObject), (Object[])new Object[]{dataObject}, (int)9);
    }

    public CILServiceResult insert(CILServiceParameters cILServiceParameters) throws RemoteException, GLException {
        return (CILServiceResult)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_insert_glog_fusion_cil_CILServiceParameters), (Object[])new Object[]{cILServiceParameters}, (int)10);
    }

    public Object __WL_invoke(Object object, Object[] objectArray, int n) throws Throwable {
        switch (n) {
            case 0: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).produceMetaDataSchema((MetaData)objectArray[0]);
            }
            case 1: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).findAllPks((CILServiceParameters)objectArray[0]);
            }
            case 2: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).findAll((CILServiceParameters)objectArray[0]);
            }
            case 3: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).update((CILServiceParameters)objectArray[0]);
            }
            case 4: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).delete((DataObject)objectArray[0]);
            }
            case 5: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).create((MetaData)objectArray[0]);
            }
            case 6: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).insert((DataObject)objectArray[0]);
            }
            case 7: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).delete((CILServiceParameters)objectArray[0]);
            }
            case 8: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).find((CILServiceParameters)objectArray[0]);
            }
            case 9: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).update((DataObject)objectArray[0]);
            }
            case 10: {
                return ((CILServiceSessionHome_jwvcj5_Intf)object).insert((CILServiceParameters)objectArray[0]);
            }
        }
        throw new IllegalArgumentException("No method found for index : " + n);
    }

    public void __WL_handleException(int n, Throwable throwable) throws Throwable {
        switch (n) {
            case 0: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 1: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 2: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 3: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 4: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 5: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 6: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 7: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 8: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 9: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 10: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            default: {
                throw new IllegalArgumentException("No method found for index : " + n);
            }
        }
    }

    public void remove() throws RemoveException, RemoteException {
        super.remove(md_eo_remove);
    }

    public EJBHome getEJBHome() throws RemoteException {
        return super.getEJBHome(md_eo_getEJBHome);
    }

    public Handle getHandle() throws RemoteException {
        return super.getHandle(md_eo_getHandle);
    }

    public Object getPrimaryKey() throws RemoteException {
        return super.getPrimaryKey(md_eo_getPrimaryKey);
    }

    public boolean isIdentical(javax.ejb.EJBObject eJBObject) throws RemoteException {
        return super.isIdentical(md_eo_isIdentical_javax_ejb_EJBObject, eJBObject);
    }

    public void operationsComplete() {
    }
}
