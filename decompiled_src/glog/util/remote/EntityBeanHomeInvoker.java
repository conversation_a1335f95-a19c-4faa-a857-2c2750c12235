/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.Functions;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.LocalStubFactory;
import glog.util.remote.BeanBaseInvoker;
import glog.util.remote.BeanCreator;
import glog.util.remote.BeanData;
import glog.util.remote.EntityFinder;
import glog.util.remote.LockData;
import glog.util.remote.NamingDirectory;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Vector;

public class EntityBeanHomeInvoker
extends BeanBaseInvoker {
    private Method create;
    private Method findByPrimaryKey;
    private Method findAll;
    private Method findByPrimaryKeys;
    private Method findInCache;
    private Method onCreate;
    private Method onUpdate;
    private Method onRemove;
    private Method listBeansInCache;
    private Method listLocks;
    private Method unlock;
    private Method getDataNoLock;
    private Method getLockData;
    private Method getMaxCacheSize;
    private Method updateMaxCacheSize;
    private Hashtable finderInvokers = new Hashtable();
    private Hashtable creatorInvokers = new Hashtable();
    private String beanName;
    private Object beanHome;
    private Object localBeanHome;
    private Class beanDataClass;
    private Class beanPkClass;
    private BeanCreator[] creators;
    private EntityFinder[] finders;
    private NamingDirectory namingDirectory;
    static HashMap entityBeanMethodsCache = new HashMap();

    public EntityBeanHomeInvoker(String beanName, Class beanDataClass, Class beanPkClass, BeanCreator[] creators, EntityFinder[] finders) throws GLException {
        this.init(beanName, beanDataClass, beanPkClass, creators, finders);
    }

    public EntityBeanHomeInvoker(Object remoteBeanHome, Class beanDataClass, Class beanPkClass, BeanCreator[] creators, EntityFinder[] finders) {
        this.init(remoteBeanHome, beanDataClass, beanPkClass, creators, finders);
        this.beanHome = remoteBeanHome;
    }

    public EntityBeanHomeInvoker(String beanName, int dummy) throws GLException {
        this.init(beanName);
    }

    public EntityBeanHomeInvoker(String beanName) throws GLException {
        this.init(beanName);
    }

    private void init(String beanName) throws GLException {
        this.beanName = beanName;
        this.namingDirectory = NamingDirectory.get();
    }

    private void init(String beanName, Class beanDataClass, Class beanPkClass, BeanCreator[] creators, EntityFinder[] finders) throws GLException {
        this.init(beanName);
        this.beanDataClass = beanDataClass;
        this.beanPkClass = beanPkClass;
        this.creators = creators;
        this.finders = finders;
        this.namingDirectory = NamingDirectory.get();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private Object getBeanHome() throws GLException {
        if (this.beanHome == null) {
            EntityBeanHomeInvoker entityBeanHomeInvoker = this;
            synchronized (entityBeanHomeInvoker) {
                if (this.beanHome == null) {
                    try {
                        Object beanHome = this.namingDirectory.lookup(this.beanName);
                        Class<?> beanHomeClass = beanHome.getClass();
                        if (this.beanDataClass == null) {
                            this.beanDataClass = Class.forName((String)beanHomeClass.getField("dataClass").get(null));
                        }
                        if (this.beanPkClass == null) {
                            this.beanPkClass = Class.forName((String)beanHomeClass.getField("primaryKeyClass").get(null));
                        }
                        this.init(beanHome, this.beanDataClass, this.beanPkClass, this.creators, this.finders);
                        this.beanHome = beanHome;
                    }
                    catch (Throwable t) {
                        throw GLException.factory(t);
                    }
                }
            }
        }
        return this.beanHome;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private Object getLocalBeanHome() throws GLException {
        if (this.localBeanHome == null) {
            EntityBeanHomeInvoker entityBeanHomeInvoker = this;
            synchronized (entityBeanHomeInvoker) {
                if (this.localBeanHome == null) {
                    try {
                        Object localBeanHome = LocalStubFactory.get().getHome(this.beanName);
                        Class<?> beanHomeClass = localBeanHome.getClass();
                        this.beanDataClass = Class.forName((String)beanHomeClass.getField("dataClass").get(null));
                        this.beanPkClass = Class.forName((String)beanHomeClass.getField("primaryKeyClass").get(null));
                        this.localBeanHome = localBeanHome;
                    }
                    catch (Throwable t) {
                        throw GLException.factory(t);
                    }
                }
            }
        }
        return this.localBeanHome;
    }

    public void release() {
        if (this.namingDirectory != null) {
            this.namingDirectory.release();
        }
    }

    public Object create(BeanData data) throws GLException {
        this.lookupRemote();
        return this.invoke(this.create, this.beanHome, new Object[]{data});
    }

    public Object create(BeanCreator creator, Object[] args) throws GLException {
        this.lookupRemote();
        Method creatorInvoker = (Method)this.creatorInvokers.get(creator);
        return this.invoke(creatorInvoker, this.beanHome, args);
    }

    public Object findByPrimaryKey(Pk pk) throws GLException {
        this.lookupRemote();
        return this.invoke(this.findByPrimaryKey, this.beanHome, new Object[]{pk});
    }

    public Enumeration findAll() throws GLException {
        this.lookupRemote();
        return (Enumeration)this.invoke(this.findAll, this.beanHome, Functions.EMPTY_OBJECT_ARRAY);
    }

    public Enumeration findByPrimaryKeys(Vector pks) throws GLException {
        this.lookupRemote();
        return (Enumeration)this.invoke(this.findByPrimaryKeys, this.beanHome, new Object[]{pks});
    }

    public Enumeration find(EntityFinder finder, Object[] args) throws GLException {
        this.lookupRemote();
        Method finderInvoker = (Method)this.finderInvokers.get(finder);
        return (Enumeration)this.invoke(finderInvoker, this.beanHome, args);
    }

    public Enumeration findInCache(Pk pk) throws GLException {
        this.lookupRemote();
        return (Enumeration)this.invoke(this.findInCache, this.beanHome, new Object[]{pk});
    }

    public void onCreate(Pk pk, BeanData data) throws GLException {
        this.lookupRemote();
        this.invoke(this.onCreate, this.beanHome, new Object[]{pk, data});
    }

    public void onUpdate(Pk pk, BeanData data) throws GLException {
        this.lookupRemote();
        this.invoke(this.onUpdate, this.beanHome, new Object[]{pk, data});
    }

    public void onRemove(Pk pk, BeanData data) throws GLException {
        this.lookupRemote();
        this.invoke(this.onRemove, this.beanHome, new Object[]{pk, data});
    }

    public Collection listBeansInCache() throws GLException {
        this.lookupRemote();
        return (Collection)this.invoke(this.listBeansInCache, this.beanHome, Functions.EMPTY_OBJECT_ARRAY);
    }

    public Collection listLocks() throws GLException {
        this.lookupRemote();
        return (Collection)this.invoke(this.listLocks, this.beanHome, Functions.EMPTY_OBJECT_ARRAY);
    }

    public void unlock(Pk pk) throws GLException {
        this.lookupRemote();
        this.invoke(this.unlock, this.beanHome, new Object[]{pk});
    }

    public BeanData getDataNoLock(Pk pk) throws GLException {
        this.lookupRemote();
        return (BeanData)this.invoke(this.getDataNoLock, this.beanHome, new Object[]{pk});
    }

    public LockData getLockData(Pk pk) throws GLException {
        this.lookupRemote();
        return (LockData)this.invoke(this.getLockData, this.beanHome, new Object[]{pk});
    }

    public int getMaxCacheSize() throws GLException {
        this.lookupRemote();
        return (Integer)this.invoke(this.getMaxCacheSize, this.beanHome, Functions.EMPTY_OBJECT_ARRAY);
    }

    public void updateMaxCacheSize(int maxBeansInCache) throws GLException {
        this.lookupRemote();
        this.invoke(this.updateMaxCacheSize, this.beanHome, new Object[]{new Integer(maxBeansInCache)});
    }

    public BeanData newBeanData() throws GLException {
        try {
            this.lookup();
            return (BeanData)this.beanDataClass.newInstance();
        }
        catch (Throwable ex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.BeanDataCreation_Failure", null), ex);
        }
    }

    private void lookupRemote() throws GLException {
        this.getBeanHome();
    }

    private void lookupLocal() throws GLException {
        this.getLocalBeanHome();
    }

    private void lookup() throws GLException {
        if (this.beanHome == null && this.localBeanHome == null) {
            this.lookupLocal();
        }
    }

    public final Class getBeanDataClass() {
        try {
            this.lookup();
            return this.beanDataClass;
        }
        catch (Throwable t) {
            GLException.factory(t);
            return null;
        }
    }

    public final Class getBeanPkClass() {
        try {
            this.lookup();
            return this.beanPkClass;
        }
        catch (Throwable t) {
            GLException.factory(t);
            return null;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void init(Object beanHome, Class beanDataClass, Class beanPkClass, BeanCreator[] creators, EntityFinder[] finders) {
        this.beanDataClass = beanDataClass;
        this.beanPkClass = beanPkClass;
        Class<?> beanClass = beanHome.getClass();
        String beanClassName = beanClass.getName();
        EntityBeanMethods ebms = null;
        ebms = (EntityBeanMethods)entityBeanMethodsCache.get(beanClassName);
        boolean initialized = false;
        if (ebms == null) {
            HashMap hashMap = entityBeanMethodsCache;
            synchronized (hashMap) {
                ebms = (EntityBeanMethods)entityBeanMethodsCache.get(beanClassName);
                if (ebms == null) {
                    this.initEntityMethods(beanClass);
                    initialized = true;
                }
            }
        }
        if (!initialized) {
            this.create = ebms.create;
            this.findByPrimaryKey = ebms.findByPrimaryKey;
            this.findAll = ebms.findAll;
            this.findByPrimaryKeys = ebms.findByPrimaryKeys;
            this.findInCache = ebms.findInCache;
            this.onCreate = ebms.onCreate;
            this.onUpdate = ebms.onUpdate;
            this.onRemove = ebms.onRemove;
            this.listBeansInCache = ebms.listBeansInCache;
            this.listLocks = ebms.listLocks;
            this.unlock = ebms.unlock;
            this.getDataNoLock = ebms.getDataNoLock;
            this.getLockData = ebms.getLockData;
            this.getMaxCacheSize = ebms.getMaxCacheSize;
            this.updateMaxCacheSize = ebms.updateMaxCacheSize;
        }
        try {
            if (creators != null) {
                for (int i = 0; i < creators.length; ++i) {
                    this.creatorInvokers.put(creators[i], beanClass.getMethod("create", creators[i].getClasses()));
                }
            }
            if (finders != null) {
                for (int i = 0; i < finders.length; ++i) {
                    this.finderInvokers.put(finders[i], beanClass.getMethod(finders[i].getName(), finders[i].getClasses()));
                }
            }
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function: " + beanClassName);
        }
    }

    private void initEntityMethods(Class beanClass) {
        try {
            this.create = beanClass.getMethod("create", this.beanDataClass);
            this.findByPrimaryKey = beanClass.getMethod("findByPrimaryKey", this.beanPkClass);
            this.findAll = beanClass.getMethod("findAll", Functions.EMPTY_CLASS_ARRAY);
            this.findByPrimaryKeys = beanClass.getMethod("findByPrimaryKeys", Vector.class);
            this.findInCache = beanClass.getMethod("findInCache", this.beanPkClass);
            this.onCreate = beanClass.getMethod("onCreate", this.beanPkClass, this.beanDataClass);
            this.onUpdate = beanClass.getMethod("onUpdate", this.beanPkClass, this.beanDataClass);
            this.onRemove = beanClass.getMethod("onRemove", this.beanPkClass, this.beanDataClass);
            this.listBeansInCache = beanClass.getMethod("listBeansInCache", Functions.EMPTY_CLASS_ARRAY);
            this.listLocks = beanClass.getMethod("listLocks", Functions.EMPTY_CLASS_ARRAY);
            this.unlock = beanClass.getMethod("unlock", Pk.class);
            this.getDataNoLock = beanClass.getMethod("getDataNoLock", Pk.class);
            this.getLockData = beanClass.getMethod("getLockData", Pk.class);
            this.getMaxCacheSize = beanClass.getMethod("getMaxCacheSize", Functions.EMPTY_CLASS_ARRAY);
            this.updateMaxCacheSize = beanClass.getMethod("updateMaxCacheSize", Integer.TYPE);
            EntityBeanMethods ebms = null;
            ebms = new EntityBeanMethods(this.create, this.findByPrimaryKey, this.findAll, this.findByPrimaryKeys, this.findInCache, this.onCreate, this.onUpdate, this.onRemove, this.listBeansInCache, this.listLocks, this.unlock, this.getDataNoLock, this.getLockData, this.getMaxCacheSize, this.updateMaxCacheSize);
            entityBeanMethodsCache.put(beanClass.getName(), ebms);
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function");
        }
    }

    static class EntityBeanMethods {
        public Method create;
        public Method findByPrimaryKey;
        public Method findAll;
        public Method findByPrimaryKeys;
        public Method findInCache;
        public Method onCreate;
        public Method onUpdate;
        public Method onRemove;
        public Method listBeansInCache;
        public Method listLocks;
        public Method unlock;
        public Method getDataNoLock;
        public Method getLockData;
        public Method getMaxCacheSize;
        public Method updateMaxCacheSize;

        public EntityBeanMethods(Method create, Method findByPrimaryKey, Method findAll, Method findByPrimaryKeys, Method findInCache, Method onCreate, Method onUpdate, Method onRemove, Method listBeansInCache, Method listLocks, Method unlock, Method getDataNoLock, Method getLockData, Method getMaxCacheSize, Method updateMaxCacheSize) {
            this.create = create;
            this.findByPrimaryKey = findByPrimaryKey;
            this.findAll = findAll;
            this.findByPrimaryKeys = findByPrimaryKeys;
            this.findInCache = findInCache;
            this.onCreate = onCreate;
            this.onUpdate = onUpdate;
            this.onRemove = onRemove;
            this.listBeansInCache = listBeansInCache;
            this.listLocks = listLocks;
            this.unlock = unlock;
            this.getDataNoLock = getDataNoLock;
            this.getLockData = getLockData;
            this.getMaxCacheSize = getMaxCacheSize;
            this.updateMaxCacheSize = updateMaxCacheSize;
        }
    }
}
