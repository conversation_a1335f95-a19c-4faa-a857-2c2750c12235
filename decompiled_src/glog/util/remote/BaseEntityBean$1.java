/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.1
implements BaseEntityBean.EjbFunctor {
    BaseEntityBean.1() {
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preCreate();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.doCreate();
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postCreate();
    }

    @Override
    public void broadcast() throws GLException {
        BaseEntityBean.this.broadcastCreate();
    }
}
