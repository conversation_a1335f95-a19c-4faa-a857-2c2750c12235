/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import java.util.Hashtable;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;

public static class NamingDirectory.NDContext
extends InitialContext {
    public NamingDirectory.NDContext(Hashtable properties) throws NamingException {
        super(properties);
    }

    public Context getImpl() {
        return this.defaultInitCtx;
    }

    public void setImpl(Context initCtx) {
        this.defaultInitCtx = initCtx;
    }
}
