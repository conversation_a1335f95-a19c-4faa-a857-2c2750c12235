/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.GLDuplicateRecord;
import java.sql.SQLException;

class BeanManagedEntityBean.1
extends BeanManagedEntityBean.DBModifier {
    BeanManagedEntityBean.1(T2SharedConnection x0) {
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getCreateStatement();
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        try {
            return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, true);
        }
        catch (GLException glex) {
            SQLException sqlex = sql.getLastSQLException();
            if (sqlex != null && sqlex.getErrorCode() == 1) {
                throw new GLDuplicateRecord(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK(), sqlex);
            }
            throw glex;
        }
    }

    @Override
    public String getBundledErrorKey() {
        return "Insert_Record_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Unable to add {0} {1} to the database. Most likely you do not have rights to add {0} records.";
    }
}
