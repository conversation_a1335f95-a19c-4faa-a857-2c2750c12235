/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.server.wallet.gen.WalletSessionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.GLMetaData;
import glog.util.jdbc.noserver.SqlColumn;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.wallet.WalletPassword;
import glog.util.wallet.Wallets;

public abstract class PasswordEntityBean
extends BeanManagedEntityBean {
    public static final String ALL = "all";

    public PasswordEntityBean() {
    }

    public PasswordEntityBean(boolean dbIsShared) {
        super(dbIsShared);
    }

    protected abstract GLMetaData.PwdCol[] getPwdCols();

    protected abstract Object getPwd(String var1);

    protected abstract void setPwd(String var1, Object var2);

    @Override
    protected boolean preCreate() throws GLException {
        if (!super.preCreate()) {
            return false;
        }
        this.savePasswords();
        return true;
    }

    @Override
    protected boolean preStore() throws GLException {
        if (!super.preStore()) {
            return false;
        }
        this.savePasswords();
        return true;
    }

    private void savePasswords() throws GLException {
        try {
            for (GLMetaData.PwdCol pwdCol : this.getPwdCols()) {
                Object password = this.getPwd(pwdCol.useCase);
                if (password == null) continue;
                WalletPassword decodedPassword = (WalletPassword)SqlColumn.mapFromDB(password, WalletPassword.class);
                String user = PasswordEntityBean.getWalletUser(pwdCol.useCase, pwdCol.maxLength, this.getPK().toString());
                if (user != null) {
                    new WalletSessionWrapper(false).setPassword(user, decodedPassword.toString(), true);
                }
                this.setPwd(pwdCol.useCase, SqlColumn.mapToDB(new WalletPassword(user, decodedPassword)));
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static String getWalletUser(String useCase, int maxLength, Object id) {
        Wallets wallets = Wallets.getWallets();
        if (!wallets.isEnabled() || !wallets.isEnabled(useCase)) {
            return null;
        }
        StringBuilder sb = new StringBuilder(useCase);
        sb.append(':').append(id);
        String user = sb.toString();
        if (user.length() > maxLength) {
            user = user.substring(user.length() - maxLength);
        }
        return user;
    }

    public static String getWalletUser(Class beanClass, Object id) throws GLException {
        try {
            PasswordEntityBean bean = (PasswordEntityBean)beanClass.newInstance();
            GLMetaData.PwdCol pwdCol = bean.getPwdCols()[0];
            return PasswordEntityBean.getWalletUser(pwdCol.useCase, pwdCol.maxLength, id);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }
}
