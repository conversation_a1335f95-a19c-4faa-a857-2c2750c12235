/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.SessionBean
 *  javax.ejb.SessionContext
 */
package glog.util.remote;

import glog.util.log.Log;
import glog.util.log.LogIds;
import glog.util.remote.BaseBean;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.SessionBean;
import javax.ejb.SessionContext;

public abstract class BaseStatelessBean
extends BaseBean
implements SessionBean {
    public BaseStatelessBean() {
        this.reset();
    }

    public void ejbActivate() throws RemoteException {
        this.log("activated");
        this.activate();
    }

    public void ejbPassivate() throws RemoteException {
        this.log("passivated");
        this.passivate();
    }

    public void ejbRemove() throws RemoteException {
        this.log("removed");
        this.reset();
    }

    public void ejbCreate() throws CreateException {
        this.log("created");
    }

    public void setSessionContext(SessionContext ctx) {
        this.setContext(ctx);
    }

    public final SessionContext getSessionContext() {
        return (SessionContext)this.getContext();
    }

    protected void reset() {
        this.passivate();
    }

    protected void activate() {
    }

    protected void passivate() {
    }

    protected String id() {
        return "" + Integer.toHexString(System.identityHashCode(this));
    }

    protected void log(String verb) {
        if (Log.idOn[LogIds.EJB.index]) {
            Log.logID(LogIds.EJB, verb + " " + this.id());
        }
    }
}
