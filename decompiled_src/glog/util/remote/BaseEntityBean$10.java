/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.10
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ String val$whereClause;
    final /* synthetic */ Object[] val$whereValues;
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.10(String string, Object[] objectArray, Object object) {
        this.val$whereClause = string;
        this.val$whereValues = objectArray;
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFindAll();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindMultiple(this.val$whereClause, this.val$whereValues, this.val$transaction);
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFindAll();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
