/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;

class BeanManagedEntityBean.2
extends BeanManagedEntityBean.DBModifier {
    BeanManagedEntityBean.2(T2SharedConnection x0) {
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getLoadStatement();
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeLoad(this.conn, sql);
    }

    @Override
    public String getUseCase() {
        return "readFailure";
    }

    @Override
    public String getBundledErrorKey() {
        return "Read_Record_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Unable to read {0} {1} from the database. Either the record has been removed by another user or you do not have rights to view it.";
    }
}
