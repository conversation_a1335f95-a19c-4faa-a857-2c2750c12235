/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public static class RouteTracking.MachineTracking {
    private int machineCount = 0;

    public synchronized void increment() {
        ++this.machineCount;
    }

    public synchronized void diagnoseXML(Document d, Element machineElem) throws GLException {
        machineElem.setAttribute("count", String.valueOf(this.machineCount));
    }

    public synchronized int getCount() {
        return this.machineCount;
    }
}
