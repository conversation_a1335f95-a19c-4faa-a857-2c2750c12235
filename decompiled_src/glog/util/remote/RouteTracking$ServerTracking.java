/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.DOMGenerator;
import glog.util.exception.GLException;
import glog.util.remote.RouteTracking;
import java.util.Map;
import java.util.TreeMap;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public static class RouteTracking.ServerTracking {
    private Map byMachine = new TreeMap();

    public synchronized void increment(String machine) {
        RouteTracking.MachineTracking machineTracking = (RouteTracking.MachineTracking)this.byMachine.get(machine);
        if (machineTracking == null) {
            machineTracking = new RouteTracking.MachineTracking();
            this.byMachine.put(machine, machineTracking);
        }
        machineTracking.increment();
    }

    public synchronized void diagnoseXML(Document d, Element serverElem) throws GLException {
        for (Map.Entry entry : this.byMachine.entrySet()) {
            String machine = (String)entry.getKey();
            RouteTracking.MachineTracking machineTracking = (RouteTracking.MachineTracking)entry.getValue();
            Element machineElem = (Element)DOMGenerator.insertElement(d, "machine", serverElem);
            machineElem.setAttribute("id", machine);
            machineTracking.diagnoseXML(d, machineElem);
        }
    }

    public synchronized Map getMachines() {
        return new TreeMap(this.byMachine);
    }
}
