/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.server.appserver.WebServerLoader;
import glog.server.appserver.WebServerMap;
import glog.util.exception.GLException;
import glog.util.jdbc.Loader;
import glog.util.jdbc.T2SharedConnection;

static final class NamingDirectory.2
implements Loader.Read {
    NamingDirectory.2() {
    }

    @Override
    public void execute(T2SharedConnection conn) throws GLException {
        WebServerMap.setMap(webmap = WebServerLoader.get().loadServerMap(conn));
    }
}
