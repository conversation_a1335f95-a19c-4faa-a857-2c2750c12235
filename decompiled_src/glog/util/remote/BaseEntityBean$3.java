/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.3
implements BaseEntityBean.EjbFunctor {
    BaseEntityBean.3() {
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preStore();
    }

    @Override
    public void doIt() throws GLException {
        if (BaseEntityBean.this.dataChanged && BaseEntityBean.this.isHeaderModified) {
            BaseEntityBean.this.doStore();
        }
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postStore();
    }

    @Override
    public void broadcast() throws GLException {
        BaseEntityBean.this.broadcastStore();
    }
}
