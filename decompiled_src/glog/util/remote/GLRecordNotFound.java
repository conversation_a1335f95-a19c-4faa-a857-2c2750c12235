/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLCreateException;
import glog.util.exception.NoExceptionLogging;
import glog.util.jdbc.Pk;
import glog.util.remote.RemoveRecordNotFound;

public class GLRecordNotFound
extends GLCreateException
implements NoExceptionLogging {
    public GLRecordNotFound(String table, Pk primaryKey) {
        super(new Cause("cause.Remove_Record_Not_Exist", null, new Object[][]{{"primaryKey", primaryKey}, {"table", table}}), null);
    }

    public GLRecordNotFound(Cause cause) {
        this(cause, null);
    }

    public GLRecordNotFound(Cause cause, Throwable t) {
        super(cause, t);
    }

    @Override
    protected Throwable toRemoveException() {
        return new RemoveRecordNotFound(this);
    }
}
