/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.8
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.8(Object object) {
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFindAll();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindAll(this.val$transaction);
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFindAll();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
