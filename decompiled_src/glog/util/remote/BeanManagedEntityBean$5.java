/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;

class BeanManagedEntityBean.5
extends BeanManagedEntityBean.DBModifier {
    final /* synthetic */ Pk val$primaryKey;
    final /* synthetic */ Object val$transaction;

    BeanManagedEntityBean.5(T2SharedConnection x0, Pk pk, Object object) {
        this.val$primaryKey = pk;
        this.val$transaction = object;
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getFindByPrimaryKeyStatement(this.val$primaryKey);
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeFind(this.conn, sql, this.val$transaction);
    }

    @Override
    public String getBundledErrorKey() {
        return "Bean_Find_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Bean_Find_Failure";
    }
}
