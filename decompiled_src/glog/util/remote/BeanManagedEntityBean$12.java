/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;

class BeanManagedEntityBean.12
extends BeanManagedEntityBean.DBModifier {
    final /* synthetic */ String val$sqlStatement;
    final /* synthetic */ Object[] val$sqlValues;
    final /* synthetic */ Object val$transaction;

    BeanManagedEntityBean.12(T2SharedConnection x0, String string, Object[] objectArray, Object object) {
        this.val$sqlStatement = string;
        this.val$sqlValues = objectArray;
        this.val$transaction = object;
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getFindStatementSQL(this.val$sqlStatement, this.val$sqlValues);
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, this.val$transaction);
    }

    @Override
    public String getBundledErrorKey() {
        return "Bean_Find_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Bean_Find_Failure";
    }
}
