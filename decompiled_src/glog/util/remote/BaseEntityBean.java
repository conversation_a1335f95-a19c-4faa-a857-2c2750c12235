/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EntityBean
 *  javax.ejb.EntityContext
 *  javax.ejb.FinderException
 *  javax.ejb.RemoveException
 */
package glog.util.remote;

import glog.server.appserver.AppServerConstants;
import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.exception.Cause;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLError;
import glog.util.exception.GLException;
import glog.util.exception.RemoteExceptionWrapper;
import glog.util.exception.RemoveExceptionWrapper;
import glog.util.jdbc.Pk;
import glog.util.local.LocalConstants;
import glog.util.local.LocalContext;
import glog.util.log.Log;
import glog.util.log.LogIds;
import glog.util.remote.BaseBean;
import glog.util.remote.BeanData;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Map;
import java.util.TreeMap;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EntityBean;
import javax.ejb.EntityContext;
import javax.ejb.FinderException;
import javax.ejb.RemoveException;

public abstract class BaseEntityBean
extends BaseBean
implements EntityBean {
    public static final int ACTIVE_QUEUE = 0;
    public static final int INACTIVE_QUEUE = 1;
    public static final int FREE_QUEUE = 2;
    public static PkAccessor DEFAULT_PK_ACCESSOR = new PkAccessor();
    public static BeanDataAccessor DEFAULT_BEAN_DATA_ACCESSOR = new BeanDataAccessor();
    protected Enumeration pkEnumeration;
    protected boolean markedForCacheRemove = false;
    protected static ThreadLocal inOnMethod = new ThreadLocal();
    private static boolean checkDBUpdateInOnMethod = "true".equalsIgnoreCase(GLProperties.get().getProperty("glog.ejb.checkDBUpdateInOnMethod", "true"));
    private boolean isModified = false;
    private boolean isHeaderModified = false;
    private boolean dataChanged = true;
    private transient boolean forceLoad = false;

    protected boolean preCreate() throws GLException {
        return this.checkConstraints();
    }

    protected boolean preLoad() throws GLException {
        return true;
    }

    protected boolean preStore() throws GLException {
        return this.isModified() && this.checkConstraints();
    }

    protected boolean preRemove() throws GLException {
        return true;
    }

    protected void doCreate() throws GLException {
    }

    protected void doLoad() throws GLException {
    }

    protected void doStore() throws GLException {
    }

    protected void doRemove() throws GLException {
    }

    protected void postCreate() throws GLException {
        this.isModified = false;
        this.isHeaderModified = false;
        this.callOnCreate();
    }

    protected void postLoad() throws GLException {
        this.isModified = false;
        this.isHeaderModified = false;
    }

    protected void postStore() throws GLException {
        this.isModified = false;
        this.isHeaderModified = false;
        this.callOnUpdate();
    }

    protected void postRemove() throws GLException {
        this.isModified = false;
        this.isHeaderModified = false;
        this.callOnRemove();
    }

    protected void broadcastCreate() throws GLException {
    }

    protected void broadcastStore() throws GLException {
    }

    protected void broadcastRemove() throws GLException {
    }

    protected boolean preFind() throws GLException {
        return true;
    }

    protected Pk doFindByPrimaryKey(Pk primaryKey, Object transaction) throws GLException {
        return primaryKey;
    }

    protected Pk doFindOne(String whereClause, Object[] whereValues, Object transaction) throws GLException {
        return null;
    }

    protected Pk doFindOneSQL(String sqlSelect, Object[] sqlValues, Object transaction) throws GLException {
        return null;
    }

    protected Pk doFindFirst(String whereClause, Object[] whereValues) throws GLException {
        return null;
    }

    protected Pk doFindNext() throws GLException {
        return null;
    }

    protected void postFind() throws GLException {
    }

    protected boolean preFindAll() throws GLException {
        return true;
    }

    protected Enumeration doFindAll(Object transaction) throws GLException {
        return null;
    }

    protected void postFindAll() throws GLException {
    }

    protected Enumeration doFindByPrimaryKeys(Vector primaryKeys, Object transaction) throws GLException {
        return primaryKeys.elements();
    }

    protected Enumeration doFindMultiple(String whereClause, Object[] whereValues, Object transaction) throws GLException {
        return null;
    }

    protected Enumeration doFindMultipleSQL(String sqlSelect, Object[] sqlValues, Object transaction) throws GLException {
        return null;
    }

    protected boolean checkConstraints() throws GLException {
        return true;
    }

    protected void reset() {
    }

    protected void passivate() {
    }

    public void ejbActivate() throws RemoteException {
        this.forceLoad = true;
        this.reset();
    }

    public void ejbPassivate() throws RemoteException {
        this.passivate();
    }

    public void remove() throws RemoveException {
        this.ejbRemove();
    }

    public void reload() throws GLException {
        try {
            this.forceLoad = true;
            this.ejbLoad();
            this.callOnUpdate();
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void unload() throws GLException {
    }

    public Collection ejbHomeListBeansInCache() throws GLException {
        return Functions.EMPTY_LIST;
    }

    public Collection ejbHomeListLocks() throws GLException {
        return Functions.EMPTY_LIST;
    }

    public void ejbHomeUnlock(Pk pk) throws GLException {
    }

    public BeanData ejbHomeGetDataNoLock(Pk pk) throws GLException {
        return null;
    }

    public Map verify() throws GLException {
        try {
            Method getData = this.getClass().getMethod("getData", Functions.EMPTY_CLASS_ARRAY);
            BeanData oldData = (BeanData)getData.invoke((Object)this, Functions.EMPTY_OBJECT_ARRAY);
            this.reload();
            BeanData newData = (BeanData)getData.invoke((Object)this, Functions.EMPTY_OBJECT_ARRAY);
            if (!Functions.equals(oldData, newData)) {
                this.callOnUpdate();
                TreeMap<String, String[]> diffFields = new TreeMap<String, String[]>();
                Field[] fields = oldData.getClass().getFields();
                for (int i = 0; i < fields.length; ++i) {
                    Object newValue;
                    Object oldValue;
                    int modifiers = fields[i].getModifiers();
                    if ((modifiers & 0x88) != 0 || Functions.equals(oldValue = fields[i].get(oldData), newValue = fields[i].get(newData))) continue;
                    String strOld = String.valueOf(oldValue);
                    String strNew = String.valueOf(newValue);
                    diffFields.put(fields[i].getName(), new String[]{strOld, strNew});
                }
                return diffFields;
            }
            return null;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void ejbCreator() throws CreateException {
        try {
            this.checkForOnMethod();
            this.markedForCacheRemove = false;
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preCreate();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.doCreate();
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postCreate();
                }

                @Override
                public void broadcast() throws GLException {
                    BaseEntityBean.this.broadcastCreate();
                }
            });
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public boolean ejbPostCreator() throws CreateException {
        return true;
    }

    public void ejbLoad() throws RemoteException {
        try {
            this.markedForCacheRemove = false;
            if (AppServerConstants.get().isScalable() && LocalConstants.getMode() == LocalContext.Mode.appbeans) {
                this.setDbIsShared();
                if (!this.forceLoad) {
                    if (!this.isRemote()) {
                        return;
                    }
                    if (Log.idOn[LogIds.SCALABILITY_DETAILS.index]) {
                        Log.logID(LogIds.SCALABILITY_DETAILS, "Reloading remote bean {0}", this.getPK());
                    }
                }
            }
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preLoad();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.doLoad();
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postLoad();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            this.forceLoad = false;
        }
        catch (Throwable t) {
            try {
                this.getEntityContext().setRollbackOnly();
            }
            catch (IllegalStateException illegalStateException) {
                // empty catch block
            }
            throw RemoteExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbStore() throws RemoteException {
        try {
            this.checkForOnMethod();
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preStore();
                }

                @Override
                public void doIt() throws GLException {
                    if (BaseEntityBean.this.dataChanged && BaseEntityBean.this.isHeaderModified) {
                        BaseEntityBean.this.doStore();
                    }
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postStore();
                }

                @Override
                public void broadcast() throws GLException {
                    BaseEntityBean.this.broadcastStore();
                }
            });
        }
        catch (Throwable t) {
            this.getEntityContext().setRollbackOnly();
            throw RemoteExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbRemove() throws RemoveException {
        try {
            this.checkForOnMethod();
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.markedForCacheRemove ? false : BaseEntityBean.this.preRemove();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.doRemove();
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postRemove();
                }

                @Override
                public void broadcast() throws GLException {
                    BaseEntityBean.this.broadcastRemove();
                }
            });
            this.markedForCacheRemove = false;
        }
        catch (Throwable t) {
            throw RemoveExceptionWrapper.ejbFactory(t);
        }
    }

    public Pk ejbFindByKey(final Pk primaryKey, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFind();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.setPK(BaseEntityBean.this.doFindByPrimaryKey(primaryKey, transaction));
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFind();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            return this.getPK();
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0001", null, new Object[][]{{"name", this.getName()}, {"primaryKey", primaryKey}}), t));
        }
    }

    public Pk ejbFindByKey(Pk primaryKey) throws FinderException {
        return this.ejbFindByKey(primaryKey, null);
    }

    public Pk ejbFindOne(final String whereClause, final Object[] whereValues, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFind();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.setPK(BaseEntityBean.this.doFindOne(whereClause, whereValues, transaction));
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFind();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            return this.getPK();
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0004", null, new Object[][]{{"asList", Arrays.asList(whereValues)}, {"name", this.getName()}, {"whereClause", whereClause}}), t));
        }
    }

    public Pk ejbFindOne(String whereClause, Object[] whereValues) throws FinderException {
        return this.ejbFindOne(whereClause, whereValues, null);
    }

    public Pk ejbFindOneSQL(final String sqlSelect, final Object[] sqlValues, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFind();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.setPK(BaseEntityBean.this.doFindOneSQL(sqlSelect, sqlValues, transaction));
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFind();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            return this.getPK();
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0005", null, new Object[][]{{"2", sqlValues == null ? null : Arrays.asList(sqlValues)}, {"name", this.getName()}, {"sqlSelect", sqlSelect}}), t));
        }
    }

    public Pk ejbFindOneSQL(String sqlSelect, Object[] sqlValues) throws FinderException {
        return this.ejbFindOneSQL(sqlSelect, sqlValues, null);
    }

    public Enumeration ejbFindAll(final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFindAll();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindAll(transaction);
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFindAll();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            Enumeration enumeration = this.pkEnumeration;
            return enumeration;
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0002", null, new Object[][]{{"name", this.getName()}}), t));
        }
        finally {
            this.pkEnumeration = null;
        }
    }

    public Enumeration ejbFindAll() throws FinderException {
        return this.ejbFindAll(null);
    }

    public Enumeration ejbFindByPrimaryKeys(final Vector primaryKeys, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFindAll();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindByPrimaryKeys(primaryKeys, transaction);
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFindAll();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            Enumeration enumeration = this.pkEnumeration;
            return enumeration;
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0003", null, new Object[][]{{"name", this.getName()}, {"primaryKeys", primaryKeys}}), t));
        }
        finally {
            this.pkEnumeration = null;
        }
    }

    public Enumeration ejbFindByPrimaryKeys(Vector primaryKeys) throws FinderException {
        return this.ejbFindByPrimaryKeys(primaryKeys, null);
    }

    public Enumeration ejbFind(final String whereClause, final Object[] whereValues, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFindAll();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindMultiple(whereClause, whereValues, transaction);
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFindAll();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            Enumeration enumeration = this.pkEnumeration;
            return enumeration;
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0006", null, new Object[][]{{"asList", whereValues == null ? null : Arrays.asList(whereValues)}, {"name", this.getName()}, {"whereClause", whereClause}}), t));
        }
        finally {
            this.pkEnumeration = null;
        }
    }

    public Enumeration ejbFind(String whereClause, Object[] whereValues) throws FinderException {
        return this.ejbFind(whereClause, whereValues, null);
    }

    public Enumeration ejbFindSQL(final String sqlSelect, final Object[] sqlValues, final Object transaction) throws FinderException {
        try {
            this.ejb(new EjbFunctor(){

                @Override
                public boolean pre() throws GLException {
                    return BaseEntityBean.this.preFindAll();
                }

                @Override
                public void doIt() throws GLException {
                    BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindMultipleSQL(sqlSelect, sqlValues, transaction);
                }

                @Override
                public void post() throws GLException {
                    BaseEntityBean.this.postFindAll();
                }

                @Override
                public void broadcast() throws GLException {
                }
            });
            Enumeration enumeration = this.pkEnumeration;
            return enumeration;
        }
        catch (Throwable t) {
            throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.BaseEntityBean.0007", null, new Object[][]{{"2", sqlValues == null ? null : Arrays.asList(sqlValues)}, {"name", this.getName()}, {"sqlSelect", sqlSelect}}), t));
        }
        finally {
            this.pkEnumeration = null;
        }
    }

    public Enumeration ejbFindSQL(String sqlSelect, Object[] sqlValues) throws FinderException {
        return this.ejbFindSQL(sqlSelect, sqlValues, null);
    }

    public void ejb(EjbFunctor f) throws GLException {
        if (f.pre()) {
            f.doIt();
            f.post();
            f.broadcast();
        }
    }

    public boolean isModified() {
        return this.isModified;
    }

    public void setModified(boolean isModified) {
        this.isModified = isModified;
    }

    public boolean isHeaderModified() {
        return this.isHeaderModified;
    }

    public void setHeaderModified(boolean isHeaderModified) {
        this.isHeaderModified = isHeaderModified;
    }

    public boolean hasDataChanged() {
        return this.dataChanged;
    }

    public void setDataChanged(boolean dataChanged) {
        this.dataChanged = dataChanged;
    }

    public void afterBegin() {
    }

    public void beforeCompletion() {
    }

    public void afterCompletion(boolean commit) {
    }

    public void setEntityContext(EntityContext ctx) {
        this.setContext(ctx);
    }

    public void unsetEntityContext() {
        this.setContext(null);
    }

    public final EntityContext getEntityContext() {
        return (EntityContext)this.getContext();
    }

    public abstract Pk getPK();

    public abstract void setPK(Pk var1);

    public abstract String getName();

    public abstract Class getDataClass();

    public abstract Class getPkClass();

    public void markForCacheRemove() {
        this.markedForCacheRemove = true;
    }

    protected void setDbIsShared() {
    }

    protected boolean isRemote() {
        return false;
    }

    protected boolean isRemote(String domain) {
        return false;
    }

    protected void onCreate(PkAccessor pk, BeanDataAccessor data) throws GLException {
    }

    protected void onUpdate(PkAccessor pk, BeanDataAccessor data) throws GLException {
    }

    protected void onRemove(PkAccessor pk, BeanDataAccessor data) throws GLException {
    }

    protected void onCreate() throws GLException {
        this.onCreate(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    protected void onUpdate() throws GLException {
        this.onUpdate(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    protected void onRemove() throws GLException {
        this.onRemove(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void callOnCreate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        try {
            inOnMethod.set(Boolean.TRUE);
            this.onCreate(pk, data);
        }
        finally {
            inOnMethod.set(null);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void callOnUpdate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        try {
            inOnMethod.set(Boolean.TRUE);
            this.onUpdate(pk, data);
        }
        finally {
            inOnMethod.set(null);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void callOnRemove(PkAccessor pk, BeanDataAccessor data) throws GLException {
        try {
            inOnMethod.set(Boolean.TRUE);
            this.onRemove(pk, data);
        }
        finally {
            inOnMethod.set(null);
        }
    }

    protected void callOnCreate() throws GLException {
        this.callOnCreate(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    protected void callOnUpdate() throws GLException {
        this.callOnUpdate(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    protected void callOnRemove() throws GLException {
        this.callOnRemove(DEFAULT_PK_ACCESSOR, DEFAULT_BEAN_DATA_ACCESSOR);
    }

    protected final Pk getPrimaryContext() {
        EntityContext ejbContext = this.getEntityContext();
        Pk pk = null;
        if (ejbContext != null) {
            pk = (Pk)ejbContext.getPrimaryKey();
        }
        if (pk == null) {
            pk = this.getPK();
        }
        return pk;
    }

    protected String id() {
        Pk pk = null;
        try {
            pk = this.getPrimaryContext();
        }
        catch (Exception e) {
            pk = null;
        }
        return "" + Integer.toHexString(System.identityHashCode(this)) + ", PK = " + (pk == null ? "null" : pk.toString());
    }

    protected abstract Field[] getBeanFields();

    protected void checkForOnMethod() {
        if (this.isModified && checkDBUpdateInOnMethod && inOnMethod.get() != null) {
            throw new GLError("Do not update the database in an onXX() trigger. Use pre/post methods instead.", null);
        }
    }

    public static interface EjbFunctor {
        public boolean pre() throws GLException;

        public void doIt() throws GLException;

        public void post() throws GLException;

        public void broadcast() throws GLException;
    }
}
