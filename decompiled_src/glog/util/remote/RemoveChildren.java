/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.NamingDirectory;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Enumeration;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;

public class RemoveChildren {
    public static void remove(Class childHomeInterface, String finder, Class[] finderParameters, Object[] finderArguments) throws GLException {
        try {
            Method finderMethod = childHomeInterface.getMethod(finder, finderParameters);
            Field nameField = childHomeInterface.getField("NAME");
            NamingDirectory nd = NamingDirectory.get();
            try {
                EJBHome home = (EJBHome)nd.lookup((String)nameField.get(null));
                Enumeration e = (Enumeration)finderMethod.invoke((Object)home, finderArguments);
                while (e.hasMoreElements()) {
                    EJBObject bean = (EJBObject)e.nextElement();
                    bean.remove();
                }
            }
            catch (InvocationTargetException ite) {
                if (ite.getTargetException() instanceof FinderNoMoreRecords) {
                    return;
                }
                throw ite;
            }
            finally {
                nd.release();
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static void clearFk(Class childHomeInterface, String finder, Class[] finderParameters, Object[] finderArguments, String fkSetter, Class fkClass) throws GLException {
        try {
            Object[] nullArgs = new Object[]{null};
            Class[] fkClasses = new Class[]{fkClass};
            Method finderMethod = childHomeInterface.getMethod(finder, finderParameters);
            Field nameField = childHomeInterface.getField("NAME");
            NamingDirectory nd = NamingDirectory.get();
            try {
                EJBHome home = (EJBHome)nd.lookup((String)nameField.get(null));
                Enumeration e = (Enumeration)finderMethod.invoke((Object)home, finderArguments);
                while (e.hasMoreElements()) {
                    EJBObject bean = (EJBObject)e.nextElement();
                    Method m = bean.getClass().getMethod(fkSetter, fkClasses);
                    m.invoke((Object)bean, nullArgs);
                }
            }
            catch (InvocationTargetException ite) {
                if (ite.getTargetException() instanceof FinderNoMoreRecords) {
                    return;
                }
                throw ite;
            }
            finally {
                nd.release();
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }
}
