/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.Functions;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.LocalConstants;
import glog.util.remote.BeanBaseInvoker;
import glog.util.remote.BeanData;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.transaction.GLTransactionHelper;
import glog.util.transaction.ManualTransaction;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Hashtable;
import java.util.Map;
import java.util.Vector;

public class EntityBeanInvoker
extends BeanBaseInvoker {
    private Method setData;
    private Method getData;
    private Method setLinks;
    private Method getPK;
    private Method remove;
    private Method reload;
    private Method unload;
    private Method verify;
    private Method markForCacheRemove;
    private Hashtable getMethods;
    private Object beanRemote;
    private Class beanClass;

    public EntityBeanInvoker(Object beanRemote, Class beanDataClass) {
        try {
            this.beanRemote = beanRemote;
            this.beanClass = beanRemote.getClass();
            this.setData = this.beanClass.getMethod("setData", beanDataClass);
            this.getData = this.beanClass.getMethod("getData", emptyClasses);
            this.setLinks = this.beanClass.getMethod("setLinks", Vector.class, Vector.class);
            this.getPK = this.beanClass.getMethod("getPK", emptyClasses);
            this.remove = this.beanClass.getMethod("remove", emptyClasses);
            this.reload = this.beanClass.getMethod("reload", emptyClasses);
            this.unload = this.beanClass.getMethod("unload", emptyClasses);
            this.verify = this.beanClass.getMethod("verify", emptyClasses);
            this.markForCacheRemove = this.beanClass.getMethod("markForCacheRemove", emptyClasses);
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function: " + this.beanClass.getName());
        }
    }

    public void setData(BeanData data) throws GLException {
        this.invoke(this.setData, this.beanRemote, new Object[]{data});
    }

    public BeanData getData() throws GLException {
        return (BeanData)this.invoke(this.getData, this.beanRemote, emptyArgs);
    }

    public Object getField(Field field) throws GLException {
        return this.invoke(this.getFieldMethod(field), this.beanRemote, emptyArgs);
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
        this.invoke(this.setLinks, this.beanRemote, new Object[]{pksToAdd, pksToRemove});
    }

    public Pk getPK() throws GLException {
        return (Pk)this.invoke(this.getPK, this.beanRemote, emptyArgs);
    }

    public void remove() throws GLException {
        this.invoke(this.remove, this.beanRemote, emptyArgs);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void reload() throws GLException {
        BeanManagedEntityBean.setInReload(Boolean.TRUE);
        try {
            this.invoke(this.reload, this.beanRemote, emptyArgs);
        }
        finally {
            BeanManagedEntityBean.setInReload(null);
        }
    }

    public void unload() throws GLException {
        this.removeFromContainerCache();
    }

    public Map verify() throws GLException {
        return (Map)this.invoke(this.verify, this.beanRemote, emptyArgs);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void removeFromContainerCache() throws GLException {
        block9: {
            try {
                if (GLTransactionHelper.isTransactional()) {
                    this.invoke(this.markForCacheRemove, this.beanRemote, emptyArgs);
                    this.invoke(this.remove, this.beanRemote, emptyArgs);
                    break block9;
                }
                switch (LocalConstants.getMode()) {
                    case appserver: 
                    case standalone: 
                    case webalone: {
                        this.invoke(this.markForCacheRemove, this.beanRemote, emptyArgs);
                        this.invoke(this.remove, this.beanRemote, emptyArgs);
                        break;
                    }
                    default: {
                        ManualTransaction trx = new ManualTransaction();
                        try {
                            trx.begin();
                            this.invoke(this.markForCacheRemove, this.beanRemote, emptyArgs);
                            this.invoke(this.remove, this.beanRemote, emptyArgs);
                            break;
                        }
                        finally {
                            trx.commit();
                        }
                    }
                }
            }
            catch (Throwable t) {
                throw GLException.factory(t);
            }
        }
    }

    private Method getFieldMethod(Field field) {
        try {
            Method fieldMethod;
            if (this.getMethods == null) {
                this.getMethods = new Hashtable();
            }
            if ((fieldMethod = (Method)this.getMethods.get(field)) == null) {
                fieldMethod = this.beanClass.getMethod("get" + Functions.capitalize(field.getName()), emptyClasses);
                this.getMethods.put(field, fieldMethod);
            }
            return fieldMethod;
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean set method");
        }
    }
}
