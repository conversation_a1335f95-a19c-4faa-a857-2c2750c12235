/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;
import java.util.Vector;

class BaseEntityBean.9
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ Vector val$primaryKeys;
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.9(Vector vector, Object object) {
        this.val$primaryKeys = vector;
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFindAll();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindByPrimaryKeys(this.val$primaryKeys, this.val$transaction);
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFindAll();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
