/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.local.LocalContext;

static class NamingDirectory.3 {
    static final /* synthetic */ int[] $SwitchMap$glog$util$local$LocalContext$Mode;

    static {
        $SwitchMap$glog$util$local$LocalContext$Mode = new int[LocalContext.Mode.values().length];
        try {
            NamingDirectory.3.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.webapp.ordinal()] = 1;
        }
        catch (NoSuchFieldError ex) {
            // empty catch block
        }
        try {
            NamingDirectory.3.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.appserver.ordinal()] = 2;
        }
        catch (NoSuchFieldError ex) {
            // empty catch block
        }
        try {
            NamingDirectory.3.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.appbeans.ordinal()] = 3;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
    }
}
