/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.6
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ String val$whereClause;
    final /* synthetic */ Object[] val$whereValues;
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.6(String string, Object[] objectArray, Object object) {
        this.val$whereClause = string;
        this.val$whereValues = objectArray;
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFind();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.setPK(BaseEntityBean.this.doFindOne(this.val$whereClause, this.val$whereValues, this.val$transaction));
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFind();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
