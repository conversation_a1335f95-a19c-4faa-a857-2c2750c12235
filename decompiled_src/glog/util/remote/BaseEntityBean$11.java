/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.11
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ String val$sqlSelect;
    final /* synthetic */ Object[] val$sqlValues;
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.11(String string, Object[] objectArray, Object object) {
        this.val$sqlSelect = string;
        this.val$sqlValues = objectArray;
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFindAll();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.pkEnumeration = BaseEntityBean.this.doFindMultipleSQL(this.val$sqlSelect, this.val$sqlValues, this.val$transaction);
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFindAll();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
