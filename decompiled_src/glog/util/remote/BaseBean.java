/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import java.rmi.RemoteException;

public abstract class BaseBean {
    private Object ctx;

    public T2SharedConnection getConnection() throws GLException {
        Object connection = null;
        return new T2SharedConnection(DataFunction.EJB);
    }

    public T2SharedConnection getConnectionRemote() throws RemoteException {
        try {
            return this.getConnection();
        }
        catch (GLException glex) {
            throw new RemoteException("Connection Failure", glex);
        }
    }

    protected void setContext(Object ctx) {
        this.ctx = ctx;
    }

    protected Object getContext() {
        return this.ctx;
    }
}
