/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.local.LocalContext;

static class EntityBeanInvoker.1 {
    static final /* synthetic */ int[] $SwitchMap$glog$util$local$LocalContext$Mode;

    static {
        $SwitchMap$glog$util$local$LocalContext$Mode = new int[LocalContext.Mode.values().length];
        try {
            EntityBeanInvoker.1.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.appserver.ordinal()] = 1;
        }
        catch (NoSuchFieldError ex) {
            // empty catch block
        }
        try {
            EntityBeanInvoker.1.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.standalone.ordinal()] = 2;
        }
        catch (NoSuchFieldError ex) {
            // empty catch block
        }
        try {
            EntityBeanInvoker.1.$SwitchMap$glog$util$local$LocalContext$Mode[LocalContext.Mode.webalone.ordinal()] = 3;
        }
        catch (NoSuchFieldError noSuchFieldError) {
            // empty catch block
        }
    }
}
