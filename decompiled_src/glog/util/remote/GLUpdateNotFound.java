/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLRemoteException;
import glog.util.exception.NoExceptionLogging;
import glog.util.jdbc.Pk;
import glog.util.remote.UpdateRecordNotFound;

public class GLUpdateNotFound
extends GLRemoteException
implements NoExceptionLogging {
    public GLUpdateNotFound(String table, Pk primaryKey) {
        super(new Cause("cause.Update_Record_Not_Exist", null, new Object[][]{{"primaryKey", primaryKey}, {"table", table}}), null);
    }

    public GLUpdateNotFound(Cause cause) {
        this(cause, null);
    }

    public GLUpdateNotFound(Cause cause, Throwable t) {
        super(cause, t);
    }

    @Override
    protected Throwable toRemoteException() {
        return new UpdateRecordNotFound(this);
    }
}
