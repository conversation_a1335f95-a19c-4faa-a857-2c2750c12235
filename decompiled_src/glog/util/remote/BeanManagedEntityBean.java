/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.RemoveException
 */
package glog.util.remote;

import glog.database.security.SecurityUtil;
import glog.ejb.reference.DataQueryTypeMap;
import glog.server.appserver.AppServerConstants;
import glog.server.appserver.AppServerIsolation;
import glog.server.appserver.AppServerLoader;
import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.LocalTimestamp;
import glog.util.TypeUnit;
import glog.util.cache.HashMapCache;
import glog.util.exception.Cause;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.exception.GLUnloggedException;
import glog.util.exception.RemoteExceptionWrapper;
import glog.util.exception.RemoveExceptionWrapper;
import glog.util.j2ee.remote.ASEntityBean;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.ResultSetUtil;
import glog.util.jdbc.SqlColumn;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.SqlUpdate;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoUpdateFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.UpdateFilter;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import glog.util.message.BeanUpdateTopic;
import glog.util.message.CRUTopic;
import glog.util.message.QueryUpdateTopic;
import glog.util.remote.AuditCleanup;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.GLDuplicateRecord;
import glog.util.remote.GLNoMoreRecords;
import glog.util.remote.GLRecordNotFound;
import glog.util.remote.GLUpdateNotFound;
import glog.util.remote.NamingDirectory;
import glog.util.transaction.FIFOSynchronization;
import glog.util.transaction.GLTransactionHelper;
import glog.util.transaction.TransactionProperties;
import glog.util.uom.UOMInsertFilter;
import glog.util.uom.UOMQueryFilter;
import glog.util.uom.UOMUpdateFilter;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.rmi.RemoteException;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.RemoveException;

public abstract class BeanManagedEntityBean
extends ASEntityBean {
    static HashMapCache createCache = new HashMapCache("BeanCreateSql");
    static HashMapCache loadCache = new HashMapCache("BeanLoadSql");
    static HashMapCache storeCache = new HashMapCache("BeanStoreSql");
    static HashMapCache updateDateCache = new HashMapCache("BeanUpdateDateSql");
    private static ThreadLocal inReload = new ThreadLocal();
    private boolean dbIsShared = false;
    private LocalTimestamp lastLoaded;
    private transient LocalTimestamp updateDate;
    protected Object transaction;
    private static boolean checkingUpdateDate = "true".equalsIgnoreCase(GLProperties.get().getProperty("glog.ejb.checkingUpdateDate", "false"));
    private static HashSet checkingUpdateDateSuppress = null;
    private static final String SQL_GET_DATA_INFO = "select distinct adi.data_info_seq from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and aud.audit_object_type_gid=? and aud.object_gid=?";
    private static final int ORACLE_UNIQUE_CONSTRAINT_VIOLATION = 1;
    private static final HashMap retries;

    public BeanManagedEntityBean() {
        this.dbIsShared = false;
    }

    public BeanManagedEntityBean(boolean dbIsShared) {
        this.dbIsShared = dbIsShared;
    }

    @Override
    public void unload() throws GLException {
        super.unload();
        this.getPrimaryKeyCache().remove(this.getPK());
    }

    public static void setInReload(Object reload) throws GLException {
        inReload.set(reload);
    }

    public String getTable() {
        return this.getPkCallback().getTableName().toUpperCase();
    }

    protected abstract PkCallback getPkCallback();

    protected abstract SqlStatement getCreateStatement() throws GLException;

    protected abstract SqlStatement getLoadStatement() throws GLException;

    protected abstract SqlStatement getStoreStatement() throws GLException;

    protected abstract SqlStatement getRemoveStatement() throws GLException;

    @Override
    public void doCreate() throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getCreateStatement();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                try {
                    return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, true);
                }
                catch (GLException glex) {
                    SQLException sqlex = sql.getLastSQLException();
                    if (sqlex != null && sqlex.getErrorCode() == 1) {
                        throw new GLDuplicateRecord(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK(), sqlex);
                    }
                    throw glex;
                }
            }

            @Override
            public String getBundledErrorKey() {
                return "Insert_Record_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Unable to add {0} {1} to the database. Most likely you do not have rights to add {0} records.";
            }
        });
    }

    @Override
    public void broadcastCreate() throws GLException {
        if (AppServerConstants.get().isScalable()) {
            Pk pk = this.getSuppressionOwnerPK();
            if (pk == null || (AppServerIsolation.get() & AppServerIsolation.BEAN_UPDATE_ALL) != 0L || !TransactionProperties.getInstance().containsBeanCreate(pk)) {
                GLTransactionHelper.registerSynchronization(new BeanUpdateTopic.Scheduler(this, CRUTopic.CRU_CREATE));
            }
            if (this.isSuppressionOwner()) {
                TransactionProperties.getInstance().addBeanCreate(this.getPK());
            }
            String pkClassName = this.getPkClass().getName();
            for (String queryName : AppServerLoader.get().getCreateRemoveQueryClassNamesForPkClass(pkClassName)) {
                GLTransactionHelper.registerSynchronization(new QueryUpdateTopic.Scheduler(this.getPK(), queryName, CRUTopic.CRU_CREATE));
            }
        }
    }

    protected Pk getSuppressionOwnerPK() {
        return null;
    }

    protected boolean isSuppressionOwner() {
        return false;
    }

    @Override
    public void doLoad() throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getLoadStatement();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeLoad(this.conn, sql);
            }

            @Override
            public String getUseCase() {
                return "readFailure";
            }

            @Override
            public String getBundledErrorKey() {
                return "Read_Record_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Unable to read {0} {1} from the database. Either the record has been removed by another user or you do not have rights to view it.";
            }
        });
    }

    @Override
    public void doStore() throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getStoreStatement();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, false);
            }

            @Override
            public void noRecords() throws GLException {
                throw new GLUpdateNotFound(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK());
            }

            @Override
            public String getUseCase() {
                return "writeFailure";
            }

            @Override
            public String getBundledErrorKey() {
                return "Write_Record_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Unable to update {0} {1} to the database. Either the record has been removed by another user or you do not have rights to edit it.";
            }
        });
    }

    @Override
    public void broadcastStore() throws GLException {
        if (AppServerConstants.get().isScalable()) {
            GLTransactionHelper.registerSynchronization(new BeanUpdateTopic.Scheduler(this, CRUTopic.CRU_UPDATE));
            String pkClassName = this.getPkClass().getName();
            for (String queryName : AppServerLoader.get().getUpdateQueryClassNamesForPkClass(pkClassName)) {
                GLTransactionHelper.registerSynchronization(new QueryUpdateTopic.Scheduler(this.getPK(), queryName, CRUTopic.CRU_UPDATE));
            }
        }
    }

    @Override
    public void doRemove() throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getRemoveStatement();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, false);
            }

            @Override
            public void noRecords() throws GLException {
                throw new GLRecordNotFound(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK());
            }

            @Override
            public String getBundledErrorKey() {
                return "Remove_Record_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Unable to remove {0} {1} from the database. Either the record has already been removed by another user or you do not have rights to remove it.";
            }
        });
    }

    @Override
    public void broadcastRemove() throws GLException {
        if (AppServerConstants.get().isScalable()) {
            GLTransactionHelper.registerSynchronization(new BeanUpdateTopic.Scheduler(this, CRUTopic.CRU_REMOVE));
            String pkClassName = this.getPkClass().getName();
            for (String queryName : AppServerLoader.get().getCreateRemoveQueryClassNamesForPkClass(pkClassName)) {
                GLTransactionHelper.registerSynchronization(new QueryUpdateTopic.Scheduler(this.getPK(), queryName, CRUTopic.CRU_REMOVE));
            }
        }
    }

    @Override
    public Pk doFindByPrimaryKey(final Pk primaryKey, final Object transaction) throws GLException {
        if (!this.dbIsShared && this.getPrimaryKeyCache().contains(primaryKey)) {
            return primaryKey;
        }
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindByPrimaryKeyStatement(primaryKey);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFind(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.getPK();
    }

    @Override
    public Pk doFindOne(final String whereClause, final Object[] whereValues, final Object transaction) throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindStatement(whereClause, whereValues);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFind(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.getPK();
    }

    @Override
    public Pk doFindOneSQL(final String sqlSelect, final Object[] sqlValues, final Object transaction) throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindStatementSQL(sqlSelect, sqlValues);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFind(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.getPK();
    }

    public LocalTimestamp getUpdateDate() throws GLException {
        this.updateDate = null;
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getUpdateDateStatement();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeUpdateDate(this.conn, sql);
            }

            @Override
            public String getBundledErrorKey() {
                return "Update_Date_Read_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Unable to read {0} {1} from the database. Either the record has been removed by another user or you do not have rights to view it.";
            }
        });
        return this.updateDate;
    }

    protected void checkUpdateDate() throws GLException {
        try {
            if (this.checkingUpdateDate()) {
                LocalTimestamp updateDate = this.getUpdateDate();
                if (updateDate == null) {
                    return;
                }
                if (this.lastLoaded.compareTo(updateDate) < 0) {
                    throw GLException.factory(new GLException.CausedBy("cause.ejbTimestampCheckFailed", null, new Object[][]{{"name", this.getName()}, {"pk", this.getPK()}, {"lastLoaded", this.lastLoaded}, {"updateDate", updateDate}}));
                }
            }
        }
        catch (GLException glex) {
            this.unload();
            throw glex;
        }
    }

    protected boolean checkingUpdateDate() throws GLException {
        if (!checkingUpdateDate) {
            return false;
        }
        if (checkingUpdateDateSuppress != null && checkingUpdateDateSuppress.contains(this.getName())) {
            return false;
        }
        TransactionProperties trx = TransactionProperties.getInstance();
        return trx == null || trx.getEntityTimestampCheck();
    }

    @Override
    public Enumeration doFindAll(final Object transaction) throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindAll();
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.pkEnumeration;
    }

    @Override
    public Enumeration doFindByPrimaryKeys(final Vector primaryKeys, final Object transaction) throws GLException {
        if (!this.dbIsShared) {
            HashSet findKeys = new HashSet(primaryKeys);
            if (this.getPrimaryKeyCache().containsAll(findKeys)) {
                return primaryKeys.elements();
            }
        }
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindByPrimaryKeysStatement(primaryKeys);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.pkEnumeration;
    }

    @Override
    public Enumeration doFindMultiple(final String whereClause, final Object[] whereValues, final Object transaction) throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindStatement(whereClause, whereValues);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.pkEnumeration;
    }

    @Override
    public Enumeration doFindMultipleSQL(final String sqlStatement, final Object[] sqlValues, final Object transaction) throws GLException {
        this.dbModify(new DBModifier(this.getConnection()){

            @Override
            public SqlStatement getStatement() throws GLException {
                return BeanManagedEntityBean.this.getFindStatementSQL(sqlStatement, sqlValues);
            }

            @Override
            public boolean execute(SqlStatement sql) throws GLException {
                return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, transaction);
            }

            @Override
            public String getBundledErrorKey() {
                return "Bean_Find_Failure";
            }

            @Override
            public String getBundledErrorDescription() {
                return "Bean_Find_Failure";
            }
        });
        return this.pkEnumeration;
    }

    @Override
    public void ejbStore() throws RemoteException {
        try {
            this.checkUpdateDate();
            super.ejbStore();
        }
        catch (Throwable t) {
            throw RemoteExceptionWrapper.factory(t);
        }
    }

    @Override
    public boolean ejbPostCreator() throws CreateException {
        if (super.ejbPostCreator() && !this.dbIsShared) {
            Pk pk = this.getPrimaryContext();
            this.getPrimaryKeyCache().add(pk);
            if (GLTransactionHelper.isTransactional()) {
                try {
                    GLTransactionHelper.registerSynchronization(new PkCacheCommitListener(this, pk));
                }
                catch (Throwable t) {
                    throw CreateExceptionWrapper.ejbFactory(t);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public void ejbActivate() throws RemoteException {
        super.ejbActivate();
        if (!this.dbIsShared) {
            this.getPrimaryKeyCache().add(this.getPrimaryContext());
        }
    }

    @Override
    public void ejbPassivate() throws RemoteException {
        super.ejbPassivate();
        if (!this.dbIsShared) {
            this.getPrimaryKeyCache().remove(this.getPrimaryContext());
        }
    }

    @Override
    public void ejbRemove() throws RemoveException {
        super.ejbRemove();
        if (!this.dbIsShared) {
            this.getPrimaryKeyCache().remove(this.getPrimaryContext());
        }
    }

    @Override
    public void ejbLoad() throws RemoteException {
        try {
            super.ejbLoad();
        }
        catch (Throwable t) {
            try {
                this.getPrimaryKeyCache().remove(this.getPrimaryContext());
            }
            catch (Throwable t2) {
                GLException.factory(t2);
            }
            throw RemoteExceptionWrapper.ejbFactory(t);
        }
    }

    protected Set getPrimaryKeyCache() {
        throw new AbstractMethodError();
    }

    protected SqlStatement getCreateStatement(Class columnDefClass) throws GLException {
        try {
            Field[] beanFields = this.getBeanFields();
            Object[] beanFieldValues = new Object[beanFields.length];
            for (int i = 0; i < beanFields.length; ++i) {
                beanFieldValues[i] = beanFields[i].get(this);
            }
            String columnDefClassName = columnDefClass.getName();
            String createSql = (String)createCache.get(columnDefClassName);
            if (createSql == null) {
                PkCallback pkCallback = this.getPkCallback();
                Field[] columnFields = Functions.getOrderedFields(columnDefClass);
                StringBuffer sbCreate = new StringBuffer("insert into " + pkCallback.getTableName() + " (");
                for (int i = 0; i < columnFields.length; ++i) {
                    SqlColumn sqlColumn = (SqlColumn)columnFields[i].get(null);
                    if (i > 0) {
                        sbCreate.append(", ");
                    }
                    sbCreate.append(sqlColumn.getName());
                }
                sbCreate.append(") values " + ResultSetUtil.getQuestionMarks(columnFields.length));
                createSql = sbCreate.toString();
                createCache.put(columnDefClassName, createSql);
            }
            UOMInsertFilter filter = new UOMInsertFilter(createSql);
            return new SqlUpdate((UpdateFilter)filter, beanFieldValues, "Bean_Create_Error");
        }
        catch (IllegalAccessException iaex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.Reflection_Failure", null), iaex);
        }
    }

    protected SqlStatement getLoadStatement(Class columnDefClass) throws GLException {
        try {
            PkCallback pkCallback = this.getPkCallback();
            String[] pkFields = pkCallback.getSelectList();
            Object[] pkFieldValues = new Object[pkFields.length];
            Pk pk = this.getPrimaryContext();
            for (int i = 0; i < pkFieldValues.length; ++i) {
                pkFieldValues[i] = this.UOMwhere(pk.getDbValue(i));
            }
            String columnDefClassName = columnDefClass.getName();
            UOMQueryFilter filter = null;
            String loadSql = (String)loadCache.get(columnDefClassName);
            if (loadSql == null) {
                int i;
                Field[] columnFields = Functions.getOrderedFields(columnDefClass);
                StringBuffer sbLoad = new StringBuffer("select ");
                for (i = 0; i < columnFields.length; ++i) {
                    SqlColumn sqlColumn = (SqlColumn)columnFields[i].get(null);
                    if (i > 0) {
                        sbLoad.append(",");
                    }
                    sbLoad.append(sqlColumn.getName());
                }
                if (checkingUpdateDate) {
                    sbLoad.append(", update_date");
                }
                sbLoad.append(" from " + pkCallback.getTableName() + " where ");
                for (i = 0; i < pkFields.length; ++i) {
                    if (i > 0) {
                        sbLoad.append(" and ");
                    }
                    sbLoad.append(pkFields[i] + " = ?");
                }
                loadSql = sbLoad.toString();
                loadCache.put(columnDefClassName, loadSql);
            }
            filter = UOMQueryFilter.get(loadSql);
            return new SqlQuery((QueryFilter)filter, pkFieldValues, "Bean_Load_Error");
        }
        catch (IllegalAccessException ex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.Reflection_Failure", null), ex);
        }
    }

    protected SqlStatement getUpdateDateStatement() throws GLException {
        PkCallback pkCallback = this.getPkCallback();
        String[] pkFields = pkCallback.getSelectList();
        Object[] pkFieldValues = new Object[pkFields.length];
        Pk pk = this.getPK();
        for (int i = 0; i < pkFieldValues.length; ++i) {
            pkFieldValues[i] = this.UOMwhere(pk.getDbValue(i));
        }
        String updateDateSql = (String)updateDateCache.get(this.getClass());
        if (updateDateSql == null) {
            StringBuffer sbUpdateDate = new StringBuffer("select update_date from " + pkCallback.getTableName() + " where ");
            for (int i = 0; i < pkFields.length; ++i) {
                if (i > 0) {
                    sbUpdateDate.append(" and ");
                }
                sbUpdateDate.append(pkFields[i] + " = ?");
            }
            updateDateSql = sbUpdateDate.toString();
            updateDateCache.put(this.getClass(), updateDateSql);
        }
        UOMQueryFilter filter = UOMQueryFilter.get(updateDateSql);
        return new SqlQuery((QueryFilter)filter, pkFieldValues, "Bean_Load_Error");
    }

    protected boolean executeLoad(T2SharedConnection connection, SqlStatement sql) throws GLException {
        try {
            SqlQuery q = (SqlQuery)sql;
            q.open(connection.get());
            if (!q.next()) {
                boolean bl = inReload.get() != null;
                return bl;
            }
            int length = q.getInitialFieldCount();
            if (checkingUpdateDate) {
                this.lastLoaded = q.getLocalTimestamp(length);
                --length;
            }
            Field[] beanFields = this.getBeanFields();
            for (int i = 0; i < length; ++i) {
                Object obj = q.getObject(i + 1);
                beanFields[i].set(this, obj);
            }
            this.transaction = this.getPrimaryContext().getTransaction();
            boolean bl = true;
            return bl;
        }
        catch (IllegalAccessException ex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.Reflect_Failure", null), ex);
        }
        finally {
            sql.close();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected boolean executeUpdateDate(T2SharedConnection connection, SqlStatement sql) throws GLException {
        try {
            SqlQuery q = (SqlQuery)sql;
            q.open(connection.get());
            if (!q.next()) {
                boolean bl = false;
                return bl;
            }
            this.updateDate = q.getLocalTimestamp(1);
            boolean bl = true;
            return bl;
        }
        finally {
            sql.close();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected boolean executeUpdate(T2SharedConnection connection, SqlStatement sql, boolean getUpdateDate) throws GLException {
        try {
            SqlUpdate u = (SqlUpdate)sql;
            if (u.execute(connection.get()) == 1) {
                if (checkingUpdateDate && getUpdateDate) {
                    this.lastLoaded = this.getUpdateDate();
                }
                boolean bl = true;
                return bl;
            }
            boolean bl = false;
            return bl;
        }
        finally {
            sql.close();
        }
    }

    protected SqlStatement getStoreStatement(Class columnDefClass) throws GLException {
        try {
            int i;
            PkCallback pkCallback = this.getPkCallback();
            String[] pkFields = pkCallback.getSelectList();
            Pk pk = this.getPrimaryContext();
            Field[] beanFields = this.getBeanFields();
            Field[] columnFields = Functions.getOrderedFields(columnDefClass);
            Object[] beanFieldValues = new Object[columnFields.length];
            for (i = 0; i < columnFields.length - pkFields.length; ++i) {
                beanFieldValues[i] = beanFields[i + pkFields.length].get(this);
            }
            int j = 0;
            while (i < columnFields.length) {
                beanFieldValues[i] = this.UOMwhere(pk.getDbValue(j));
                ++i;
                ++j;
            }
            String columnDefClassName = columnDefClass.getName();
            UOMUpdateFilter filter = null;
            String storeSql = (String)storeCache.get(columnDefClassName);
            if (storeSql == null) {
                StringBuffer sbStore = new StringBuffer("update " + pkCallback.getTableName() + " set ");
                for (i = pkFields.length; i < columnFields.length; ++i) {
                    SqlColumn sqlColumn = (SqlColumn)columnFields[i].get(null);
                    if (i > pkFields.length) {
                        sbStore.append(", ");
                    }
                    sbStore.append(sqlColumn.getName() + " = ?");
                }
                sbStore.append(" where ");
                for (i = 0; i < pkFields.length; ++i) {
                    if (i > 0) {
                        sbStore.append(" and ");
                    }
                    sbStore.append(pkFields[i] + " = ?");
                }
                storeSql = sbStore.toString();
                storeCache.put(columnDefClassName, storeSql);
            }
            filter = UOMUpdateFilter.get(storeSql);
            return new SqlUpdate((UpdateFilter)filter, beanFieldValues, "Bean_Store_Error");
        }
        catch (IllegalAccessException iaex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.Reflection_Failure", null), iaex);
        }
    }

    protected SqlStatement getRemoveStatement(Class columnDefClass) throws GLException {
        Object sql = null;
        PkCallback pkCallback = this.getPkCallback();
        String[] pkFields = pkCallback.getSelectList();
        StringBuffer sbRemove = new StringBuffer("delete from " + pkCallback.getTableName() + " where ");
        for (int i = 0; i < pkFields.length; ++i) {
            if (i > 0) {
                sbRemove.append(" and ");
            }
            sbRemove.append(pkFields[i] + " = ?");
        }
        Pk pk = this.getPrimaryContext();
        Object[] pkFieldValues = new Object[pkFields.length];
        for (int i = 0; i < pkFieldValues.length; ++i) {
            pkFieldValues[i] = this.UOMwhere(pk.getDbValue(i));
        }
        return new SqlUpdate((UpdateFilter)NoUpdateFilter.get(sbRemove.toString()), pkFieldValues, "Bean_Remove_Error");
    }

    protected SqlStatement getFindStatement(String whereClause, Object[] whereValues) throws GLException {
        String strFind = this.getFindSelect();
        if (whereClause != null) {
            strFind = strFind + " where " + whereClause;
        }
        return this.getFindStatementSQL(strFind, whereValues);
    }

    protected SqlStatement getFindStatementSQL(String sqlStatement, Object[] sqlValues) throws GLException {
        return new SqlQuery((QueryFilter)UOMQueryFilter.get(sqlStatement), sqlValues, "Bean_FindSQL_Error");
    }

    protected SqlStatement getFindByPrimaryKeyStatement(Pk primaryKey) throws GLException {
        PkCallback pkCallback = this.getPkCallback();
        String[] pkFields = pkCallback.getSelectList();
        StringBuffer whereClause = new StringBuffer();
        Object[] whereValues = new Object[pkFields.length];
        for (int i = 0; i < pkFields.length; ++i) {
            if (i > 0) {
                whereClause.append(" and ");
            }
            whereClause.append(pkFields[i] + " = ?");
            whereValues[i] = this.UOMwhere(primaryKey.getDbValue(i));
        }
        return this.getFindStatement(whereClause.toString(), whereValues);
    }

    protected SqlStatement getFindAll() throws GLException {
        return this.getFindStatement(null, null);
    }

    protected SqlStatement getFindByPrimaryKeysStatement(Vector primaryKeys) throws GLException {
        PkCallback pkCallback = this.getPkCallback();
        String[] pkFields = pkCallback.getSelectList();
        StringBuffer whereClause = new StringBuffer();
        int pkSize = primaryKeys.size();
        Object[] whereValues = new Object[pkFields.length * pkSize];
        for (int i = 0; i < pkSize; ++i) {
            if (i > 0) {
                whereClause.append(" or ");
            }
            whereClause.append("(");
            Pk primaryKey = (Pk)primaryKeys.elementAt(i);
            for (int j = 0; j < pkFields.length; ++j) {
                if (j > 0) {
                    whereClause.append(" and ");
                }
                whereClause.append(pkFields[j] + " = ?");
                whereValues[i * pkFields.length + j] = this.UOMwhere(primaryKey.getDbValue(j));
            }
            whereClause.append(")");
        }
        return this.getFindStatement(whereClause.toString(), whereValues);
    }

    protected String getFindSelect() {
        PkCallback pkCallback = this.getPkCallback();
        String[] pkFields = pkCallback.getSelectList();
        StringBuffer sbFind = new StringBuffer("select ");
        for (int i = 0; i < pkFields.length; ++i) {
            if (i > 0) {
                sbFind.append(",");
            }
            sbFind.append(pkFields[i]);
        }
        sbFind.append(" from " + pkCallback.getTableName());
        return sbFind.toString();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected boolean executeFind(T2SharedConnection connection, SqlStatement sql, Object transaction) throws GLException {
        try (SqlQuery q = (SqlQuery)sql;){
            q.open(connection.get());
            if (!q.next()) {
                throw new GLNoMoreRecords(this.getTable());
            }
            PkCallback pkCallback = this.getPkCallback();
            this.setPK(pkCallback.getPk(q, false, transaction));
            boolean bl = true;
            return bl;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected boolean executeFindAll(T2SharedConnection connection, SqlStatement sql, Object transaction) throws GLException {
        try (SqlQuery q = (SqlQuery)sql;){
            q.open(connection.get());
            Vector<Pk> foundRecords = new Vector<Pk>();
            PkCallback pkCallback = this.getPkCallback();
            while (q.next()) {
                foundRecords.addElement(pkCallback.getPk(q, false, transaction));
            }
            this.pkEnumeration = foundRecords.elements();
            if (foundRecords.size() == 0) {
                throw new GLNoMoreRecords(this.getTable());
            }
            boolean bl = true;
            return bl;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    protected void dbModify(DBModifier dbModifier) throws GLException {
        try {
            String useCase = dbModifier.getUseCase();
            Retry retry = useCase != null ? BeanManagedEntityBean.getRetry(useCase) : null;
            int numRetries = retry != null ? retry.numRetries : 0;
            int retryMillis = retry != null ? retry.retryMillis : 0;
            dbModifier.openConnection();
            SqlStatement sql = dbModifier.getStatement();
            boolean ok = false;
            int iter = 0;
            while (!ok) {
                ok = dbModifier.execute(sql);
                if (ok) continue;
                GLException.CausedBy cause = new GLException.CausedBy(dbModifier.getBundledErrorKey(), dbModifier.getBundledErrorDescription(), new Object[][]{{"cachedUser", dbModifier.getConnection().getCachedUser()}, {"class", this.getClass()}, {"currentUserName", SecurityUtil.getCurrentUserName()}, {"primaryContext", this.getPrimaryContext()}, {"vpdUser", dbModifier.getConnection().getDatabaseUser()}});
                if (iter >= numRetries) {
                    dbModifier.noRecords();
                    throw GLException.factory((Cause)cause, null);
                }
                new GLUnloggedException(cause).log(1, false, LogIds.EXCEPTION);
                Log.logID(LogIds.EXCEPTION, String.format("%s failed: retry #%d after %d ms delay", useCase, ++iter, retryMillis));
                try {
                    Thread.sleep(retryMillis);
                }
                catch (InterruptedException ie) {
                    throw GLException.factory(ie);
                    return;
                }
            }
        }
        finally {
            dbModifier.closeConnection();
        }
    }

    protected void checkForReadOnly() throws GLException {
        if (this.transaction != null) {
            Pk pk = this.getPK();
            String bean = pk.getPkCallback().getTableName();
            throw GLException.factory((Cause)new GLException.CausedBy("cause.DBClassBuilderReadOnlyBean", null, new Object[][]{{"bean", bean}, {"pk", pk}, {"transaction", this.transaction}}), null);
        }
    }

    private Object UOMwhere(Object pkValue) {
        return pkValue instanceof TypeUnit ? ((TypeUnit)pkValue).getDBUnits() : pkValue;
    }

    public String toString() {
        String eol = System.getProperty("line.separator");
        StringBuffer sb = new StringBuffer("bean:" + eol);
        Field[] fields = this.getBeanFields();
        for (int i = 0; i < fields.length; ++i) {
            try {
                sb.append(fields[i].getName() + " = " + fields[i].get(this) + eol);
                continue;
            }
            catch (Exception e) {
                // empty catch block
            }
        }
        return sb.toString();
    }

    protected void removeAudit(String auditType) throws GLException {
        AuditCleanup.removeAudit(auditType, this.getPK(), this.getConnection());
    }

    protected void removeDocuments() throws GLException {
        Pk pk = this.getPK();
        Set dataQueryTypes = DataQueryTypeMap.getDataQueryTypes(pk.getClass());
        for (String dataQueryType : dataQueryTypes) {
            BeanManagedEntityBean.remove("ejb.Document", "findByOwner", new Class[]{String.class, String.class}, new Object[]{dataQueryType, pk.toString()});
        }
    }

    protected static void remove(String beanName, String findMethod, Class[] classes, Object[] args) throws GLException {
        NamingDirectory nd = NamingDirectory.get();
        try {
            Object homeIntf = nd.lookup(beanName);
            Method finder = homeIntf.getClass().getMethod(findMethod, classes);
            Enumeration e = (Enumeration)finder.invoke(homeIntf, args);
            while (e.hasMoreElements()) {
                ((EJBObject)e.nextElement()).remove();
            }
        }
        catch (InvocationTargetException ite) {
            Throwable t = ite.getTargetException();
            if (!(t instanceof FinderNoMoreRecords)) {
                throw GLException.factory(t);
            }
        }
        catch (RemoveExceptionWrapper rew) {
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
        finally {
            nd.release();
        }
    }

    protected static <T extends Pk> void removeChildren(Class childHomeInterface, String finder, Class[] finderParameters, Object[] finderArguments, OnRemove onRemove, String parentDescription, String childDescription, LogId logId, T pk) throws GLException {
        try {
            Method finderMethod = childHomeInterface.getMethod(finder, finderParameters);
            Field nameField = childHomeInterface.getField("NAME");
            NamingDirectory nd = NamingDirectory.get();
            try {
                EJBHome home = (EJBHome)nd.lookup((String)nameField.get(null));
                if (Log.idOn[logId.index]) {
                    Log.logID(logId, "Begin remove {1} from {0}: {2}", parentDescription, childDescription, pk);
                }
                Enumeration e = (Enumeration)finderMethod.invoke((Object)home, finderArguments);
                while (e.hasMoreElements()) {
                    EJBObject bean = (EJBObject)e.nextElement();
                    if (onRemove != null) {
                        onRemove.callback(bean);
                    }
                    bean.remove();
                    if (!Log.idOn[logId.index]) continue;
                    Log.logID(logId, "Remove {1} element for {0} ", finderArguments[0], childDescription);
                }
            }
            catch (InvocationTargetException ite) {
                if (ite.getTargetException() instanceof FinderNoMoreRecords) {
                    return;
                }
                throw ite;
            }
            finally {
                nd.release();
            }
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(new GLException.CausedBy("cause.RemoveChildError", null, new Object[][]{{"childDescription", childDescription}, {"parentDescription", parentDescription}, {"pk", pk}}), t);
        }
    }

    public static Retry getRetry(String useCase) {
        return (Retry)retries.get(useCase);
    }

    private static void initRetries() {
        retries.clear();
        Iterator<GLProperties.Entry> it = GLProperties.get().propertyEntries("glog.bean.retries.");
        while (it.hasNext()) {
            GLProperties.Entry e = it.next();
            String useCase = e.getKey();
            String v = e.getValue();
            StringTokenizer st = new StringTokenizer(v, ",");
            int retryMillis = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
            int numRetries = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
            if (numRetries <= 0) continue;
            retries.put(useCase, new Retry(numRetries, retryMillis));
        }
    }

    static {
        try {
            Iterator<GLProperties.Entry> it = GLProperties.get().entries("glog.ejb.checkingUpdateDateSuppress");
            while (it.hasNext()) {
                if (checkingUpdateDateSuppress == null) {
                    checkingUpdateDateSuppress = new HashSet();
                }
                GLProperties.Entry entry = it.next();
                checkingUpdateDateSuppress.add(entry.getValue());
            }
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
        retries = new HashMap();
        BeanManagedEntityBean.initRetries();
        GLProperties.get();
        GLProperties.addListener("glog.bean.retries.", new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) {
                BeanManagedEntityBean.initRetries();
            }
        });
    }

    public static class Retry {
        public int numRetries;
        public int retryMillis;

        public Retry(int numRetries, int retryMillis) {
            this.numRetries = numRetries;
            this.retryMillis = retryMillis;
        }
    }

    public static interface OnRemove {
        public void callback(EJBObject var1) throws GLException;
    }

    private static class PkCacheCommitListener
    implements FIFOSynchronization {
        private BeanManagedEntityBean entity;
        private Pk pk;

        public PkCacheCommitListener(BeanManagedEntityBean entity, Pk pk) {
            this.entity = entity;
            this.pk = pk;
        }

        public void beforeCompletion() {
        }

        public void afterCompletion(int rollback) {
            if (rollback != 3) {
                this.entity.getPrimaryKeyCache().remove(this.pk);
            }
        }
    }

    public abstract class DBModifier {
        protected T2SharedConnection conn;

        public DBModifier(T2SharedConnection conn) {
            this.conn = conn;
        }

        public void openConnection() throws GLException {
            this.conn.open();
        }

        public void closeConnection() throws GLException {
            this.conn.close();
        }

        public T2SharedConnection getConnection() throws GLException {
            return this.conn;
        }

        public String getUseCase() {
            return null;
        }

        public void noRecords() throws GLException {
        }

        abstract SqlStatement getStatement() throws GLException;

        abstract boolean execute(SqlStatement var1) throws GLException;

        abstract String getBundledErrorKey();

        abstract String getBundledErrorDescription();
    }
}
