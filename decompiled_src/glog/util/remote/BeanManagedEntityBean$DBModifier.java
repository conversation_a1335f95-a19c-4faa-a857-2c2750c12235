/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;

public abstract class BeanManagedEntityBean.DBModifier {
    protected T2SharedConnection conn;

    public BeanManagedEntityBean.DBModifier(T2SharedConnection conn) {
        this.conn = conn;
    }

    public void openConnection() throws GLException {
        this.conn.open();
    }

    public void closeConnection() throws GLException {
        this.conn.close();
    }

    public T2SharedConnection getConnection() throws GLException {
        return this.conn;
    }

    public String getUseCase() {
        return null;
    }

    public void noRecords() throws GLException {
    }

    abstract SqlStatement getStatement() throws GLException;

    abstract boolean execute(SqlStatement var1) throws GLException;

    abstract String getBundledErrorKey();

    abstract String getBundledErrorDescription();
}
