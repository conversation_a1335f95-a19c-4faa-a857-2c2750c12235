/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import java.lang.reflect.Method;

static class EntityBeanHomeInvoker.EntityBeanMethods {
    public Method create;
    public Method findByPrimaryKey;
    public Method findAll;
    public Method findByPrimaryKeys;
    public Method findInCache;
    public Method onCreate;
    public Method onUpdate;
    public Method onRemove;
    public Method listBeansInCache;
    public Method listLocks;
    public Method unlock;
    public Method getDataNoLock;
    public Method getLockData;
    public Method getMaxCacheSize;
    public Method updateMaxCacheSize;

    public EntityBeanHomeInvoker.EntityBeanMethods(Method create, Method findByPrimaryKey, Method findAll, Method findByPrimaryKeys, Method findInCache, Method onCreate, Method onUpdate, Method onRemove, Method listBeansInCache, Method listLocks, Method unlock, Method getDataNoLock, Method getLockData, Method getMaxCacheSize, Method updateMaxCacheSize) {
        this.create = create;
        this.findByPrimaryKey = findByPrimaryKey;
        this.findAll = findAll;
        this.findByPrimaryKeys = findByPrimaryKeys;
        this.findInCache = findInCache;
        this.onCreate = onCreate;
        this.onUpdate = onUpdate;
        this.onRemove = onRemove;
        this.listBeansInCache = listBeansInCache;
        this.listLocks = listLocks;
        this.unlock = unlock;
        this.getDataNoLock = getDataNoLock;
        this.getLockData = getLockData;
        this.getMaxCacheSize = getMaxCacheSize;
        this.updateMaxCacheSize = updateMaxCacheSize;
    }
}
