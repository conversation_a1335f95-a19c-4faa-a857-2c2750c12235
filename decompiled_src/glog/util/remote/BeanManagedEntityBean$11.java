/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;

class BeanManagedEntityBean.11
extends BeanManagedEntityBean.DBModifier {
    final /* synthetic */ String val$whereClause;
    final /* synthetic */ Object[] val$whereValues;
    final /* synthetic */ Object val$transaction;

    BeanManagedEntityBean.11(T2SharedConnection x0, String string, Object[] objectArray, Object object) {
        this.val$whereClause = string;
        this.val$whereValues = objectArray;
        this.val$transaction = object;
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getFindStatement(this.val$whereClause, this.val$whereValues);
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, this.val$transaction);
    }

    @Override
    public String getBundledErrorKey() {
        return "Bean_Find_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Bean_Find_Failure";
    }
}
