/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.lang.reflect.Field;

public final class CodeGenClassConverter {
    public static String getBeanNameFromPk(Pk pk) throws GLException {
        try {
            String className = pk.getClass().getName();
            className = className.substring(0, className.length() - 2) + "HomeDB";
            Class<?> c = Class.forName(className);
            Field nameField = c.getField("NAME");
            return (String)nameField.get(null);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static String getBeanNameFromDataClass(Class dataClass) throws GLException {
        try {
            String className = dataClass.getName();
            className = className.substring(0, className.length() - 4) + "HomeDB";
            Class<?> c = Class.forName(className);
            Field nameField = c.getField("NAME");
            return (String)nameField.get(null);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static String getDataClassNameFromPk(Pk pk) {
        String pkClassName = pk.getClass().getName();
        return pkClassName.substring(0, pkClassName.length() - 2) + "Data";
    }

    public static Class getDataClassFromPk(Pk pk) throws GLException {
        try {
            return Class.forName(CodeGenClassConverter.getDataClassNameFromPk(pk));
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static String getBeanClassNameFromPk(Pk pk) {
        String pkClassName = pk.getClass().getName();
        int ix = pkClassName.lastIndexOf(46);
        String pkPackage = pkClassName.substring(0, ix);
        String pkName = pkClassName.substring(ix + 1);
        ix = pkPackage.lastIndexOf(46);
        String beanPackage = pkPackage.substring(0, ix);
        String beanName = pkName.substring(0, pkName.length() - 2) + "Bean";
        return beanPackage + "." + beanName;
    }

    public static Class getBeanClassFromPk(Pk pk) throws GLException {
        try {
            return Class.forName(CodeGenClassConverter.getBeanClassNameFromPk(pk));
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }
}
