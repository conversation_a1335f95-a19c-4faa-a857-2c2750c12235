/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.5
implements BaseEntityBean.EjbFunctor {
    final /* synthetic */ Pk val$primaryKey;
    final /* synthetic */ Object val$transaction;

    BaseEntityBean.5(Pk pk, Object object) {
        this.val$primaryKey = pk;
        this.val$transaction = object;
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preFind();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.setPK(BaseEntityBean.this.doFindByPrimaryKey(this.val$primaryKey, this.val$transaction));
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postFind();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
