/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLCreateException;
import glog.util.exception.NoExceptionLogging;
import glog.util.jdbc.Pk;
import glog.util.remote.CreateDuplicateRecord;
import java.sql.SQLException;

public class GLDuplicateRecord
extends GLCreateException
implements NoExceptionLogging {
    public GLDuplicateRecord(String table, Pk primaryKey, SQLException sqlex) {
        super(new Cause("cause.Duplicate_Record_Found", null, new Object[][]{{"primaryKey", primaryKey}, {"table", table}}), (Throwable)sqlex);
    }

    public GLDuplicateRecord(String table, Pk primaryKey) {
        this(table, primaryKey, null);
    }

    public GLDuplicateRecord(Cause cause) {
        this(cause, null);
    }

    public GLDuplicateRecord(Cause cause, Throwable t) {
        super(cause, t);
    }

    @Override
    protected Throwable toCreateException() {
        return new CreateDuplicateRecord(this);
    }
}
