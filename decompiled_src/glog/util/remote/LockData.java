/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.transaction.Transaction
 */
package glog.util.remote;

import glog.util.Functions;
import glog.util.jdbc.Pk;
import java.io.Serializable;
import java.util.ArrayList;
import javax.transaction.Transaction;

public class LockData
implements Serializable,
Comparable {
    private Pk pk;
    private String owner;
    private String ownerId;
    private long secondsSinceBegin;
    private String process;
    private String activeThread;
    private String[] waiters;

    public LockData(Pk pk, Object owner) {
        this.pk = pk;
        this.owner = owner != null ? owner.toString() : null;
        this.ownerId = owner != null ? owner.toString() : null;
    }

    public LockData(Pk pk, Object owner, String ownerId, long millisSinceBegin, Serializable process, Thread activeThread, Object[] waiters) {
        this.pk = pk;
        this.owner = LockData.toShortName(owner);
        this.ownerId = ownerId;
        this.secondsSinceBegin = millisSinceBegin / 1000L;
        this.process = process != null ? process.toString() : null;
        this.activeThread = activeThread != null ? activeThread.getName() : null;
        this.waiters = this.convert(waiters);
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LockData)) {
            return false;
        }
        LockData other = (LockData)o;
        return Functions.equals(this.pk, other.pk);
    }

    public int hashCode() {
        return this.pk.hashCode();
    }

    public int compareTo(Object o) {
        if (this == o) {
            return 0;
        }
        if (!(o instanceof LockData)) {
            return 1;
        }
        LockData other = (LockData)o;
        return Functions.compareTo(this.pk, other.pk);
    }

    public final Pk getPK() {
        return this.pk;
    }

    public final String getOwner() {
        return this.owner;
    }

    public final String getOwnerId() {
        return this.ownerId;
    }

    public final long getSecondsSinceBegin() {
        return this.secondsSinceBegin;
    }

    public final String getProcess() {
        return this.process;
    }

    public final String getActiveThread() {
        return this.activeThread;
    }

    public final String[] getWaiters() {
        return this.waiters;
    }

    final String[] convert(Object[] waiters) {
        ArrayList<String> waitersList = new ArrayList<String>();
        if (waiters != null && waiters.length > 0) {
            for (int k = 0; k < waiters.length; ++k) {
                Object waiter = waiters[k];
                if (!(waiter instanceof Transaction)) continue;
                Transaction tx = (Transaction)waiter;
                waitersList.add(LockData.toShortName(tx) + ":" + tx.hashCode());
            }
        }
        return waitersList.toArray(new String[0]);
    }

    static String toShortName(Object obj) {
        if (obj == null) {
            return null;
        }
        if (!(obj instanceof Transaction)) {
            return obj.toString();
        }
        Transaction tx = (Transaction)obj;
        String name = tx.toString();
        int index = name.indexOf("(");
        if (index < 0) {
            return name;
        }
        return name.substring(0, index) + ":" + tx.hashCode();
    }
}
