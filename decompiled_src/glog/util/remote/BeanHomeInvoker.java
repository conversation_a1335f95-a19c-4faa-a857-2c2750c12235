/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanBaseInvoker;
import glog.util.remote.BeanData;
import java.lang.reflect.Method;

public class BeanHomeInvoker
extends BeanBaseInvoker {
    private Method create;
    private Method findByPrimaryKey;
    private Object beanHome;
    private Class beanDataClass;

    public BeanHomeInvoker(Object beanHome, Class beanDataClass, Class beanPkClass) {
        try {
            this.beanHome = beanHome;
            this.beanDataClass = beanDataClass;
            Class<?> beanClass = beanHome.getClass();
            this.create = beanClass.getMethod("create", beanDataClass);
            this.findByPrimaryKey = beanClass.getMethod("findByPrimaryKey", beanPkClass);
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function");
        }
    }

    public Object create(BeanData data) throws GLException {
        return this.invoke(this.create, this.beanHome, new Object[]{data});
    }

    public Object findByPrimaryKey(Pk pk) throws GLException {
        return this.invoke(this.findByPrimaryKey, this.beanHome, new Object[]{pk});
    }

    public BeanData newBeanData() throws GLException {
        try {
            return (BeanData)this.beanDataClass.newInstance();
        }
        catch (Throwable ex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.BeanDataCreation_Failure", null), ex);
        }
    }
}
