/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.jdbc.Pk;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.transaction.FIFOSynchronization;

private static class BeanManagedEntityBean.PkCacheCommitListener
implements FIFOSynchronization {
    private BeanManagedEntityBean entity;
    private Pk pk;

    public BeanManagedEntityBean.PkCacheCommitListener(BeanManagedEntityBean entity, Pk pk) {
        this.entity = entity;
        this.pk = pk;
    }

    public void beforeCompletion() {
    }

    public void afterCompletion(int rollback) {
        if (rollback != 3) {
            this.entity.getPrimaryKeyCache().remove(this.pk);
        }
    }
}
