/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanBaseInvoker;
import glog.util.remote.BeanData;
import java.lang.reflect.Method;
import java.util.Vector;

public class BeanInvoker
extends BeanBaseInvoker {
    private Method setData;
    private Method getData;
    private Method setLinks;
    private Method getPK;
    private Method remove;
    private Object beanRemote;

    public BeanInvoker(Object beanRemote, Class beanDataClass) {
        try {
            this.beanRemote = beanRemote;
            Class<?> beanClass = beanRemote.getClass();
            this.setData = beanClass.getMethod("setData", beanDataClass);
            this.getData = beanClass.getMethod("getData", emptyClasses);
            this.setLinks = beanClass.getMethod("setLinks", Vector.class, Vector.class);
            this.getPK = beanClass.getMethod("getPK", emptyClasses);
            this.remove = beanClass.getMethod("remove", emptyClasses);
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function");
        }
    }

    public void setData(BeanData data) throws GLException {
        this.invoke(this.setData, this.beanRemote, new Object[]{data});
    }

    public BeanData getData() throws GLException {
        return (BeanData)this.invoke(this.getData, this.beanRemote, emptyArgs);
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
        this.invoke(this.setLinks, this.beanRemote, new Object[]{pksToAdd, pksToRemove});
    }

    public Pk getPK() throws GLException {
        return (Pk)this.invoke(this.getPK, this.beanRemote, emptyArgs);
    }

    public void remove() throws GLException {
        this.invoke(this.remove, this.beanRemote, emptyArgs);
    }
}
