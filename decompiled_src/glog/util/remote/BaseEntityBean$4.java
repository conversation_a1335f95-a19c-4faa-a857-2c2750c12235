/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.4
implements BaseEntityBean.EjbFunctor {
    BaseEntityBean.4() {
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.markedForCacheRemove ? false : BaseEntityBean.this.preRemove();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.doRemove();
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postRemove();
    }

    @Override
    public void broadcast() throws GLException {
        BaseEntityBean.this.broadcastRemove();
    }
}
