/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.remote.InvokerExceptionMapper;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

abstract class BeanBaseInvoker {
    protected static final Object[] emptyArgs = new Object[0];
    protected static final Class[] emptyClasses = new Class[0];

    BeanBaseInvoker() {
    }

    protected Object invoke(Method method, Object thisBean, Object[] arguments) throws GLException {
        try {
            return method.invoke(thisBean, arguments);
        }
        catch (InvocationTargetException itex) {
            throw InvokerExceptionMapper.handle(itex);
        }
        catch (IllegalAccessException iaex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.BeanInvoker_InvalidMethod", null, new Object[][]{{"method", method}}), null);
        }
        catch (IllegalArgumentException iaex) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.BeanInvoker_InvalidArgument", null, arguments), null);
        }
    }
}
