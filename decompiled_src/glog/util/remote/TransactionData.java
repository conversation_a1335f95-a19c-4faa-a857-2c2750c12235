/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.DOMGenerator;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.util.j2ee.remote.ASManagement;
import glog.util.jdbc.Pk;
import glog.util.remote.LockData;
import glog.webserver.util.XMLHelper;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;
import javax.transaction.xa.Xid;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

public class TransactionData
implements Serializable,
Comparable {
    private String name;
    private Xid xid;
    private String status;
    private long duration;
    private long left;
    private String topic;
    private String thread;
    private ArrayList locks;
    private static final String lineSeparator = System.getProperty("line.separator");
    private static final String TAB_CHAR = "    ";
    private static final String TRANSACTIONS_XML_MAJOR_VERSION = "5.5";
    private static final String TRANSACTIONS_XML_MINOR_VERSION = "01";

    public TransactionData(String name, Xid xid, String status, long duration, long left, String topic, String thread) {
        this.name = name;
        this.xid = xid;
        this.status = status;
        this.duration = duration;
        this.left = left;
        this.topic = topic;
        this.thread = thread;
        this.locks = new ArrayList();
    }

    public String getName() {
        return this.name;
    }

    public Xid getXid() {
        return this.xid;
    }

    public String getStatus() {
        return this.status;
    }

    public long getDuration() {
        return this.duration;
    }

    public long getLeft() {
        return this.left;
    }

    public String getTopic() {
        return this.topic;
    }

    public String getActiveThreadName() {
        return this.thread;
    }

    public List getLocks() {
        return this.locks;
    }

    public void add(LockData lockData) {
        this.locks.add(lockData);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof TransactionData)) {
            return false;
        }
        TransactionData other = (TransactionData)obj;
        Xid otherXid = other.getXid();
        if (this.xid == null && otherXid == null) {
            return Functions.equals(this.name, other.getName());
        }
        return Functions.equals(this.xid, otherXid);
    }

    public int hasCode() {
        if (this.xid != null) {
            return this.xid.hashCode();
        }
        if (this.name != null) {
            return this.name.hashCode();
        }
        return -1;
    }

    public String toString() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        this.diagnose(pw, "");
        return sw.toString();
    }

    public synchronized void diagnose(PrintWriter os, String tab) {
        StringBuffer sb = new StringBuffer();
        os.print(tab + "TransactionData: name=" + this.name + ",");
        os.print("status=" + this.status + ",");
        os.print("duration=" + this.duration + ",");
        os.print("topic=" + this.topic + ",");
        os.print("left=" + this.left + ",");
        os.println("thread=" + this.thread);
        os.println(tab + "locks=[");
        tab = tab + TAB_CHAR;
        int size = this.locks.size();
        for (int i = 0; i < size; ++i) {
            LockData lockData = (LockData)this.locks.get(i);
            Pk pk = lockData.getPK();
            os.println(tab + TransactionData.getBeanName(pk) + " " + pk + lineSeparator);
            String[] waiters = lockData.getWaiters();
            for (int k = 0; k < waiters.length; ++k) {
                os.println(tab + TAB_CHAR + waiters[k] + " waiting" + lineSeparator);
            }
        }
        os.println(tab + "]");
    }

    public static void diagnoseAll(PrintWriter os, String tab) throws GLException {
        os.println(tab + "server_time=" + TransactionData.now());
        os.println(tab + "version=" + TRANSACTIONS_XML_MAJOR_VERSION + "." + TRANSACTIONS_XML_MINOR_VERSION);
        tab = tab + TAB_CHAR;
        List txDatas = ASManagement.getTransactionDatas();
        for (TransactionData txData : txDatas) {
            txData.diagnose(os, tab);
        }
    }

    public static String diagnose() throws GLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        TransactionData.diagnoseAll(pw, "");
        return sw.toString();
    }

    public void diagnoseXML(Document d, Element topLevelElement) {
        Node transactionElem = DOMGenerator.insertElement(d, "transaction", topLevelElement);
        DOMGenerator.insertTagValue(d, "name", transactionElem, this.name);
        DOMGenerator.insertTagValue(d, "status", transactionElem, this.status);
        DOMGenerator.insertTagValue(d, "duration", transactionElem, new Long(this.duration));
        DOMGenerator.insertTagValue(d, "left", transactionElem, new Long(this.left));
        DOMGenerator.insertTagValue(d, "topic", transactionElem, this.topic);
        DOMGenerator.insertTagValue(d, "thread", transactionElem, this.thread);
        Node locksElem = DOMGenerator.insertElement(d, "locks", transactionElem);
        int size = this.locks.size();
        for (int i = 0; i < size; ++i) {
            LockData lockData = (LockData)this.locks.get(i);
            Pk pk = lockData.getPK();
            long time = lockData.getSecondsSinceBegin();
            Node lockElem = DOMGenerator.insertElement(d, "lock", locksElem);
            DOMGenerator.insertTagValue(d, "name", lockElem, TransactionData.getBeanName(pk));
            DOMGenerator.insertTagValue(d, "value", lockElem, pk);
            String[] waiters = lockData.getWaiters();
            Node waitersElem = DOMGenerator.insertElement(d, "waiters", lockElem);
            DOMGenerator.insertTagValue(d, "numWaiters", lockElem, new Integer(waiters.length));
            for (int k = 0; k < waiters.length; ++k) {
                Node waiterElem = DOMGenerator.insertElement(d, "waiter", waitersElem);
                waiterElem.appendChild(d.createTextNode(waiters[k]));
            }
            DOMGenerator.insertTagValue(d, "duration", lockElem, new Long(time));
        }
    }

    public static void diagnoseXML(Document d) throws GLException {
        Element topLevelElement = d.createElement("diagnostics");
        d.appendChild(topLevelElement);
        DOMGenerator.insertTypeUnit(d, "server_time", topLevelElement, TransactionData.now());
        Node versionElement = DOMGenerator.insertElement(d, "version", topLevelElement);
        DOMGenerator.insertTagValue(d, "major", versionElement, TRANSACTIONS_XML_MAJOR_VERSION);
        DOMGenerator.insertTagValue(d, "minor", versionElement, TRANSACTIONS_XML_MINOR_VERSION);
        List txDatas = ASManagement.getTransactionDatas();
        for (TransactionData txData : txDatas) {
            txData.diagnoseXML(d, topLevelElement);
        }
    }

    public static String diagnoseXML() throws GLException {
        XMLHelper xmlHelper = XMLHelper.instance();
        Document d = xmlHelper.createEmptyXMLDocument();
        TransactionData.diagnoseXML(d);
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        xmlHelper.display(d, (Writer)pw);
        return sw.toString();
    }

    public static String getBeanName(Pk pk) {
        String pkName = pk.getClass().getName();
        int index0 = pkName.lastIndexOf(46);
        int index1 = pkName.lastIndexOf("PK");
        return pkName.substring(index0 + 1, index1);
    }

    public int compareTo(Object o) {
        if (this == o) {
            return 0;
        }
        if (!(o instanceof TransactionData)) {
            return 1;
        }
        TransactionData other = (TransactionData)o;
        long value = this.duration - other.getDuration();
        if (value > 0L) {
            return -1;
        }
        if (value < 0L) {
            return 1;
        }
        String otherThread = other.getActiveThreadName();
        if (this.thread == null) {
            return -1;
        }
        if (otherThread == null) {
            return 1;
        }
        return this.thread.compareTo(otherThread);
    }

    private static LocalTimestamp now() {
        LocalTimestamp result = new LocalTimestamp();
        result.setTimeZone(LocalTimestamp.getDisplayTimeZone());
        return result;
    }
}
