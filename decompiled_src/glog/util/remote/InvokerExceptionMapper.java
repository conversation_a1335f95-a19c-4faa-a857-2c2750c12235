/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 *  javax.ejb.RemoveException
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import java.lang.reflect.InvocationTargetException;
import java.rmi.RemoteException;
import java.util.Hashtable;
import javax.ejb.CreateException;
import javax.ejb.FinderException;
import javax.ejb.RemoveException;

public abstract class InvokerExceptionMapper {
    private static Hashtable exceptionMappers = new Hashtable();

    public abstract GLException map(Throwable var1) throws GLException;

    public static InvokerExceptionMapper findMapper(Class mostDerived) {
        for (Class c = mostDerived; c != null; c = c.getSuperclass()) {
            InvokerExceptionMapper mapper = (InvokerExceptionMapper)exceptionMappers.get(c);
            if (mapper == null) continue;
            return mapper;
        }
        return null;
    }

    public static GLException handle(InvocationTargetException itex) throws GLException {
        Throwable targetException = itex.getTargetException();
        InvokerExceptionMapper mapper = InvokerExceptionMapper.findMapper(targetException.getClass());
        if (mapper != null) {
            return mapper.map(targetException);
        }
        return GLException.factory((Cause)new GLException.CausedBy("cause.BeanInvoker_UnknownTargetException", null), targetException);
    }

    static {
        exceptionMappers.put(GLException.class, new InvokerExceptionMapper(){

            @Override
            public GLException map(Throwable ex) throws GLException {
                return (GLException)ex;
            }
        });
        exceptionMappers.put(RemoteException.class, new InvokerExceptionMapper(){

            @Override
            public GLException map(Throwable ex) throws GLException {
                return GLException.factory(ex);
            }
        });
        exceptionMappers.put(CreateException.class, new InvokerExceptionMapper(){

            @Override
            public GLException map(Throwable ex) throws GLException {
                return GLException.factory(ex);
            }
        });
        exceptionMappers.put(FinderException.class, new InvokerExceptionMapper(){

            @Override
            public GLException map(Throwable ex) throws GLException {
                return GLException.factory(ex);
            }
        });
        exceptionMappers.put(RemoveException.class, new InvokerExceptionMapper(){

            @Override
            public GLException map(Throwable ex) throws GLException {
                return GLException.factory(ex);
            }
        });
    }
}
