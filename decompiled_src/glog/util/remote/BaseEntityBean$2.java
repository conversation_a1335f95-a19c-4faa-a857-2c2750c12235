/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;

class BaseEntityBean.2
implements BaseEntityBean.EjbFunctor {
    BaseEntityBean.2() {
    }

    @Override
    public boolean pre() throws GLException {
        return BaseEntityBean.this.preLoad();
    }

    @Override
    public void doIt() throws GLException {
        BaseEntityBean.this.doLoad();
    }

    @Override
    public void post() throws GLException {
        BaseEntityBean.this.postLoad();
    }

    @Override
    public void broadcast() throws GLException {
    }
}
