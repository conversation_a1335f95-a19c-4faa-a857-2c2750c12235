/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.Functions;
import glog.util.jdbc.Pk;
import java.io.Serializable;

public class CachedBean
implements Comparable,
Serializable {
    private Pk pk;
    private boolean locked;
    private boolean remote;

    public CachedBean(Pk pk, boolean locked, boolean remote) {
        this.pk = pk;
        this.locked = locked;
        this.remote = remote;
    }

    public Pk getPK() {
        return this.pk;
    }

    public boolean isLocked() {
        return this.locked;
    }

    public boolean isRemote() {
        return this.remote;
    }

    public String toString() {
        return this.pk.toString();
    }

    public int compareTo(Object other) {
        if (this == other) {
            return 0;
        }
        if (!(other instanceof CachedBean)) {
            return 1;
        }
        CachedBean o = (CachedBean)other;
        int result = Functions.compareTo(this.pk, o.pk);
        if (result == 0 && this.pk != null) {
            result = Functions.compareTo(this.pk.getTransaction(), o.pk.getTransaction());
        }
        return result;
    }
}
