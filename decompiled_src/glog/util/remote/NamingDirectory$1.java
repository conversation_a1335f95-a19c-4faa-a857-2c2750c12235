/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.server.appserver.AppServerLoader;
import glog.server.appserver.AppServerMap;
import glog.util.exception.GLException;
import glog.util.jdbc.Loader;
import glog.util.jdbc.T2SharedConnection;

static final class NamingDirectory.1
implements Loader.Read {
    NamingDirectory.1() {
    }

    @Override
    public void execute(T2SharedConnection conn) throws GLException {
        AppServerMap.setMap(map = AppServerLoader.get().loadServerMap(conn));
    }
}
