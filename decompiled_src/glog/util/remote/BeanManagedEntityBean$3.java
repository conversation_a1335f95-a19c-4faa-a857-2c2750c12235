/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.GLUpdateNotFound;

class BeanManagedEntityBean.3
extends BeanManagedEntityBean.DBModifier {
    BeanManagedEntityBean.3(T2SharedConnection x0) {
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getStoreStatement();
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, false);
    }

    @Override
    public void noRecords() throws GLException {
        throw new GLUpdateNotFound(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK());
    }

    @Override
    public String getUseCase() {
        return "writeFailure";
    }

    @Override
    public String getBundledErrorKey() {
        return "Write_Record_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Unable to update {0} {1} to the database. Either the record has been removed by another user or you do not have rights to edit it.";
    }
}
