/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.DOMGenerator;
import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.webserver.util.XMLHelper;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.Map;
import java.util.TreeMap;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public class RouteTracking {
    private Map trackByMachine = new TreeMap();
    private Map trackByServer = new TreeMap();
    private LocalTimestamp lastResetTime = RouteTracking.now();
    private static RouteTracking theRouteTracker = new RouteTracking();

    public static RouteTracking get() {
        return theRouteTracker;
    }

    public synchronized void diagnoseXML(Document d, Element topLevelElem) throws GLException {
        DOMGenerator.insertTypeUnit(d, "last_reset_time", topLevelElem, this.lastResetTime);
        for (Map.Entry entry : this.trackByMachine.entrySet()) {
            MachineTracking machineTracking = (MachineTracking)entry.getValue();
            Element machineElem = (Element)DOMGenerator.insertElement(d, "machine", topLevelElem);
            machineElem.setAttribute("id", (String)entry.getKey());
            machineTracking.diagnoseXML(d, machineElem);
        }
        for (Map.Entry entry : this.trackByServer.entrySet()) {
            ServerTracking serverTracking = (ServerTracking)entry.getValue();
            Element serverElem = (Element)DOMGenerator.insertElement(d, "server", topLevelElem);
            serverElem.setAttribute("id", (String)entry.getKey());
            serverTracking.diagnoseXML(d, serverElem);
        }
    }

    public synchronized void track(String machine, String server) {
        MachineTracking machineTracking = (MachineTracking)this.trackByMachine.get(machine);
        if (machineTracking == null) {
            machineTracking = new MachineTracking();
            this.trackByMachine.put(machine, machineTracking);
        }
        machineTracking.increment();
        ServerTracking serverTracking = (ServerTracking)this.trackByServer.get(server);
        if (serverTracking == null) {
            serverTracking = new ServerTracking();
            this.trackByServer.put(server, serverTracking);
        }
        serverTracking.increment(machine);
    }

    public synchronized Map getServers() {
        return new TreeMap(this.trackByServer);
    }

    public synchronized void reset() {
        this.lastResetTime = RouteTracking.now();
        this.trackByMachine.clear();
        this.trackByServer.clear();
    }

    public LocalTimestamp getLastResetTime() {
        return this.lastResetTime;
    }

    private static LocalTimestamp now() {
        LocalTimestamp result = new LocalTimestamp();
        result.setTimeZone(LocalTimestamp.getDisplayTimeZone());
        return result;
    }

    public static void main(String[] args) {
        try {
            RouteTracking.get().track("ENG03-01", "DEFAULT");
            RouteTracking.get().track("ENG03-01", "SECONDARY");
            RouteTracking.get().track("ENG04-02", "SECONDARY");
            Document d = XMLHelper.instance().createEmptyXMLDocument();
            Element root = (Element)DOMGenerator.insertElement(d, "root", d);
            RouteTracking.get().diagnoseXML(d, root);
            XMLHelper.instance().display(d, (Writer)new PrintWriter(System.out));
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public static class ServerTracking {
        private Map byMachine = new TreeMap();

        public synchronized void increment(String machine) {
            MachineTracking machineTracking = (MachineTracking)this.byMachine.get(machine);
            if (machineTracking == null) {
                machineTracking = new MachineTracking();
                this.byMachine.put(machine, machineTracking);
            }
            machineTracking.increment();
        }

        public synchronized void diagnoseXML(Document d, Element serverElem) throws GLException {
            for (Map.Entry entry : this.byMachine.entrySet()) {
                String machine = (String)entry.getKey();
                MachineTracking machineTracking = (MachineTracking)entry.getValue();
                Element machineElem = (Element)DOMGenerator.insertElement(d, "machine", serverElem);
                machineElem.setAttribute("id", machine);
                machineTracking.diagnoseXML(d, machineElem);
            }
        }

        public synchronized Map getMachines() {
            return new TreeMap(this.byMachine);
        }
    }

    public static class MachineTracking {
        private int machineCount = 0;

        public synchronized void increment() {
            ++this.machineCount;
        }

        public synchronized void diagnoseXML(Document d, Element machineElem) throws GLException {
            machineElem.setAttribute("count", String.valueOf(this.machineCount));
        }

        public synchronized int getCount() {
            return this.machineCount;
        }
    }
}
