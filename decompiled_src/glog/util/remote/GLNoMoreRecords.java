/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.Cause;
import glog.util.exception.GLFinderException;
import glog.util.exception.NoExceptionLogging;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.FinderNoMoreRecords;

public class GLNoMoreRecords
extends GLFinderException
implements NoExceptionLogging {
    public GLNoMoreRecords(String table) {
        this(new Cause("cause.No_Records_Found", null, new Object[][]{{"table", table}}), null);
    }

    public GLNoMoreRecords(BeanManagedEntityBean bean) {
        this(bean.getTable());
    }

    public GLNoMoreRecords(Cause cause) {
        this(cause, null);
    }

    public GLNoMoreRecords(Cause cause, Throwable t) {
        super(cause, t);
    }

    @Override
    protected Throwable toFinderException() {
        return new FinderNoMoreRecords(this);
    }
}
