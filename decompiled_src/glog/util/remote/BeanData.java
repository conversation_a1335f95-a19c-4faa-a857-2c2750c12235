/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EntityBean
 */
package glog.util.remote;

import glog.server.bngenerator.BNType;
import glog.server.bngenerator.BNTypeContext;
import glog.server.status.ObjectStatus;
import glog.server.status.StatusFunction;
import glog.server.workflow.status.StatusError;
import glog.util.Functions;
import glog.util.beandata.BeanDataLoader;
import glog.util.cache.HashMapCache;
import glog.util.data.Legacy;
import glog.util.data.LobData;
import glog.util.data.LobDataUtil;
import glog.util.datasource.DataFunction;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.SqlColumn;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.NamingDirectory;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import javax.ejb.EntityBean;

@Legacy
public abstract class BeanData
implements Serializable,
Cloneable {
    private BNType bnType = null;
    private BNTypeContext bnContext = null;
    private ObjectStatus status = null;
    private transient Map mapStatus = null;
    static HashMapCache fieldColumnsCache = new HashMapCache("BeanDataFields");
    private static HashMapCache canPerformCache = new HashMapCache();
    private static HashMapCache canPerformWithCausesCache = new HashMapCache();
    private static HashMapCache findByPrimaryKeyCache = new HashMapCache();
    private static ThreadLocal ejbCreateSuppressed = new ThreadLocal();

    public void getFromBean(EntityBean bean, Class columnDefClass) throws GLException {
        String className = columnDefClass.getName();
        Field[] dataFields = this.getBeanDataFields();
        List fieldColumns = null;
        fieldColumns = (List)fieldColumnsCache.get(className);
        if (fieldColumns == null) {
            fieldColumns = this.allocateFieldColumns(bean, columnDefClass, dataFields);
            fieldColumnsCache.put(className, fieldColumns);
        }
        int i = 0;
        Iterator it = fieldColumns.iterator();
        while (it.hasNext()) {
            String dataFieldName = dataFields[i].getName();
            try {
                BeanFieldSqlColumn fieldColumn = (BeanFieldSqlColumn)it.next();
                Field beanField = fieldColumn.beanField;
                SqlColumn sqlColumn = fieldColumn.sqlColumn;
                Object beanValue = beanField.get(bean);
                dataFields[i].set(this, sqlColumn.convertFromDB(beanValue));
                ++i;
            }
            catch (IllegalAccessException ex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Data_Error", null, new Object[][]{{"dataFieldName", dataFieldName}}), ex);
            }
        }
    }

    public void setToBean(EntityBean bean, Class columnDefClass) throws GLException {
        String className = columnDefClass.getName();
        Field[] dataFields = this.getBeanDataFields();
        List fieldColumns = null;
        fieldColumns = (List)fieldColumnsCache.get(className);
        if (fieldColumns == null) {
            fieldColumns = this.allocateFieldColumns(bean, columnDefClass, dataFields);
            fieldColumnsCache.put(className, fieldColumns);
        }
        int i = 0;
        Iterator it = fieldColumns.iterator();
        while (it.hasNext()) {
            String dataFieldName = dataFields[i].getName();
            try {
                BeanFieldSqlColumn fieldColumn = (BeanFieldSqlColumn)it.next();
                Field beanField = fieldColumn.beanField;
                SqlColumn sqlColumn = fieldColumn.sqlColumn;
                Object dataValue = dataFields[i].get(this);
                beanField.set(bean, sqlColumn.convertToDB(dataValue));
                ++i;
            }
            catch (IllegalAccessException ex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Data_Error", null, new Object[][]{{"dataFieldName", dataFieldName}}), ex);
            }
        }
    }

    public String toString() {
        return this.toString("");
    }

    public String toString(String tab) {
        StringWriter swriter = new StringWriter();
        PrintWriter pwriter = new PrintWriter(swriter);
        Class<?> dataClass = this.getClass();
        Field[] dataFields = Functions.getOrderedFields(dataClass);
        for (int i = 0; i < dataFields.length; ++i) {
            try {
                String dataFieldName = dataFields[i].getName();
                Field dataField = dataClass.getField(dataFieldName);
                if (Modifier.isStatic(dataField.getModifiers())) continue;
                Object dataValue = dataFields[i].get(this);
                pwriter.println(tab + "    " + dataFieldName + ": " + dataValue);
                continue;
            }
            catch (Exception ex) {
                // empty catch block
            }
        }
        if (this.status != null) {
            pwriter.println(tab + "    status : " + this.status);
        }
        return swriter.toString();
    }

    public Object clone() {
        try {
            return super.clone();
        }
        catch (Throwable t) {
            t.printStackTrace();
            return null;
        }
    }

    public void setMapStatus(Map mapStatus) {
        this.mapStatus = mapStatus;
    }

    public Map toMap() {
        TreeMap<String, Object> map = new TreeMap<String, Object>();
        Class<?> dataClass = this.getClass();
        Field[] dataFields = Functions.getOrderedFields(dataClass);
        for (int i = 0; i < dataFields.length; ++i) {
            try {
                String dataFieldName = dataFields[i].getName();
                Field dataField = dataClass.getField(dataFieldName);
                if (Modifier.isStatic(dataField.getModifiers())) continue;
                Object dataValue = dataFields[i].get(this);
                map.put(dataFieldName, dataValue);
                continue;
            }
            catch (Exception ex) {
                // empty catch block
            }
        }
        if (this.mapStatus != null) {
            map.put("Status", this.mapStatus);
        }
        if (this.status != null) {
            map.put("Status", this.status.get());
        }
        return map;
    }

    public void copy(BeanData other) throws GLException {
        if (other == this) {
            return;
        }
        if (!other.getClass().isAssignableFrom(this.getClass())) {
            throw GLException.factory(new GLException.CausedBy("cause.BeanDataCopyBadObject", null, new Object[][]{{"to", this.getClass().getName()}, {"from", other.getClass().getName()}}));
        }
        Class<?> dataClass = other.getClass();
        Field[] dataFields = Functions.getOrderedFields(dataClass);
        for (int i = 0; i < dataFields.length; ++i) {
            try {
                Object otherDataValue = dataFields[i].get(other);
                dataFields[i].set(this, otherDataValue);
                continue;
            }
            catch (IllegalAccessException iaex) {
                throw GLException.factory(iaex);
            }
        }
    }

    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if (other.getClass() != this.getClass()) {
            return false;
        }
        Class<?> dataClass = this.getClass();
        Field[] dataFields = Functions.getOrderedFields(dataClass);
        for (int i = 0; i < dataFields.length; ++i) {
            try {
                Object dataValue = dataFields[i].get(this);
                Object otherDataValue = dataFields[i].get(other);
                if (Functions.equals(dataValue, otherDataValue)) continue;
                return false;
            }
            catch (IllegalAccessException iaex) {
                return false;
            }
        }
        return true;
    }

    public Set getDifferingFields(BeanData other) {
        LinkedHashSet<String> differingFields = new LinkedHashSet<String>();
        if (other != null) {
            if (other.getClass() != this.getClass()) {
                differingFields.add("(all)");
            } else {
                Class<?> dataClass = this.getClass();
                Field[] dataFields = Functions.getOrderedFields(dataClass);
                for (int i = 0; i < dataFields.length; ++i) {
                    try {
                        Object dataValue = dataFields[i].get(this);
                        Object otherDataValue = dataFields[i].get(other);
                        if (Functions.equals(dataValue, otherDataValue)) continue;
                        differingFields.add(dataFields[i].getName());
                        continue;
                    }
                    catch (IllegalAccessException iaex) {
                        // empty catch block
                    }
                }
            }
        } else {
            differingFields.add("(all)");
        }
        return differingFields;
    }

    public abstract Field[] getBeanDataFields();

    public abstract Map getBeanDataFieldMap();

    public BeanData merge(BeanData other) throws GLException {
        if (this.getClass() != other.getClass()) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.BeanData.0001", null), null);
        }
        Field[] dataFields = this.getBeanDataFields();
        for (int i = 0; i < dataFields.length; ++i) {
            try {
                if (dataFields[i].get(other) == null) continue;
                dataFields[i].set(this, dataFields[i].get(other));
                continue;
            }
            catch (Exception ex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Data_Error", null, new Object[][]{{"name", dataFields[i].getName()}}), ex);
            }
        }
        return this;
    }

    public BNType getBNType() {
        return this.bnType;
    }

    public void setBNType(BNType bnType) {
        this.bnType = bnType;
    }

    public BNTypeContext getBNContext() {
        return this.bnContext;
    }

    public void setBNContext(BNTypeContext bnContext) {
        this.bnContext = bnContext;
    }

    private List allocateFieldColumns(EntityBean bean, Class columnDefClass, Field[] dataFields) throws GLException {
        ArrayList<BeanFieldSqlColumn> fieldColumns = new ArrayList<BeanFieldSqlColumn>();
        Class<?> beanClass = bean.getClass();
        for (int i = 0; i < dataFields.length; ++i) {
            String dataFieldName = dataFields[i].getName();
            try {
                Field beanField = beanClass.getField(dataFieldName);
                Field columnDefField = columnDefClass.getField(dataFieldName);
                SqlColumn sqlColumn = (SqlColumn)columnDefField.get(null);
                fieldColumns.add(new BeanFieldSqlColumn(beanField, sqlColumn));
                continue;
            }
            catch (ClassCastException ccex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Column_Type_Mismatch", null, new Object[][]{{"dataFieldName", dataFieldName}}), ccex);
            }
            catch (NoSuchFieldException nsfex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Column_Data_Mismatch", null, new Object[][]{{"dataFieldName", dataFieldName}}), nsfex);
            }
            catch (IllegalAccessException ex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Data_Error", null, new Object[][]{{"dataFieldName", dataFieldName}}), ex);
            }
            catch (IllegalArgumentException argex) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.Bean_Column_Data_Mismatch", null, new Object[][]{{"dataFieldName", dataFieldName}}), argex);
            }
        }
        return fieldColumns;
    }

    public String getQueryName() {
        return null;
    }

    public abstract Pk getPk();

    public abstract void setPk(Pk var1);

    public T2SharedConnection getConnection() throws GLException {
        Object connection = null;
        return new T2SharedConnection(DataFunction.EJB);
    }

    protected ObjectStatus newObjectStatus() throws GLException {
        return null;
    }

    public Map getStatus() {
        return this.status != null ? this.status.get() : null;
    }

    public boolean hasStatus() {
        return this.status != null;
    }

    public String getStatus(String type) {
        return this.status != null ? this.status.get(type) : null;
    }

    public void setStatus(String type, String value) {
        if (this.status != null) {
            this.status.set(type, value);
        }
    }

    public void initStatus(String type, String value) throws GLException {
        if (this.status == null) {
            this.status = this.newObjectStatus();
        }
        this.status.set(type, value);
    }

    public ObjectStatus getObjectStatus() {
        return this.status;
    }

    public void setObjectStatus(ObjectStatus status) {
        this.status = status;
    }

    public LobData[] getLobDatas() {
        return null;
    }

    public static BeanData getDataWithLobs(T2SharedConnection connection, BeanData beanData) throws GLException {
        try {
            LobData[] lobDatas = beanData.getLobDatas();
            if (lobDatas != null) {
                LobData[] fromLobDatas = LobDataUtil.loadLobDatas(lobDatas, connection, beanData.getPk());
                LobDataUtil.setLobDatas(fromLobDatas, lobDatas);
            }
            return beanData;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    protected static BeanData load(Connection conn, Pk pk, Class dataClass) throws GLException {
        BeanDataLoader loader = BeanDataLoader.get(dataClass, 1024, 1);
        return loader.load(pk, new T2SharedConnection(conn));
    }

    protected static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize, Class dataClass) throws GLException {
        BeanDataLoader loader = BeanDataLoader.get(dataClass, 1024, fetchSize);
        ArrayList list = new ArrayList();
        list.addAll(loader.load(whereClause, prepareArguments, null, new T2SharedConnection(conn)).values());
        return list;
    }

    protected static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize, Class dataClass) throws GLException {
        BeanDataLoader loader = BeanDataLoader.get(dataClass, 1024, fetchSize);
        ArrayList list = new ArrayList();
        list.addAll(loader.load(fromWhere, prepareArguments, alias, null, new T2SharedConnection(conn)).values());
        return list;
    }

    protected boolean canPerform(StatusFunction statusFunction, Object context, String jndiName) throws GLException {
        try {
            Object remote = this.getRemote(jndiName);
            Class<?> remoteClass = remote.getClass();
            Method canPerform = (Method)canPerformCache.get(remoteClass);
            if (canPerform == null) {
                canPerform = remoteClass.getMethod("canPerform", StatusFunction.class, Object.class);
                canPerformCache.put(remoteClass, canPerform);
            }
            return (Boolean)canPerform.invoke(remote, statusFunction, context);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    protected StatusError canPerformWithCauses(StatusFunction statusFunction, Object context, String jndiName) throws GLException {
        try {
            Object remote = this.getRemote(jndiName);
            Class<?> remoteClass = remote.getClass();
            Method canPerformWithCauses = (Method)canPerformWithCausesCache.get(remoteClass);
            if (canPerformWithCauses == null) {
                canPerformWithCauses = remoteClass.getMethod("canPerformWithCauses", StatusFunction.class, Object.class);
                canPerformWithCausesCache.put(remoteClass, canPerformWithCauses);
            }
            return (StatusError)canPerformWithCauses.invoke(remote, statusFunction, context);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    protected Object getRemote(String jndiName) throws GLException {
        try {
            Pk pk = this.getPk();
            NamingDirectory nd = NamingDirectory.get();
            Object home = nd.lookup(jndiName);
            Class<?> homeClass = home.getClass();
            Method find = (Method)findByPrimaryKeyCache.get(homeClass);
            if (find == null) {
                find = homeClass.getMethod("findByPrimaryKey", pk.getClass());
                findByPrimaryKeyCache.put(home, find);
            }
            return find.invoke(home, pk);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static boolean isEjbCreateSuppressed() {
        return Boolean.TRUE.equals(ejbCreateSuppressed.get());
    }

    public static void suppressEjbCreate(boolean suppress) {
        ejbCreateSuppressed.set(suppress ? Boolean.TRUE : null);
    }

    static class BeanFieldSqlColumn {
        public Field beanField;
        public SqlColumn sqlColumn;

        public BeanFieldSqlColumn(Field beanField, SqlColumn sqlColumn) {
            this.beanField = beanField;
            this.sqlColumn = sqlColumn;
        }
    }
}
