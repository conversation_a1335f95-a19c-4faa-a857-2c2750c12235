/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;
import java.util.Vector;

class BeanManagedEntityBean.10
extends BeanManagedEntityBean.DBModifier {
    final /* synthetic */ Vector val$primaryKeys;
    final /* synthetic */ Object val$transaction;

    BeanManagedEntityBean.10(T2SharedConnection x0, Vector vector, Object object) {
        this.val$primaryKeys = vector;
        this.val$transaction = object;
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getFindByPrimaryKeysStatement(this.val$primaryKeys);
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, this.val$transaction);
    }

    @Override
    public String getBundledErrorKey() {
        return "Bean_Find_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Bean_Find_Failure";
    }
}
