/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.util.remote;

import glog.server.appserver.AppFunction;
import glog.server.synch.LockExecutor;
import glog.server.synch.SynchUseCase;
import glog.server.synch.TransactionalLock;
import glog.server.workflow.adhoc.ObjectAuditCleanup;
import glog.server.workflow.aud.AuditLock;
import glog.util.GLProperties;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.exception.RemoveExceptionWrapper;
import glog.util.jdbc.Pk;
import glog.util.jdbc.SqlUpdate;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoUpdateFilter;
import glog.util.jdbc.noserver.UpdateFilter;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.NamingDirectory;
import glog.util.uom.data.Duration;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Enumeration;
import javax.ejb.EJBObject;

public class AuditCleanup {
    private static final String SQL_DELETE_AUDIT_DATA_INFO = "delete from audit_data_info where audit_trail_gid in (select aud.audit_trail_gid from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and aud.audit_object_type_gid=? and aud.object_gid=?)";
    private static final String SQL_DELETE_DATA_INFO = "delete from data_info where data_info_seq in (select distinct adi.data_info_seq from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and aud.audit_object_type_gid=? and aud.object_gid=?)";
    private static final String SQL_DELETE_DATA_INFO_DETAIL = "delete from data_info_detail where data_info_seq in (select distinct adi.data_info_seq from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and aud.audit_object_type_gid=? and aud.object_gid=?)";
    private static final String SQL_DELETE_OLD_AUDIT_DATA_INFO = "delete from audit_data_info where audit_trail_gid in (select aud.audit_trail_gid from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and (aud.object_gid is null or aud.notify_subject_gid like '% - REMOVED') and aud.audit_time < vpd.gmt_sysdate - ?)";
    private static final String SQL_DELETE_OLD_DATA_INFO_DETAILS = "delete from data_info_detail where data_info_seq in (select distinct adi.data_info_seq from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and (aud.object_gid is null or aud.notify_subject_gid like '% - REMOVED') and aud.audit_time < vpd.gmt_sysdate - ?)";
    private static final String SQL_DELETE_OLD_DATA_INFO = "delete from data_info where data_info_seq in (select distinct adi.data_info_seq from audit_data_info adi, audit_trail aud where adi.audit_trail_gid=aud.audit_trail_gid and (aud.object_gid is null or aud.notify_subject_gid like '% - REMOVED') and aud.audit_time < vpd.gmt_sysdate - ?)";
    private static final String ASYNCHRONOUS_REMOVAL_PROP = "glog.audit.asynchronousRemoval";
    private static boolean asynchronousAuditRemoval = false;

    public static void removeAudit(String auditType, Pk pk, T2SharedConnection conn) throws GLException {
        if (asynchronousAuditRemoval) {
            new ObjectAuditCleanup(auditType, pk).publish();
        } else {
            AuditCleanup.cleanupAudit(auditType, pk, conn);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void cleanupAudit(final String auditType, final Pk pk, final T2SharedConnection conn) throws GLException {
        final Class[] auditObjectClasses = new Class[]{String.class, Pk.class};
        try {
            conn.open();
            final Object[] auditObjectArr = new Object[]{auditType, pk};
            AppFunction.checkUsage(SynchUseCase.AUDIT);
            new TransactionalLock("select rowid from com_method where com_method_gid='AUD'", null, new LockExecutor(){

                /*
                 * WARNING - Removed try catching itself - possible behaviour change.
                 */
                @Override
                public void synchronize() throws GLException {
                    Class<AuditLock> clazz = AuditLock.class;
                    synchronized (AuditLock.class) {
                        SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_DATA_INFO_DETAIL), new Object[]{auditType, pk.toString()}, "AuditCleanup.delete", conn.get());
                        SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_DATA_INFO), new Object[]{auditType, pk.toString()}, "AuditCleanup.delete", conn.get());
                        SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_AUDIT_DATA_INFO), new Object[]{auditType, pk.toString()}, "AuditCleanup.delete", conn.get());
                        AuditCleanup.cleanupAudit("ejb.AuditTrail", "findByAuditObject", auditObjectClasses, auditObjectArr);
                        // ** MonitorExit[var1_1] (shouldn't be in output)
                        return;
                    }
                }
            }, SynchUseCase.AUDIT).lock();
        }
        finally {
            conn.close();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void cleanupAudit(Duration olderThan) throws GLException {
        Class[] auditObjectClasses = new Class[]{Duration.class};
        try (T2SharedConnection conn = new T2SharedConnection(DataFunction.DIRECT_ADMIN);){
            conn.open();
            Object[] auditObjectArr = new Object[]{new Double(olderThan.getAmount("D"))};
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(SQL_DELETE_OLD_DATA_INFO_DETAILS), auditObjectArr, "AuditCleanup.deleteOld", conn.get());
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(SQL_DELETE_OLD_DATA_INFO), auditObjectArr, "AuditCleanup.deleteOld", conn.get());
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(SQL_DELETE_OLD_AUDIT_DATA_INFO), auditObjectArr, "AuditCleanup.deleteOld", conn.get());
            AuditCleanup.cleanupAudit("ejb.AuditTrail", "findOlderThan", auditObjectClasses, new Object[]{olderThan});
        }
    }

    private static void cleanupAudit(String beanName, String findMethod, Class[] classes, Object[] args) throws GLException {
        NamingDirectory nd = NamingDirectory.get();
        try {
            Object homeIntf = nd.lookup(beanName);
            Method finder = homeIntf.getClass().getMethod(findMethod, classes);
            Enumeration e = (Enumeration)finder.invoke(homeIntf, args);
            while (e.hasMoreElements()) {
                ((EJBObject)e.nextElement()).remove();
            }
        }
        catch (InvocationTargetException ite) {
            Throwable t = ite.getTargetException();
            if (!(t instanceof FinderNoMoreRecords)) {
                throw GLException.factory(t);
            }
        }
        catch (RemoveExceptionWrapper rew) {
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
        finally {
            nd.release();
        }
    }

    private static void initAuditRemoval() {
        asynchronousAuditRemoval = "true".equalsIgnoreCase(GLProperties.get().getProperty(ASYNCHRONOUS_REMOVAL_PROP, "false"));
    }

    static {
        AuditCleanup.initAuditRemoval();
        GLProperties.get();
        GLProperties.addListener(ASYNCHRONOUS_REMOVAL_PROP, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) {
                AuditCleanup.initAuditRemoval();
            }
        });
    }
}
