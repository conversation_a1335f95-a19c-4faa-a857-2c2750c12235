/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.GLRecordNotFound;

class BeanManagedEntityBean.4
extends BeanManagedEntityBean.DBModifier {
    BeanManagedEntityBean.4(T2SharedConnection x0) {
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getRemoveStatement();
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeUpdate(this.conn, sql, false);
    }

    @Override
    public void noRecords() throws GLException {
        throw new GLRecordNotFound(BeanManagedEntityBean.this.getTable(), BeanManagedEntityBean.this.getPK());
    }

    @Override
    public String getBundledErrorKey() {
        return "Remove_Record_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Unable to remove {0} {1} from the database. Either the record has already been removed by another user or you do not have rights to remove it.";
    }
}
