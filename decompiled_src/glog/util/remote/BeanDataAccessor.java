/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.remote.BaseEntityBean;
import glog.util.remote.BeanData;
import glog.util.remote.BeanUpdateWithData;
import glog.util.remote.BeanUpdateWithSomeData;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collection;

public class BeanDataAccessor {
    private BeanData beanData;

    public BeanDataAccessor() {
    }

    public BeanDataAccessor(BeanData beanData) {
        this.beanData = beanData;
    }

    public BeanData get(BaseEntityBean bean) throws GLException {
        try {
            BeanUpdateWithSomeData update;
            Collection supportedFields;
            if (!(bean instanceof BeanUpdateWithData)) {
                throw GLException.factory(new GLException.CausedBy("cause.BeanDataNotSupported", null, new Object[][]{{"bean", bean.getName()}}));
            }
            if (bean instanceof BeanUpdateWithSomeData && (supportedFields = (update = (BeanUpdateWithSomeData)((Object)bean)).getSupportedFields()) != null) {
                throw GLException.factory(new GLException.CausedBy("cause.FullBeanDataNotSupported", null, new Object[][]{{"bean", bean.getName()}, {"supportedFields", this.getFieldList(supportedFields)}}));
            }
            if (this.beanData != null) {
                return this.beanData;
            }
            Method getDataMethod = bean.getClass().getMethod("getData", null);
            return (BeanData)getDataMethod.invoke((Object)bean, (Object[])null);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public Object getMember(Field field, BaseEntityBean bean) throws GLException {
        try {
            BeanUpdateWithSomeData update;
            Collection supportedFields;
            if (!(bean instanceof BeanUpdateWithData)) {
                throw GLException.factory(new GLException.CausedBy("cause.BeanDataNotSupported", null, new Object[][]{{"bean", bean.getName()}}));
            }
            if (bean instanceof BeanUpdateWithSomeData && (supportedFields = (update = (BeanUpdateWithSomeData)((Object)bean)).getSupportedFields()) != null && !supportedFields.contains(field)) {
                throw GLException.factory(new GLException.CausedBy("cause.BeanFieldNotSupported", null, new Object[][]{{"bean", bean.getName()}, {"field", field.getName()}, {"supportedFields", this.getFieldList(supportedFields)}}));
            }
            BeanData theBeanData = this.beanData;
            if (theBeanData == null) {
                Method getDataMethod = bean.getClass().getMethod("getData", null);
                theBeanData = (BeanData)getDataMethod.invoke((Object)bean, (Object[])null);
            }
            return field.get(theBeanData);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    private String getFieldList(Collection fields) {
        StringBuffer sb = new StringBuffer();
        String delim = "(";
        for (Field field : fields) {
            sb.append(delim).append(field.getName());
            delim = ",";
        }
        sb.append(")");
        return sb.toString();
    }
}
