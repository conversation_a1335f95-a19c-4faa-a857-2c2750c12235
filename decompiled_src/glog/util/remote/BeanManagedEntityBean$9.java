/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.util.exception.GLException;
import glog.util.jdbc.SqlStatement;
import glog.util.jdbc.T2SharedConnection;
import glog.util.remote.BeanManagedEntityBean;

class BeanManagedEntityBean.9
extends BeanManagedEntityBean.DBModifier {
    final /* synthetic */ Object val$transaction;

    BeanManagedEntityBean.9(T2SharedConnection x0, Object object) {
        this.val$transaction = object;
        super(BeanManagedEntityBean.this, x0);
    }

    @Override
    public SqlStatement getStatement() throws GLException {
        return BeanManagedEntityBean.this.getFindAll();
    }

    @Override
    public boolean execute(SqlStatement sql) throws GLException {
        return BeanManagedEntityBean.this.executeFindAll(this.conn, sql, this.val$transaction);
    }

    @Override
    public String getBundledErrorKey() {
        return "Bean_Find_Failure";
    }

    @Override
    public String getBundledErrorDescription() {
        return "Bean_Find_Failure";
    }
}
