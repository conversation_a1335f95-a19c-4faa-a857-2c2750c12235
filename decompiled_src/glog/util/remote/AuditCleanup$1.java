/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.server.synch.LockExecutor;
import glog.server.workflow.aud.AuditLock;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.SqlUpdate;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoUpdateFilter;
import glog.util.jdbc.noserver.UpdateFilter;
import glog.util.remote.AuditCleanup;

static final class AuditCleanup.1
extends LockExecutor {
    final /* synthetic */ String val$auditType;
    final /* synthetic */ Pk val$pk;
    final /* synthetic */ T2SharedConnection val$conn;
    final /* synthetic */ Class[] val$auditObjectClasses;
    final /* synthetic */ Object[] val$auditObjectArr;

    AuditCleanup.1(String string, Pk pk, T2SharedConnection t2SharedConnection, Class[] classArray, Object[] objectArray) {
        this.val$auditType = string;
        this.val$pk = pk;
        this.val$conn = t2SharedConnection;
        this.val$auditObjectClasses = classArray;
        this.val$auditObjectArr = objectArray;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void synchronize() throws GLException {
        Class<AuditLock> clazz = AuditLock.class;
        synchronized (AuditLock.class) {
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_DATA_INFO_DETAIL), new Object[]{this.val$auditType, this.val$pk.toString()}, "AuditCleanup.delete", this.val$conn.get());
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_DATA_INFO), new Object[]{this.val$auditType, this.val$pk.toString()}, "AuditCleanup.delete", this.val$conn.get());
            SqlUpdate.execute((UpdateFilter)NoUpdateFilter.get(AuditCleanup.SQL_DELETE_AUDIT_DATA_INFO), new Object[]{this.val$auditType, this.val$pk.toString()}, "AuditCleanup.delete", this.val$conn.get());
            AuditCleanup.cleanupAudit("ejb.AuditTrail", "findByAuditObject", this.val$auditObjectClasses, this.val$auditObjectArr);
            // ** MonitorExit[var1_1] (shouldn't be in output)
            return;
        }
    }
}
