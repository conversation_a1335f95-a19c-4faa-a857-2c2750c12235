/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.http.HttpSession
 *  weblogic.rjvm.PeerGoneException
 */
package glog.util.remote;

import glog.database.security.SecurityAuthenticatedUser;
import glog.database.security.SecurityAuthenticatedUserRole;
import glog.database.security.SecuritySession;
import glog.database.security.SecurityUser;
import glog.database.security.SecurityUserRole;
import glog.database.security.SecurityUtil;
import glog.database.security.SubjectUtil;
import glog.database.security.crypto.Password;
import glog.database.security.crypto.Tracking;
import glog.database.security.dbrealm.CachingRealm;
import glog.database.security.dbrealm.GLManageableRealm;
import glog.database.security.dbrealm.RdbmsUser;
import glog.database.security.jaas.AuthenticationContext;
import glog.database.security.ldap.NameSpace;
import glog.server.appserver.AppFunction;
import glog.server.appserver.AppMachine;
import glog.server.appserver.AppServerConstants;
import glog.server.appserver.AppServerLoader;
import glog.server.appserver.AppServerMachines;
import glog.server.appserver.AppServerMap;
import glog.server.appserver.AppServerSession;
import glog.server.appserver.AppServerSessionHome;
import glog.server.appserver.ServerMachines;
import glog.server.appserver.WebMachine;
import glog.server.appserver.WebServerConstants;
import glog.server.appserver.WebServerLoader;
import glog.server.appserver.WebServerMachines;
import glog.server.appserver.WebServerMap;
import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.appclass.Base64Encoding;
import glog.util.cache.HashMapCache;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.exception.GLNoServerException;
import glog.util.j2ee.deploy.ASEJBRuntimeDeployer;
import glog.util.j2ee.remote.ASNamingDirectory;
import glog.util.jdbc.Loader;
import glog.util.jdbc.T2SharedConnection;
import glog.util.local.LocalConstants;
import glog.util.local.LocalStubFactory;
import glog.util.log.Log;
import glog.util.log.LogIds;
import glog.util.message.TopicSubscriptions;
import glog.util.remote.RouteTracking;
import glog.util.remote.SessionBeanHomeInvoker;
import glog.util.thread.ThreadStopper;
import glog.util.wallet.Wallet;
import glog.util.wallet.Wallets;
import glog.webserver.i18n.Translator;
import glog.webserver.session.SessionStore;
import java.net.ConnectException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.security.auth.Subject;
import javax.servlet.http.HttpSession;
import weblogic.rjvm.PeerGoneException;

public class NamingDirectory {
    private AuthenticationContext authContext = null;
    private boolean isReleased = false;
    private static HashMapCache serviceCache = new HashMapCache("JNDIService");
    private static final Tracking DEFAULT_TRACKING = new Tracking();
    private Properties properties = new Properties();
    private NDContext initialContext;
    private transient String user;
    private transient String domain;
    private transient String vpdDomain;
    private transient AppFunction function;
    private transient String machine;
    private transient String server;
    private transient String primary;
    private transient boolean appToApp = false;
    private transient boolean forcedRouting = true;
    private transient Subject localSubject;
    private static NamingDirectory HELPER = new NamingDirectory();
    private static boolean appServer = LocalConstants.isAppServer();
    private static boolean webServer = LocalConstants.isWebServer();
    private static ThreadLocal currentDatabase;
    private static ThreadLocal currentUserRole;
    private static ThreadLocal currentDomain;
    private static ThreadLocal currentGLUser;
    private static ThreadLocal currentPassword;
    private static ThreadLocal httpSession;
    private static final String DBA_DOMAIN = "DBA";
    private static AppServerMap map;
    private static boolean isReloadingAppServerMap;
    private static WebServerMap webmap;
    private static boolean isReloadingWebServerMap;
    private static boolean pastStartup;
    private static boolean pastActivate;
    private static Set allowStartupJNDI;
    private static boolean runtimeDeploymentOn;

    public static NamingDirectory get(AppFunction function, String machine) throws GLException {
        if (!(NamingDirectory.isAppServerContext() || ASNamingDirectory.get().supportsClientSideJAAS() && LocalConstants.supportsJaas())) {
            String userName = (String)currentGLUser.get();
            Password password = (Password)currentPassword.get();
            if (userName == null || password == null) {
                return NamingDirectory.getForGuest(function, machine);
            }
            return NamingDirectory.get(null, userName, password, false, function, machine);
        }
        Subject subject = SubjectUtil.getCurrentSubject();
        return SubjectUtil.getPrincipal(subject) != null ? new NamingDirectory(function, machine) : NamingDirectory.getForGuest(function, machine);
    }

    public static NamingDirectory get(AppFunction function) throws GLException {
        return NamingDirectory.get(function, null);
    }

    public static NamingDirectory get(String machine) throws GLException {
        return NamingDirectory.get((AppFunction)null, machine);
    }

    public static NamingDirectory get() throws GLException {
        return NamingDirectory.get((AppFunction)null, null);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static NamingDirectory get(String nameSpace, String userName, Password credential, boolean authenticate, AppFunction function, String machine) throws GLException {
        try {
            if (nameSpace == null && userName == null) {
                return NamingDirectory.get(machine);
            }
            try {
                if (!NamingDirectory.isAppServerContext()) {
                    currentDomain.set(DBA_DOMAIN);
                    currentDatabase.set("GC3");
                }
                NameSpace[] spaces = nameSpace == null ? NameSpace.getSearchOrders("GC3") : (nameSpace.compareToIgnoreCase("default") == 0 ? NameSpace.getSearchOrders(null) : NameSpace.getSearchOrders(nameSpace));
                String userDomain = null;
                String userDatabase = "GC3";
                String userRole = "DEFAULT";
                String vpdDomain = null;
                if (authenticate) {
                    try (SessionBeanHomeInvoker invoker = new SessionBeanHomeInvoker("SecuritySessionHome", null, machine, function);){
                        SecuritySession session = (SecuritySession)invoker.create();
                        SecurityAuthenticatedUser user = null;
                        user = spaces == null || nameSpace == null || nameSpace.compareToIgnoreCase("GC3") == 0 ? session.authenticateUser(userName, credential) : session.authenticateLDAPUser(spaces, userName, credential);
                        userName = user.getName();
                        credential = user.getCredential();
                        userDomain = user.getDomain();
                        userDatabase = user.getDatabase();
                        userRole = user.getDefaultUserRoleGid();
                        vpdDomain = user.getVpdDomain();
                    }
                } else {
                    userDomain = NamingDirectory.getLoginDomain(userName);
                }
                NamingDirectory result = new NamingDirectory(userName, credential, function, machine);
                if (!NamingDirectory.isAppServerContext()) {
                    currentGLUser.set(userName);
                    currentDomain.set(userDomain);
                    currentDatabase.set(userDatabase);
                    currentPassword.set(credential);
                    currentUserRole.set(userRole);
                }
                result.vpdDomain = vpdDomain;
                return result;
            }
            catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static NamingDirectory get(String nameSpace, String userName, Password credential, AppFunction function, String machine) throws GLException {
        return NamingDirectory.get(nameSpace, userName, credential, true, function, machine);
    }

    public static NamingDirectory get(String nameSpace, String userName, Password credential, AppFunction function) throws GLException {
        return NamingDirectory.get(nameSpace, userName, credential, function, null);
    }

    public static NamingDirectory get(String nameSpace, String userName, Password credential) throws GLException {
        return NamingDirectory.get(nameSpace, userName, credential, null, null);
    }

    public static NamingDirectory get(String userName, Password credential, AppFunction function) throws GLException {
        return NamingDirectory.get(null, userName, credential, function, null);
    }

    public static NamingDirectory get(String userName, Password credential) throws GLException {
        return NamingDirectory.get(null, userName, credential, null, null);
    }

    public static NamingDirectory get(String userName, String credential, AppFunction function) throws GLException {
        return NamingDirectory.get(null, userName, Password.valueOf(credential), function, null);
    }

    public static NamingDirectory get(String userName, String credential) throws GLException {
        return NamingDirectory.get(null, userName, Password.valueOf(credential), null, null);
    }

    public static NamingDirectory getRemote(String machine, String userName, Password credential) throws GLException {
        return NamingDirectory.get(null, userName, credential, false, null, machine);
    }

    public static NamingDirectory getForSystem(AppFunction function, String machine) throws GLException {
        String user = NamingDirectory.getSystemUser();
        Password password = NamingDirectory.getPassword(user, "GC3", "glog.appSystem.password", "CHANGEME1");
        if (!NamingDirectory.isAppServerContext()) {
            currentGLUser.set("DBA.ADMIN");
            currentDomain.set(DBA_DOMAIN);
            currentDatabase.set("GC3");
            currentUserRole.set("ADMIN");
            currentPassword.set(null);
        }
        return NamingDirectory.get(null, user, password, function, machine);
    }

    public static NamingDirectory getForGuest(AppFunction function, String machine) throws GLException {
        Password guestPassword = NamingDirectory.getPassword("guest", "Guest", "glog.appGuest.password", "CHANGEME");
        if (!NamingDirectory.isAppServerContext()) {
            currentGLUser.set("GUEST.ADMIN");
            currentDomain.set("GUEST");
            currentDatabase.set("GC3");
            currentUserRole.set("ADMIN");
            currentPassword.set(null);
        }
        return NamingDirectory.get(null, "guest", guestPassword, false, function, machine);
    }

    public static String getSystemUser() {
        String systemUsername = System.getProperty("GC3User");
        if (systemUsername == null) {
            systemUsername = GLProperties.get().getProperty("glog.appSystem.user", "system");
        }
        return systemUsername;
    }

    public static Password getSystemPassword() throws GLException {
        return NamingDirectory.getPropertyPassword("GC3", "glog.appSystem.password", "system", "CHANGEME1");
    }

    public static Password getPropertyPassword(String prefix, String property, String walletKey, String defaultValue) throws GLException {
        String pwd = null;
        String encodedPwd = System.getProperty(prefix + "EncodedPassword");
        if (encodedPwd != null) {
            pwd = Base64Encoding.decode(encodedPwd).toString();
        }
        if (pwd == null) {
            pwd = System.getProperty(prefix + "Password");
        }
        if (pwd == null) {
            pwd = GLProperties.get().getProperty(property, defaultValue);
        }
        if (pwd.startsWith("{w")) {
            Wallet wallet;
            String key = pwd.substring("{w".length());
            if (key.length() == 0) {
                key = walletKey;
            }
            if ((wallet = Wallets.getWallet(key)) != null) {
                pwd = wallet.getPassword(key);
            }
        }
        return new Password(pwd, Password.TEXT);
    }

    public static Password getPropertyPasswordDefault(String prefix, String property, String walletKey, String defaultValue) {
        try {
            return NamingDirectory.getPropertyPassword(prefix, property, walletKey, defaultValue);
        }
        catch (Throwable t) {
            GLException.factory(t);
            return new Password(defaultValue, Password.TEXT);
        }
    }

    private static Password getPassword(String user, String prefix, String property, String defaultValue) throws GLException {
        Password password = null;
        if (NamingDirectory.isAppServerContext()) {
            password = NamingDirectory.getPasswordFromAppServer(user);
        }
        if (password == null) {
            password = NamingDirectory.getPropertyPassword(prefix, property, user, defaultValue);
        }
        return password;
    }

    public static NamingDirectory getForSystem(AppFunction function) throws GLException {
        return NamingDirectory.getForSystem(function, null);
    }

    public static NamingDirectory getForSystem() throws GLException {
        return NamingDirectory.getForSystem(null);
    }

    public static void updateSystemPasswordForWebserver(String systemPassword) throws GLException {
        try {
            Properties props = System.getProperties();
            props.put("GC3Password", systemPassword);
            System.setProperties(props);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static Password getPasswordFromAppServer(String username) throws GLException {
        RdbmsUser user;
        GLManageableRealm realm = CachingRealm.getGLRealm();
        if (realm != null && (user = (RdbmsUser)realm.getUser(username)) != null) {
            return user.getPassword();
        }
        return null;
    }

    public static NamingDirectory impersonateSecurity(String glUserName, String userRoleGid) throws GLException {
        if (glUserName == null) {
            return null;
        }
        if (!NamingDirectory.isAppServerContext()) {
            throw GLException.factory(new GLException.CausedBy("cause.Security_ClientImpersonation", null));
        }
        SecurityUser user = SecurityUtil.getUser(glUserName);
        if (user == null) {
            return null;
        }
        Password credential = user.getCredential();
        if (userRoleGid != null && !userRoleGid.equals(user.getDefaultUserRoleGid())) {
            SecurityUserRole userRole = SecurityUtil.getUserRole(userRoleGid);
            credential = new Password(credential);
            credential.setUserRole(userRoleGid);
            String vpdDomain = userRole.getVpdDomainName();
            if (vpdDomain != null) {
                credential.setDomainContext(vpdDomain);
            }
            NamingDirectory nd = NamingDirectory.get(user.getName(), credential);
            currentUserRole.set(userRole.getUserRoleGid());
            currentDatabase.set(userRole.getDatabase());
            nd.vpdDomain = userRole.getVpdDomainName();
            return nd;
        }
        return NamingDirectory.get(user.getName(), credential);
    }

    public String getVpdDomain() {
        return this.vpdDomain;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void changeCurrentUserRole(String userRoleGid) throws GLException {
        if (userRoleGid == null) {
            return;
        }
        try (SessionBeanHomeInvoker invoker = new SessionBeanHomeInvoker("SecuritySessionHome", null, null, AppFunction.LOGIN);){
            SecuritySession session = (SecuritySession)invoker.create();
            SecurityAuthenticatedUserRole userRole = session.setCurrentUserRoleGid(userRoleGid);
            currentUserRole.set(userRole.getUserRoleGid());
            currentDatabase.set(userRole.getDatabase());
            this.vpdDomain = userRole.getVpdDomainName();
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void resetCurrentUserRole() throws GLException {
        try (SessionBeanHomeInvoker invoker = new SessionBeanHomeInvoker("SecuritySessionHome", null, null, AppFunction.LOGIN);){
            SecuritySession session = (SecuritySession)invoker.create();
            SecurityAuthenticatedUserRole userRole = session.resetCurrentUserRoleGid();
            currentUserRole.set(userRole.getUserRoleGid());
            currentDatabase.set(userRole.getDatabase());
            this.vpdDomain = userRole.getVpdDomainName();
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    public static boolean isAppServer() {
        return appServer;
    }

    public static boolean isWebServer() {
        return webServer;
    }

    public static boolean isAppServerContext() {
        return LocalConstants.isAppServerContext();
    }

    public static void pushAppServerContext() {
        LocalConstants.pushAppServerContext();
    }

    public static void popAppServerContext() {
        LocalConstants.popAppServerContext();
    }

    public static boolean enforceStrictUsers() {
        return CachingRealm.enforceStrictUsers();
    }

    public static boolean enforceStrictUserRoles() {
        return CachingRealm.enforceStrictUserRoles();
    }

    public static boolean isRunningAppServer() {
        return NamingDirectory.isAppServerContext();
    }

    public static NamingDirectory getForDomain(String domainName, AppFunction function, String machine) throws GLException {
        if (!NamingDirectory.isAppServerContext()) {
            throw GLException.factory(new GLException.CausedBy("cause.NamingDirectory.0001", null));
        }
        SecurityUser user = SecurityUtil.getUser(SecurityUser.getAdminUser(domainName));
        if (user == null) {
            return null;
        }
        return NamingDirectory.get(null, user.getName(), user.getCredential(), function, machine);
    }

    public static NamingDirectory getForDomain(String domainName, AppFunction function) throws GLException {
        return NamingDirectory.getForDomain(domainName, function, null);
    }

    public static NamingDirectory getForDomain(String domainName) throws GLException {
        return NamingDirectory.getForDomain(domainName, null);
    }

    public static final String getCurrentGLUser() {
        return NamingDirectory.isAppServerContext() ? SecurityUtil.getCurrentUserName() : (String)currentGLUser.get();
    }

    public static final String getCurrentRoleUser() {
        String glUser;
        String glDomain;
        String domain = NamingDirectory.getCurrentDomain();
        return Functions.equals(domain, glDomain = Functions.getDomainFromGid(glUser = NamingDirectory.getCurrentGLUser())) ? glUser : domain + ".ADMIN";
    }

    public static final Password getCurrentPassword() {
        if (!NamingDirectory.isAppServerContext()) {
            return (Password)currentPassword.get();
        }
        return null;
    }

    public static final void setCurrentPassword(Password password) {
        if (!NamingDirectory.isAppServerContext()) {
            currentPassword.set(password);
        }
    }

    public static final String getCurrentWLUser() {
        return NamingDirectory.getCurrentGLUser();
    }

    public static final String getCurrentDomain() {
        try {
            String domainContext;
            if (NamingDirectory.isAppServerContext()) {
                return SecurityUtil.getCurrentDomain();
            }
            Password password = (Password)currentPassword.get();
            if (password != null && (domainContext = password.getDomainContext()) != null) {
                return domainContext;
            }
            return (String)currentDomain.get();
        }
        catch (GLException glex) {
            glex.printStackTrace();
            return null;
        }
    }

    public static final String getLoginDomain(String loginUser) {
        int dot = loginUser.lastIndexOf(46);
        return dot != -1 ? loginUser.substring(0, dot) : DBA_DOMAIN;
    }

    public static final String getCurrentLoginDomain() {
        return NamingDirectory.getLoginDomain(NamingDirectory.getCurrentGLUser());
    }

    public static void setCurrentUserRole(String userRoleGid) throws GLException {
        currentUserRole.set(userRoleGid);
    }

    public static final String getCurrentUserRole() {
        try {
            return NamingDirectory.isAppServerContext() ? SecurityUtil.getLoginUserRoleGid() : (String)currentUserRole.get();
        }
        catch (GLException glex) {
            glex.printStackTrace();
            return null;
        }
    }

    public static final String getCurrentDatabase() {
        return (String)currentDatabase.get();
    }

    public static final void setCurrentDatabase(String database) {
        currentDatabase.set(database);
    }

    public static final String getCurrentAppServer() {
        String user = NamingDirectory.getCurrentGLUser();
        String domain = NamingDirectory.getCurrentDomain();
        AppServerMap appServerMap = AppServerMap.get();
        return appServerMap != null ? ((AppServerMachines)appServerMap.getMachines(user, domain, null)).getServer() : AppServerConstants.get().getDefaultServer();
    }

    public static final String getCurrentMachineURL() throws GLException {
        return HELPER.routeServerByUser(NamingDirectory.getCurrentWLUser(), null, null);
    }

    public static int getCurrentTrackingMode() {
        return NamingDirectory.isAppServerContext() ? SecurityUtil.getCurrentTrackingMode() : NamingDirectory.getCurrentTracking().mode;
    }

    public static int getCurrentTrackingSeverity() {
        return NamingDirectory.isAppServerContext() ? SecurityUtil.getCurrentTrackingSeverity() : NamingDirectory.getCurrentTracking().severity;
    }

    public static String getCurrentTrackingDiagnostic() {
        return NamingDirectory.isAppServerContext() ? SecurityUtil.getCurrentTrackingDiagnostic() : NamingDirectory.getCurrentTracking().diagnostic;
    }

    public static Long getCurrentTrackingNTierUid() {
        return NamingDirectory.isAppServerContext() ? SecurityUtil.getCurrentTrackingNTierUid() : NamingDirectory.getCurrentTracking().ntierUid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Object lookup(String serviceName) throws GLException {
        try {
            this.checkStartupJNDI(serviceName);
            if (ThreadStopper.stopThreadOn[3]) {
                ThreadStopper.checkCurrentThreadStopped();
            }
            if (this.isReleased) {
                throw GLException.factory(new GLException.CausedBy("cause.namingDirectory_already_Released", null));
            }
            Context context = this.getContext(serviceName);
            Object obj = null;
            boolean cached = false;
            if (appServer && ASNamingDirectory.get().supportsServiceCaching()) {
                obj = NamingDirectory.getService(this.machine, serviceName);
                boolean bl = cached = obj != null;
                if (!cached) {
                    Map serviceMap;
                    Map map = serviceMap = NamingDirectory.getServiceMap(this.machine);
                    synchronized (map) {
                        obj = serviceMap.get(serviceName);
                        if (obj == null) {
                            obj = this.lookupContext(context, serviceName);
                            serviceMap.put(serviceName, obj);
                        }
                    }
                }
            } else {
                obj = this.lookupContext(context, serviceName);
            }
            this.trackRoute(serviceName, cached);
            return obj;
        }
        catch (GLNoServerException nsex) {
            throw nsex;
        }
        catch (Exception ex) {
            if (serviceName != null && !serviceName.equals("") && ASNamingDirectory.get().supportsRuntimeDeployment() && runtimeDeploymentOn) {
                try {
                    Map serviceMap;
                    ASEJBRuntimeDeployer.deployRemote(this.machine, serviceName);
                    Map map = serviceMap = NamingDirectory.getServiceMap(this.machine);
                    synchronized (map) {
                        Object obj0 = serviceMap.get(serviceName);
                        if (obj0 == null) {
                            obj0 = this.lookupContext(this.getContext(serviceName), serviceName);
                            serviceMap.put(serviceName, obj0);
                        }
                        this.trackRoute(serviceName, false);
                        return obj0;
                    }
                }
                catch (Throwable throwable) {
                    // empty catch block
                }
            }
            throw GLException.factory((Cause)new GLException.CausedBy("cause.Unknown_Service", null, new Object[][]{{"serviceName", serviceName}}), ex);
        }
    }

    private Object lookupContext(Context context, String serviceName) throws GLException, NamingException {
        try {
            Object object = LocalConstants.supportsDeploy(serviceName) ? LocalStubFactory.get().getHome(serviceName) : context.lookup(serviceName);
            return object;
        }
        catch (GLException glex) {
            Throwable t = glex.getWrappedException();
            if (t != null && t instanceof PeerGoneException) {
                context = this.handleConnectException((Exception)((PeerGoneException)t), serviceName);
                Object object = context != null ? context.lookup(serviceName) : null;
                return object;
            }
            throw glex;
        }
        catch (NamingException nex) {
            Throwable rootCause = nex.getRootCause();
            if (rootCause != null && rootCause instanceof ConnectException) {
                context = this.handleConnectException(nex, serviceName);
                Object object = context != null ? context.lookup(serviceName) : null;
                return object;
            }
            throw nex;
        }
        finally {
            if (context != null) {
                ASNamingDirectory.get().newContext(((NDContext)context).getImpl());
            }
        }
    }

    public Context getImplContext() {
        return this.initialContext.getImpl();
    }

    private void trackRoute(String serviceName, boolean cached) {
        if (!NamingDirectory.isAppServerContext() && Log.idOn[LogIds.SCALABILITY_DETAILS.index]) {
            Log.logID(LogIds.SCALABILITY_DETAILS, "Routing {0}/{1}/{2}{7}: {3} to {4}{5}{6}", this.user, this.domain, this.function, serviceName, LocalConstants.supportsDeploy(serviceName) ? "local" : this.machine, this.primary != null ? " for " + this.primary : "", cached ? " (cached)" : "", this.forcedRouting ? "(pegged)" : "");
            RouteTracking.get().track(this.machine, this.server);
        }
    }

    public void release() {
        try {
            this.isReleased = true;
            if (this.initialContext != null) {
                this.initialContext.close();
                this.initialContext = null;
            }
            if (this.authContext != null) {
                try {
                    this.authContext.logout();
                }
                catch (Throwable t) {
                    GLException.factory(t);
                }
            }
            if (this.localSubject != null) {
                SubjectUtil.popSubject();
            }
        }
        catch (NamingException nex) {
            nex.printStackTrace();
        }
    }

    public static boolean isScalable() {
        return AppServerConstants.get().isScalable();
    }

    public static boolean isRoutingSuppressed() {
        return AppServerConstants.get().isRoutingSuppressed();
    }

    private NamingDirectory() {
    }

    private NamingDirectory(AppFunction function, String machine) throws GLException {
        String routedMachine = this.routeServerByDomain(null, function, machine);
        ASNamingDirectory.get().setupJNDIEnvironment(this, this.properties, routedMachine);
    }

    private NamingDirectory(String userName, Password password, AppFunction function, String machine) throws GLException {
        if (Functions.IN_CODEGEN) {
            throw GLException.factory(new GLException.CausedBy("cause.NamingDirectory.0002", null));
        }
        String routedMachine = this.routeServerByUser(userName, function, machine);
        if (password != null) {
            password.setClient(NamingDirectory.isAppServerContext() ? "app" : "web");
            password.setPrimaryMachine(this.primary);
            ASNamingDirectory asND = ASNamingDirectory.get();
            asND.setupJNDIEnvironment(this, this.properties, userName, password, routedMachine);
            if (LocalConstants.supportsJaas()) {
                this.authContext = asND.jaasLogin(this, userName, password, routedMachine);
            } else {
                this.localSubject = SubjectUtil.pushSubjectIfDiffers(userName, password);
            }
        }
        routedMachine = (String)this.properties.get("java.naming.provider.url");
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Context getContext(String serviceName) throws GLException {
        if (this.initialContext == null) {
            if (Functions.IN_CODEGEN) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.NamingDirectory.0003", null), null);
            }
            NamingDirectory namingDirectory = this;
            synchronized (namingDirectory) {
                if (this.initialContext == null) {
                    try {
                        Hashtable ndProps = LocalConstants.supportsDeploy(serviceName) ? new Hashtable() : this.properties;
                        this.initialContext = new NDContext(ndProps);
                        GLException.allowTranslation();
                        Translator.allowTranslation();
                    }
                    catch (NamingException nex) {
                        Throwable rootCause = nex.getRootCause();
                        if (rootCause != null && rootCause instanceof ConnectException) {
                            return this.handleConnectException(nex, serviceName);
                        }
                        throw GLException.factory((Cause)new GLException.CausedBy(this.getContextString(), null), nex);
                    }
                }
            }
        }
        return this.initialContext;
    }

    public Context handleConnectException(Exception ex, String serviceName) throws GLException {
        if (AppServerConstants.get().isScalable()) {
            AppServerMap appServerMap = AppServerMap.get();
            if (appServerMap != null) {
                appServerMap.setPingable(this.machine, false);
            }
            if (Log.idOn[LogIds.SCALABILITY.index]) {
                Log.logID(LogIds.SCALABILITY, "Machine {0} is not responding:\n{1}", this.machine, appServerMap);
            }
            if (!this.forcedRouting) {
                this.properties.put("java.naming.provider.url", this.routeServer(NamingDirectory.getAppServerMapEx()));
                return this.getContext(serviceName);
            }
        }
        throw new GLNoServerException("cause.singleServerDown", "Machine {machine} is not responding", new Object[][]{{"machine", this.properties.get("java.naming.provider.url")}});
    }

    public void handleRmiTimeoutException() throws GLException {
        if (AppServerConstants.get().isScalable()) {
            AppServerMap appServerMap = AppServerMap.get();
            if (appServerMap != null) {
                appServerMap.setPingable(this.machine, false, false);
            }
            if (Log.idOn[LogIds.SCALABILITY.index]) {
                Log.logID(LogIds.SCALABILITY, "Machine {0} is too busy to respond:\n{1}", this.machine, appServerMap);
            }
            try {
                if (!this.forcedRouting && this.routeServer(NamingDirectory.getAppServerMapEx()) != null) {
                    return;
                }
            }
            catch (Throwable t) {
                GLException.factory(t);
            }
        }
        throw new GLNoServerException("cause.singleServerDown", "Machine {machine} is too busy to respond", new Object[][]{{"machine", this.properties.get("java.naming.provider.url")}});
    }

    public String routeServerByDomain(String domain, AppFunction function, String machine) throws GLException {
        this.function = function;
        this.server = AppServerConstants.get().getDefaultServer();
        if (machine != null) {
            return this.setMachine(machine);
        }
        AppServerMap appServerMap = NamingDirectory.getAppServerMapEx();
        if (appServerMap == null) {
            return this.setMachine(AppServerConstants.get().getDefaultMachineURL());
        }
        machine = NamingDirectory.checkForRoutingSuppressed();
        if (machine != null) {
            return this.setMachine(machine);
        }
        this.setupTransients(NamingDirectory.getCurrentGLUser(), domain, function);
        return this.routeServer(appServerMap);
    }

    public String routeServerByUser(String userName, AppFunction function, String machine) throws GLException {
        this.function = function;
        this.server = AppServerConstants.get().getDefaultServer();
        if (machine != null) {
            return this.setMachine(machine);
        }
        AppServerMap appServerMap = NamingDirectory.getAppServerMapEx();
        if (appServerMap == null) {
            return this.setMachine(AppServerConstants.get().getDefaultMachineURL());
        }
        machine = NamingDirectory.checkForRoutingSuppressed();
        if (machine != null) {
            return this.setMachine(machine);
        }
        this.setupTransients(userName != null ? userName : NamingDirectory.getCurrentGLUser(), userName != null ? NamingDirectory.getLoginDomain(userName) : NamingDirectory.getCurrentLoginDomain(), function);
        return this.routeServer(appServerMap);
    }

    public String getMachine() {
        return this.machine;
    }

    public String getPrimary() {
        return this.primary;
    }

    private String setMachine(String url) {
        this.primary = null;
        this.machine = url;
        return this.machine;
    }

    private String setMachine(AppMachine appMachine) {
        this.primary = null;
        this.machine = null;
        if (appMachine != null) {
            this.machine = appMachine.getURL();
            AppMachine appPrimary = appMachine.getPrimary();
            if (appPrimary != null) {
                this.primary = appPrimary.getGid();
            }
        }
        return this.machine;
    }

    private void setupTransients(String user, String domain, AppFunction function) {
        this.user = user;
        this.domain = domain;
        this.function = function;
        if (this.domain == null) {
            this.domain = NamingDirectory.getCurrentDomain();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private String routeServer(AppServerMap appServerMap) throws GLException {
        this.server = AppServerConstants.get().getDefaultServer();
        String machine = NamingDirectory.checkForRoutingSuppressed();
        if (machine != null) {
            return this.setMachine(machine);
        }
        if (!webServer || NamingDirectory.isAppServerContext()) {
            return this.routeAppToApp(appServerMap);
        }
        ServerMachines serverMachines = null;
        HttpSession session = (HttpSession)httpSession.get();
        String dedicatedAppServer = null;
        if (session != null) {
            try {
                String forcedMachine = (String)SessionStore.getAttribute(session, "route");
                if (forcedMachine != null) {
                    String string = this.setMachine(forcedMachine);
                    return string;
                }
                dedicatedAppServer = (String)SessionStore.getAttribute(session, "dedicatedAppServer");
                if (dedicatedAppServer != null) {
                    serverMachines = (AppServerMachines)appServerMap.getServer(dedicatedAppServer);
                }
            }
            catch (IllegalStateException ise) {
            }
            finally {
                try {
                    SessionStore.removeAttribute(session, "dedicatedAppServer");
                }
                catch (IllegalStateException ise) {}
            }
        }
        this.forcedRouting = false;
        if (serverMachines == null) {
            serverMachines = (AppServerMachines)appServerMap.getMachines(this.user, this.domain, this.function);
        }
        this.server = serverMachines.getServer();
        AppMachine appMachine = ((AppServerMachines)serverMachines).next(null);
        String url = this.setMachine(appMachine);
        if (url == null) {
            if (dedicatedAppServer != null) {
                throw new GLNoServerException("cause.DedicatedClusterDown", "No application machine in the {dedicated} cluster is responding.", new Object[][]{{"dedicated", dedicatedAppServer}});
            }
            String strFunction = this.function != null ? this.function.toString() : "any";
            String strDomain = this.domain != null ? this.domain.toString() : "any";
            throw new GLNoServerException("cause.ServerDown", "No application machine is available to service {function} functionality in {domain} domain.", new Object[][]{{"domain", strDomain}, {"function", strFunction}});
        }
        return url;
    }

    private String routeAppToApp(AppServerMap appServerMap) throws GLException {
        AppServerMachines serverMachines;
        this.setMachine(AppServerConstants.get().getThisMachineURL());
        if (this.function != null && this.function.dedicateAppToApp() && (serverMachines = (AppServerMachines)appServerMap.getMachinesAppToApp(this.user, this.domain, this.function)) != null && (this.function.scaleAppToApp() || !appServerMap.machineSupportsFunction(this.machine, this.function))) {
            AppMachine appMachine = serverMachines.next(null);
            String url = this.setMachine(appMachine);
            if (url == null) {
                String strFunction = this.function != null ? this.function.toString() : "any";
                String strDomain = this.domain != null ? this.domain.toString() : "any";
                throw new GLNoServerException("cause.ServerDownUsingLocalMachine", "No application machine is available to service {function} functionality in {domain} domain - using the local machine.", new Object[][]{{"domain", strDomain}, {"function", strFunction}});
            }
            this.forcedRouting = false;
            this.server = serverMachines.getServer();
            return url;
        }
        return this.machine;
    }

    public static void setSession(HttpSession session) {
        httpSession.set(session);
    }

    public static boolean isDomainShared(String domain) {
        AppServerMap appServerMap = NamingDirectory.getAppServerMap();
        return appServerMap != null ? appServerMap.isDomainShared(domain) : true;
    }

    public static boolean isBeanShared(Class pkClass, String domain) {
        AppServerMap appServerMap = NamingDirectory.getAppServerMap();
        return appServerMap != null ? appServerMap.isBeanShared(pkClass, domain) : true;
    }

    public static boolean isBeanLocal(Class pkClass, String domain) {
        return appServer;
    }

    public static boolean isAppToApp(AppFunction function) {
        if (webServer || !function.dedicateAppToApp()) {
            return false;
        }
        AppServerMap appServerMap = NamingDirectory.getAppServerMap();
        if (appServerMap == null) {
            return false;
        }
        AppServerMachines serverMachines = (AppServerMachines)appServerMap.getMachinesAppToApp(null, null, function);
        String machineURL = AppServerConstants.get().getThisMachineURL();
        return serverMachines != null && (function.scaleAppToApp() || !appServerMap.machineSupportsFunction(machineURL, function));
    }

    public static String getMachineURL(String machineGid) {
        if (machineGid == null || machineGid.length() == 0) {
            return null;
        }
        AppServerMap appServerMap = NamingDirectory.getAppServerMap();
        if (appServerMap == null) {
            return null;
        }
        return appServerMap.getMachineURL(machineGid);
    }

    private static Object getService(String machineName, String serviceName) {
        Map byMachine = (Map)serviceCache.get(machineName);
        return byMachine != null ? byMachine.get(serviceName) : null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static Map getServiceMap(String machineName) {
        HashMap byMachine = (HashMap)serviceCache.get(machineName);
        if (byMachine == null) {
            HashMapCache hashMapCache = serviceCache;
            synchronized (hashMapCache) {
                byMachine = (Map)serviceCache.get(machineName);
                if (byMachine == null) {
                    byMachine = new HashMap();
                    serviceCache.put(machineName, byMachine);
                }
            }
        }
        return byMachine;
    }

    private static void setService(String machineName, String serviceName, Object service) {
        Map byMachine = NamingDirectory.getServiceMap(machineName);
        byMachine.put(serviceName, service);
    }

    public static boolean areServicesCached(String machineName) {
        return serviceCache.get(machineName) != null;
    }

    public static void clearServices(String machineName) {
        serviceCache.remove(machineName);
    }

    public static void clearAllServices() {
        serviceCache.clear();
    }

    public static boolean isPastStartup() {
        return !appServer || pastStartup;
    }

    public static void setPastStartup() {
        pastStartup = true;
    }

    public static boolean isPastActivate() {
        return !appServer || pastActivate;
    }

    public static void setPastActivate() {
        pastActivate = true;
    }

    public static void allowStartupJNDI(String serviceName) {
        allowStartupJNDI.add(serviceName);
    }

    private void checkStartupJNDI(String serviceName) throws GLException {
        if (!appServer || allowStartupJNDI.contains(serviceName)) {
            return;
        }
        if (Functions.equals(this.machine, AppServerConstants.get().getThisMachineURL()) && !pastStartup) {
            throw GLException.factory(new GLException.CausedBy("cause.JNDINotNecessarilyInitialized", "solution.avoidJNDIDuringStartup"));
        }
    }

    private String getContextString() {
        String machine = (String)this.properties.get("java.naming.provider.url");
        String user = (String)this.properties.get("java.naming.security.principal");
        return "Could not logon to machine " + machine + " as user " + (user == null ? "guest" : user);
    }

    public static String stripOffCurrentDomain(String s) {
        String domain = NamingDirectory.getCurrentDomain() + ".";
        return s.startsWith(domain) ? s.substring(domain.length()) : s;
    }

    private static final Tracking getCurrentTracking() {
        Password password = (Password)currentPassword.get();
        return password != null ? password.getTracking() : DEFAULT_TRACKING;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static String checkForRoutingSuppressed() {
        if (AppServerConstants.get().isRoutingSuppressed()) {
            switch (LocalConstants.getMode()) {
                case webapp: {
                    LocalConstants.pushAppServerContext();
                    try {
                        String string = AppServerConstants.get().getThisMachineURL();
                        return string;
                    }
                    finally {
                        LocalConstants.popAppServerContext();
                    }
                }
                case appserver: 
                case appbeans: {
                    return AppServerConstants.get().getThisMachineURL();
                }
            }
            return null;
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    private static AppServerMap getAppServerMapEx() throws GLException {
        if (!AppServerMap.needsReload() || isReloadingAppServerMap) return AppServerMap.get();
        Class<NamingDirectory> clazz = NamingDirectory.class;
        synchronized (NamingDirectory.class) {
            if (!AppServerMap.needsReload()) return AppServerMap.get();
            NamingDirectory.reloadAppServerMap();
            // ** MonitorExit[var0] (shouldn't be in output)
            return AppServerMap.get();
        }
    }

    public static AppServerMap getAppServerMap() {
        try {
            return NamingDirectory.getAppServerMapEx();
        }
        catch (GLException glex) {
            return null;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AppServerMap reloadAppServerMap() throws GLException {
        if (NamingDirectory.isAppServerContext() && !pastStartup) {
            return null;
        }
        map = null;
        try {
            if (!AppServerConstants.get().isScalable()) {
                AppServerMap.setMap(map);
                AppServerMap appServerMap = map;
                return appServerMap;
            }
            isReloadingAppServerMap = true;
            try {
                if (NamingDirectory.isAppServerContext()) {
                    AppFunction.checkForReload();
                    Loader.load(new Loader.Read(){

                        @Override
                        public void execute(T2SharedConnection conn) throws GLException {
                            AppServerMap.setMap(map = AppServerLoader.get().loadServerMap(conn));
                        }
                    });
                    TopicSubscriptions.loadForApp();
                } else {
                    map = NamingDirectory.findAndLoadAppServerMap();
                    AppServerMap.setMap(map);
                    TopicSubscriptions.loadForWeb();
                }
            }
            finally {
                isReloadingAppServerMap = false;
            }
            AppServerMap appServerMap = map;
            return appServerMap;
        }
        finally {
            map = null;
        }
    }

    public static boolean isReloadingAppServerMap() {
        return isReloadingAppServerMap;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static AppServerMap findAndLoadAppServerMap() throws GLException {
        List<String> topologyMachineURLs = AppServerConstants.get().getTopologyMachineURLs();
        for (String machine : topologyMachineURLs) {
            if (!Functions.canInvokeAppServer(machine)) continue;
            NamingDirectory nd = NamingDirectory.get(AppFunction.LOGIN, machine);
            try {
                AppServerSessionHome home = (AppServerSessionHome)nd.lookup("AppServerSessionHome");
                AppServerSession session = home.create();
                AppServerMap appServerMap = session.loadAppServerMap();
                if (Log.idOn[LogIds.SCALABILITY.index]) {
                    Log.logID(LogIds.SCALABILITY, "Loaded topology from {0}: {1}", machine, appServerMap);
                }
                AppServerMap appServerMap2 = appServerMap;
                nd.release();
                return appServerMap2;
            }
            catch (Throwable throwable) {
                try {
                    nd.release();
                    throw throwable;
                }
                catch (Throwable t) {
                    t.printStackTrace();
                    GLException.factory(t);
                }
            }
        }
        throw new GLNoServerException("cause.allServersDown", "Unable to connect to any OTM Server: {servers}", new Object[][]{{"servers", topologyMachineURLs}});
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    private static WebServerMap getWebServerMapEx() throws GLException {
        if (!WebServerMap.needsReload() || isReloadingWebServerMap) return WebServerMap.get();
        Class<NamingDirectory> clazz = NamingDirectory.class;
        synchronized (NamingDirectory.class) {
            if (!WebServerMap.needsReload()) return WebServerMap.get();
            NamingDirectory.reloadWebServerMap();
            // ** MonitorExit[var0] (shouldn't be in output)
            return WebServerMap.get();
        }
    }

    public static WebServerMap getWebServerMap() {
        try {
            return NamingDirectory.getWebServerMapEx();
        }
        catch (GLException glex) {
            return null;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static WebServerMap reloadWebServerMap() throws GLException {
        if (NamingDirectory.isAppServerContext() && !pastStartup) {
            return null;
        }
        webmap = null;
        try {
            if (!WebServerConstants.get().isScalable()) {
                WebServerMap.setMap(webmap);
                WebServerMap webServerMap = webmap;
                return webServerMap;
            }
            isReloadingWebServerMap = true;
            try {
                if (NamingDirectory.isAppServerContext()) {
                    Loader.load(new Loader.Read(){

                        @Override
                        public void execute(T2SharedConnection conn) throws GLException {
                            WebServerMap.setMap(webmap = WebServerLoader.get().loadServerMap(conn));
                        }
                    });
                } else {
                    webmap = NamingDirectory.findAndLoadWebServerMap();
                    WebServerMap.setMap(webmap);
                }
            }
            finally {
                isReloadingWebServerMap = false;
            }
            WebServerMap webServerMap = webmap;
            return webServerMap;
        }
        finally {
            webmap = null;
        }
    }

    public static boolean isReloadingWebServerMap() {
        return isReloadingWebServerMap;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static WebServerMap findAndLoadWebServerMap() throws GLException {
        List<String> topologyMachineURLs = AppServerConstants.get().getTopologyMachineURLs();
        for (String machine : topologyMachineURLs) {
            if (!Functions.canInvokeAppServer(machine)) continue;
            NamingDirectory nd = NamingDirectory.get(machine);
            try {
                AppServerSessionHome home = (AppServerSessionHome)nd.lookup("AppServerSessionHome");
                AppServerSession session = home.create();
                WebServerMap webServerMap = session.loadWebServerMap();
                if (Log.idOn[LogIds.SCALABILITY.index]) {
                    Log.logID(LogIds.SCALABILITY, "Web Topology: {0}", webServerMap);
                }
                WebServerMap webServerMap2 = webServerMap;
                nd.release();
                return webServerMap2;
            }
            catch (Throwable throwable) {
                try {
                    nd.release();
                    throw throwable;
                }
                catch (Throwable t) {
                    t.printStackTrace();
                    GLException.factory(t);
                }
            }
        }
        throw new GLNoServerException("cause.allServersDown", "Unable to connect to any OTM Server: {servers}", new Object[][]{{"servers", topologyMachineURLs}});
    }

    public static Iterator<AppMachine> getAllAppMachines() {
        AppServerMap map = NamingDirectory.getAppServerMap();
        if (map != null) {
            return ((AppServerMachines)map.getAllMachines()).iterator();
        }
        String url = AppServerConstants.get().getDefaultMachineURL();
        return Collections.singleton(new AppMachine(url, url)).iterator();
    }

    public static Iterator<WebMachine> getAllWebMachines() {
        WebServerMap map = NamingDirectory.getWebServerMap();
        if (map != null) {
            return ((WebServerMachines)map.getAllMachines()).iterator();
        }
        List<String> webURLs = WebServerConstants.get().getTopologyMachineURLs();
        ArrayList<WebMachine> webMachines = new ArrayList<WebMachine>(webURLs.size());
        for (String url : webURLs) {
            webMachines.add(new WebMachine(url, url));
        }
        return webMachines.iterator();
    }

    public static boolean supportsMultipleWebMachines() {
        WebServerMap map = NamingDirectory.getWebServerMap();
        if (map != null) {
            return ((WebServerMachines)map.getAllMachines()).size() > 1;
        }
        List<String> webURLs = WebServerConstants.get().getTopologyMachineURLs();
        return webURLs.size() > 1;
    }

    public static void checkForRJVMShutdown(String machine, Throwable t) throws GLException {
        String msg;
        if (machine != null && t instanceof java.rmi.ConnectException && (msg = t.getMessage()) != null && msg.contains("RJVM has already been shutdown") && (NamingDirectory.areServicesCached(machine) || ASEJBRuntimeDeployer.isRemote(machine))) {
            NamingDirectory.clearServices(machine);
            ASEJBRuntimeDeployer.clearRemote(machine);
            return;
        }
        throw GLException.factory(t);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void main(String[] args) {
        NamingDirectory nd = null;
        try {
            nd = args.length == 1 ? NamingDirectory.get() : (args.length >= 2 ? NamingDirectory.get(args[0], Password.valueOf(args[1])) : NamingDirectory.get("GUEST.ADMIN", Password.DEFAULT));
            nd.lookup("ejb.Shipment");
            System.out.println("Found ejb.Shipment");
            Thread.sleep(4000L);
            System.out.println("after sleep");
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
        finally {
            if (nd != null) {
                nd.release();
            }
        }
        try {
            System.in.read();
        }
        catch (Exception exception) {
            // empty catch block
        }
    }

    static {
        GLProperties.get().registerProperty("glog.session.serverURL", "Server URL for server-to-server JNDI/bean lookup", "t3://localhost:7001");
        GLProperties.get().registerProperty("glog.session.delegatedServerURL", "Delegated Server URL for server-to-server JNDI/bean lookup", "t3://localhost:7001");
        GLProperties.get().registerProperty("glog.webserver.remoteClient", "If true, T3 connections should be cached in an HTTP session variable", "false");
        GLProperties.get().registerProperty("glog.webserver.localClient", "If true, T3 connections should be reestablished in each call to support Weblogic multi-threaded security assumptions", "false");
        currentDatabase = new ThreadLocal();
        currentUserRole = new ThreadLocal();
        currentDomain = new ThreadLocal();
        currentGLUser = new ThreadLocal();
        currentPassword = new ThreadLocal();
        httpSession = new ThreadLocal();
        isReloadingAppServerMap = false;
        isReloadingWebServerMap = false;
        pastStartup = false;
        pastActivate = false;
        allowStartupJNDI = new HashSet();
        runtimeDeploymentOn = "true".equals(GLProperties.get().getProperty("glog.util.deploy.RuntimeDeployment.on", "true"));
    }

    public static class NDContext
    extends InitialContext {
        public NDContext(Hashtable properties) throws NamingException {
            super(properties);
        }

        public Context getImpl() {
            return this.defaultInitCtx;
        }

        public void setImpl(Context initCtx) {
            this.defaultInitCtx = initCtx;
        }
    }
}
