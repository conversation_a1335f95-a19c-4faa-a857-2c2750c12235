/*
 * Decompiled with CFR 0.152.
 */
package glog.util.remote;

import glog.server.appserver.AppFunction;
import glog.util.exception.GLException;
import glog.util.remote.BeanBaseInvoker;
import glog.util.remote.BeanCreator;
import glog.util.remote.NamingDirectory;
import java.lang.reflect.Method;
import java.util.Hashtable;

public class Session<PERSON>eanHomeInvoker
extends BeanBaseInvoker {
    private NamingDirectory nd = null;
    private Method create;
    private Hashtable creatorInvokers = new Hashtable();
    private Object beanHome;

    public SessionBeanHomeInvoker(String beanName, BeanCreator[] creators, String server, AppFunction appFunction) throws GLException {
        this.nd = NamingDirectory.get(appFunction, server);
        this.init(this.nd.lookup(beanName), creators);
    }

    public SessionBeanHomeInvoker(String beanName, BeanCreator[] creators, String server) throws GLException {
        this(beanName, creators, server, null);
    }

    public SessionBeanHomeInvoker(String beanName, BeanCreator[] creators) throws GLException {
        this(beanName, creators, null);
    }

    public SessionBeanHomeInvoker(Object beanHome, BeanCreator[] creators) {
        this.init(beanHome, creators);
    }

    public Object create() throws GLException {
        return this.invoke(this.create, this.beanHome, new Object[0]);
    }

    public Object create(BeanCreator creator, Object[] args) throws GLException {
        Method creatorInvoker = (Method)this.creatorInvokers.get(creator);
        return this.invoke(creatorInvoker, this.beanHome, args);
    }

    protected void init(Object beanHome, BeanCreator[] creators) {
        try {
            this.beanHome = beanHome;
            Class<?> beanClass = beanHome.getClass();
            this.create = beanClass.getMethod("create", new Class[0]);
            if (creators != null) {
                for (int i = 0; i < creators.length; ++i) {
                    this.creatorInvokers.put(creators[i], beanClass.getMethod("create", creators[i].getClasses()));
                }
            }
        }
        catch (NoSuchMethodException nsmex) {
            nsmex.printStackTrace();
            throw new InternalError("Missing bean function");
        }
    }

    public void close() throws GLException {
        this.nd.release();
    }
}
