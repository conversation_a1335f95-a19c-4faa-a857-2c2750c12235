/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.util.GLProperties;
import glog.util.LocalTimestamp;
import glog.util.cache.StaticMap;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import java.util.Iterator;
import java.util.Map;
import java.util.SimpleTimeZone;
import java.util.StringTokenizer;
import java.util.TimeZone;

class AddTimeZone {
    private static final int MSEC_IN_HOUR = 3600000;
    private static String[] MONTHS = new String[]{"JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"};
    private static String[] DOWS = new String[]{"NONE", "SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"};
    private Map<String, TimeZone> customTimeZoneMap = new StaticMap("LocalTimestamp.customTimeZoneMap").get();
    private Map<String, TimeZone> timeZoneCache = new StaticMap("LocalTimestamp.timeZoneCache").get();
    private SimpleTimeZone simpleTimeZone;

    AddTimeZone() {
    }

    private int parseMonth(String month) throws GLException {
        for (int i = 0; i < MONTHS.length; ++i) {
            if (!MONTHS[i].equals(month)) continue;
            return i;
        }
        throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_UnknownMonth", null, new Object[][]{{"month", month}}), null);
    }

    private int parseDOW(String dow) throws GLException {
        boolean neg;
        boolean bl = neg = dow.length() > 0 && dow.charAt(0) == '-';
        if (neg) {
            dow = dow.substring(1);
        }
        for (int i = 0; i < DOWS.length; ++i) {
            if (!DOWS[i].equals(dow)) continue;
            return i * (neg ? -1 : 1);
        }
        throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_UnknownDOW", null, new Object[][]{{"dow", dow}}), null);
    }

    private SimpleTimeZone parseTimeZoneSpec(String timeZoneId, String timeZoneSpec) throws GLException {
        StringTokenizer tokenizer = new StringTokenizer(timeZoneSpec, ",");
        if (!tokenizer.hasMoreTokens()) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_MissingGMTOffset", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
        }
        double gmtOffsetInHours = Double.parseDouble(tokenizer.nextToken());
        int rawOffset = (int)(gmtOffsetInHours * 3600000.0);
        if (tokenizer.hasMoreTokens()) {
            int dstStartMonth = this.parseMonth(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTStartDay", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstStartDay = Integer.parseInt(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTStartDayOfWeek", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstStartDayOfWeek = this.parseDOW(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTStartTime", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstStartTime = (int)(Double.parseDouble(tokenizer.nextToken()) * 3600000.0);
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTEndMonth", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstEndMonth = this.parseMonth(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTEndDay", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstEndDay = Integer.parseInt(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTEndDayOfWeek", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstEndDayOfWeek = this.parseDOW(tokenizer.nextToken());
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTEndTime", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int dstEndTime = (int)(Double.parseDouble(tokenizer.nextToken()) * 3600000.0);
            if (!tokenizer.hasMoreTokens()) {
                throw GLException.factory((Cause)new GLException.CausedBy("cause.TimeZoneParse_DSTSavings", null, new Object[][]{{"timeZoneId", timeZoneId}}), null);
            }
            int daylightSavings = (int)(Double.parseDouble(tokenizer.nextToken()) * 3600000.0);
            return new SimpleTimeZone(rawOffset, timeZoneId, dstStartMonth, dstStartDay, dstStartDayOfWeek, dstStartTime, dstEndMonth, dstEndDay, dstEndDayOfWeek, dstEndTime, daylightSavings);
        }
        return new SimpleTimeZone(rawOffset, timeZoneId);
    }

    public void load() {
        try {
            this.customTimeZoneMap.put(LocalTimestamp.LOCAL_TZ.getID(), LocalTimestamp.LOCAL_TZ);
            Iterator<GLProperties.Entry> it = GLProperties.get().entries("glog.timezone.");
            while (it.hasNext()) {
                GLProperties.Entry entry = it.next();
                String timeZoneId = entry.getKey();
                String timeZoneSpec = entry.getValue();
                try {
                    SimpleTimeZone tz = this.parseTimeZoneSpec(timeZoneId, timeZoneSpec);
                    if (tz == null) {
                        throw new Exception();
                    }
                    this.customTimeZoneMap.put(tz.getID(), tz);
                }
                catch (Throwable t) {
                    GLException.factory(t);
                }
            }
            String[] ids = TimeZone.getAvailableIDs();
            for (int i = 0; i < ids.length; ++i) {
                TimeZone tz = TimeZone.getTimeZone(ids[i]);
                this.timeZoneCache.put(ids[i], tz);
            }
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public TimeZone getTimeZone(String id) {
        TimeZone out = this.customTimeZoneMap.get(id);
        if (out == null) {
            out = this.timeZoneCache.get(id);
        }
        if (out == null) {
            out = TimeZone.getTimeZone("UTC");
        }
        return out;
    }
}
