/*
 * Decompiled with CFR 0.152.
 */
package glog.util.ejb.preload;

import glog.util.file.FileUtils;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.StringTokenizer;
import java.util.TreeSet;

public class CreateEjbPreLoadXml {
    private BufferedReader in = null;
    private BufferedWriter out = null;
    private final String TAB = "\t";
    private final String NEXT_LINE = "\n";

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private void doIt(Collection preLoad, String path, String modulePath) {
        try {
            this.initXml(path);
            this.writeTillPreLoadMarker();
            this.writePreLoad(preLoad, modulePath);
            this.writeAfterPreLoadMarker();
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            try {
                if (this.in != null) {
                    this.in.close();
                }
                if (this.out != null) {
                    this.out.flush();
                    this.out.close();
                }
            }
            catch (IOException e) {}
        }
    }

    private void initXml(String path) throws Exception {
        this.in = new BufferedReader(new FileReader(path + "/application.xml.fresh"));
        this.out = new BufferedWriter(new FileWriter(path + "/application.xml", false));
    }

    private void writeTillPreLoadMarker() throws Exception {
        String line;
        while ((line = this.in.readLine()) != null && !line.trim().equals("<!-- Pre-Load Module entries marker -->")) {
            this.out.write(line + "\n");
        }
        this.out.flush();
    }

    private void writePreLoad(Collection preLoadList, String modulePath) throws Exception {
        Iterator it = preLoadList.iterator();
        while (it.hasNext()) {
            String jarName = it.next().toString();
            if (!this.checkJar(jarName, modulePath)) continue;
            this.out.write("\t<module>\n");
            this.out.write("\t\t<ejb>modules/" + jarName + "</ejb>" + "\n");
            this.out.write("\t</module>\n");
            this.out.flush();
        }
    }

    private void writeAfterPreLoadMarker() throws Exception {
        String line;
        while ((line = this.in.readLine()) != null) {
            this.out.write(line + "\n");
        }
        this.out.flush();
    }

    private boolean checkJar(String jarName, String path) throws Exception {
        File applicationFile = new File(path, jarName);
        if (!applicationFile.exists()) {
            System.err.println("Warning: Missing - " + path + "\\" + jarName);
            return false;
        }
        return true;
    }

    private static void help() {
        System.err.println("parsePreLoadEjb <pre-load-list path> <pre-load-list file> <xml-location> <module-path>");
        System.exit(-1);
    }

    public static void main(String[] args) throws Exception {
        if (args.length != 4) {
            CreateEjbPreLoadXml.help();
        }
        TreeSet preLoadSet = new TreeSet();
        StringTokenizer fileTokenizer = new StringTokenizer(args[1], ",");
        while (fileTokenizer.hasMoreTokens()) {
            String filename = fileTokenizer.nextToken();
            File f = new File(args[0], filename);
            ArrayList preLoadList = FileUtils.readLines(f.toString());
            preLoadSet.addAll(preLoadList);
        }
        new CreateEjbPreLoadXml().doIt(preLoadSet, args[2], args[3]);
    }
}
