/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.util.GLProperties;
import glog.util.StartupShutdown;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import java.util.HashSet;
import java.util.Properties;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OTMProxySelector {
    protected static final LogId LOG_ALIAS_DETAILS = LogIds.PROXY;
    protected ThreadLocal<Boolean> useProxy = new ThreadLocal();
    private static OTMProxySelector otmProxySelector = null;
    private static final String PROXY_HOST = "glog.integration.http.proxyHost";
    private static String httpProxyHost = GLProperties.get().getProperty("glog.integration.http.proxyHost");
    private static final String PROXY_PORT = "glog.integration.http.proxyPort";
    private static String httpProxyPort = GLProperties.get().getProperty("glog.integration.http.proxyPort");
    private static final String NON_PROXY_HOSTS = "glog.integration.http.nonProxyHosts";
    private static String httpNonProxyHosts = GLProperties.get().getProperty("glog.integration.http.nonProxyHosts");
    private static final String CLIENT_NON_PROXY_HOSTS = "glog.integration.http.client.nonProxyHosts";
    private static String clientHttpNonProxyHosts = GLProperties.get().getProperty("glog.integration.http.client.nonProxyHosts");
    private static String completeHttpNonProxyHosts = "";
    private static final String HTTP_NON_PROXY_HOSTS = "http.nonProxyHosts";
    private static final String HTTPS_NON_PROXY_HOSTS = "https.nonProxyHosts";
    private static final String DB_CONNECTION_URL = "glog.database.dbaOnly.t2client.connectionURL";
    private static String dbConnectionURL = GLProperties.get().getProperty("glog.database.dbaOnly.t2client.connectionURL");
    public static GLProperties glProperties = GLProperties.get();
    private static final String TAG_KEY = "\\(HOST=([a-zA-Z_0-9-]+)\\)";

    public ThreadLocal<Boolean> getUseProxy() {
        return this.useProxy;
    }

    public void setUseProxy(boolean useProxy) {
        this.setUseProxy(useProxy ? Boolean.TRUE : Boolean.FALSE);
    }

    public void setUseProxy(Boolean useProxy) {
        this.useProxy.set(useProxy);
    }

    private OTMProxySelector() {
    }

    public static OTMProxySelector getInstance() {
        if (otmProxySelector == null) {
            otmProxySelector = new OTMProxySelector();
        }
        return otmProxySelector;
    }

    private static void removeNonProxyHostsDupliates() {
        if (httpNonProxyHosts != null && httpNonProxyHosts.trim().length() > 0 || clientHttpNonProxyHosts != null && clientHttpNonProxyHosts.trim().length() > 0) {
            String individualNonProxyHost;
            StringTokenizer tokenizer;
            HashSet<String> nonProxyHostSets = new HashSet<String>();
            boolean isFirst = true;
            StringBuffer nonProxyHostsWithoutDuplicates = new StringBuffer();
            if (httpNonProxyHosts != null && httpNonProxyHosts.trim().length() > 0) {
                tokenizer = new StringTokenizer(httpNonProxyHosts, "|\"");
                while (tokenizer.hasMoreTokens()) {
                    individualNonProxyHost = tokenizer.nextToken();
                    if (individualNonProxyHost == null) continue;
                    if (individualNonProxyHost.startsWith("$")) {
                        individualNonProxyHost = glProperties.replaceMacros(individualNonProxyHost);
                    }
                    if (individualNonProxyHost.startsWith("$") || nonProxyHostSets.contains(individualNonProxyHost)) continue;
                    nonProxyHostSets.add(individualNonProxyHost);
                    if (!isFirst) {
                        nonProxyHostsWithoutDuplicates.append("|");
                    }
                    nonProxyHostsWithoutDuplicates.append(individualNonProxyHost);
                    isFirst = false;
                }
            }
            if (clientHttpNonProxyHosts != null && clientHttpNonProxyHosts.trim().length() > 0) {
                tokenizer = new StringTokenizer(clientHttpNonProxyHosts, "|\"");
                while (tokenizer.hasMoreTokens()) {
                    individualNonProxyHost = tokenizer.nextToken();
                    if (individualNonProxyHost == null) continue;
                    if (individualNonProxyHost.startsWith("$")) {
                        individualNonProxyHost = glProperties.replaceMacros(individualNonProxyHost);
                    }
                    if (individualNonProxyHost.startsWith("$") || nonProxyHostSets.contains(individualNonProxyHost)) continue;
                    nonProxyHostSets.add(individualNonProxyHost);
                    if (!isFirst) {
                        nonProxyHostsWithoutDuplicates.append("|");
                    }
                    nonProxyHostsWithoutDuplicates.append(individualNonProxyHost);
                    isFirst = false;
                }
            }
            if (dbConnectionURL != null && dbConnectionURL.trim().length() > 0) {
                Matcher matcher = Pattern.compile(TAG_KEY).matcher(dbConnectionURL);
                while (matcher.find()) {
                    String dbServer = matcher.group();
                    if ((dbServer = dbServer.substring(6, dbServer.length() - 1)).startsWith("$") || nonProxyHostSets.contains(dbServer)) continue;
                    nonProxyHostSets.add(dbServer);
                    if (!isFirst) {
                        nonProxyHostsWithoutDuplicates.append("|");
                    }
                    nonProxyHostsWithoutDuplicates.append(dbServer);
                    isFirst = false;
                }
            }
            completeHttpNonProxyHosts = nonProxyHostsWithoutDuplicates.toString();
            OTMProxySelector.setNonProxyHosts();
        }
    }

    private static void setNonProxyHosts() {
        Properties sysProps = System.getProperties();
        if (httpNonProxyHosts != null && httpNonProxyHosts.trim().length() > 0) {
            if (Log.idOn[OTMProxySelector.LOG_ALIAS_DETAILS.index]) {
                Log.logID(LOG_ALIAS_DETAILS, "OTMProxySelector : Setting http non proxy hosts to {0}.", httpNonProxyHosts);
            }
            sysProps.put(HTTP_NON_PROXY_HOSTS, completeHttpNonProxyHosts);
            sysProps.put(HTTPS_NON_PROXY_HOSTS, completeHttpNonProxyHosts);
        }
    }

    private static void setSystemProxyProperties() {
        if (httpProxyHost != null && httpProxyHost.trim().length() > 0 && httpProxyPort != null && httpProxyPort.trim().length() > 0) {
            Properties sysProps = System.getProperties();
            sysProps.put("http.proxyHost", httpProxyHost);
            sysProps.put("http.proxyPort", httpProxyPort);
            sysProps.put("https.proxyHost", httpProxyHost);
            sysProps.put("https.proxyPort", httpProxyPort);
        }
    }

    static {
        GLProperties.get();
        GLProperties.addListener(PROXY_HOST, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                httpProxyHost = glProperties.getProperty(OTMProxySelector.PROXY_HOST);
                OTMProxySelector.setSystemProxyProperties();
            }
        });
        GLProperties.get();
        GLProperties.addListener(PROXY_PORT, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                httpProxyPort = glProperties.getProperty(OTMProxySelector.PROXY_PORT);
                OTMProxySelector.setSystemProxyProperties();
            }
        });
        GLProperties.get();
        GLProperties.addListener(DB_CONNECTION_URL, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                dbConnectionURL = glProperties.getProperty(OTMProxySelector.DB_CONNECTION_URL);
                OTMProxySelector.removeNonProxyHostsDupliates();
            }
        });
        GLProperties.get();
        GLProperties.addListener(NON_PROXY_HOSTS, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                httpNonProxyHosts = glProperties.getProperty(OTMProxySelector.NON_PROXY_HOSTS);
                OTMProxySelector.removeNonProxyHostsDupliates();
            }
        });
        GLProperties.get();
        GLProperties.addListener(CLIENT_NON_PROXY_HOSTS, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                clientHttpNonProxyHosts = glProperties.getProperty(OTMProxySelector.CLIENT_NON_PROXY_HOSTS);
                OTMProxySelector.removeNonProxyHostsDupliates();
            }
        });
        OTMProxySelector.removeNonProxyHostsDupliates();
    }

    public static class Startup
    implements StartupShutdown {
        @Override
        public void load(T2SharedConnection conn) throws GLException {
        }

        @Override
        public void activate(T2SharedConnection conn) throws GLException {
            OTMProxySelector.setSystemProxyProperties();
        }

        @Override
        public void unload() throws GLException {
        }
    }
}
