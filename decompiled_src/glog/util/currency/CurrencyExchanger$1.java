/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.local.WebApp;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

static final class CurrencyExchanger.1
implements WebApp.Initializer<Map> {
    CurrencyExchanger.1() {
    }

    @Override
    public Map init(WebApp.Type type, Object ... args) {
        return Collections.synchronizedMap(new HashMap());
    }
}
