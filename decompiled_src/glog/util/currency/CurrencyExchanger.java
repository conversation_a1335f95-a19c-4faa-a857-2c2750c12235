/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.business.currency.CurrencyExchangeLoader;
import glog.util.GLProperties;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.currency.CurrencyExchangeNotify;
import glog.util.currency.CurrencyExchangeRate;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.local.WebApp;
import glog.util.uom.UOMConversionData;
import glog.util.uom.data.Duration;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

public class CurrencyExchanger
implements Serializable,
Cloneable {
    public static final String GC3_DEFAULT = "DEFAULT";
    public static final Duration ONE_DAY = new Duration(1.0, "D");
    public static final TreeMap RELOAD = new TreeMap(LocalDate.COMPARATOR);
    public static final double NO_RATE = -1.0;
    private static WebApp<Map> exchangeRatesByGid = new WebApp<Map>(new WebApp.Initializer<Map>(){

        @Override
        public Map init(WebApp.Type type, Object ... args) {
            return Collections.synchronizedMap(new HashMap());
        }
    }, new Object[0]);
    private String gid;
    public String xid;
    private LocalDate effectiveDate;
    private LocalDate expirationDate;
    private int maxPrecision;
    private int fractionalDigits;
    private boolean isOverrideFlag;
    private String domainName;
    private HashMap rates = new HashMap();
    private static Map currencyCodes = new HashMap();
    public static UnknownRateExchanger UNKNOWN_RATE_EXCHANGER = new UnknownRateExchanger();
    private static String BASE_CURRENCY = null;
    private static boolean USE_60 = false;

    public static Map getExchangeRatesByGid() {
        return exchangeRatesByGid.get();
    }

    public static void setExchangeRatesByGid(Map _exchangeRatesByGid) {
        if (exchangeRatesByGid.get() != _exchangeRatesByGid) {
            exchangeRatesByGid.get().clear();
            exchangeRatesByGid.get().putAll(_exchangeRatesByGid);
        }
    }

    public static Map getExchangeRatesByDate(String exchangeRateGid) {
        CurrencyExchanger.get(exchangeRateGid, null);
        return (Map)exchangeRatesByGid.get().get(exchangeRateGid);
    }

    public static void setExchangeRatesByDate(String exchangeRateGid, Map exchangeRatesByDate) {
        exchangeRatesByGid.get().put(exchangeRateGid, exchangeRatesByDate);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static CurrencyExchanger get(String exchangeRateGid, LocalDate date) {
        TreeMap exchangeRatesByDateForGid;
        if (exchangeRateGid == null) {
            exchangeRateGid = GC3_DEFAULT;
        }
        if ((exchangeRatesByDateForGid = (TreeMap)exchangeRatesByGid.get().get(exchangeRateGid)) == RELOAD) {
            WebApp<Map> webApp = exchangeRatesByGid;
            synchronized (webApp) {
                exchangeRatesByDateForGid = (TreeMap)exchangeRatesByGid.get().get(exchangeRateGid);
                if (exchangeRatesByDateForGid == RELOAD) {
                    try {
                        CurrencyExchangeLoader.loadCurrencyExchangeRates(exchangeRateGid);
                    }
                    catch (Throwable t) {
                        exchangeRatesByGid.get().remove(exchangeRateGid);
                        GLException.factory(t);
                    }
                    exchangeRatesByDateForGid = (TreeMap)exchangeRatesByGid.get().get(exchangeRateGid);
                }
            }
        }
        if (exchangeRatesByDateForGid != null) {
            LocalDate next;
            SortedMap effectiveRateMap;
            if (date == null) {
                date = new LocalDate();
            }
            if (!(effectiveRateMap = exchangeRatesByDateForGid.headMap(next = new LocalDate(date.add(ONE_DAY)))).isEmpty()) {
                CurrencyExchanger exchanger = (CurrencyExchanger)effectiveRateMap.get(effectiveRateMap.lastKey());
                LocalDate endDate = exchanger.expirationDate;
                if (endDate == null || endDate.dateCompare(date) >= 0) {
                    return exchanger;
                }
            }
        }
        return UNKNOWN_RATE_EXCHANGER;
    }

    public void add() {
        CurrencyExchanger.getExchangeRatesByDateForGid(this.gid).put(this.effectiveDate, this);
    }

    public static void load(String exchangeRateGid) throws GLException {
        exchangeRatesByGid.get().put(exchangeRateGid, RELOAD);
        CurrencyExchangeNotify.load(exchangeRateGid);
    }

    public static void reload(String exchangeRateGid) throws GLException {
        exchangeRatesByGid.get().put(exchangeRateGid, RELOAD);
        CurrencyExchangeNotify.reload(exchangeRateGid);
    }

    public static void unload(String exchangeRateGid) throws GLException {
        exchangeRatesByGid.get().remove(exchangeRateGid);
        CurrencyExchangeNotify.unload(exchangeRateGid);
    }

    public static void clear() {
        exchangeRatesByGid.get().clear();
    }

    private static TreeMap getExchangeRatesByDateForGid(String exchangeRateGid) {
        TreeMap map = (TreeMap)exchangeRatesByGid.get().get(exchangeRateGid);
        if (map == null) {
            map = new TreeMap(LocalDate.COMPARATOR);
            exchangeRatesByGid.get().put(exchangeRateGid, map);
        }
        return map;
    }

    public CurrencyExchanger(String gid, String xid, LocalDate effectiveDate, LocalDate expirationDate, Integer maxPrecision, Integer fractionalDigits, boolean isOverrideFlag, String domainName) {
        this.gid = gid;
        this.xid = xid;
        this.effectiveDate = effectiveDate;
        this.expirationDate = expirationDate;
        this.maxPrecision = maxPrecision == null ? -1 : maxPrecision;
        this.fractionalDigits = fractionalDigits == null ? -1 : fractionalDigits;
        this.isOverrideFlag = isOverrideFlag;
        this.domainName = domainName;
    }

    public String getGid() {
        return this.gid;
    }

    public String getXid() {
        return this.xid;
    }

    public LocalDate getEffectiveDate() {
        return this.effectiveDate;
    }

    public boolean isOverride() {
        return this.isOverrideFlag;
    }

    public HashMap getRates() {
        return this.rates;
    }

    public String getDomainName() {
        return this.domainName;
    }

    public HashMap getRates(String from) {
        HashMap result = (HashMap)this.rates.get(from);
        if (result == null) {
            result = new HashMap();
            this.rates.put(from, result);
        }
        return result;
    }

    public Currency convert(Currency currency, String to, LocalDate date) {
        return this.convert(currency, to, date, null);
    }

    public Currency convert(Currency currency, String to, LocalDate date, Info info) {
        return USE_60 ? this.convert60(currency, to, date, true, info, null) : this.convert61(currency, to, date, info);
    }

    public Currency convert61(Currency currency, String to, LocalDate date, Info info) {
        double factor = this.getRate(currency.type, to, info);
        if (factor == -1.0) {
            factor = this.getInvertedRate(currency.type, to, info);
        }
        if (factor == -1.0) {
            factor = this.getTriangulatedRate(currency.type, to, CurrencyExchanger.baseCurrency(), info);
        }
        if (factor == -1.0) {
            LocalDate prevEffDate = new LocalDate(this.effectiveDate.subtract(ONE_DAY));
            CurrencyExchanger previous = CurrencyExchanger.get(this.gid, prevEffDate);
            if (previous != UNKNOWN_RATE_EXCHANGER && previous != this) {
                previous = previous.merge(this);
                return previous.convert61(currency, to, date, info);
            }
            CurrencyExchanger deflt = CurrencyExchanger.get(GC3_DEFAULT, date);
            if (deflt != null && deflt != this && this.isOverrideFlag) {
                deflt = deflt.merge(this);
                return deflt.convert61(currency, to, date, info);
            }
        }
        if (factor == -1.0) {
            return UNKNOWN_RATE_EXCHANGER.convert61(currency, to, date, info);
        }
        double amount = currency.amount * factor;
        CurrencyExchangeRate rate = info != null ? info.rate : null;
        amount = rate != null ? rate.adjustForPrecision(amount, this.maxPrecision, this.fractionalDigits) : CurrencyExchangeRate.adjustForRatePrecision(amount, this.maxPrecision, this.fractionalDigits);
        return new Currency(amount, to);
    }

    public Currency convert60(Currency currency, String to, LocalDate date, boolean fromTo, Info info, Set triangulations) {
        String triangulation;
        String fromType = fromTo ? currency.type : to;
        String toType = fromTo ? to : currency.type;
        CurrencyExchangeRate rate = (CurrencyExchangeRate)this.getRates(fromType).get(toType);
        if (rate == null) {
            CurrencyExchanger deflt = CurrencyExchanger.get(GC3_DEFAULT, date);
            if (fromTo) {
                return this.convert60(currency, to, date, false, info, triangulations);
            }
            try {
                String baseCurrency = CurrencyExchanger.baseCurrency();
                if (!currency.type.equals(baseCurrency) && !to.equals(baseCurrency)) {
                    Currency inBase = this.convert60(currency, baseCurrency, date, true, info, triangulations);
                    return this.convert60(inBase, to, date, true, info, triangulations);
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            LocalDate prevEffDate = new LocalDate(this.effectiveDate.subtract(ONE_DAY));
            CurrencyExchanger previous = CurrencyExchanger.get(this.gid, prevEffDate);
            if (previous != UNKNOWN_RATE_EXCHANGER && previous != this) {
                return previous.convert60(currency, to, date, true, info, triangulations);
            }
            if (deflt == null || this == deflt || !this.isOverrideFlag) {
                return UNKNOWN_RATE_EXCHANGER.convert60(currency, to, date, fromTo, info, triangulations);
            }
            return deflt.convert60(currency, to, date, true, info, triangulations);
        }
        if (info != null) {
            info.rate = rate;
            info.exchange = this;
        }
        if ((triangulation = rate.getTriangulation()) != null) {
            String descriptor;
            if (triangulations == null) {
                triangulations = new HashSet<String>();
            }
            if (triangulations.contains(descriptor = this.getTriangulationDescriptor(currency.type, triangulation))) {
                return UNKNOWN_RATE_EXCHANGER.convert60(currency, to, date, fromTo, info, triangulations);
            }
            triangulations.add(descriptor);
            if (!currency.type.equals(triangulation) && !to.equals(triangulation)) {
                return this.convert60(this.convert60(currency, rate.getTriangulation(), date, true, info, triangulations), to, date, true, info, triangulations);
            }
            return UNKNOWN_RATE_EXCHANGER.convert60(currency, to, date, fromTo, info, triangulations);
        }
        double amount = fromTo ? currency.amount * rate.getRate() : currency.amount / rate.getRate();
        amount = rate.adjustForPrecision(amount, this.maxPrecision, this.fractionalDigits);
        return new Currency(amount, to);
    }

    public String toString() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        pw.println("gid = " + this.gid);
        pw.println("effective date = " + this.effectiveDate);
        pw.println("expiration date = " + this.expirationDate);
        pw.println("max precision = " + this.maxPrecision);
        pw.println("fractional digits = " + this.fractionalDigits);
        pw.println("is override = " + this.isOverrideFlag);
        pw.println("rates:");
        for (Map.Entry entry : this.rates.entrySet()) {
            String from = (String)entry.getKey();
            HashMap map = (HashMap)entry.getValue();
            pw.println("    from: " + from);
            for (Map.Entry entry2 : map.entrySet()) {
                String to = (String)entry2.getKey();
                CurrencyExchangeRate cer = (CurrencyExchangeRate)entry2.getValue();
                pw.println("        to: " + to + ", " + cer);
            }
        }
        return sw.toString();
    }

    public static void setEnumCode(String code) {
        currencyCodes.put(code, code);
    }

    public static String getEnumCode(String code) {
        String newCode = (String)currencyCodes.get(code);
        return newCode == null ? code : newCode;
    }

    protected Object clone() throws CloneNotSupportedException {
        CurrencyExchanger o = (CurrencyExchanger)super.clone();
        o.rates = new HashMap(this.rates);
        return o;
    }

    private CurrencyExchanger merge(CurrencyExchanger other) {
        try {
            CurrencyExchanger exchanger = (CurrencyExchanger)this.clone();
            for (Map.Entry entry : other.rates.entrySet()) {
                String from = (String)entry.getKey();
                Map toRates = (Map)entry.getValue();
                exchanger.getRates(from).putAll(toRates);
            }
            return exchanger;
        }
        catch (Throwable t) {
            return this;
        }
    }

    private String getTriangulationDescriptor(String from, String triangulation) {
        return from + "/" + triangulation;
    }

    private double getRate(String fromType, String toType, Info info) {
        CurrencyExchangeRate rate = (CurrencyExchangeRate)this.getRates(fromType).get(toType);
        if (rate == null) {
            return -1.0;
        }
        this.setInfo(info, rate);
        return this.accountForTriangulation(rate, fromType, toType);
    }

    private double getInvertedRate(String fromType, String toType, Info info) {
        CurrencyExchangeRate rate = (CurrencyExchangeRate)this.getRates(toType).get(fromType);
        if (rate == null) {
            return -1.0;
        }
        this.setInfo(info, rate);
        return 1.0 / this.accountForTriangulation(rate, toType, fromType);
    }

    private double accountForTriangulation(CurrencyExchangeRate rate, String from, String to) {
        String triangulation = rate.getTriangulation();
        if (triangulation == null) {
            return rate.getRate();
        }
        double factor = this.getTriangulatedRate(from, to, triangulation, null);
        return factor != -1.0 ? factor : rate.getRate();
    }

    private double getTriangulatedRate(String fromType, String toType, String triangulation, Info info) {
        if (triangulation == null || triangulation.equals(fromType) || triangulation.equals(toType)) {
            return -1.0;
        }
        double fromFactor = this.getRate(fromType, triangulation, info);
        if (fromFactor == -1.0) {
            fromFactor = this.getInvertedRate(fromType, triangulation, info);
        }
        if (fromFactor == -1.0) {
            return -1.0;
        }
        double toFactor = this.getRate(triangulation, toType, null);
        if (toFactor == -1.0) {
            toFactor = this.getInvertedRate(triangulation, toType, null);
        }
        if (toFactor == -1.0) {
            return -1.0;
        }
        return fromFactor * toFactor;
    }

    public void setInfo(Info info, CurrencyExchangeRate rate) {
        if (info != null) {
            info.rate = rate;
            info.exchange = this;
        }
    }

    public static String baseCurrency() {
        if (BASE_CURRENCY == null || BASE_CURRENCY.isEmpty()) {
            UOMConversionData uomConversionData = UOMConversionData.get();
            if (uomConversionData != null) {
                BASE_CURRENCY = uomConversionData.getStorageDefault(Currency.class);
            }
            if (BASE_CURRENCY == null || BASE_CURRENCY.isEmpty()) {
                BASE_CURRENCY = "USD";
            }
        }
        return BASE_CURRENCY;
    }

    public static void initProperties() {
        BASE_CURRENCY = GLProperties.get().getProperty("glog.currency.base");
        USE_60 = "true".equals(GLProperties.get().getProperty("glog.currency.60conversion", "false"));
        Currency.resetDefaultUseType();
    }

    static {
        CurrencyExchanger.initProperties();
        GLProperties.get();
        GLProperties.addListener("glog.currency.", new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                CurrencyExchanger.initProperties();
            }
        });
    }

    private static class UnknownRateExchanger
    extends CurrencyExchanger {
        private UnknownRateExchanger() {
            super("UNKNOWN", "UNKNOWN", new LocalDate(), null, null, null, false, "PUBLIC");
        }

        @Override
        public Currency convert(Currency currency, String to, LocalDate date, Info info) {
            String rateExchangeGid = currency.context != null ? currency.context.getRateExchangeGid() : null;
            LocalDate rateDate = currency.context != null ? currency.context.getDate() : null;
            GLException.factory((Cause)new GLException.CausedBy("cause.Currency_ConversionError", null, new Object[][]{{"exchangeGid", rateExchangeGid}, {"fromType", currency.type}, {"date", rateDate}, {"toType", to}}), null);
            if (info != null) {
                info.rate = CurrencyExchangeRate.UNKNOWN_RATE;
                info.exchange = this;
            }
            return new Currency(0.0, to);
        }

        @Override
        public Currency convert60(Currency currency, String to, LocalDate date, boolean fromTo, Info info, Set triangulations) {
            return this.convert(currency, to, date, info);
        }

        @Override
        public Currency convert61(Currency currency, String to, LocalDate date, Info info) {
            return this.convert(currency, to, date, info);
        }
    }

    public static class Info
    implements Serializable {
        public CurrencyExchanger exchange;
        public CurrencyExchangeRate rate;
    }
}
