/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.currency.CurrencyExchanger;
import glog.util.exception.GLException;
import glog.util.message.CacheRefresh;
import java.util.Map;

public static class CurrencyExchangeNotify.CurrencyClientRefresh
extends CacheRefresh {
    private String exchangeRateGid;
    private int cru;

    public CurrencyExchangeNotify.CurrencyClientRefresh(String exchangeRateGid, int cru) {
        this.exchangeRateGid = exchangeRateGid;
        this.cru = cru;
    }

    @Override
    public void onRefresh() throws GLException {
        switch (this.cru) {
            case 1: {
                CurrencyExchanger.reload(this.exchangeRateGid);
                break;
            }
            case 3: {
                CurrencyExchanger.reload(this.exchangeRateGid);
                break;
            }
            case 2: {
                CurrencyExchanger.unload(this.exchangeRateGid);
                break;
            }
        }
    }

    @Override
    public String toString() {
        return this.exchangeRateGid + ":" + this.cru;
    }

    @Override
    protected Map getProperties() {
        return WEB_SERVERS;
    }
}
