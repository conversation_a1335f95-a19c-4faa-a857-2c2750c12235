/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.LocalDate;
import glog.util.currency.Currency;

public class Currency.ConstContext
extends Currency.Context {
    protected Currency.ConstContext() {
        super(Currency.this);
    }

    @Override
    public Object clone() {
        return new Currency.Context(Currency.this, this.getRateExchangeGid(), this.calcBasisType, this.getDate(), this.getFunctionalCurrencyGid(), this.getFunctionalCurrencyAmt());
    }

    public void setRateExchangeGid(String rateExchangeGid) {
        throw new Error();
    }

    @Override
    public void setCalcBasisType(String calcBasisType) {
        throw new Error();
    }

    public void setDate(LocalDate date) {
        throw new Error();
    }

    public void setFunctionalCurrencyGid(String functionalCurrencyGid) {
        throw new Error();
    }

    public void setFunctionalCurrencyAmount(double functionalCurrencyAmount) {
        throw new Error();
    }

    @Override
    public void setContextFieldValue(int fieldFor, Object value) {
        throw new Error();
    }
}
