/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.GLProperties;
import glog.util.currency.CurrencyExchanger;
import glog.util.exception.GLException;

static final class CurrencyExchanger.2
extends GLProperties.Listener {
    CurrencyExchanger.2() {
    }

    @Override
    public void onPropertyChange(GLProperties.Change change) throws GLException {
        CurrencyExchanger.initProperties();
    }
}
