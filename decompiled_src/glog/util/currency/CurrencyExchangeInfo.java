/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import java.io.Serializable;
import java.util.Date;

public class CurrencyExchangeInfo
implements Serializable {
    private Date effectiveDate = null;
    private double exchangeRate = 0.0;

    public CurrencyExchangeInfo() {
    }

    public CurrencyExchangeInfo(double rate, Date date) {
        this.exchangeRate = rate;
        this.effectiveDate = date;
    }

    public Date getEffectiveDate() {
        return this.effectiveDate;
    }

    public void setEffectiveDate(Date date) {
        this.effectiveDate = date;
    }

    public double getExchangeRate() {
        return this.exchangeRate;
    }

    public void setExchangeRate(double rate) {
        this.exchangeRate = rate;
    }
}
