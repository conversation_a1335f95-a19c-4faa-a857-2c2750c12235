/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.currency.CurrencyExchangeRate;
import glog.util.currency.CurrencyExchanger;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import java.util.Set;

private static class CurrencyExchanger.UnknownRateExchanger
extends CurrencyExchanger {
    private CurrencyExchanger.UnknownRateExchanger() {
        super("UNKNOWN", "UNKNOWN", new LocalDate(), null, null, null, false, "PUBLIC");
    }

    @Override
    public Currency convert(Currency currency, String to, LocalDate date, CurrencyExchanger.Info info) {
        String rateExchangeGid = currency.context != null ? currency.context.getRateExchangeGid() : null;
        LocalDate rateDate = currency.context != null ? currency.context.getDate() : null;
        GLException.factory((Cause)new GLException.CausedBy("cause.Currency_ConversionError", null, new Object[][]{{"exchangeGid", rateExchangeGid}, {"fromType", currency.type}, {"date", rateDate}, {"toType", to}}), null);
        if (info != null) {
            info.rate = CurrencyExchangeRate.UNKNOWN_RATE;
            info.exchange = this;
        }
        return new Currency(0.0, to);
    }

    @Override
    public Currency convert60(Currency currency, String to, LocalDate date, boolean fromTo, CurrencyExchanger.Info info, Set triangulations) {
        return this.convert(currency, to, date, info);
    }

    @Override
    public Currency convert61(Currency currency, String to, LocalDate date, CurrencyExchanger.Info info) {
        return this.convert(currency, to, date, info);
    }
}
