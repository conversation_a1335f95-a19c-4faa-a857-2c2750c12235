/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.ServletException
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 */
package glog.util.currency;

import glog.server.appserver.AppServerConstants;
import glog.server.appserver.WebMachine;
import glog.util.currency.CurrencyExchanger;
import glog.util.exception.GLException;
import glog.util.message.CacheRefresh;
import glog.util.message.CacheRefreshTopic;
import glog.util.remote.NamingDirectory;
import glog.util.transaction.GLTransactionHelper;
import glog.webserver.util.SignedServlet;
import java.io.IOException;
import java.util.Iterator;
import java.util.Map;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CurrencyExchangeNotify {
    public static void load(String exchangeRateGid) throws GLException {
        CurrencyExchangeNotify.notify(new CurrencyClientRefresh(exchangeRateGid, 1));
    }

    public static void reload(String exchangeRateGid) throws GLException {
        CurrencyExchangeNotify.notify(new CurrencyClientRefresh(exchangeRateGid, 3));
    }

    public static void unload(String exchangeRateGid) throws GLException {
        CurrencyExchangeNotify.notify(new CurrencyClientRefresh(exchangeRateGid, 2));
    }

    public static void notify(CurrencyClientRefresh refresh) throws GLException {
        if (!NamingDirectory.isPastStartup()) {
            return;
        }
        GLTransactionHelper.registerSynchronization(new CurrencyClientRefreshScheduler(refresh));
    }

    public static class CurrencyClientRefreshServlet
    extends SignedServlet {
        private CurrencyClientRefresh refresh;

        public CurrencyClientRefreshServlet(CurrencyClientRefresh refresh) {
            this.refresh = refresh;
        }

        @Override
        public void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
            try {
                this.refresh.onRefresh();
            }
            catch (Throwable t) {
                throw new ServletException(t);
            }
        }

        @Override
        protected boolean handleSocketException(Exception e) {
            if (super.handleSocketException(e)) {
                GLException.factory(e);
                return true;
            }
            return false;
        }
    }

    public static class CurrencyClientRefresh
    extends CacheRefresh {
        private String exchangeRateGid;
        private int cru;

        public CurrencyClientRefresh(String exchangeRateGid, int cru) {
            this.exchangeRateGid = exchangeRateGid;
            this.cru = cru;
        }

        @Override
        public void onRefresh() throws GLException {
            switch (this.cru) {
                case 1: {
                    CurrencyExchanger.reload(this.exchangeRateGid);
                    break;
                }
                case 3: {
                    CurrencyExchanger.reload(this.exchangeRateGid);
                    break;
                }
                case 2: {
                    CurrencyExchanger.unload(this.exchangeRateGid);
                    break;
                }
            }
        }

        @Override
        public String toString() {
            return this.exchangeRateGid + ":" + this.cru;
        }

        @Override
        protected Map getProperties() {
            return WEB_SERVERS;
        }
    }

    public static class CurrencyClientRefreshScheduler
    extends CacheRefreshTopic.Scheduler {
        private CurrencyClientRefresh refresh;

        public CurrencyClientRefreshScheduler(CurrencyClientRefresh refresh) {
            super(refresh);
            this.refresh = refresh;
        }

        @Override
        public void afterCompletion(int status) {
            if (status == 3) {
                if (AppServerConstants.get().isScalable()) {
                    super.afterCompletion(status);
                } else {
                    Iterator<WebMachine> it = NamingDirectory.getAllWebMachines();
                    while (it.hasNext()) {
                        try {
                            WebMachine webMachine = it.next();
                            String webURL = webMachine.getURL();
                            new CurrencyClientRefreshServlet(this.refresh).callForVoid(webURL);
                        }
                        catch (Throwable throwable) {}
                    }
                }
            }
        }
    }
}
