/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.servlet.ServletException
 *  javax.servlet.http.HttpServletRequest
 *  javax.servlet.http.HttpServletResponse
 */
package glog.util.currency;

import glog.util.currency.CurrencyExchangeNotify;
import glog.util.exception.GLException;
import glog.webserver.util.SignedServlet;
import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public static class CurrencyExchangeNotify.CurrencyClientRefreshServlet
extends SignedServlet {
    private CurrencyExchangeNotify.CurrencyClientRefresh refresh;

    public CurrencyExchangeNotify.CurrencyClientRefreshServlet(CurrencyExchangeNotify.CurrencyClientRefresh refresh) {
        this.refresh = refresh;
    }

    @Override
    public void service(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            this.refresh.onRefresh();
        }
        catch (Throwable t) {
            throw new ServletException(t);
        }
    }

    @Override
    protected boolean handleSocketException(Exception e) {
        if (super.handleSocketException(e)) {
            GLException.factory(e);
            return true;
        }
        return false;
    }
}
