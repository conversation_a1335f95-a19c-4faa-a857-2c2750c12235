/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.server.appserver.AppServerConstants;
import glog.server.appserver.WebMachine;
import glog.util.currency.CurrencyExchangeNotify;
import glog.util.message.CacheRefreshTopic;
import glog.util.remote.NamingDirectory;
import java.util.Iterator;

public static class CurrencyExchangeNotify.CurrencyClientRefreshScheduler
extends CacheRefreshTopic.Scheduler {
    private CurrencyExchangeNotify.CurrencyClientRefresh refresh;

    public CurrencyExchangeNotify.CurrencyClientRefreshScheduler(CurrencyExchangeNotify.CurrencyClientRefresh refresh) {
        super(refresh);
        this.refresh = refresh;
    }

    @Override
    public void afterCompletion(int status) {
        if (status == 3) {
            if (AppServerConstants.get().isScalable()) {
                super.afterCompletion(status);
            } else {
                Iterator<WebMachine> it = NamingDirectory.getAllWebMachines();
                while (it.hasNext()) {
                    try {
                        WebMachine webMachine = it.next();
                        String webURL = webMachine.getURL();
                        new CurrencyExchangeNotify.CurrencyClientRefreshServlet(this.refresh).callForVoid(webURL);
                    }
                    catch (Throwable throwable) {}
                }
            }
        }
    }
}
