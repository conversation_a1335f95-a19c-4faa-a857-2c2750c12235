/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;

public class CurrencyExchangeRate
implements Serializable {
    public static CurrencyExchangeRate UNKNOWN_RATE = new CurrencyExchangeRate(0.0, null, null, null);
    private static final double LOG_10 = Math.log(10.0);
    private double rate;
    private String triangulation;
    private int maxPrecision;
    private int fractionalDigits;

    public CurrencyExchangeRate(double rate, String triangulation, Integer maxPrecision, Integer fractionalDigits) {
        this.rate = rate;
        this.triangulation = triangulation;
        this.maxPrecision = maxPrecision == null ? -1 : maxPrecision;
        this.fractionalDigits = fractionalDigits == null ? -1 : fractionalDigits;
    }

    public double adjustForPrecision(double amount, int maxPrecision, int fractionalDigits) {
        maxPrecision = Math.max(this.maxPrecision, maxPrecision);
        fractionalDigits = Math.max(this.fractionalDigits, fractionalDigits);
        return CurrencyExchangeRate.adjustForRatePrecision(amount, maxPrecision, fractionalDigits);
    }

    public static double adjustForRatePrecision(double amount, int maxPrecision, int fractionalDigits) {
        if (maxPrecision > 0 || fractionalDigits >= 0) {
            int numWholeDigits = (int)(Math.log(amount) / LOG_10) + 1;
            if (maxPrecision > 0) {
                if (numWholeDigits > maxPrecision) {
                    amount -= amount % Math.pow(10.0, maxPrecision - 1);
                    fractionalDigits = 0;
                } else {
                    int frac = maxPrecision - numWholeDigits;
                    int n = fractionalDigits = fractionalDigits >= 0 ? Math.min(fractionalDigits, frac) : frac;
                }
            }
            if (fractionalDigits >= 0) {
                double pow = Math.pow(10.0, fractionalDigits);
                amount = Math.rint(amount * pow) / pow;
            }
        }
        return amount;
    }

    public String getTriangulation() {
        return this.triangulation;
    }

    public double getRate() {
        return this.rate;
    }

    public String toString() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        pw.print("rate=");
        pw.print(this.rate);
        pw.print(',');
        pw.print("triangulation=");
        pw.print(this.triangulation);
        pw.print(',');
        pw.print("maxPrecision=");
        pw.print(this.maxPrecision);
        pw.print(',');
        pw.print("fractionalDigits=");
        pw.print(this.fractionalDigits);
        pw.print(')');
        return sw.toString();
    }

    public static final String getCurrencyNumFromCode(String code) {
        return code;
    }
}
