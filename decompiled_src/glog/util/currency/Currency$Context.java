/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.noserver.SqlColumn;
import glog.util.uom.UOMContext;
import java.util.Date;

public class Currency.Context
implements UOMContext {
    private String rateExchangeGid;
    protected String calcBasisType;
    private LocalDate date;
    private String functCurrencyGid;
    private double functCurrencyAmount;

    protected Currency.Context() {
    }

    protected Currency.Context(Currency.Context context) {
        this(context.rateExchangeGid, context.calcBasisType, context.date, context.functCurrencyGid, context.functCurrencyAmount);
    }

    public Currency.Context(String rateExchangeGid, String calcBasisType, LocalDate date) {
        this(rateExchangeGid, calcBasisType, date, null, 0.0);
    }

    public Currency.Context(String rateExchangeGid, String calcBasisType, LocalDate date, String functCurrencyGid) {
        this(rateExchangeGid, calcBasisType, date, functCurrencyGid, 0.0);
    }

    public Currency.Context(String rateExchangeGid, String calcBasisType, LocalDate date, String functCurrencyGid, double functCurrencyAmount) {
        this.rateExchangeGid = rateExchangeGid;
        this.calcBasisType = calcBasisType;
        this.date = date;
        this.functCurrencyGid = functCurrencyGid;
        this.functCurrencyAmount = functCurrencyAmount;
        Currency.this.context = this;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Currency.Context)) {
            return false;
        }
        Currency.Context oc = (Currency.Context)o;
        return Functions.equals(this.rateExchangeGid, oc.rateExchangeGid) && Functions.equals(this.date, oc.date) && Functions.equals(this.calcBasisType, oc.calcBasisType) && Functions.equals(this.functCurrencyGid, oc.functCurrencyGid) && Functions.equals(this.getFunctionalCurrencyAmount(), oc.getFunctionalCurrencyAmount());
    }

    public int hashCode() {
        return Functions.hashCode(this.rateExchangeGid) + Functions.hashCode(this.date) + Functions.hashCode(this.calcBasisType) + Functions.hashCode(this.functCurrencyGid) + Functions.hashCode(new Double(this.functCurrencyAmount));
    }

    @Override
    public Object clone() {
        try {
            return super.clone();
        }
        catch (Throwable t) {
            t.printStackTrace();
            return null;
        }
    }

    public String getRateExchangeGid() {
        return this.rateExchangeGid;
    }

    private void setRateExchangeGid(String rateExchangeGid) {
        this.rateExchangeGid = rateExchangeGid;
    }

    public String getCalcBasisType() {
        return this.calcBasisType;
    }

    public void setCalcBasisType(String calcBasisType) {
        this.calcBasisType = calcBasisType;
    }

    public LocalDate getDate() {
        return this.date;
    }

    private void setDate(LocalDate date) {
        this.date = date;
    }

    public String getFunctionalCurrencyGid() {
        return this.functCurrencyGid;
    }

    private void setFunctionalCurrencyGid(String functionalCurrencyGid) {
        this.functCurrencyGid = functionalCurrencyGid;
    }

    public double getFunctionalCurrencyAmt() {
        Double amt = this.getFunctionalCurrencyAmount();
        return amt != null ? amt : 0.0;
    }

    public Double getFunctionalCurrencyAmount() {
        if (this.date != null && this.functCurrencyGid != null) {
            if (Currency.this.recalcFunctAmount) {
                Currency functCurrency = Currency.this.convert(this.functCurrencyGid);
                this.functCurrencyAmount = functCurrency != null ? functCurrency.getCurrencyAmount() : 0.0;
                Currency.this.recalcFunctAmount = false;
            }
            return new Double(this.functCurrencyAmount);
        }
        return null;
    }

    private void setFunctionalCurrencyAmount(double functionalCurrencyAmount) {
        this.functCurrencyAmount = functionalCurrencyAmount;
    }

    @Override
    public int[] getContextFields() {
        return new int[]{3, 4, 5, 6};
    }

    @Override
    public int[] getDisplayContextFields() {
        return new int[]{3, 4, 5, 6};
    }

    @Override
    public int[] getUpdateContextFields() {
        return new int[]{5, 6};
    }

    @Override
    public void setContextFieldValue(int fieldFor, Object value) {
        switch (fieldFor) {
            case 3: {
                if (value instanceof String) {
                    this.setDate((LocalDate)LocalDate.valueOf((String)value));
                    break;
                }
                this.setDate(new LocalDate((Date)value));
                break;
            }
            case 4: {
                this.setRateExchangeGid((String)value);
                break;
            }
            case 5: {
                this.setFunctionalCurrencyGid((String)value);
                break;
            }
            case 6: {
                if (value instanceof String) {
                    this.setFunctionalCurrencyAmount(value == null ? 0.0 : new Double((String)value));
                    break;
                }
                this.setFunctionalCurrencyAmount(value == null ? 0.0 : SqlColumn.getDoubleFromDB(value));
                break;
            }
        }
    }

    @Override
    public Object getContextFieldValue(int fieldFor) {
        switch (fieldFor) {
            case 3: {
                return this.getDate();
            }
            case 4: {
                return this.getRateExchangeGid();
            }
            case 5: {
                return this.getFunctionalCurrencyGid();
            }
            case 6: {
                return this.getFunctionalCurrencyAmount();
            }
        }
        return null;
    }

    @Override
    public String getContextFieldXMLTag(int fieldFor) {
        switch (fieldFor) {
            case 3: {
                return "EXCHG_DATE";
            }
            case 4: {
                return "EXCHG_ID";
            }
            case 5: {
                return "FUNCT_ID";
            }
            case 6: {
                return "FUNCT_AMT";
            }
        }
        return null;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("(exchange=").append(this.rateExchangeGid).append(",date=").append(this.date).append(",basis=").append(this.calcBasisType).append(",functCurrencyAmount=").append(this.functCurrencyAmount).append(",functCurrencyGid=").append(this.functCurrencyGid).append(")");
        return sb.toString();
    }

    static /* synthetic */ void access$000(Currency.Context x0, LocalDate x1) {
        x0.setDate(x1);
    }

    static /* synthetic */ void access$100(Currency.Context x0, String x1) {
        x0.setRateExchangeGid(x1);
    }

    static /* synthetic */ void access$200(Currency.Context x0, String x1) {
        x0.setFunctionalCurrencyGid(x1);
    }

    static /* synthetic */ void access$300(Currency.Context x0, double x1) {
        x0.setFunctionalCurrencyAmount(x1);
    }
}
