/*
 * Decompiled with CFR 0.152.
 */
package glog.util.currency;

import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.Measurable;
import glog.util.TypeUnit;
import glog.util.currency.CurrencyExchanger;
import glog.util.exception.GLException;
import glog.util.jdbc.noserver.SqlColumn;
import glog.util.uom.UOMContext;
import glog.util.uom.UOMLoader;
import java.io.Serializable;
import java.text.NumberFormat;
import java.util.Date;
import java.util.Locale;
import java.util.StringTokenizer;

public class Currency
implements TypeUnit,
Measurable,
Serializable,
Comparable {
    double amount = 0.0;
    String type = Currency.getDefaultUseType();
    Context context = null;
    private boolean typeEnumed = true;
    private Currency forDisplay = null;
    private static String defaultUseType = null;
    private boolean recalcFunctAmount = false;
    private static Currency ZERO = null;
    public static Context STANDARD_CONTEXT = new Currency().new ConstContext();

    public Currency() {
    }

    public Currency(double amount) {
        this.amount = amount;
    }

    public Currency(Double amount) {
        this.amount = amount;
    }

    public Currency(double amount, String type) {
        this.amount = amount;
        this.setType(type);
    }

    public Currency(Double amount, String type) {
        this((double)amount, type);
    }

    public void setRateDate(LocalDate date) {
        this.getCurrencyContext().setDate(date);
        this.recalcFunctionalAmount();
    }

    public LocalDate getRateDate() {
        return this.context != null ? this.context.getDate() : null;
    }

    public void setRateExchange(String rateExchangeGid) {
        this.getCurrencyContext().setRateExchangeGid(rateExchangeGid);
        this.recalcFunctionalAmount();
    }

    public String getRateExchange() {
        return this.context != null ? this.context.getRateExchangeGid() : null;
    }

    public void setCalculationBasis(String calcBasisType) {
        this.getCurrencyContext().setCalcBasisType(calcBasisType);
    }

    public String getCalculationBasis() {
        return this.context != null ? this.context.getCalcBasisType() : null;
    }

    public void setFunctionalCurrencyGid(String functionalCurrencyGid) {
        this.getCurrencyContext().setFunctionalCurrencyGid(functionalCurrencyGid);
        this.recalcFunctionalAmount();
    }

    public String getFunctionalCurrencyGid() {
        return this.context != null ? this.context.getFunctionalCurrencyGid() : null;
    }

    public void setFunctionalCurrencyAmount(double functionalCurrencyAmount) {
        this.getCurrencyContext().setFunctionalCurrencyAmount(functionalCurrencyAmount);
        this.functionalAmountSet();
    }

    public double getFunctionalCurrencyAmount() {
        return this.context != null ? this.context.getFunctionalCurrencyAmt() : 0.0;
    }

    public Currency convert(String toType) {
        return this.convert(toType, null);
    }

    public Currency convert(String toType, CurrencyExchanger.Info info) {
        this.enumType();
        if (toType == this.type || toType.equals(this.type)) {
            return this;
        }
        CurrencyExchanger theExchanger = CurrencyExchanger.get(this.getRateExchange(), this.getRateDate());
        Currency result = theExchanger.convert(this, toType, this.getRateDate(), info);
        result.setContext(this.context);
        return result;
    }

    public final Currency toStandard() {
        return this.convert(Currency.getDefaultUseType());
    }

    public final int compareTo(Currency other) {
        double diff;
        Currency inBasis = this.getInBasis();
        double otherval = other.amount;
        if (other.type != inBasis.type) {
            otherval = other.convert((String)inBasis.type).amount;
        }
        return (diff = inBasis.amount - otherval) > 0.0 ? 1 : (diff < 0.0 ? -1 : 0);
    }

    @Override
    public int compareTo(Object other) {
        return this.compareTo((Currency)other);
    }

    @Override
    public Object absolute(Object other) {
        Currency currency = this.subtract((Currency)other);
        currency.amount = Math.abs(currency.amount);
        return currency;
    }

    @Override
    public double relative(Object other) {
        Currency currency = (Currency)this.absolute(other);
        return currency.divide(this);
    }

    public final Currency add(Currency other) {
        Currency inBasis = this.getInBasis();
        double otherval = other.amount;
        if (other.type != inBasis.type) {
            otherval = other.convert((String)inBasis.type).amount;
        }
        Currency result = new Currency(inBasis.amount + otherval, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency subtract(Currency other) {
        Currency inBasis = this.getInBasis();
        double otherval = other.amount;
        if (other.type != inBasis.type) {
            otherval = other.convert((String)inBasis.type).amount;
        }
        Currency result = new Currency(inBasis.amount - otherval, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final double divide(Currency other) {
        Currency inBasis = this.getInBasis();
        double otherval = other.amount;
        if (other.type != inBasis.type) {
            otherval = other.convert((String)inBasis.type).amount;
        }
        return inBasis.amount / otherval;
    }

    public final Currency increment(double inc) {
        Currency inBasis = this.getInBasis();
        Currency result = new Currency(inBasis.amount + inc, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency decrement(double dec) {
        Currency inBasis = this.getInBasis();
        Currency result = new Currency(inBasis.amount - dec, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency multiply(double scale) {
        Currency inBasis = this.getInBasis();
        Currency result = new Currency(inBasis.amount * scale, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency divide(double scale) {
        Currency inBasis = this.getInBasis();
        Currency result = new Currency(inBasis.amount / scale, inBasis.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency negative() {
        Currency result = new Currency(-this.amount, this.type);
        result.setContext(this.context);
        return result;
    }

    public final Currency round(int fractionalDigits) {
        return this.round(fractionalDigits, false);
    }

    public final Currency round(int fractionalDigits, boolean useInBasis) {
        Currency basis;
        Currency currency = basis = useInBasis ? this.getInBasis() : this;
        if (fractionalDigits >= 0) {
            double pow = Math.pow(10.0, fractionalDigits);
            double decimalPointMoved = basis.amount * pow;
            double roundedInt = Math.rint(decimalPointMoved);
            Currency result = new Currency(roundedInt / pow, basis.type);
            result.setContext(this.context);
            return result;
        }
        return basis;
    }

    public final Currency floor(int fractionalDigits) {
        return this.floor(fractionalDigits, false);
    }

    public final Currency floor(int fractionalDigits, boolean useInBasis) {
        Currency basis;
        Currency currency = basis = useInBasis ? this.getInBasis() : this;
        if (fractionalDigits >= 0) {
            double pow = Math.pow(10.0, fractionalDigits);
            double decimalPointMoved = basis.amount * pow;
            double roundedInt = Math.floor(decimalPointMoved);
            Currency result = new Currency(roundedInt / pow, basis.type);
            result.setContext(this.context);
            return result;
        }
        return basis;
    }

    public final Currency ceil(int fractionalDigits) {
        return this.ceil(fractionalDigits, false);
    }

    public final Currency ceil(int fractionalDigits, boolean useInBasis) {
        Currency basis;
        Currency currency = basis = useInBasis ? this.getInBasis() : this;
        if (fractionalDigits >= 0) {
            double pow = Math.pow(10.0, fractionalDigits);
            double decimalPointMoved = basis.amount * pow;
            double roundedInt = Math.ceil(decimalPointMoved);
            Currency result = new Currency(roundedInt / pow, basis.type);
            result.setContext(this.context);
            return result;
        }
        return basis;
    }

    public final Currency truncate(int fractionalDigits) {
        return this.round(fractionalDigits, true);
    }

    public final Currency getInBasis() {
        Currency result = this;
        String calcBasisType = this.getCalculationBasis();
        if (calcBasisType != null && calcBasisType != this.type) {
            result = this.convert(calcBasisType);
        }
        return result;
    }

    public final double getAmount(String asType) {
        this.enumType();
        return asType == null || this.type == asType || this.type.equals(asType) ? this.amount : this.convert((String)asType).amount;
    }

    public final double getAmount() {
        return this.getAmount(Currency.getDefaultUseType());
    }

    public final void setAmount(double amount, String asType) {
        this.amount = amount;
        this.setType(asType);
        this.recalcFunctionalAmount();
    }

    public final void setAmount(double amount) {
        this.setAmount(amount, Currency.getDefaultUseType());
    }

    public double getCurrencyAmount() {
        return this.amount;
    }

    public void setCurrencyAmount(double amount) {
        this.amount = amount;
        this.recalcFunctionalAmount();
    }

    public String getCurrencyType() {
        return this.type;
    }

    public void setCurrencyType(String type) {
        this.setType(type);
    }

    public String toString() {
        return Functions.formatNumber(this.amount) + " " + this.getType();
    }

    public static Currency valueOf(String currency) {
        if (currency == null || currency.length() == 0) {
            return null;
        }
        StringTokenizer tokenizer = new StringTokenizer(currency);
        Double val = Double.valueOf(tokenizer.nextToken());
        String type = tokenizer.hasMoreTokens() ? tokenizer.nextToken() : Currency.getDefaultUseType();
        return new Currency(val, type);
    }

    public static Currency valueOf(String currency, Locale locale) {
        if (currency == null || currency.length() == 0) {
            return null;
        }
        Currency obj = null;
        try {
            StringTokenizer tokenizer = new StringTokenizer(currency);
            Double val = NumberFormat.getInstance(locale).parse(tokenizer.nextToken()).doubleValue();
            String type = tokenizer.hasMoreTokens() ? tokenizer.nextToken() : Currency.getDefaultUseType();
            obj = new Currency(val, type);
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
        return obj;
    }

    private String getType(Object asType) {
        return asType == null ? Currency.getDefaultUseType() : (String)asType;
    }

    @Override
    public final Object getStandardType() {
        return Currency.getDefaultUseType();
    }

    @Override
    public Object getUnits(Object asType) {
        Currency converted = this.convert(this.getType(asType));
        return new Double(converted.amount);
    }

    @Override
    public void setUnits(Object units, Object asType) {
        this.amount = (Double)units;
        this.type = this.getType(asType);
        this.recalcFunctionalAmount();
    }

    @Override
    public Object getUnits() {
        return this.getUnits(null);
    }

    @Override
    public void setUnits(Object units) {
        this.setUnits(units, null);
    }

    @Override
    public Object getType() {
        return this.type;
    }

    @Override
    public void setType(Object toType) {
        if (toType != null) {
            this.type = (String)toType;
            this.typeEnumed = false;
            this.recalcFunctionalAmount();
        }
    }

    @Override
    public Object getDBUnits() {
        return SqlColumn.getDBUnits(this.amount);
    }

    @Override
    public void setDBUnits(Object units) {
        this.amount = units == null ? 0.0 : SqlColumn.getDoubleFromDB(units);
    }

    @Override
    public TypeUnit convert(Object toType) {
        return toType == null ? this : this.convert((String)toType);
    }

    @Override
    public TypeUnit convert(Object toType, Object info) {
        return toType == null ? this : this.convert((String)toType, (CurrencyExchanger.Info)info);
    }

    public double doubleValue() {
        return this.amount;
    }

    @Override
    public void setDisplayPreference(Object preferredType) {
        this.forDisplay = this.convert((String)preferredType);
    }

    @Override
    public TypeUnit getForDisplay() {
        return this.forDisplay;
    }

    @Override
    public boolean supportsContext() {
        return true;
    }

    @Override
    public Object getContext() {
        if (this.context == null) {
            this.context = new Context();
        }
        return this.context;
    }

    public Context getCurrencyContext() {
        return (Context)this.getContext();
    }

    @Override
    public void setContext(Object context) {
        this.setContext((Context)context);
    }

    public void setContext(Context context) {
        this.context = context == null ? new Context() : new Context(context);
        this.recalcFunctionalAmount();
    }

    private void recalcFunctionalAmount() {
        this.recalcFunctAmount = true;
    }

    private void functionalAmountSet() {
        this.recalcFunctAmount = false;
    }

    @Override
    public Object newInfo() {
        return new CurrencyExchanger.Info();
    }

    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (o == this) {
            return true;
        }
        if (!(o instanceof Currency)) {
            return false;
        }
        Currency od = (Currency)o;
        if (od.amount != this.amount) {
            return false;
        }
        if (od.type == null && this.type == null) {
            return true;
        }
        if (od.type == null || this.type == null) {
            return false;
        }
        return od.type.equals(this.type);
    }

    public boolean isZero() {
        return this.amount == 0.0;
    }

    public double getRateToStandard() {
        if (this.isZero()) {
            return Double.NaN;
        }
        Currency rateCurrency = this.toStandard();
        double rateAmount = rateCurrency.amount / this.amount;
        return rateAmount;
    }

    @Override
    public String getXMLUnitTag() {
        return "CURRENCY";
    }

    @Override
    public String getXMLUnitValue() {
        return Functions.formatNumber(this.getUnits(this.getType()));
    }

    @Override
    public String getXMLTypeTag() {
        return "CR";
    }

    @Override
    public String getXMLTypeValue() {
        return (String)this.getType();
    }

    public String getXMLRateToBaseValue() {
        double excRate = this.getRateToStandard();
        return excRate == Double.NaN ? null : Functions.formatNumber(excRate);
    }

    private void enumType() {
        if (this.typeEnumed) {
            return;
        }
        this.type = Currency.enumCode(this.type);
        this.typeEnumed = true;
    }

    public static String enumCode(String type) {
        return CurrencyExchanger.getEnumCode(type);
    }

    protected static String getDefaultUseType() {
        if (defaultUseType == null) {
            String storageDefault = UOMLoader.getStorageDefault(Currency.class);
            if (storageDefault == UOMLoader.NOT_LOADED || storageDefault == null) {
                storageDefault = CurrencyExchanger.baseCurrency();
            }
            defaultUseType = Currency.enumCode(storageDefault);
        }
        return defaultUseType;
    }

    static void resetDefaultUseType() {
        defaultUseType = null;
    }

    public static final Currency getZero() {
        if (ZERO == null) {
            ZERO = new Currency(0.0);
        }
        return ZERO;
    }

    public class ConstContext
    extends Context {
        protected ConstContext() {
        }

        @Override
        public Object clone() {
            return new Context(this.getRateExchangeGid(), this.calcBasisType, this.getDate(), this.getFunctionalCurrencyGid(), this.getFunctionalCurrencyAmt());
        }

        public void setRateExchangeGid(String rateExchangeGid) {
            throw new Error();
        }

        @Override
        public void setCalcBasisType(String calcBasisType) {
            throw new Error();
        }

        public void setDate(LocalDate date) {
            throw new Error();
        }

        public void setFunctionalCurrencyGid(String functionalCurrencyGid) {
            throw new Error();
        }

        public void setFunctionalCurrencyAmount(double functionalCurrencyAmount) {
            throw new Error();
        }

        @Override
        public void setContextFieldValue(int fieldFor, Object value) {
            throw new Error();
        }
    }

    public class Context
    implements UOMContext {
        private String rateExchangeGid;
        protected String calcBasisType;
        private LocalDate date;
        private String functCurrencyGid;
        private double functCurrencyAmount;

        protected Context() {
        }

        protected Context(Context context) {
            this(context.rateExchangeGid, context.calcBasisType, context.date, context.functCurrencyGid, context.functCurrencyAmount);
        }

        public Context(String rateExchangeGid, String calcBasisType, LocalDate date) {
            this(rateExchangeGid, calcBasisType, date, null, 0.0);
        }

        public Context(String rateExchangeGid, String calcBasisType, LocalDate date, String functCurrencyGid) {
            this(rateExchangeGid, calcBasisType, date, functCurrencyGid, 0.0);
        }

        public Context(String rateExchangeGid, String calcBasisType, LocalDate date, String functCurrencyGid, double functCurrencyAmount) {
            this.rateExchangeGid = rateExchangeGid;
            this.calcBasisType = calcBasisType;
            this.date = date;
            this.functCurrencyGid = functCurrencyGid;
            this.functCurrencyAmount = functCurrencyAmount;
            Currency.this.context = this;
        }

        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof Context)) {
                return false;
            }
            Context oc = (Context)o;
            return Functions.equals(this.rateExchangeGid, oc.rateExchangeGid) && Functions.equals(this.date, oc.date) && Functions.equals(this.calcBasisType, oc.calcBasisType) && Functions.equals(this.functCurrencyGid, oc.functCurrencyGid) && Functions.equals(this.getFunctionalCurrencyAmount(), oc.getFunctionalCurrencyAmount());
        }

        public int hashCode() {
            return Functions.hashCode(this.rateExchangeGid) + Functions.hashCode(this.date) + Functions.hashCode(this.calcBasisType) + Functions.hashCode(this.functCurrencyGid) + Functions.hashCode(new Double(this.functCurrencyAmount));
        }

        @Override
        public Object clone() {
            try {
                return super.clone();
            }
            catch (Throwable t) {
                t.printStackTrace();
                return null;
            }
        }

        public String getRateExchangeGid() {
            return this.rateExchangeGid;
        }

        private void setRateExchangeGid(String rateExchangeGid) {
            this.rateExchangeGid = rateExchangeGid;
        }

        public String getCalcBasisType() {
            return this.calcBasisType;
        }

        public void setCalcBasisType(String calcBasisType) {
            this.calcBasisType = calcBasisType;
        }

        public LocalDate getDate() {
            return this.date;
        }

        private void setDate(LocalDate date) {
            this.date = date;
        }

        public String getFunctionalCurrencyGid() {
            return this.functCurrencyGid;
        }

        private void setFunctionalCurrencyGid(String functionalCurrencyGid) {
            this.functCurrencyGid = functionalCurrencyGid;
        }

        public double getFunctionalCurrencyAmt() {
            Double amt = this.getFunctionalCurrencyAmount();
            return amt != null ? amt : 0.0;
        }

        public Double getFunctionalCurrencyAmount() {
            if (this.date != null && this.functCurrencyGid != null) {
                if (Currency.this.recalcFunctAmount) {
                    Currency functCurrency = Currency.this.convert(this.functCurrencyGid);
                    this.functCurrencyAmount = functCurrency != null ? functCurrency.getCurrencyAmount() : 0.0;
                    Currency.this.recalcFunctAmount = false;
                }
                return new Double(this.functCurrencyAmount);
            }
            return null;
        }

        private void setFunctionalCurrencyAmount(double functionalCurrencyAmount) {
            this.functCurrencyAmount = functionalCurrencyAmount;
        }

        @Override
        public int[] getContextFields() {
            return new int[]{3, 4, 5, 6};
        }

        @Override
        public int[] getDisplayContextFields() {
            return new int[]{3, 4, 5, 6};
        }

        @Override
        public int[] getUpdateContextFields() {
            return new int[]{5, 6};
        }

        @Override
        public void setContextFieldValue(int fieldFor, Object value) {
            switch (fieldFor) {
                case 3: {
                    if (value instanceof String) {
                        this.setDate((LocalDate)LocalDate.valueOf((String)value));
                        break;
                    }
                    this.setDate(new LocalDate((Date)value));
                    break;
                }
                case 4: {
                    this.setRateExchangeGid((String)value);
                    break;
                }
                case 5: {
                    this.setFunctionalCurrencyGid((String)value);
                    break;
                }
                case 6: {
                    if (value instanceof String) {
                        this.setFunctionalCurrencyAmount(value == null ? 0.0 : new Double((String)value));
                        break;
                    }
                    this.setFunctionalCurrencyAmount(value == null ? 0.0 : SqlColumn.getDoubleFromDB(value));
                    break;
                }
            }
        }

        @Override
        public Object getContextFieldValue(int fieldFor) {
            switch (fieldFor) {
                case 3: {
                    return this.getDate();
                }
                case 4: {
                    return this.getRateExchangeGid();
                }
                case 5: {
                    return this.getFunctionalCurrencyGid();
                }
                case 6: {
                    return this.getFunctionalCurrencyAmount();
                }
            }
            return null;
        }

        @Override
        public String getContextFieldXMLTag(int fieldFor) {
            switch (fieldFor) {
                case 3: {
                    return "EXCHG_DATE";
                }
                case 4: {
                    return "EXCHG_ID";
                }
                case 5: {
                    return "FUNCT_ID";
                }
                case 6: {
                    return "FUNCT_AMT";
                }
            }
            return null;
        }

        public String toString() {
            StringBuffer sb = new StringBuffer();
            sb.append("(exchange=").append(this.rateExchangeGid).append(",date=").append(this.date).append(",basis=").append(this.calcBasisType).append(",functCurrencyAmount=").append(this.functCurrencyAmount).append(",functCurrencyGid=").append(this.functCurrencyGid).append(")");
            return sb.toString();
        }
    }
}
