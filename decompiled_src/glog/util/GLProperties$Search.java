/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.util.GLProperties;

private static class GLProperties.Search {
    public GLProperties.Property property;
    public GLProperties props;

    public GLProperties.Search(GLProperties props, GLProperties.Property property) {
        this.props = props;
        this.property = property;
    }

    public String toString() {
        return "property = " + this.property + "; source = " + this.props.source;
    }
}
