/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import java.io.InputStream;
import java.io.Writer;

private static class Spawn.PipeOutput
extends Thread {
    private InputStream is;
    private StringBuffer out;
    private Writer log;

    public Spawn.PipeOutput(InputStream is, StringBuffer out, Writer log) {
        this.is = is;
        this.out = out;
        this.log = log;
        this.start();
    }

    @Override
    public void run() {
        try {
            int ic;
            while ((ic = this.is.read()) != -1) {
                char c = (char)ic;
                if (this.out != null) {
                    this.out.append(c);
                }
                if (this.log == null) continue;
                this.log.write(c);
                this.log.flush();
            }
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }
}
