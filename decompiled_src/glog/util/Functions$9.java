/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

static final class Functions.9
extends ThreadLocal {
    Functions.9() {
    }

    protected DecimalFormat initialValue() {
        DecimalFormat df = (DecimalFormat)NumberFormat.getNumberInstance(Locale.US);
        df.applyPattern("#0.0000000");
        return df;
    }
}
