/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

private class ParameterDictionary.Key {
    private String str;
    private int counter;

    public ParameterDictionary.Key(String str) {
        this.str = str;
    }

    public ParameterDictionary.Key(String str, int counter) {
        this.str = str;
        this.counter = counter;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o instanceof ParameterDictionary.Key) {
            ParameterDictionary.Key other = (ParameterDictionary.Key)o;
            return this.str.equals(other.str);
        }
        return false;
    }

    public int hashCode() {
        return this.str.hashCode();
    }

    public String toString() {
        return this.str;
    }

    public String getString() {
        return this.str;
    }

    public int getCounter() {
        return this.counter;
    }
}
