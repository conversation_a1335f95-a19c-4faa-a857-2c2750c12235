/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Vector;

public class GLGroupException
extends GLException {
    private Vector exceptions = new Vector();

    public final boolean isEmpty() {
        return this.exceptions.isEmpty();
    }

    public final void add(GLException glex) {
        this.exceptions.addElement(glex);
    }

    public final ListIterator getContexts() {
        Vector all = new Vector();
        for (GLException glex : this.exceptions) {
            ListIterator it2 = glex.getCauseIterator();
            while (it2.hasNext()) {
                all.add(it2.next());
            }
        }
        return all.listIterator(0);
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        pw.println("------- Group Exception: " + this.exceptions.size() + " exceptions -------");
        for (GLException glex : this.exceptions) {
            glex.printStackTrace(pw);
        }
        pw.println("------- End of Group Exception -------");
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        ps.println("------- Group Exception: " + this.exceptions.size() + " exceptions -------");
        for (GLException glex : this.exceptions) {
            glex.printStackTrace(ps);
        }
        ps.println("------- End of Group Exception -------");
    }

    @Override
    public String getStackTraceString() {
        String ret = "------- Group Exception: " + this.exceptions.size() + " exceptions -------\n";
        for (GLException glex : this.exceptions) {
            ret = ret + glex.getStackTraceString();
        }
        return ret + "------- End of Group Exception -------\n";
    }

    public GLException[] getExceptions() {
        Object[] result = new GLException[this.exceptions.size()];
        this.exceptions.copyInto(result);
        return result;
    }

    @Override
    protected String getTranslation(String key, Locale locale) {
        return key;
    }
}
