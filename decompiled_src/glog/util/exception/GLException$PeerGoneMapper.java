/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  weblogic.rjvm.PeerGoneException
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLNoServerException;
import weblogic.rjvm.PeerGoneException;

static class GLException.PeerGoneMapper
implements GLException.Mapper {
    GLException.PeerGoneMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        if (t instanceof PeerGoneException) {
            return new GLNoServerException();
        }
        return t;
    }
}
