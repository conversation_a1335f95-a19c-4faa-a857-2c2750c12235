/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 *  javax.ejb.RemoveException
 *  weblogic.rjvm.PeerGoneException
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLCreateException;
import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;
import glog.util.exception.GLFinderException;
import glog.util.exception.GLRemoteException;
import glog.util.exception.GLRemoveException;
import glog.util.exception.RemoteExceptionWrapper;
import glog.util.exception.RemoveExceptionWrapper;
import java.rmi.ConnectException;
import java.rmi.RemoteException;
import java.util.Map;
import javax.ejb.CreateException;
import javax.ejb.FinderException;
import javax.ejb.RemoveException;
import weblogic.rjvm.PeerGoneException;

static class GLException.EJBExceptionMapper
implements GLException.Mapper {
    GLException.EJBExceptionMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        GLException glex = null;
        if (t instanceof FinderException) {
            glex = t instanceof FinderExceptionWrapper ? GLException.factory(((FinderExceptionWrapper)((Object)t)).unwrap()) : new GLFinderException(new Cause("Generic_Finder_Exception", null, (Map)null), t);
        } else if (t instanceof CreateException) {
            glex = t instanceof CreateExceptionWrapper ? GLException.factory(((CreateExceptionWrapper)((Object)t)).unwrap()) : new GLCreateException(new Cause("Generic_Create_Exception", null, (Map)null), t);
        } else if (t instanceof RemoveException) {
            glex = t instanceof RemoveExceptionWrapper ? GLException.factory(((RemoveExceptionWrapper)((Object)t)).unwrap()) : new GLRemoveException(new Cause("Generic_Remove_Exception", null, (Map)null), t);
        } else {
            if (t instanceof PeerGoneException || t instanceof ConnectException) {
                return new GLExceptionProd(new GLException.CausedBy("cause.unknownServerDown", null), t);
            }
            if (t instanceof RemoteException) {
                Throwable cause;
                glex = t instanceof RemoteExceptionWrapper ? GLException.factory(((RemoteExceptionWrapper)t).unwrap()) : ((cause = t.getCause()) == null ? new GLRemoteException(new Cause("Generic_Remote_Exception", null, (Map)null), t) : GLException.factory(cause));
            }
        }
        if (glex != null) {
            glex.log(Integer.MAX_VALUE, true);
            return glex;
        }
        return t;
    }
}
