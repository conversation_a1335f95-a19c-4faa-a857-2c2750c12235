/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLConnectException;
import glog.util.exception.GLException;
import java.net.ConnectException;
import javax.naming.CommunicationException;

static class GLException.CommunicationMapper
implements GLException.Mapper {
    GLException.CommunicationMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        CommunicationException commEx;
        if (t instanceof CommunicationException && (t = (commEx = (CommunicationException)t).getRootCause()) != null && t instanceof ConnectException) {
            return new GLConnectException(t);
        }
        return t;
    }
}
