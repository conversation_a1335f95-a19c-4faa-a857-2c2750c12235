/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLAccessException;
import glog.util.exception.GLException;
import glog.util.exception.GLRemoteException;
import java.rmi.AccessException;

static class GLException.AccessExceptionMapper
implements GLException.Mapper {
    GLException.AccessExceptionMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        if (!(t instanceof GLRemoteException)) {
            return t;
        }
        GLRemoteException remoteEx = (GLRemoteException)t;
        Throwable wrappedException = remoteEx.getWrappedException();
        if (!(wrappedException instanceof AccessException)) {
            return t;
        }
        String msg = wrappedException.getMessage();
        String module = null;
        String method = null;
        if (msg.contains("Security Violation")) {
            int ixMethod;
            int ixModule = msg.indexOf("module=");
            if (ixModule != -1) {
                int ixModuleEnd = msg.indexOf(",", ixModule);
                module = msg.substring(ixModule + 7, ixModuleEnd);
            }
            if ((ixMethod = msg.indexOf("method=")) != -1) {
                int ixMethodEnd = msg.indexOf(",", ixMethod);
                method = msg.substring(ixMethod + 7, ixMethodEnd);
            }
        }
        if (module == null) {
            return t;
        }
        if (method == null) {
            return new GLAccessException("cause.BeanAccessErr", new Object[][]{{"bean", module}}, null, null, wrappedException);
        }
        return new GLAccessException("cause.BeanMethodAccessErr", new Object[][]{{"bean", module}, {"method", method}}, null, null, wrappedException);
    }
}
