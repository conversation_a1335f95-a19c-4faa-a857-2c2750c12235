/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import glog.util.exception.WrapperHelper;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.sql.SQLException;

public class SQLExceptionWrapper
extends SQLException
implements Wrapper {
    private WrapperHelper helper;

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    @Override
    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    public static SQLExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toSQLException();
        }
        return t instanceof SQLExceptionWrapper ? (SQLExceptionWrapper)t : new SQLExceptionWrapper(s, t);
    }

    public static SQLExceptionWrapper factory(Throwable t) {
        return SQLExceptionWrapper.factory(null, t);
    }

    public static SQLExceptionWrapper ejbFactory(String s, Throwable t) {
        return SQLExceptionWrapper.factory(s, t);
    }

    protected SQLExceptionWrapper(String s, Throwable t) {
        super(s);
        this.helper = new WrapperHelper(t);
    }

    protected SQLExceptionWrapper(Throwable t) {
        this(null, t);
    }
}
