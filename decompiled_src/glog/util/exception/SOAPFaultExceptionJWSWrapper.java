/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.xml.soap.Detail
 *  javax.xml.soap.Name
 *  javax.xml.soap.SOAPFactory
 *  javax.xml.soap.SOAPFault
 *  javax.xml.ws.soap.SOAPFaultException
 */
package glog.util.exception;

import glog.util.SOAPConstants;
import glog.util.exception.GLException;
import java.io.PrintWriter;
import java.io.StringWriter;
import javax.xml.namespace.QName;
import javax.xml.soap.Detail;
import javax.xml.soap.Name;
import javax.xml.soap.SOAPFactory;
import javax.xml.soap.SOAPFault;
import javax.xml.ws.soap.SOAPFaultException;

public class SOAPFaultExceptionJWSWrapper
extends SOAPFaultException
implements SOAPConstants {
    private static final long serialVersionUID = 1L;
    private static SOAPFactory factory = null;
    private static QName SERVICE_EXCEPTION = null;
    private static String exceptionMessage = "Internal Error Occurred: Review fault detail";
    private static Name TRACE_TAG = null;

    public static SOAPFaultExceptionJWSWrapper factory(String s, Throwable t) {
        return SOAPFaultExceptionJWSWrapper.factory(SERVICE_EXCEPTION, s, "AppServer", SOAPFaultExceptionJWSWrapper.createDetail(t));
    }

    public static SOAPFaultExceptionJWSWrapper factory(Throwable t) {
        return SOAPFaultExceptionJWSWrapper.factory(null, t);
    }

    public static SOAPFaultExceptionJWSWrapper factory(QName faultCode, String faultString, String faultActor, String faultDetail) {
        try {
            SOAPFault fault = factory.createFault();
            if (faultActor != null) {
                fault.setFaultActor(faultActor);
            }
            if (faultCode != null) {
                fault.setFaultCode(faultCode);
            }
            if (faultString == null) {
                fault.setFaultString(exceptionMessage);
            } else {
                fault.setFaultString(faultString);
            }
            if (faultDetail != null) {
                Detail detail = fault.addDetail();
                detail.addDetailEntry(TRACE_TAG).addTextNode(faultDetail);
            }
            return new SOAPFaultExceptionJWSWrapper(fault);
        }
        catch (Throwable t2) {
            GLException.factory(t2);
            return null;
        }
    }

    SOAPFaultExceptionJWSWrapper(SOAPFault faultDetail) {
        super(faultDetail);
    }

    private static String createDetail(Throwable t) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        t.printStackTrace(pw);
        return sw.toString();
    }

    static {
        try {
            if (factory == null) {
                factory = SOAPFactory.newInstance();
            }
            SERVICE_EXCEPTION = new QName("http://xmlns.oracle.com/apps/otm", "ServiceException");
            TRACE_TAG = factory.createName("trace");
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
    }
}
