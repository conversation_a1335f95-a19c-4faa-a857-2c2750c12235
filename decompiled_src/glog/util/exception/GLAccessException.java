/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLExceptionProd;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.util.Locale;
import java.util.Map;

public class GLAccessException
extends GLExceptionProd {
    private Cause complexCause;
    private transient Throwable accessException;
    private transient String complex;

    public GLAccessException(String simpleCause, Object[][] simpleArgs, String complexCause, Object[][] complexArgs) {
        this(simpleCause, simpleArgs, complexCause, complexArgs, null);
    }

    public GLAccessException(String simpleCause, Object[][] simpleArgs, String complexCause, Object[][] complexArgs, Throwable t) {
        super(FOR_STAGED_CONSTRUCTION);
        this.complexCause = new Cause(complexCause, null, complexArgs);
        this.accessException = t;
        this.init(new Cause(simpleCause, (String)null, simpleArgs), null);
    }

    @Override
    public String[][] evaluateCauses(Locale locale, int formatFlags) {
        String[][] result = super.evaluateCauses(locale, formatFlags);
        if (this.complexCause != null) {
            String complexKey = this.complexCause.getCauseTranslationKey();
            Map complexParameters = this.complexCause.getParameters();
            this.complex = this.translate(complexKey, complexParameters, locale, formatFlags);
        }
        return result;
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        if (this.complex != null) {
            ps.println(this.complex);
        }
        if (this.accessException != null) {
            this.accessException.printStackTrace(ps);
        } else {
            super.printStackTrace(ps);
        }
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        if (this.complex != null) {
            pw.println(this.complex);
        }
        if (this.accessException != null) {
            this.accessException.printStackTrace(pw);
        } else {
            super.printStackTrace(pw);
        }
    }
}
