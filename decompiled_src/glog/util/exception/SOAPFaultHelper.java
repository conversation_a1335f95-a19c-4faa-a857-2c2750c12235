/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.xml.rpc.soap.SOAPFaultException
 *  javax.xml.soap.Detail
 *  javax.xml.soap.DetailEntry
 *  javax.xml.soap.Name
 *  javax.xml.soap.Node
 *  javax.xml.soap.SOAPElement
 *  javax.xml.soap.SOAPException
 *  javax.xml.soap.SOAPFactory
 *  javax.xml.soap.Text
 */
package glog.util.exception;

import glog.util.SOAPConstants;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.exception.GLTimeoutException;
import glog.util.exception.SOAPFaultExceptionWrapper;
import glog.util.j2ee.webservice.ASWebServiceHelper;
import java.util.HashMap;
import java.util.Iterator;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import javax.xml.namespace.QName;
import javax.xml.rpc.soap.SOAPFaultException;
import javax.xml.soap.Detail;
import javax.xml.soap.DetailEntry;
import javax.xml.soap.Name;
import javax.xml.soap.Node;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPFactory;
import javax.xml.soap.Text;

public class SOAPFaultHelper
implements SOAPConstants {
    private transient SOAPFactory theSOAPFactory;
    private static final QName GLEXCEPTION_QNAME = new QName("http://glog.com/gc3v50", GLException.class.getName());

    public SOAPFaultHelper() {
        this(null);
    }

    public SOAPFaultHelper(SOAPFactory sf) {
        if (sf == null) {
            try {
                this.theSOAPFactory = ASWebServiceHelper.newInstance().getSOAPFactory();
            }
            catch (Throwable t) {
                t.printStackTrace();
            }
        } else {
            this.theSOAPFactory = sf;
        }
    }

    public GLException fromSOAPFaultException(SOAPFaultException fault) {
        Throwable wrappedException = fault.getCause();
        QName qname = fault.getFaultCode();
        String faultString = fault.getFaultString();
        GLException glex = faultString != null && faultString.indexOf("java.net.SocketTimeoutException") != -1 ? new GLTimeoutException() : GLException.factory();
        if (qname == null || !qname.equals(GLEXCEPTION_QNAME)) {
            Iterator it;
            Detail detail;
            if (faultString != null) {
                glex.addCause(new GLException.CausedBy(faultString), false);
            }
            if ((detail = fault.getDetail()) != null && (it = detail.getDetailEntries()).hasNext()) {
                String detailString = "";
                Object detailItem = it.next();
                if (detailItem instanceof DetailEntry) {
                    DetailEntry entry = (DetailEntry)detailItem;
                    detailString = entry.getValue().trim();
                } else if (detailItem instanceof Text) {
                    Text ele = (Text)detailItem;
                    detailString = ele.getValue();
                } else {
                    detailString = detailItem.toString();
                }
                if (detailString.length() > 0) {
                    StringBuffer sb = new StringBuffer();
                    if (faultString != null) {
                        sb.append(faultString).append("\n");
                    }
                    sb.append("SOAPDetails:\n").append(detailString);
                    glex.setStackTrace(sb.toString());
                }
            }
            glex.log(Integer.MAX_VALUE, true, null);
            return glex;
        }
        try {
            Detail detail = fault.getDetail();
            Iterator it = this.getFaultElements((SOAPElement)detail, "message");
            while (it.hasNext()) {
                DetailEntry message = (DetailEntry)it.next();
                SOAPElement causeElem = this.getFaultElement((SOAPElement)message, "cause");
                SOAPElement solutionElem = this.getFaultElement((SOAPElement)message, "solution");
                HashMap<String, String> parameterMap = new HashMap<String, String>();
                Iterator itp = this.getFaultElements((SOAPElement)message, "parameter");
                while (itp.hasNext()) {
                    SOAPElement parameterElem = (SOAPElement)itp.next();
                    parameterMap.put(this.getFaultAttribute(parameterElem, "name"), this.getFaultAttribute(parameterElem, "value"));
                }
                Cause cause = new Cause(this.getFaultAttribute(causeElem, "code"), this.getFaultText(causeElem), this.getFaultAttribute(solutionElem, "solution"), this.getFaultText(solutionElem), parameterMap);
                glex.addCause(cause, false);
            }
            glex.setLogTimestamp(this.getFaultText((SOAPElement)detail, "timestamp"));
            glex.setStackTrace(this.getFaultText((SOAPElement)detail, "trace"));
            glex.log(Integer.MAX_VALUE, true);
        }
        catch (SOAPException sex) {
            GLException.factory(sex);
        }
        return glex;
    }

    public Throwable toSOAPFaultException(GLException glex) {
        try {
            Detail detail = this.theSOAPFactory.createDetail();
            String primaryCause = null;
            ListIterator it = glex.getCauseIterator();
            while (it.hasNext()) {
                Cause cause = (Cause)it.next();
                String causeTranslationKey = cause.getCauseTranslationKey();
                String solutionTranslationKey = cause.getSolutionTranslationKey();
                Map parameters = cause.getParameters();
                DetailEntry message = detail.addDetailEntry(this.createName("message"));
                if (causeTranslationKey != null) {
                    SOAPElement causeElem = message.addChildElement(this.createName("cause"));
                    causeElem.addAttribute(this.createName("code"), causeTranslationKey);
                    String strCause = glex.translate(causeTranslationKey, parameters, Locale.US, 1);
                    if (primaryCause == null) {
                        primaryCause = strCause;
                    }
                    causeElem.addTextNode(strCause);
                }
                if (solutionTranslationKey != null) {
                    SOAPElement solutionElem = message.addChildElement(this.createName("solution"));
                    solutionElem.addAttribute(this.createName("code"), solutionTranslationKey);
                    solutionElem.addTextNode(glex.translate(solutionTranslationKey, parameters, Locale.US, 1));
                }
                for (Map.Entry entry : parameters.entrySet()) {
                    SOAPElement parameter = message.addChildElement(this.createName("parameter"));
                    parameter.addAttribute(this.createName("name"), (String)entry.getKey());
                    parameter.addAttribute(this.createName("value"), (String)entry.getValue());
                }
            }
            detail.addDetailEntry(this.createName("timestamp")).addTextNode(glex.getLogTimestamp());
            detail.addDetailEntry(this.createName("trace")).addTextNode(glex.getStackTraceString());
            Throwable wrappedException = glex.getWrappedException();
            if (wrappedException != null) {
                detail.addDetailEntry(this.createName("root")).addTextNode(wrappedException.toString());
            }
            if (primaryCause == null) {
                primaryCause = this.toString();
            }
            return new SOAPFaultExceptionWrapper(GLEXCEPTION_QNAME, primaryCause, "GC3 Application Server", detail);
        }
        catch (SOAPException ex) {
            return new SOAPFaultException(GLEXCEPTION_QNAME, this.toString(), "GC3 Application Server", null);
        }
    }

    private Name createName(String localPart) throws SOAPException {
        return this.theSOAPFactory.createName(localPart);
    }

    private Iterator getFaultElements(SOAPElement parent, String childTag) throws SOAPException {
        return parent.getChildElements(this.createName(childTag));
    }

    private SOAPElement getFaultElement(SOAPElement parent, String childTag) throws SOAPException {
        Iterator it = this.getFaultElements(parent, childTag);
        while (it.hasNext()) {
            Node node = (Node)it.next();
            if (!(node instanceof SOAPElement)) continue;
            return (SOAPElement)node;
        }
        return null;
    }

    private String getFaultAttribute(SOAPElement elem, String attributeTag) throws SOAPException {
        return elem != null ? elem.getAttributeValue(this.createName(attributeTag)) : null;
    }

    private String getFaultText(SOAPElement elem) throws SOAPException {
        if (elem == null) {
            return null;
        }
        Iterator it = elem.getChildElements();
        while (it.hasNext()) {
            Node node = (Node)it.next();
            if (!(node instanceof Text)) continue;
            return node.getValue();
        }
        return null;
    }

    private String getFaultText(SOAPElement parent, String childTag) throws SOAPException {
        SOAPElement elem = this.getFaultElement(parent, childTag);
        if (elem == null) {
            return null;
        }
        return this.getFaultText(elem);
    }
}
