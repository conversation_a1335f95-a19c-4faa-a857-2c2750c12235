/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Wrapper;
import glog.util.exception.WrapperHelper;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;

public class EJBWrapperHelper
extends W<PERSON><PERSON>Helper {
    private String stackTrace = null;

    public EJBWrapperHelper(Throwable t) {
        super(t);
    }

    @Override
    public void printStackTrace(PrintWriter pw, Wrapper parent) {
        if (this.stackTrace != null) {
            pw.print(this.stackTrace);
        } else {
            super.printStackTrace(pw, parent);
        }
    }

    @Override
    public void printStackTrace(PrintStream ps, Wrapper parent) {
        if (this.stackTrace != null) {
            ps.print(this.stackTrace);
        } else {
            super.printStackTrace(ps, parent);
        }
    }

    public final String getStackTraceString() {
        return this.stackTrace;
    }

    protected void saveStackTrace(Wrapper parent) {
        if (this.stackTrace == null) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            this.printStackTrace(pw, parent);
            this.stackTrace = sw.toString();
        }
    }
}
