/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.EJBWrapper;
import glog.util.exception.GLException;

static class GLException.EJBWrapperMapper
implements GLException.Mapper {
    GLException.EJBWrapperMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        if (t instanceof EJBWrapper) {
            EJBWrapper wrapper = (EJBWrapper)((Object)t);
            return wrapper.unwrap();
        }
        return t;
    }
}
