/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import java.io.Serializable;

public static class GLException.Id
implements Serializable {
    public String prefix;
    public int index;

    public GLException.Id(int index) {
        this(GLException.getDefaultSequencePrefix(), index);
    }

    public GLException.Id(String prefix, int index) {
        this.prefix = prefix;
        this.index = index;
    }
}
