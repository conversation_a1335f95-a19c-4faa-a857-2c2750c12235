/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.RemoveException
 */
package glog.util.exception;

import glog.util.exception.EJBWrapper;
import glog.util.exception.EJBWrapperHelper;
import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;
import javax.ejb.RemoveException;

public class RemoveExceptionWrapper
extends RemoveException
implements EJBWrapper {
    private EJBWrapperHelper helper = new EJBWrapperHelper((Throwable)((Object)this));

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    @Override
    public final String getStackTraceString() {
        return this.helper.getStackTraceString();
    }

    protected void saveStackTrace() {
        this.helper.saveStackTrace(this);
    }

    public static RemoveExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toRemoveException();
        }
        return t instanceof RemoveExceptionWrapper ? (RemoveExceptionWrapper)((Object)t) : new RemoveExceptionWrapper(s, t);
    }

    public static RemoveExceptionWrapper factory(Throwable t) {
        return RemoveExceptionWrapper.factory(null, t);
    }

    public static RemoveExceptionWrapper ejbFactory(String s, Throwable t) {
        RemoveExceptionWrapper few = RemoveExceptionWrapper.factory(s, t);
        few.saveStackTrace();
        return few;
    }

    public static RemoveExceptionWrapper ejbFactory(Throwable t) {
        return RemoveExceptionWrapper.ejbFactory(null, t);
    }

    protected RemoveExceptionWrapper(String s, Throwable t) {
        super(s);
        this.helper = new EJBWrapperHelper(t);
    }

    protected RemoveExceptionWrapper(Throwable t) {
        this(null, t);
    }
}
