/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.regexp.RE
 */
package glog.util.exception;

import glog.server.process.BusinessProcess;
import glog.server.process.BusinessProcessID;
import glog.util.GLProperties;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import glog.webserver.i18n.Translator;
import java.lang.reflect.Constructor;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.StringTokenizer;
import org.apache.regexp.RE;

public class GLExceptionProd
extends GLException {
    private static List<TraceSuppressor> traceSuppressors = new LinkedList<TraceSuppressor>();
    protected static Constructor thisConstructor;
    private String diagLogFileID = null;
    private BusinessProcessID processId;

    public GLExceptionProd() {
        this.init(null, null);
    }

    public GLExceptionProd(Throwable t) {
        this.init(null, t);
    }

    public GLExceptionProd(Cause cause, Throwable t) {
        this.init(cause, t);
    }

    public GLExceptionProd(GLException.ForStagedConstruction fsc) {
    }

    public static GLException factory(Throwable t) {
        return GLExceptionProd.factory(null, t);
    }

    public static GLException factory(Cause cause, Throwable t) {
        return GLExceptionProd.factory(cause, t, thisConstructor);
    }

    public final BusinessProcessID getProcessId() {
        return this.processId;
    }

    @Override
    public void log(int nLevels, boolean stackTrace, LogId logId) {
        if (logId == null) {
            logId = LogIds.EXCEPTION;
        }
        if (!Log.idOn[logId.index]) {
            return;
        }
        StringBuffer sb = new StringBuffer();
        if (stackTrace) {
            String trace = this.getStackTraceString();
            trace = this.checkForTraceSuppression(trace);
            sb.append(trace);
        } else {
            String indent = "";
            String[][] causes = this.evaluateCauses(Locale.US);
            nLevels = Math.min(causes.length, nLevels);
            for (int i = 0; i < nLevels; ++i) {
                sb.append(indent).append(causes[i][0]).append("\n");
                if (causes[i][1] != null) {
                    sb.append(indent).append(causes[i][1]).append("\n");
                }
                indent = indent + "    ";
            }
        }
        String str = sb.toString();
        this.logTimestamp = Log.getLogTimestamp();
        Log.logID(logId, Log.ERROR, str);
    }

    @Override
    protected void warn(String warning) {
        Log.logID(LogIds.EXCEPTION, Log.WARNING, "Wrapping GLException with GLException:\n" + this.getStackTraceString());
    }

    @Override
    protected void initProcess() {
        BusinessProcess process = BusinessProcess.getCurrentProcess();
        this.processId = process != null ? process.getID() : null;
        this.diagLogFileID = process != null ? process.getDiagLogFileID() : null;
    }

    public String getDiagLogFileID() {
        return this.diagLogFileID;
    }

    @Override
    protected String getTranslation(String key, Locale locale) {
        try {
            String translated = Translator.getErrorValue(key, locale);
            if (translated == key) {
                translated = Translator.getExplanationValue(key, locale);
            }
            return translated;
        }
        catch (Throwable t) {
            GLException.factory(t);
            return key;
        }
    }

    protected String checkForTraceSuppression(String trace) {
        if (Log.isPastStartup()) {
            return trace;
        }
        for (TraceSuppressor suppressor : traceSuppressors) {
            if (!trace.contains(suppressor.clz) || !suppressor.regexp.match(trace)) continue;
            int ix = trace.indexOf("\n");
            String truncatedTrace = trace.substring(0, ix);
            StringBuilder sb = new StringBuilder();
            sb.append("STARTUP ERROR in " + suppressor.subsystem).append(":\n").append(truncatedTrace);
            return sb.toString();
        }
        return trace;
    }

    static {
        for (String value : GLProperties.get().values("glog.log.suppressStartupTrace")) {
            StringTokenizer tokenizer = new StringTokenizer(value, ",");
            if (tokenizer.countTokens() != 3) continue;
            try {
                TraceSuppressor suppressor = new TraceSuppressor();
                suppressor.clz = tokenizer.nextToken();
                suppressor.regexp = new RE(tokenizer.nextToken());
                suppressor.subsystem = tokenizer.nextToken();
                traceSuppressors.add(suppressor);
            }
            catch (Throwable t) {
                t.printStackTrace();
            }
        }
        thisConstructor = GLExceptionProd.getConstructor(GLExceptionProd.class);
    }

    private static class TraceSuppressor {
        public String clz;
        public RE regexp;
        public String subsystem;

        private TraceSuppressor() {
        }
    }
}
