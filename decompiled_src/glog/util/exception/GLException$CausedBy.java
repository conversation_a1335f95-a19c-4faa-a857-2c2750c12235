/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import java.util.Map;

public static class GLException.CausedBy
extends Cause {
    public GLException.CausedBy(String causeTranslationKey, String causeReadable, String solutionTranslationKey, String solutionReadable, Map parameters) {
        super(causeTranslationKey, causeReadable, solutionTranslationKey, solutionReadable, parameters);
    }

    public GLException.CausedBy(String causeTranslationKey, String solutionTranslationKey, Map parameters) {
        super(causeTranslationKey, solutionTranslationKey, parameters);
    }

    public GLException.CausedBy(String causeTranslationKey, String solutionTranslationKey, Object[][] parameters) {
        super(causeTranslationKey, solutionTranslationKey, parameters);
    }

    public GLException.CausedBy(String causeTranslationKey, String solutionTranslationKey, Object[] parameters) {
        super(causeTranslationKey, solutionTranslationKey, parameters);
    }

    public GLException.CausedBy(String causeTranslationKey, String solutionTranslationKey) {
        super(causeTranslationKey, solutionTranslationKey);
    }

    public GLException.CausedBy(String text) {
        super(text);
    }
}
