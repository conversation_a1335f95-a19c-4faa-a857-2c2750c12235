/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;
import java.io.PrintStream;
import java.io.PrintWriter;

public class GLExceptionWithAttachment
extends GLExceptionProd {
    private String log;

    public GLExceptionWithAttachment(String cause, String message, String log) {
        super(FOR_STAGED_CONSTRUCTION);
        this.causes.addFirst(new GLException.CausedBy(cause, null));
        if (message != null) {
            this.causes.addLast(new GLException.CausedBy(message));
        }
        this.log = log;
        this.init(null, null);
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        if (this.log != null) {
            ps.println(this.log);
        }
        super.printStackTrace(ps);
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        if (this.log != null) {
            pw.println(this.log);
        }
        super.printStackTrace(pw);
    }
}
