/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.Functions;
import java.io.Serializable;
import java.util.LinkedList;
import java.util.Map;

public class Reason
implements Serializable {
    private String cause;
    private String causeCode;
    private String solution;
    private String solutionCode;
    private Map parameters;

    public Reason() {
    }

    public Reason(String cause, String causeCode, String solution, String solutionCode, Map parameters) {
        this.cause = cause;
        this.causeCode = causeCode;
        this.solution = solution;
        this.solutionCode = solutionCode;
        this.parameters = parameters;
    }

    public String getCause() {
        return this.cause;
    }

    public String getCauseCode() {
        return this.causeCode;
    }

    public String getSolution() {
        return this.solution;
    }

    public String getSolutionCode() {
        return this.solutionCode;
    }

    public String[][] getParameters() {
        LinkedList<String[]> list = new LinkedList<String[]>();
        for (Map.Entry entry : this.parameters.entrySet()) {
            list.add(new String[]{(String)entry.getKey(), (String)entry.getValue()});
        }
        return Functions.toArray(list, String[].class);
    }

    public void setCause(String cause) {
    }

    public void setCauseCode(String causeCode) {
    }

    public void setSolution(String solution) {
    }

    public void setSolutionCode(String SolutionCode) {
    }

    public void setParameters(String[][] parameters) {
    }
}
