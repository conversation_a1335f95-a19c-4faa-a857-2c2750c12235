/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import java.lang.reflect.Constructor;
import java.util.Locale;

public class GLExceptionBuild
extends GLException {
    protected static Constructor thisConstructor = GLExceptionBuild.getConstructor(GLExceptionBuild.class);

    public GLExceptionBuild() {
        super((Cause)null, (Throwable)null);
    }

    public GLExceptionBuild(Throwable t) {
        super((Cause)null, t);
    }

    public GLExceptionBuild(Cause cause, Throwable t) {
        super(cause, t);
    }

    public GLExceptionBuild(GLException.ForStagedConstruction fsc) {
    }

    public static GLException factory(Throwable t) {
        return GLExceptionBuild.factory(null, t);
    }

    public static GLException factory(Cause cause, Throwable t) {
        return GLExceptionBuild.factory(cause, t, thisConstructor);
    }

    @Override
    protected String getTranslation(String key, Locale locale) {
        return key;
    }
}
