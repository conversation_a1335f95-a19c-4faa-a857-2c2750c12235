/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import java.io.Serializable;

public abstract class ErrorCause
implements Serializable {
    protected String causeGid = null;
    protected String solutionGid = null;
    protected Object[] args = null;

    protected ErrorCause(String causeGid, String solutionGid, Object[] args) {
        this.causeGid = causeGid;
        this.solutionGid = solutionGid;
        this.args = args;
    }

    public Cause[] getCauses(String l_causeGid, String l_solutionGid, Object[] l_args) {
        return new Cause[]{new Cause(this.causeGid, this.solutionGid, this.args), new Cause(l_causeGid, l_solutionGid, l_args)};
    }

    public Cause[] getCausesMappedArgs(String l_causeGid, String l_solutionGid, Object[][] l_args) {
        return new Cause[]{new Cause(this.causeGid, this.solutionGid, this.args), new Cause(l_causeGid, l_solutionGid, l_args)};
    }
}
