/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLOracleDeadlockException;
import java.sql.SQLException;

static class GLException.DeadlockMapper
implements GLException.Mapper {
    GLException.DeadlockMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        SQLException s;
        if (t instanceof SQLException && ((s = (SQLException)t).getErrorCode() == 60 || s.toString().contains(GLException.ORACLE_DEADLOCK_STRING))) {
            return new GL<PERSON>racleDeadlockException(t);
        }
        return t;
    }
}
