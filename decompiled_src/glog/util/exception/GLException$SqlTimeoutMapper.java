/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLSqlTimeoutException;
import glog.util.jdbc.noserver.SqlTimeout;
import java.sql.SQLTimeoutException;

static class GLException.SqlTimeoutMapper
implements GLException.Mapper {
    GLException.SqlTimeoutMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        return t instanceof SQLTimeoutException ? new GLSqlTimeoutException(t, SqlTimeout.get()) : t;
    }
}
