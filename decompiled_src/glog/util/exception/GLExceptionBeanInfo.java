/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class GLExceptionBeanInfo
extends SimpleBeanInfo {
    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("reasons", GLException.class, "getReasons", "setReasons"), new PropertyDescriptor("root", GLException.class, "getRootReason", "setRootReason"), new PropertyDescriptor("timestamp", GLException.class, "getTimestamp", "setTimestamp"), new PropertyDescriptor("trace", GLException.class, "getTrace", "setTrace")};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
