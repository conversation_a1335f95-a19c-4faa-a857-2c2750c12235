/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class CauseBeanInfo
extends SimpleBeanInfo {
    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("reason", Cause.class, "getReason", null), new PropertyDescriptor("solution", Cause.class, "getSolution", null), new PropertyDescriptor("parameters", Cause.class, "getParameters", null)};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
