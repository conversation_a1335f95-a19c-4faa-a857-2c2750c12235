/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.xml.rpc.soap.SOAPFaultException
 *  javax.xml.soap.Detail
 *  javax.xml.soap.Name
 *  javax.xml.soap.SOAPException
 *  javax.xml.soap.SOAPFactory
 */
package glog.util.exception;

import glog.util.SOAPConstants;
import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import glog.util.exception.WrapperHelper;
import glog.util.j2ee.webservice.ASWebServiceHelper;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import javax.xml.namespace.QName;
import javax.xml.rpc.soap.SOAPFaultException;
import javax.xml.soap.Detail;
import javax.xml.soap.Name;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPFactory;

public class SOAPFaultExceptionWrapper
extends SOAPFaultException
implements Wrapper,
SOAPConstants {
    private transient WrapperHelper helper;
    private transient Detail detail;
    private static SOAPFactory factory = null;
    private static QName SERVICE_EXCEPTION = null;
    private static Name TRACE_TAG = null;

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    public static SOAPFaultExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toSOAPFaultException();
        }
        return t instanceof SOAPFaultExceptionWrapper ? (SOAPFaultExceptionWrapper)((Object)t) : new SOAPFaultExceptionWrapper(s, t);
    }

    public static SOAPFaultExceptionWrapper factory(Throwable t) {
        return SOAPFaultExceptionWrapper.factory(null, t);
    }

    SOAPFaultExceptionWrapper(QName faultCode, String faultString, String faultActor, Detail faultDetail) {
        super(faultCode, faultString, faultActor, faultDetail);
        this.helper = new WrapperHelper(null);
        this.setupDetail();
    }

    protected SOAPFaultExceptionWrapper(String s, Throwable t) {
        super(SERVICE_EXCEPTION, s, "AppServer", SOAPFaultExceptionWrapper.createDetail(factory));
        this.helper = new WrapperHelper(t);
        this.setupDetail();
    }

    protected SOAPFaultExceptionWrapper(Throwable t) {
        this(null, t);
    }

    private void setupDetail() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        this.helper.printStackTrace(pw, (Wrapper)this);
        Detail detail = this.getDetail();
        if (detail != null) {
            try {
                detail.addDetailEntry(TRACE_TAG).addTextNode(sw.toString());
            }
            catch (SOAPException ex) {
                GLException.factory(ex);
            }
        }
    }

    private static Detail createDetail(SOAPFactory factory) {
        try {
            return factory.createDetail();
        }
        catch (SOAPException ex) {
            GLException.factory(ex);
            return null;
        }
    }

    static {
        try {
            if (factory == null) {
                factory = ASWebServiceHelper.newInstance().getSOAPFactory();
            }
            SERVICE_EXCEPTION = new QName("ServiceException");
            TRACE_TAG = factory.createName("trace");
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
    }
}
