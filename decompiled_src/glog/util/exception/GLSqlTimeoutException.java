/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLExceptionProd;
import glog.util.jdbc.noserver.SqlTimeout;

public class GLSqlTimeoutException
extends GLExceptionProd {
    public GLSqlTimeoutException() {
    }

    public GLSqlTimeoutException(Throwable t, SqlTimeout.Timeout timeout) {
        Object[][] objectArray = new Object[2][];
        objectArray[0] = new Object[]{"timeout", timeout != null ? timeout.getTimeoutInSecs() : 0};
        Object[] objectArray2 = new Object[2];
        objectArray2[0] = "useCase";
        objectArray2[1] = timeout != null ? (timeout.getUseCase() != null ? timeout.getUseCase() : "") : "";
        objectArray[1] = objectArray2;
        super(new Cause("cause.sqlTimeout", null, objectArray), t);
    }
}
