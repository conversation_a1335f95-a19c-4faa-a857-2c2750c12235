/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.EJBWrapper;
import glog.util.exception.EJBWrapperHelper;
import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.rmi.RemoteException;

public class RemoteExceptionWrapper
extends RemoteException
implements EJBWrapper {
    private EJBWrapperHelper helper = new EJBWrapperHelper(this);

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    @Override
    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    @Override
    public final String getStackTraceString() {
        return this.helper.getStackTraceString();
    }

    protected void saveStackTrace() {
        this.helper.saveStackTrace(this);
    }

    public static RemoteExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toRemoteException();
        }
        return t instanceof RemoteExceptionWrapper ? (RemoteExceptionWrapper)t : new RemoteExceptionWrapper(s, t);
    }

    public static RemoteExceptionWrapper factory(Throwable t) {
        return RemoteExceptionWrapper.factory(null, t);
    }

    public static RemoteExceptionWrapper ejbFactory(String s, Throwable t) {
        RemoteExceptionWrapper few = RemoteExceptionWrapper.factory(s, t);
        few.saveStackTrace();
        return few;
    }

    public static RemoteExceptionWrapper ejbFactory(Throwable t) {
        return RemoteExceptionWrapper.ejbFactory(null, t);
    }

    protected RemoteExceptionWrapper(String s, Throwable t) {
        super(s);
        this.helper = new EJBWrapperHelper(t);
    }

    protected RemoteExceptionWrapper(Throwable t) {
        this(null, t);
    }
}
