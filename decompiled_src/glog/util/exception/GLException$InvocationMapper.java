/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import java.lang.reflect.InvocationTargetException;

static class GLException.InvocationMapper
implements GLException.Mapper {
    GLException.InvocationMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        return t instanceof InvocationTargetException ? ((InvocationTargetException)t).getTargetException() : t;
    }
}
