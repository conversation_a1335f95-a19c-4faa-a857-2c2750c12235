/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.xml.rpc.soap.SOAPFaultException
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.SOAPFaultHelper;
import java.rmi.RemoteException;
import javax.xml.rpc.soap.SOAPFaultException;

static class GLException.SOAPFaultExceptionMapper
implements GLException.Mapper {
    GLException.SOAPFaultExceptionMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        Throwable tt = t instanceof RemoteException ? ((RemoteException)t).getCause() : t;
        return tt != null && tt instanceof SOAPFaultException ? new SOAPFaultHelper().fromSOAPFaultException((SOAPFaultException)tt) : t;
    }
}
