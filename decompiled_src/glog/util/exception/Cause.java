/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.Functions;
import glog.util.exception.GLException;
import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;

public class Cause
implements Serializable {
    private String causeTranslationKey;
    private String solutionTranslationKey;
    private String causeReadable;
    private String solutionReadable;
    private Map parameters = Collections.EMPTY_MAP;
    private boolean translatable = true;
    private GLException.Id exceptionId = new GLException.Id(0);

    public Cause(String causeTranslationKey, String causeReadable, String solutionTranslationKey, String solutionReadable, Map parameters) {
        this.causeTranslationKey = causeTranslationKey;
        this.causeReadable = causeReadable;
        this.solutionTranslationKey = solutionTranslationKey;
        this.solutionReadable = solutionReadable;
        if (parameters != null) {
            this.parameters = new TreeMap();
            for (Map.Entry entry : parameters.entrySet()) {
                Object value = entry.getValue();
                this.parameters.put(entry.getKey(), value != null ? value.toString() : null);
            }
        }
    }

    public Cause(String causeTranslationKey, String solutionTranslationKey, Map parameters) {
        this(causeTranslationKey, null, solutionTranslationKey, null, parameters);
    }

    public Cause(String causeTranslationKey, String solutionTranslationKey, Object[][] parameters) {
        this(causeTranslationKey, solutionTranslationKey, parameters != null ? Functions.toMap(parameters) : null);
    }

    public Cause(String causeTranslationKey, String solutionTranslationKey, Object[] parameters) {
        this(causeTranslationKey, solutionTranslationKey, GLException.objectArrToMap(parameters));
    }

    public Cause(String causeTranslationKey, String solutionTranslationKey) {
        this(causeTranslationKey, solutionTranslationKey, (Map)null);
    }

    public Cause(String text) {
        this.translatable = false;
        this.causeTranslationKey = text;
    }

    public final String getCauseTranslationKey() {
        return this.causeTranslationKey;
    }

    public final String getSolutionTranslationKey() {
        return this.solutionTranslationKey;
    }

    public final String getCauseReadable() {
        return this.causeReadable;
    }

    public final String getSolutionReadable() {
        return this.solutionReadable;
    }

    public final Map getParameters() {
        return this.parameters;
    }

    public final boolean isTranslatable() {
        return this.translatable;
    }

    public GLException.Id getExceptionId() {
        return this.exceptionId;
    }

    public void setExceptionId(GLException.Id exceptionId) {
        this.exceptionId = exceptionId;
    }

    public void setExceptionId(GLException e) {
        if (e != null) {
            this.setExceptionId(e.getId());
        }
    }

    Cause() {
    }
}
