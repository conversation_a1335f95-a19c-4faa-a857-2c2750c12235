/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;

public interface EJBWrapper
extends Wrapper {
    @Override
    public Throwable unwrap();

    public String getStackTraceString();

    @Override
    public void printSuperTrace(PrintWriter var1);

    @Override
    public void printSuperTrace(PrintStream var1);
}
