/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 */
package glog.util.exception;

import glog.util.exception.EJBWrapper;
import glog.util.exception.EJBWrapperHelper;
import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;
import javax.ejb.CreateException;

public class CreateExceptionWrapper
extends CreateException
implements EJBWrapper {
    private EJBWrapperHelper helper = new EJBWrapperHelper((Throwable)((Object)this));

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    @Override
    public final String getStackTraceString() {
        return this.helper.getStackTraceString();
    }

    protected void saveStackTrace() {
        this.helper.saveStackTrace(this);
    }

    public static CreateExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toCreateException();
        }
        return t instanceof CreateExceptionWrapper ? (CreateExceptionWrapper)((Object)t) : new CreateExceptionWrapper(s, t);
    }

    public static CreateExceptionWrapper factory(Throwable t) {
        return CreateExceptionWrapper.factory(null, t);
    }

    public static CreateExceptionWrapper ejbFactory(String s, Throwable t) {
        CreateExceptionWrapper few = CreateExceptionWrapper.factory(s, t);
        few.saveStackTrace();
        return few;
    }

    public static CreateExceptionWrapper ejbFactory(Throwable t) {
        return CreateExceptionWrapper.ejbFactory(null, t);
    }

    protected CreateExceptionWrapper(String s, Throwable t) {
        super(s);
        this.helper = new EJBWrapperHelper(t);
    }

    protected CreateExceptionWrapper(Throwable t) {
        this(null, t);
    }
}
