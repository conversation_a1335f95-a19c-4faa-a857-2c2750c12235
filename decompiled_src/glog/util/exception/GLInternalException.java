/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLExceptionBuild;

public class GLInternalException
extends GLExceptionBuild {
    public GLInternalException(String message, Object[] args, Throwable t) {
        super(new Cause(message, null, args), t);
    }

    public GLInternalException(String message, Object[] args) {
        this(message, args, null);
    }

    public GLInternalException(String message, Throwable t) {
        this(message, null, t);
    }

    public GLInternalException(String message) {
        this(message, null, null);
    }
}
