/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.Serializable;

public class WrapperHelper
implements Serializable {
    private Throwable wrappedException;

    public WrapperHelper(Throwable t) {
        this.wrappedException = t;
    }

    public final Throwable unwrap() {
        return this.wrappedException;
    }

    public void printStackTrace(PrintWriter pw, Wrapper parent) {
        if (this.wrappedException != null) {
            pw.println(parent);
            this.wrappedException.printStackTrace(pw);
        } else {
            parent.printSuperTrace(pw);
        }
    }

    public void printStackTrace(PrintStream ps, Wrapper parent) {
        if (this.wrappedException != null) {
            ps.println(parent);
            this.wrappedException.printStackTrace(ps);
        } else {
            parent.printSuperTrace(ps);
        }
    }

    public void printStackTrace(Wrapper parent) {
        this.printStackTrace(System.err, parent);
    }
}
