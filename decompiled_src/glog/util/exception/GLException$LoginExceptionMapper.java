/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.database.security.GLSecurityException;
import glog.util.exception.GLException;
import javax.security.auth.login.LoginException;

static class GLException.LoginExceptionMapper
implements GLException.Mapper {
    GLException.LoginExceptionMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        if (t instanceof LoginException) {
            return new GLSecurityException(new GLException.CausedBy("cause.Login.0002", null), t);
        }
        return t;
    }
}
