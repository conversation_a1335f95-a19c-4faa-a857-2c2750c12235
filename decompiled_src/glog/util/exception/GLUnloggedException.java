/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;
import glog.util.exception.NoExceptionLogging;

public class GLUnloggedException
extends GLExceptionProd
implements NoExceptionLogging {
    public GLUnloggedException(Cause cause) {
        this(cause, null);
    }

    public GLUnloggedException(Cause cause, Throwable wrappedException) {
        super(cause, wrappedException);
    }

    public GLUnloggedException(Throwable t) {
        super((Cause)null, t);
    }

    public static GLException factory(Throwable t) {
        return GLUnloggedException.factory(null, t);
    }

    public static GLException factory(Cause cause) {
        return GLUnloggedException.factory(cause, null);
    }

    public static GLException factory(Cause cause, Throwable t) {
        return GLUnloggedException.factory(cause, t, stdCauseNoLogConstructor);
    }
}
