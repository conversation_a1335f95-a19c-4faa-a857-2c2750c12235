/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;
import java.util.Locale;
import java.util.Map;

public class GLNoServerException
extends GLExceptionProd {
    private String msg;

    public GLNoServerException() {
    }

    public GLNoServerException(String causeKey, String msg, Object[][] args) {
        super(FOR_STAGED_CONSTRUCTION);
        this.msg = msg;
        this.init(new GLException.CausedBy(causeKey, null, args), null);
    }

    @Override
    String translate(String key, Map parameters, Locale locale, int formatFlags) {
        String result = super.translate(key, parameters, locale, formatFlags);
        return result != null && result.startsWith(key) ? this.format(this.msg, parameters, formatFlags) : result;
    }
}
