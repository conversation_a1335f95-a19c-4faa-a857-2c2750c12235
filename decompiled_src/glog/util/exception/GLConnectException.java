/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.GLProperties;
import glog.util.exception.GLExceptionBuild;
import glog.util.log.Log;
import glog.util.log.LogId;

public class GLConnectException
extends GLExceptionBuild {
    private static boolean logConnectTraces;

    public GLConnectException(Throwable t) {
        super(t);
    }

    @Override
    public void log(int nLevels, boolean stackTrace, LogId logId) {
        if (logConnectTraces) {
            super.log(nLevels, stackTrace, logId);
        } else if (this.wrappedException != null) {
            Log.logID(logId, Log.ERROR, this.wrappedException.toString());
        }
    }

    private static void init() {
        logConnectTraces = "true".equalsIgnoreCase(GLProperties.get().getProperty("glog.exception.logConnectTraces", "false"));
    }

    static {
        GLConnectException.init();
        GLProperties.get();
        GLProperties.addListener("glog.exception.logConnectTraces", new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) {
                GLConnectException.init();
            }
        });
    }
}
