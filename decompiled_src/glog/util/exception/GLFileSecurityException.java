/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;

public class GLFileSecurityException
extends GLExceptionProd {
    public GLFileSecurityException(String fileName) {
        super(new GLException.CausedBy("cause.UnauthorizedAccessToFile", null, new Object[][]{{"fileName", fileName}}), null);
    }

    public GLFileSecurityException(Cause cause) {
        super(cause, null);
    }
}
