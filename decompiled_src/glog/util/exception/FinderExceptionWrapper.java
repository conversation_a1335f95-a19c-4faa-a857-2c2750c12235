/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.util.exception;

import glog.util.GLProperties;
import glog.util.exception.EJBWrapper;
import glog.util.exception.EJBWrapperHelper;
import glog.util.exception.GLException;
import glog.util.exception.Wrapper;
import java.io.PrintStream;
import java.io.PrintWriter;
import javax.ejb.FinderException;

public class FinderExceptionWrapper
extends FinderException
implements EJBWrapper {
    private EJBWrapperHelper helper;

    @Override
    public final Throwable unwrap() {
        return this.helper.unwrap();
    }

    public void printStackTrace(PrintWriter pw) {
        this.helper.printStackTrace(pw, (Wrapper)this);
    }

    public void printStackTrace(PrintStream ps) {
        this.helper.printStackTrace(ps, (Wrapper)this);
    }

    public void printStackTrace() {
        this.helper.printStackTrace(this);
    }

    @Override
    public void printSuperTrace(PrintWriter pw) {
        super.printStackTrace(pw);
    }

    @Override
    public void printSuperTrace(PrintStream ps) {
        super.printStackTrace(ps);
    }

    @Override
    public final String getStackTraceString() {
        return this.helper.getStackTraceString();
    }

    protected void saveStackTrace() {
        this.helper.saveStackTrace(this);
    }

    public static FinderExceptionWrapper factory(String s, Throwable t) {
        if (t instanceof GLException) {
            t = ((GLException)t).toFinderException();
        }
        return t instanceof FinderExceptionWrapper ? (FinderExceptionWrapper)((Object)t) : new FinderExceptionWrapper(s, t);
    }

    public static FinderExceptionWrapper factory(Throwable t) {
        return FinderExceptionWrapper.factory(null, t);
    }

    public static FinderExceptionWrapper ejbFactory(String s, Throwable t) {
        FinderExceptionWrapper few = FinderExceptionWrapper.factory(s, t);
        if (GLProperties.get().getProperty("glog.log.ID.ejbFinderWrapper.on", "false").charAt(0) == 't' || !t.getClass().getName().endsWith("GLNoMoreRecords")) {
            few.saveStackTrace();
        }
        return few;
    }

    public static FinderExceptionWrapper ejbFactory(Throwable t) {
        return FinderExceptionWrapper.ejbFactory(null, t);
    }

    protected FinderExceptionWrapper(String s, Throwable t) {
        super(s);
        this.helper = new EJBWrapperHelper(t);
    }

    protected FinderExceptionWrapper(Throwable t) {
        this(null, t);
    }
}
