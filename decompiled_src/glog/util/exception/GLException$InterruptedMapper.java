/*
 * Decompiled with CFR 0.152.
 */
package glog.util.exception;

import glog.util.exception.GLException;
import glog.util.exception.GLInterruptedException;

static class GLException.InterruptedMapper
implements GLException.Mapper {
    GLException.InterruptedMapper() {
    }

    @Override
    public Throwable map(Throwable t) {
        return t instanceof InterruptedException ? new GLInterruptedException(t) : t;
    }
}
