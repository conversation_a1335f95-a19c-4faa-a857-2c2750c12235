/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 *  javax.ejb.RemoveException
 *  javax.xml.rpc.soap.SOAPFaultException
 *  weblogic.rjvm.PeerGoneException
 */
package glog.util.exception;

import glog.database.security.GLSecurityException;
import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.GLPropertiesHelper;
import glog.util.UnlimitedMessageFormat;
import glog.util.exception.Cause;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.EJBWrapper;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLAccessException;
import glog.util.exception.GLConnectException;
import glog.util.exception.GLCreateException;
import glog.util.exception.GLExceptionBuild;
import glog.util.exception.GLExceptionProd;
import glog.util.exception.GLFinderException;
import glog.util.exception.GLInterruptedException;
import glog.util.exception.GLNoServerException;
import glog.util.exception.GLOracleDeadlockException;
import glog.util.exception.GLRemoteException;
import glog.util.exception.GLRemoveException;
import glog.util.exception.GLSqlTimeoutException;
import glog.util.exception.NoExceptionLogging;
import glog.util.exception.NoExceptionTrace;
import glog.util.exception.RemoteExceptionWrapper;
import glog.util.exception.RemoveExceptionWrapper;
import glog.util.exception.SOAPFaultHelper;
import glog.util.jdbc.noserver.SqlTimeout;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.net.ConnectException;
import java.rmi.AccessException;
import java.rmi.RemoteException;
import java.sql.SQLException;
import java.sql.SQLTimeoutException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;
import java.util.Vector;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.ejb.CreateException;
import javax.ejb.FinderException;
import javax.ejb.RemoveException;
import javax.naming.CommunicationException;
import javax.security.auth.login.LoginException;
import javax.xml.rpc.soap.SOAPFaultException;
import weblogic.rjvm.PeerGoneException;

public abstract class GLException
extends Exception {
    private static ThreadLocal trackingExceptions = new ThreadLocal();
    private static AtomicInteger exceptionCount = new AtomicInteger(1);
    protected static ThreadLocal inTranslation = new ThreadLocal();
    protected static List mappers = new LinkedList();
    protected Id id;
    protected String logTimestamp;
    protected LinkedList causes = new LinkedList();
    protected Throwable wrappedException = null;
    protected String stackTrace = null;
    protected boolean logIt = false;
    public static final int BUILD = 1;
    public static final int PRODUCTION = 2;
    private static final Class[] CAUSE_ARGS = new Class[]{Cause.class, Throwable.class};
    private static final Class[] STAGING_ARGS = new Class[]{ForStagedConstruction.class};
    protected static Constructor stdCauseConstructor = null;
    protected static Constructor stdCauseNoLogConstructor = null;
    protected static Constructor stdStagingConstructor = null;
    public static final int TRANSLATE = 1;
    public static final int SHOW_KEY = 2;
    private static final int ORACLE_DEADLOCK = 60;
    private static final String ORACLE_DEADLOCK_STRING = "ORA-00060";
    private static final int DO_TRANSLATE = 0;
    private static final int DELAY_TRANSLATE = 1;
    private static final int DONT_TRANSLATE = 2;
    private static int allowTranslationFlag;
    public static ForStagedConstruction FOR_STAGED_CONSTRUCTION;
    public static Object[] FOR_STAGED_CONSTRUCTION_OBJ;
    private static Pattern exceptionCausePattern;
    private static Pattern exceptionPattern;
    private static Pattern eolPattern;
    private static boolean exceptionPatternsLoaded;
    private static GLException OUT_OF_DEPTH;

    public GLException(ForStagedConstruction fsc) {
        this.initProcess();
    }

    public GLException(Cause cause) {
        this.init(cause, null);
    }

    public GLException(Cause cause, Throwable wrappedException) {
        this.init(cause, wrappedException);
    }

    public GLException addCause(Cause cause, boolean logIt) {
        this.causes.addFirst(cause);
        if (logIt) {
            this.log(1, false);
        }
        return this;
    }

    public GLException addCause(Cause cause) {
        return this.addCause(cause, true);
    }

    public final Throwable getWrappedException() {
        return this.wrappedException;
    }

    public final String getLogTimestamp() {
        return this.logTimestamp;
    }

    protected void setLogTimestamp(String logTimestamp) {
        this.logTimestamp = logTimestamp;
    }

    public ListIterator getCauseIterator() {
        return this.causes.listIterator(0);
    }

    public int getNumCauses() {
        return this.causes.size();
    }

    public String[][] evaluateCauses(Locale locale, int formatFlags) {
        Vector<String[]> v = new Vector<String[]>();
        ListIterator it = this.getCauseIterator();
        while (it.hasNext()) {
            Cause cause = (Cause)it.next();
            String causeTranslationKey = cause.getCauseTranslationKey();
            String solutionTranslationKey = cause.getSolutionTranslationKey();
            Map parameters = cause.getParameters();
            String strCause = this.translate(causeTranslationKey, parameters, locale, formatFlags);
            String strSolution = this.translate(solutionTranslationKey, parameters, locale, formatFlags);
            if (strCause == null && strSolution == null) continue;
            v.addElement(new String[]{strCause, strSolution});
        }
        return Functions.toArray(v, String[].class);
    }

    public String[][] evaluateCauses(Locale locale) {
        return this.evaluateCauses(locale, 1);
    }

    @Override
    public void printStackTrace(PrintWriter pw) {
        if (this.stackTrace != null) {
            pw.print(this.stackTrace);
        } else if (this.wrappedException != null) {
            pw.println(this);
            this.wrappedException.printStackTrace(pw);
        } else if (this instanceof NoExceptionTrace) {
            pw.print(this.getCauseTrace());
        } else {
            super.printStackTrace(pw);
        }
    }

    @Override
    public void printStackTrace(PrintStream ps) {
        if (this.stackTrace != null) {
            ps.print(this.stackTrace);
        } else if (this.wrappedException != null) {
            ps.println(this);
            this.wrappedException.printStackTrace(ps);
        } else if (this instanceof NoExceptionTrace) {
            ps.print(this.getCauseTrace());
        } else {
            super.printStackTrace(ps);
        }
    }

    @Override
    public void printStackTrace() {
        this.printStackTrace(System.err);
    }

    public String getStackTraceString() {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        this.printStackTrace(pw);
        return sw.toString();
    }

    @Override
    public String toString() {
        String indent = "";
        StringBuilder sb = new StringBuilder(this.getIdStr(null, " ", Sequence.trace));
        String[][] causes = this.evaluateCauses(Locale.US);
        for (int i = 0; i < causes.length; ++i) {
            if (causes[i][0] != null) {
                sb.append(indent).append(causes[i][0]).append("\n");
            }
            if (causes[i][1] != null) {
                sb.append(indent).append(causes[i][1]).append("\n");
            }
            indent = indent + "    ";
        }
        return sb.toString();
    }

    public String getTopCause() {
        String[][] causes = this.evaluateCauses(Locale.US);
        if (causes.length > 0) {
            return causes[0][0];
        }
        if (this.wrappedException != null) {
            return this.wrappedException.getMessage();
        }
        return null;
    }

    public Id getId() {
        return this.id;
    }

    public void log(int nLevels, boolean stackTrace, LogId logId) {
        StringBuilder sb = new StringBuilder();
        if (stackTrace && !(this instanceof NoExceptionTrace)) {
            sb.append(this.getStackTraceString());
        } else {
            sb.append(this.getCauseTrace(nLevels));
        }
        System.err.println(sb);
    }

    public void log(int nLevels, boolean stackTrace) {
        if (!(this instanceof NoExceptionLogging)) {
            this.log(nLevels, stackTrace, null);
        }
    }

    private String getCauseTrace() {
        return this.getCauseTrace(1000);
    }

    private String getCauseTrace(int nLevels) {
        String indent = "";
        String[][] causes = this.evaluateCauses(Locale.US);
        nLevels = Math.min(causes.length, nLevels);
        StringBuilder sb = new StringBuilder(this.getIdStr(null, " ", Sequence.trace));
        for (int i = 0; i < nLevels; ++i) {
            sb.append(indent).append(causes[i][0]).append("\n");
            if (causes[i][1] != null) {
                sb.append(indent).append(causes[i][1]).append("\n");
            }
            indent = indent + "    ";
        }
        return sb.toString();
    }

    public void setReasons(String[] reasons) {
    }

    public void setRootReason(String rootReason) {
    }

    public void setTimestamp(String timestamp) {
    }

    public void setTrace(String trace) {
    }

    public GLException(Throwable t) {
        this.init(null, t);
    }

    public GLException() {
        this.initProcess();
    }

    protected void saveStackTrace() {
        if (this.stackTrace == null) {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            this.printStackTrace(pw);
            this.stackTrace = sw.toString();
        }
    }

    protected void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public static GLException factory() {
        GLException glex;
        try {
            glex = (GLException)stdStagingConstructor.newInstance(FOR_STAGED_CONSTRUCTION_OBJ);
        }
        catch (Exception e) {
            e.printStackTrace();
            glex = new GLExceptionBuild();
        }
        return glex;
    }

    public static GLException factory(Throwable t) {
        return GLException.factory(null, t, true);
    }

    public static GLException factory(Throwable t, boolean log) {
        return GLException.factory(null, t, log);
    }

    public static GLException factory(Cause cause) {
        return GLException.factory(cause, null, true);
    }

    public static GLException factory(Cause cause, boolean log) {
        return GLException.factory(cause, null, log);
    }

    public static GLException factory(Cause cause, Throwable t) {
        return GLException.factory(cause, t, true);
    }

    public static GLException factory(Cause cause, Throwable t, boolean log) {
        return GLException.factory(cause, t, log ? stdCauseConstructor : stdCauseNoLogConstructor);
    }

    protected static GLException factory(Cause cause, Throwable t, Constructor customConstructor) {
        return GLException.factory(cause, t, customConstructor, 0);
    }

    private static GLException factory(Cause cause, Throwable t, Constructor customConstructor, int depth) {
        GLException glex;
        Throwable adj = t;
        Iterator it = mappers.iterator();
        while (adj != null && it.hasNext()) {
            Mapper mapper = (Mapper)it.next();
            if (mapper == null && Log.idOn[LogIds.GLOG.index]) {
                Log.logID(LogIds.GLOG, Log.ERROR, "Exception mapper is null! {0}", mappers);
                continue;
            }
            adj = mapper.map(adj);
        }
        if (adj instanceof GLException) {
            glex = (GLException)adj;
            if (cause != null) {
                glex.addCause(cause, true);
            }
        } else {
            try {
                glex = (GLException)customConstructor.newInstance(cause, adj);
            }
            catch (InvocationTargetException ite) {
                glex = depth == 0 ? GLException.factory(null, adj, stdCauseConstructor, depth + 1) : OUT_OF_DEPTH;
            }
            catch (Throwable e) {
                e.printStackTrace();
                glex = depth == 0 ? GLException.factory(null, adj, stdCauseConstructor, depth + 1) : OUT_OF_DEPTH;
            }
        }
        glex.checkForEJBStack(t);
        return glex;
    }

    public static void allowTranslation() {
        if (allowTranslationFlag == 1) {
            if (!Functions.IN_CODEGEN) {
                GLException.registerMapper("glog.util.jdbc.CheckForSQLExceptions");
            }
            allowTranslationFlag = 0;
        }
    }

    public static void suppressTranslation() {
        allowTranslationFlag = 1;
    }

    public static GLException ejbFactory(Throwable t) {
        return GLException.ejbFactory(null, t);
    }

    public static GLException ejbFactory(Cause cause) {
        return GLException.ejbFactory(cause, null);
    }

    public static GLException ejbFactory(Cause cause, Throwable t) {
        GLException glex = GLException.factory(cause, t);
        glex.saveStackTrace();
        return glex;
    }

    public static void trackExceptions(boolean track) {
        trackingExceptions.set(track ? new LinkedList() : null);
    }

    public static List getTrackedExceptions() {
        return (List)trackingExceptions.get();
    }

    protected Throwable toSQLException() {
        return this;
    }

    protected Throwable toFinderException() {
        return this;
    }

    protected Throwable toCreateException() {
        return this;
    }

    protected Throwable toRemoveException() {
        return this;
    }

    protected Throwable toRemoteException() {
        return this;
    }

    protected Throwable toSOAPFaultException() {
        return new SOAPFaultHelper().toSOAPFaultException(this);
    }

    protected void init(Cause cause, Throwable wrappedException) {
        try {
            this.initProcess();
            if (wrappedException != null && wrappedException instanceof GLException) {
                this.id = ((GLException)wrappedException).getId();
            } else if (cause != null) {
                this.id = cause.getExceptionId();
            }
            if (this.id == null || this.id.index == 0) {
                this.id = new Id(exceptionCount.getAndIncrement());
            }
            if (wrappedException != null) {
                if (!(wrappedException instanceof GLException)) {
                    this.wrappedException = wrappedException;
                    if (wrappedException != null) {
                        this.addCause(new Cause(wrappedException.toString()), false);
                    }
                } else {
                    GLException glex = (GLException)wrappedException;
                    this.causes = glex.causes;
                    this.wrappedException = glex.wrappedException;
                    this.saveStackTrace();
                    this.warn("Wrapping GLException with GLException:\n" + this.getStackTraceString());
                    glex.saveStackTrace();
                    this.stackTrace = glex.stackTrace;
                    this.id = glex.id;
                }
            }
            if (cause != null) {
                this.addCause(cause, false);
            }
            this.initAdditionalCauses();
            this.log(Integer.MAX_VALUE, true);
            List trackedExceptions = GLException.getTrackedExceptions();
            if (trackedExceptions != null) {
                trackedExceptions.add(this);
            }
        }
        catch (Throwable t) {
            wrappedException.printStackTrace();
            t.printStackTrace();
            throw new Error("Error initializing OTM exception");
        }
    }

    protected void initAdditionalCauses() {
    }

    protected void warn(String warning) {
        System.err.println(warning);
    }

    protected void initProcess() {
    }

    protected void checkForEJBStack(Throwable t) {
        if (t instanceof EJBWrapper) {
            EJBWrapper wrapper = (EJBWrapper)((Object)t);
            this.stackTrace = wrapper.getStackTraceString();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    String translate(String key, Map parameters, Locale locale, int formatFlags) {
        if (key == null) {
            return null;
        }
        String translation = key;
        if ((formatFlags & 1) != 0 && allowTranslationFlag == 0 && !Boolean.TRUE.equals(inTranslation.get())) {
            inTranslation.set(Boolean.TRUE);
            try {
                translation = this.getTranslation(key, locale);
            }
            catch (Throwable t) {
                GLException.factory(t);
                translation = key;
            }
            finally {
                inTranslation.set(Boolean.FALSE);
            }
        }
        StringBuffer sbResult = new StringBuffer(this.format(translation, parameters, formatFlags));
        if ((formatFlags & 2) != 0 && !translation.equals(key)) {
            sbResult.append(" {").append(key).append("}");
        }
        return sbResult.toString();
    }

    protected abstract String getTranslation(String var1, Locale var2);

    protected String format(String format, Map parameters, int formatFlags) {
        try {
            if (format == null) {
                format = "(null)";
            }
            return parameters == null ? format : UnlimitedMessageFormat.format(format, parameters);
        }
        catch (Throwable t) {
            t.printStackTrace();
            return format + parameters;
        }
    }

    public static void registerMapper(Class mapperClass) {
        try {
            Mapper mapper = (Mapper)mapperClass.newInstance();
            if (mapper == null) {
                Log.logID(LogIds.GLOG, Log.ERROR, "Mapper {0} new instance returned null", mapperClass);
            } else {
                mappers.add(mapper);
            }
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public static void registerMapper(String mapperClassName) {
        try {
            GLException.registerMapper(Class.forName(mapperClassName));
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public static Constructor getConstructor(Class c, Class[] args) {
        try {
            return c.getConstructor(args);
        }
        catch (Throwable t) {
            t.printStackTrace();
            return null;
        }
    }

    public static Constructor getConstructor(String classname, Class[] args) {
        try {
            return GLException.getConstructor(Class.forName(classname), args);
        }
        catch (Throwable t) {
            t.printStackTrace();
            return null;
        }
    }

    public static Constructor getConstructor(Class c) {
        return GLException.getConstructor(c, new Class[]{Cause.class, Throwable.class});
    }

    public static Constructor getConstructor(String classname) {
        return GLException.getConstructor(classname);
    }

    public static void setBindMode(int bindMode) {
        stdCauseConstructor = bindMode == 1 ? GLException.getConstructor("glog.util.exception.GLExceptionBuild", CAUSE_ARGS) : GLException.getConstructor("glog.util.exception.GLExceptionProd", CAUSE_ARGS);
        stdCauseNoLogConstructor = bindMode == 1 ? GLException.getConstructor("glog.util.exception.GLExceptionBuild", CAUSE_ARGS) : GLException.getConstructor("glog.util.exception.GLUnloggedException", CAUSE_ARGS);
        stdStagingConstructor = bindMode == 1 ? GLException.getConstructor("glog.util.exception.GLExceptionBuild", STAGING_ARGS) : GLException.getConstructor("glog.util.exception.GLExceptionProd", STAGING_ARGS);
    }

    static Map objectArrToMap(Object[] args) {
        if (args == null) {
            return null;
        }
        TreeMap<String, Object> map = new TreeMap<String, Object>();
        for (int i = 0; i < args.length; ++i) {
            map.put(String.valueOf(i), args[i]);
        }
        return map;
    }

    private static void initExceptionPatterns() {
        String stackTraceProp = GLProperties.get().getProperty("glog.security.stackTrace.pattern", "^\\s*at\\s+.*$");
        String stackTraceCauseProp = GLProperties.get().getProperty("glog.security.stackTraceCause.pattern", "(\\w+\\Q.\\E){2,}\\w*Exception");
        exceptionPattern = Pattern.compile(stackTraceProp);
        exceptionCausePattern = Pattern.compile(stackTraceCauseProp);
        eolPattern = Pattern.compile("\r\n|\n");
        exceptionPatternsLoaded = true;
    }

    public static String parseThrowableException(Throwable t) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        t.printStackTrace(pw);
        return GLException.parseStackTraceString(sw.toString());
    }

    public static String parseStackTraceString(String stackTrace) {
        if (stackTrace == null) {
            return "";
        }
        if (!exceptionPatternsLoaded) {
            GLException.initExceptionPatterns();
        }
        StringBuffer sb = new StringBuffer();
        String[] stackTraceLines = eolPattern.split(stackTrace);
        for (int i = 0; i < stackTraceLines.length; ++i) {
            String stackTraceLine = stackTraceLines[i];
            Matcher matcher = exceptionPattern.matcher(stackTraceLine);
            if (matcher.find()) continue;
            sb.append(stackTraceLine.trim());
            sb.append("\n");
        }
        return sb.toString();
    }

    public String[][] reevaluateCauses(String[][] evaluatedCauses, Locale locale) {
        if (evaluatedCauses == null) {
            return null;
        }
        if (!exceptionPatternsLoaded) {
            GLException.initExceptionPatterns();
        }
        for (int iCause = 0; iCause < evaluatedCauses.length; ++iCause) {
            if (evaluatedCauses[iCause][0] == null) continue;
            Matcher matcher = exceptionCausePattern.matcher(evaluatedCauses[iCause][0]);
            if (matcher.find()) {
                evaluatedCauses[iCause][0] = this.translate("cause.UnknownError", null, locale, 1);
                continue;
            }
            if (!evaluatedCauses[iCause][0].contains("[SQL_STATEMENT] ")) continue;
            evaluatedCauses[iCause][0] = this.translate("cause.UnknownError", null, locale, 1);
        }
        return evaluatedCauses;
    }

    public String reevaluate(String[][] evaluatedCauses, Locale locale) {
        String defaultEMsg = this.translate("cause.UnknownError", null, locale, 1);
        String errorMessage = null;
        try {
            if (evaluatedCauses == null) {
                return defaultEMsg;
            }
            if (!exceptionPatternsLoaded) {
                GLException.initExceptionPatterns();
            }
            int causeLen = evaluatedCauses.length;
            String[][] reevaluatedCauses = new String[causeLen][causeLen];
            for (int iCause = 0; iCause < evaluatedCauses.length; ++iCause) {
                Matcher matcher;
                if (evaluatedCauses[iCause][0] == null || (matcher = exceptionCausePattern.matcher(evaluatedCauses[iCause][0])).find() || evaluatedCauses[iCause][0].contains("[SQL_STATEMENT] ")) continue;
                reevaluatedCauses[iCause][0] = evaluatedCauses[iCause][0];
            }
            HashMap<String, String> causesMap = new HashMap<String, String>();
            for (int i = 0; i < reevaluatedCauses.length; ++i) {
                String causestr;
                if (reevaluatedCauses[i][0] == null || (causestr = reevaluatedCauses[i][0].trim()).isEmpty()) continue;
                causesMap.put(causestr, causestr);
            }
            StringBuffer sb = new StringBuffer();
            if (causesMap.size() == 0) {
                return defaultEMsg;
            }
            for (Map.Entry causesMapEntry : causesMap.entrySet()) {
                if (((String)causesMapEntry.getKey()).isEmpty()) continue;
                sb.append((String)causesMapEntry.getKey());
                sb.append("\n");
            }
            errorMessage = sb.toString();
            if (errorMessage != null && errorMessage.isEmpty()) {
                errorMessage = defaultEMsg;
            }
        }
        catch (Exception e) {
            errorMessage = defaultEMsg;
        }
        return errorMessage;
    }

    public static boolean isStackTraceSecurityEnabled() {
        return GLPropertiesHelper.getBoolean("glog.security.stackTrace.hide", true);
    }

    public String[][] parseThrowableExceptionToCauses(Throwable t, Locale locale) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        t.printStackTrace(pw);
        return this.parseStackTraceStringToCauses(sw.toString(), locale);
    }

    public String[][] parseStackTraceStringToCauses(String stackTrace, Locale locale) {
        if (stackTrace == null) {
            return null;
        }
        if (!exceptionPatternsLoaded) {
            GLException.initExceptionPatterns();
        }
        Vector<String[]> v = new Vector<String[]>();
        String[] stackTraceLines = eolPattern.split(stackTrace);
        for (int i = 0; i < stackTraceLines.length; ++i) {
            String stackTraceLine = stackTraceLines[i];
            Matcher matcher = exceptionPattern.matcher(stackTraceLine);
            if (matcher.find()) continue;
            String strCause = stackTraceLine.trim();
            strCause = this.translate(strCause, null, locale, 1);
            v.addElement(new String[]{strCause});
        }
        return Functions.toArray(v, String[].class);
    }

    private static String getDefaultSequencePrefix() {
        return GLProperties.get().getProperty("glog.exception.sequencePrefix");
    }

    private static Sequence getDefaultSequence() {
        return Sequence.valueOf(GLProperties.get().getProperty("glog.exception.sequence", "none"));
    }

    public String getIdStr(String prefix, String suffix, Sequence useCase) {
        return GLException.getIdStr(prefix, suffix, this.id, useCase);
    }

    public static String getIdStr(String prefix, String suffix, Id id, Sequence useCase) {
        if (id == null || !GLException.getDefaultSequence().ranks(useCase)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        if (prefix != null) {
            sb.append(prefix);
        }
        sb.append("[");
        if (id.prefix != null && id.prefix.length() > 0) {
            sb.append(id.prefix).append("-");
        }
        sb.append(String.format("%06d]", id.index));
        if (suffix != null) {
            sb.append(suffix);
        }
        return sb.toString();
    }

    static {
        GLException.setBindMode(Functions.IN_CODEGEN ? 1 : 2);
        GLException.registerMapper(SOAPFaultExceptionMapper.class);
        GLException.registerMapper(EJBWrapperMapper.class);
        GLException.registerMapper(EJBExceptionMapper.class);
        GLException.registerMapper(InvocationMapper.class);
        GLException.registerMapper(CommunicationMapper.class);
        GLException.registerMapper(PeerGoneMapper.class);
        GLException.registerMapper(InterruptedMapper.class);
        GLException.registerMapper(AccessExceptionMapper.class);
        GLException.registerMapper(SqlTimeoutMapper.class);
        GLException.registerMapper(DeadlockMapper.class);
        GLException.registerMapper(LoginExceptionMapper.class);
        allowTranslationFlag = 1;
        String prop = System.getProperty("translateErrors");
        if ("false".equals(prop)) {
            allowTranslationFlag = 2;
        } else if ("true".equals(prop)) {
            GLException.allowTranslation();
        }
        FOR_STAGED_CONSTRUCTION = new ForStagedConstruction();
        FOR_STAGED_CONSTRUCTION_OBJ = new Object[]{FOR_STAGED_CONSTRUCTION};
        exceptionPatternsLoaded = false;
        OUT_OF_DEPTH = new GLExceptionBuild(FOR_STAGED_CONSTRUCTION);
    }

    public static enum Sequence {
        none(0),
        trace(1),
        all(2);

        private int rank;

        private Sequence(int rank) {
            this.rank = rank;
        }

        public boolean ranks(Sequence s) {
            return this.rank >= s.rank;
        }
    }

    public static class ForStagedConstruction {
    }

    static class AccessExceptionMapper
    implements Mapper {
        AccessExceptionMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            if (!(t instanceof GLRemoteException)) {
                return t;
            }
            GLRemoteException remoteEx = (GLRemoteException)t;
            Throwable wrappedException = remoteEx.getWrappedException();
            if (!(wrappedException instanceof AccessException)) {
                return t;
            }
            String msg = wrappedException.getMessage();
            String module = null;
            String method = null;
            if (msg.contains("Security Violation")) {
                int ixMethod;
                int ixModule = msg.indexOf("module=");
                if (ixModule != -1) {
                    int ixModuleEnd = msg.indexOf(",", ixModule);
                    module = msg.substring(ixModule + 7, ixModuleEnd);
                }
                if ((ixMethod = msg.indexOf("method=")) != -1) {
                    int ixMethodEnd = msg.indexOf(",", ixMethod);
                    method = msg.substring(ixMethod + 7, ixMethodEnd);
                }
            }
            if (module == null) {
                return t;
            }
            if (method == null) {
                return new GLAccessException("cause.BeanAccessErr", new Object[][]{{"bean", module}}, null, null, wrappedException);
            }
            return new GLAccessException("cause.BeanMethodAccessErr", new Object[][]{{"bean", module}, {"method", method}}, null, null, wrappedException);
        }
    }

    static class SOAPFaultExceptionMapper
    implements Mapper {
        SOAPFaultExceptionMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            Throwable tt = t instanceof RemoteException ? ((RemoteException)t).getCause() : t;
            return tt != null && tt instanceof SOAPFaultException ? new SOAPFaultHelper().fromSOAPFaultException((SOAPFaultException)tt) : t;
        }
    }

    static class EJBExceptionMapper
    implements Mapper {
        EJBExceptionMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            GLException glex = null;
            if (t instanceof FinderException) {
                glex = t instanceof FinderExceptionWrapper ? GLException.factory(((FinderExceptionWrapper)((Object)t)).unwrap()) : new GLFinderException(new Cause("Generic_Finder_Exception", null, (Map)null), t);
            } else if (t instanceof CreateException) {
                glex = t instanceof CreateExceptionWrapper ? GLException.factory(((CreateExceptionWrapper)((Object)t)).unwrap()) : new GLCreateException(new Cause("Generic_Create_Exception", null, (Map)null), t);
            } else if (t instanceof RemoveException) {
                glex = t instanceof RemoveExceptionWrapper ? GLException.factory(((RemoveExceptionWrapper)((Object)t)).unwrap()) : new GLRemoveException(new Cause("Generic_Remove_Exception", null, (Map)null), t);
            } else {
                if (t instanceof PeerGoneException || t instanceof java.rmi.ConnectException) {
                    return new GLExceptionProd(new CausedBy("cause.unknownServerDown", null), t);
                }
                if (t instanceof RemoteException) {
                    Throwable cause;
                    glex = t instanceof RemoteExceptionWrapper ? GLException.factory(((RemoteExceptionWrapper)t).unwrap()) : ((cause = t.getCause()) == null ? new GLRemoteException(new Cause("Generic_Remote_Exception", null, (Map)null), t) : GLException.factory(cause));
                }
            }
            if (glex != null) {
                glex.log(Integer.MAX_VALUE, true);
                return glex;
            }
            return t;
        }
    }

    static class DeadlockMapper
    implements Mapper {
        DeadlockMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            SQLException s;
            if (t instanceof SQLException && ((s = (SQLException)t).getErrorCode() == 60 || s.toString().contains(GLException.ORACLE_DEADLOCK_STRING))) {
                return new GLOracleDeadlockException(t);
            }
            return t;
        }
    }

    static class SqlTimeoutMapper
    implements Mapper {
        SqlTimeoutMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            return t instanceof SQLTimeoutException ? new GLSqlTimeoutException(t, SqlTimeout.get()) : t;
        }
    }

    static class InterruptedMapper
    implements Mapper {
        InterruptedMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            return t instanceof InterruptedException ? new GLInterruptedException(t) : t;
        }
    }

    static class EJBWrapperMapper
    implements Mapper {
        EJBWrapperMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            if (t instanceof EJBWrapper) {
                EJBWrapper wrapper = (EJBWrapper)((Object)t);
                return wrapper.unwrap();
            }
            return t;
        }
    }

    static class InvocationMapper
    implements Mapper {
        InvocationMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            return t instanceof InvocationTargetException ? ((InvocationTargetException)t).getTargetException() : t;
        }
    }

    static class PeerGoneMapper
    implements Mapper {
        PeerGoneMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            if (t instanceof PeerGoneException) {
                return new GLNoServerException();
            }
            return t;
        }
    }

    static class CommunicationMapper
    implements Mapper {
        CommunicationMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            CommunicationException commEx;
            if (t instanceof CommunicationException && (t = (commEx = (CommunicationException)t).getRootCause()) != null && t instanceof ConnectException) {
                return new GLConnectException(t);
            }
            return t;
        }
    }

    static class LoginExceptionMapper
    implements Mapper {
        LoginExceptionMapper() {
        }

        @Override
        public Throwable map(Throwable t) {
            if (t instanceof LoginException) {
                return new GLSecurityException(new CausedBy("cause.Login.0002", null), t);
            }
            return t;
        }
    }

    public static interface Mapper {
        public Throwable map(Throwable var1);
    }

    public static class CausedBy
    extends Cause {
        public CausedBy(String causeTranslationKey, String causeReadable, String solutionTranslationKey, String solutionReadable, Map parameters) {
            super(causeTranslationKey, causeReadable, solutionTranslationKey, solutionReadable, parameters);
        }

        public CausedBy(String causeTranslationKey, String solutionTranslationKey, Map parameters) {
            super(causeTranslationKey, solutionTranslationKey, parameters);
        }

        public CausedBy(String causeTranslationKey, String solutionTranslationKey, Object[][] parameters) {
            super(causeTranslationKey, solutionTranslationKey, parameters);
        }

        public CausedBy(String causeTranslationKey, String solutionTranslationKey, Object[] parameters) {
            super(causeTranslationKey, solutionTranslationKey, parameters);
        }

        public CausedBy(String causeTranslationKey, String solutionTranslationKey) {
            super(causeTranslationKey, solutionTranslationKey);
        }

        public CausedBy(String text) {
            super(text);
        }
    }

    public static class Id
    implements Serializable {
        public String prefix;
        public int index;

        public Id(int index) {
            this(GLException.getDefaultSequencePrefix(), index);
        }

        public Id(String prefix, int index) {
            this.prefix = prefix;
            this.index = index;
        }
    }
}
