/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.exception.GLException;
import java.util.Vector;

public class GLProperties.ClassList {
    public Class[] classes;

    public GLProperties.ClassList(String key) {
        Vector vClasses = new Vector();
        for (GLProperties.Entry entry : GLProperties.this.entrySet(key)) {
            try {
                String className = entry.getValue();
                vClasses.add(Class.forName(className));
            }
            catch (Throwable t) {
                GLException.factory(t);
            }
        }
        this.classes = Functions.toArray(vClasses, Class.class);
    }
}
