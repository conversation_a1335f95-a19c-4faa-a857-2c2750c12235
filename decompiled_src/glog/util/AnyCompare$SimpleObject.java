/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.util.LocalTimestamp;
import glog.util.currency.Currency;

static class AnyCompare.SimpleObject {
    public int i;
    public Integer I;
    public long l;
    public Long L;
    public float f;
    public Float F;
    public double d;
    public Double D;
    public Currency c;
    public LocalTimestamp t;
    public String s;

    public AnyCompare.SimpleObject(int i, Integer I, long l, Long L, float f, Float F, double d, Double D, Currency c, LocalTimestamp t, String s) {
        this.i = i;
        this.I = I;
        this.l = l;
        this.L = L;
        this.f = f;
        this.F = F;
        this.d = d;
        this.D = D;
        this.c = c;
        this.t = t;
        this.s = s;
    }

    public String toString() {
        return "i=" + this.i + " I=" + this.I + " l=" + this.l + " L=" + this.L + " f=" + this.f + " F=" + this.F + " d=" + this.d + " D=" + this.D + " c=" + this.c + " t=" + this.t + " s=" + this.s;
    }
}
