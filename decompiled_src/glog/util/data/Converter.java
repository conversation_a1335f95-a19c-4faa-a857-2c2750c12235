/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.util.exception.GLException;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;

public interface Converter {
    public String toString(Object var1);

    public void toJsonValue(JsonObjectBuilder var1, String var2, Object var3);

    public Object fromJsonValue(JsonValue var1) throws GLException;

    public Object fromString(String var1) throws GLException;

    public Object fromObject(Object var1) throws GLException;
}
