/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(value=RetentionPolicy.RUNTIME)
@Target(value={ElementType.TYPE, ElementType.METHOD, ElementType.FIELD})
public @interface Legacy {
    public Class value() default Legacy.class;
}
