/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.util.data.LobData;

public class BlobData
extends LobData {
    public BlobData(String colName) {
        super(colName);
    }

    public BlobData(String colName, byte[] content) {
        this(colName, content, true);
    }

    public BlobData(String colName, byte[] content, boolean needModify) {
        super(colName, content, needModify);
    }

    public void setContent(byte[] content) {
        super.setContent(content);
    }

    public byte[] getBytes() {
        return (byte[])this.content;
    }
}
