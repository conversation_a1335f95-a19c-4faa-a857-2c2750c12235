/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.util.data.ComparableKeyedData;
import glog.util.data.KeyedData;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class InlineData {
    public <K, B extends KeyedData<K>> Map<K, B> map(B ... beans) throws Exception {
        return this.map(new LinkedHashMap(), (KeyedData[])beans);
    }

    public <K, B extends KeyedData<K>> Map<K, B> map(Class<B> klass, Object[] ... parameterLists) throws Exception {
        return this.map(new LinkedHashMap(), klass, parameterLists);
    }

    public <K extends Comparable<K>, B extends ComparableKeyedData<K>> Map<K, B> sortedMap(B ... beans) throws Exception {
        return this.map(new TreeMap(), (KeyedData[])beans);
    }

    public <K extends Comparable<K>, B extends ComparableKeyedData<K>> Map<K, B> sortedMap(Class<B> klass, Object[] ... parameterLists) throws Exception {
        return this.map(new TreeMap(), klass, parameterLists);
    }

    public <B> List<B> list(B ... beans) throws Exception {
        return this.list((List<B>)new LinkedList(), beans);
    }

    public <B> List<B> list(Class<B> klass, Object[] ... parameterLists) throws Exception {
        return this.list(new LinkedList(), klass, parameterLists);
    }

    public <B> List<B> indexedList(B ... beans) throws Exception {
        return this.list((List<B>)new ArrayList(), beans);
    }

    public <B> List<B> indexedList(Class<B> klass, Object[] ... parameterLists) throws Exception {
        return this.list(new ArrayList(), klass, parameterLists);
    }

    private <K, B extends KeyedData<K>> Map<K, B> map(Map<K, B> map, B ... beans) throws Exception {
        for (B bean : beans) {
            map.put(((KeyedData)bean).getKey(), bean);
        }
        return map;
    }

    private <K, B extends KeyedData<K>> Map<K, B> map(Map<K, B> map, Class<B> beanClass, Object[] ... parameterLists) throws Exception {
        for (Object[] parameterList : parameterLists) {
            Constructor<B> constructor = this.getBestConstructor(beanClass, parameterList);
            KeyedData bean = (KeyedData)constructor.newInstance(parameterList);
            map.put(bean.getKey(), bean);
        }
        return map;
    }

    private <B> List<B> list(List<B> list, B ... beans) throws Exception {
        for (B bean : beans) {
            list.add(bean);
        }
        return list;
    }

    private <B> List<B> list(List<B> list, Class<B> beanClass, Object[] ... parameterLists) throws Exception {
        for (Object[] parameterList : parameterLists) {
            Constructor<B> constructor = this.getBestConstructor(beanClass, parameterList);
            B bean = constructor.newInstance(parameterList);
            list.add(bean);
        }
        return list;
    }

    private <B> Constructor<B> getBestConstructor(Class<B> beanClass, Object[] parameterList) throws Exception {
        Constructor<?>[] constructors = beanClass.getConstructors();
        if (constructors.length == 1) {
            return constructors[0];
        }
        try {
            Class[] parameterTypes = new Class[parameterList.length];
            for (int i = 0; i < parameterList.length; ++i) {
                parameterTypes[i] = parameterList[i].getClass();
            }
            return beanClass.getConstructor(parameterTypes);
        }
        catch (NoSuchMethodException nsme) {
            for (int i = 0; i < constructors.length; ++i) {
                if (constructors[i].getParameterTypes().length != parameterList.length) continue;
                return constructors[i];
            }
            throw new Exception(String.format("Matching constructor could not be found: %s%s", beanClass, Arrays.asList(parameterList)));
        }
    }

    public static void main(String[] args) {
        try {
            Map map = new InlineData().sortedMap(new Bean[]{new Bean(34), new Bean(21), new Bean(13)});
            System.out.println(map);
            Map map2 = new InlineData().map(Bean.class, {22}, {2}, {33});
            System.out.println(map2);
        }
        catch (Throwable t) {
            t.printStackTrace();
        }
    }

    private static class Bean
    extends ComparableKeyedData<Integer> {
        private Integer i;

        public Bean(Integer i) {
            this.i = i;
        }

        @Override
        public Integer getKey() {
            return this.i;
        }

        public String toString() {
            return String.valueOf(this.i);
        }
    }
}
