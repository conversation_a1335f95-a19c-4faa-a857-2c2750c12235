/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import java.lang.reflect.Method;

private static class CSVData.ColumnHeader {
    private String name;
    private Method valueOf;
    private String defaultValue;

    public CSVData.ColumnHeader(String name, Method valueOf, String defaultValue) {
        this.name = name;
        this.valueOf = valueOf;
        this.defaultValue = defaultValue;
    }

    public String getName() {
        return this.name;
    }

    public Method getValueOf() {
        return this.valueOf;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }
}
