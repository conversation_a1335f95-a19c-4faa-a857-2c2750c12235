/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.util.data.ConverterFactory;
import glog.util.exception.GLException;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;

private static class ConverterFactory.BooleanConverter
extends ConverterFactory.DefaultConverter {
    private ConverterFactory.BooleanConverter() {
        super(null);
    }

    @Override
    public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
        if (builder != null) {
            builder.add(key, ((Boolean)value).booleanValue());
        }
    }

    @Override
    public Object fromJsonValue(JsonValue value) {
        return Boolean.valueOf(value.getValueType().name());
    }

    @Override
    public Object fromString(String value) {
        if ("Y".equalsIgnoreCase(value)) {
            return Boolean.TRUE;
        }
        if ("N".equalsIgnoreCase(value)) {
            return Boolean.FALSE;
        }
        return Boolean.valueOf(value);
    }

    @Override
    public Object fromObject(Object value) throws GLException {
        if (value == null) {
            return Boolean.FALSE;
        }
        return this.fromString(value.toString());
    }
}
