/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.fusion.cil.common.sdo.DateType;
import glog.fusion.cil.common.sdo.MeasureType;
import glog.util.data.Converter;
import glog.util.data.ConverterFactory;
import java.util.HashMap;

static final class ConverterFactory.1
extends HashMap<Class<?>, Converter> {
    private static final long serialVersionUID = 1L;

    ConverterFactory.1() {
        this.put(DateType.class, new ConverterFactory.DateTypeConverter(null));
        this.put(MeasureType.class, new ConverterFactory.MeasureTypeConverter(null));
        this.put(Boolean.class, new ConverterFactory.BooleanConverter(null));
        this.put(String.class, new ConverterFactory.StringConverter(null));
        this.put(Integer.class, new ConverterFactory.IntegerConverter(null));
        this.put(Long.class, new ConverterFactory.LongConverter(null));
        this.put(Double.class, new ConverterFactory.DoubleConverter(null));
    }
}
