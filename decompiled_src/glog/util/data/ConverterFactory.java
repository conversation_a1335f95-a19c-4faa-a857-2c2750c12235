/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.Json
 *  javax.json.JsonNumber
 *  javax.json.JsonObject
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonString
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.fusion.cil.common.sdo.DateType;
import glog.fusion.cil.common.sdo.MeasureType;
import glog.util.LocalTimestamp;
import glog.util.TypeUnit;
import glog.util.data.Converter;
import glog.util.exception.GLException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;
import javax.json.Json;
import javax.json.JsonNumber;
import javax.json.JsonObject;
import javax.json.JsonObjectBuilder;
import javax.json.JsonString;
import javax.json.JsonValue;

public class ConverterFactory {
    private static Map<Class<?>, Converter> converterMap = new HashMap<Class<?>, Converter>(){
        private static final long serialVersionUID = 1L;
        {
            this.put(DateType.class, new DateTypeConverter());
            this.put(MeasureType.class, new MeasureTypeConverter());
            this.put(Boolean.class, new BooleanConverter());
            this.put(String.class, new StringConverter());
            this.put(Integer.class, new IntegerConverter());
            this.put(Long.class, new LongConverter());
            this.put(Double.class, new DoubleConverter());
        }
    };

    public static Converter getConverter(Class<?> clz) {
        Converter converter = converterMap.get(clz);
        if (converter == null) {
            converter = new DefaultConverter();
        }
        return converter;
    }

    public static Converter getConverter(String clz) {
        DefaultConverter converter = null;
        try {
            return ConverterFactory.getConverter(Class.forName(clz));
        }
        catch (Throwable t) {
            converter = new DefaultConverter();
            return converter;
        }
    }

    private static class MeasureTypeConverter
    implements Converter {
        private MeasureTypeConverter() {
        }

        @Override
        public String toString(Object value) {
            MeasureType uom = (MeasureType)value;
            return String.format("%d %s", uom.getValue(), uom.getUomCode());
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            MeasureType uom = (MeasureType)value;
            if (builder != null) {
                builder.add(key, Json.createObjectBuilder().add("value", uom.getValue()).add("uomCode", uom.getUomCode()));
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) throws GLException {
            JsonObject json = (JsonObject)value;
            MeasureType uom = new MeasureType();
            uom.setUomCode(json.getString("uomCode"));
            uom.setValue(json.getJsonNumber("value").bigDecimalValue());
            return uom;
        }

        @Override
        public Object fromString(String value) throws GLException {
            if (value == null) {
                return null;
            }
            MeasureType uom = new MeasureType();
            Scanner scanner = new Scanner(value);
            uom.setValue(scanner.nextBigDecimal());
            uom.setUomCode(scanner.next());
            scanner.close();
            return uom;
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            if (TypeUnit.class.isAssignableFrom(value.getClass())) {
                MeasureType uom = new MeasureType();
                uom.setUomCode(((TypeUnit)value).getType().toString());
                uom.setValue(new BigDecimal(((TypeUnit)value).getUnits().toString()));
                value = uom;
            }
            return value;
        }
    }

    private static class DateTypeConverter
    implements Converter {
        private DateTypeConverter() {
        }

        @Override
        public String toString(Object value) {
            return value.toString();
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            DateType date = (DateType)value;
            if (builder != null) {
                builder.add(key, Json.createObjectBuilder().add("date", date.toJsonString()));
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) throws GLException {
            JsonObject json = (JsonObject)value;
            try {
                return DateType.fromJsonString(json.getString("date"));
            }
            catch (Throwable t) {
                throw GLException.factory(t);
            }
        }

        @Override
        public Object fromString(String value) throws GLException {
            try {
                return DateType.fromString(value);
            }
            catch (Throwable t) {
                throw GLException.factory(t);
            }
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            try {
                if (LocalTimestamp.class.isAssignableFrom(value.getClass())) {
                    DateType date = DateType.fromCalender((LocalTimestamp)value);
                    value = date;
                }
                return value;
            }
            catch (Throwable t) {
                throw GLException.factory(t);
            }
        }
    }

    private static class LongConverter
    extends DefaultConverter {
        private LongConverter() {
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, ((Long)value).longValue());
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            JsonNumber number = (JsonNumber)value;
            return (long)number.intValue();
        }

        @Override
        public Object fromString(String value) {
            return Long.valueOf(value);
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            return Long.valueOf(value.toString());
        }
    }

    private static class IntegerConverter
    extends DefaultConverter {
        private IntegerConverter() {
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, ((Integer)value).intValue());
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            JsonNumber number = (JsonNumber)value;
            return number.intValue();
        }

        @Override
        public Object fromString(String value) {
            return Integer.valueOf(value);
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            return Integer.valueOf(value.toString());
        }
    }

    private static class DoubleConverter
    extends DefaultConverter {
        private DoubleConverter() {
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, ((Double)value).doubleValue());
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            JsonNumber number = (JsonNumber)value;
            return number.doubleValue();
        }

        @Override
        public Object fromString(String value) {
            return Double.valueOf(value);
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            return Double.valueOf(value.toString());
        }
    }

    private static class BooleanConverter
    extends DefaultConverter {
        private BooleanConverter() {
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, ((Boolean)value).booleanValue());
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            return Boolean.valueOf(value.getValueType().name());
        }

        @Override
        public Object fromString(String value) {
            if ("Y".equalsIgnoreCase(value)) {
                return Boolean.TRUE;
            }
            if ("N".equalsIgnoreCase(value)) {
                return Boolean.FALSE;
            }
            return Boolean.valueOf(value);
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            if (value == null) {
                return Boolean.FALSE;
            }
            return this.fromString(value.toString());
        }
    }

    private static class StringConverter
    extends DefaultConverter {
        private StringConverter() {
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, (String)value);
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            JsonString string = (JsonString)value;
            return string.getString();
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            return value.toString();
        }
    }

    private static class DefaultConverter
    implements Converter {
        private DefaultConverter() {
        }

        @Override
        public String toString(Object value) {
            return value.toString();
        }

        @Override
        public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
            if (builder != null) {
                builder.add(key, value.toString());
            }
        }

        @Override
        public Object fromJsonValue(JsonValue value) {
            return value.toString();
        }

        @Override
        public Object fromString(String value) {
            return value;
        }

        @Override
        public Object fromObject(Object value) throws GLException {
            return value;
        }
    }
}
