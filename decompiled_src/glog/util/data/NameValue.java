/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.util.data.KeyedData;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class NameValue
extends KeyedData<String> {
    public String name;
    public String value;

    @Override
    public String getKey() {
        return this.name;
    }

    public String toString() {
        return String.format("%s=%s", this.name, this.value);
    }

    public static Map<String, String> asMap(NameValue[] nameValues) {
        HashMap<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < nameValues.length; ++i) {
            map.put(nameValues[i].name, nameValues[i].value);
        }
        return map;
    }

    public static Map<String, String> asMap(Collection<NameValue> nameValues) {
        HashMap<String, String> map = new HashMap<String, String>();
        for (NameValue nameValue : nameValues) {
            map.put(nameValue.name, nameValue.value);
        }
        return map;
    }

    public static Map<String, String> asMap(Map<String, NameValue> nameValues) {
        HashMap<String, String> map = new HashMap<String, String>();
        for (NameValue nameValue : nameValues.values()) {
            map.put(nameValue.name, nameValue.value);
        }
        return map;
    }
}
