/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.util.data.Converter;
import glog.util.exception.GLException;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;

private static class ConverterFactory.DefaultConverter
implements Converter {
    private ConverterFactory.DefaultConverter() {
    }

    @Override
    public String toString(Object value) {
        return value.toString();
    }

    @Override
    public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
        if (builder != null) {
            builder.add(key, value.toString());
        }
    }

    @Override
    public Object fromJsonValue(JsonValue value) {
        return value.toString();
    }

    @Override
    public Object fromString(String value) {
        return value;
    }

    @Override
    public Object fromObject(Object value) throws GLException {
        return value;
    }
}
