/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.JsonNumber
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.util.data.ConverterFactory;
import glog.util.exception.GLException;
import javax.json.JsonNumber;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;

private static class ConverterFactory.IntegerConverter
extends ConverterFactory.DefaultConverter {
    private ConverterFactory.IntegerConverter() {
        super(null);
    }

    @Override
    public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
        if (builder != null) {
            builder.add(key, ((Integer)value).intValue());
        }
    }

    @Override
    public Object fromJsonValue(JsonValue value) {
        JsonNumber number = (JsonNumber)value;
        return number.intValue();
    }

    @Override
    public Object fromString(String value) {
        return Integer.valueOf(value);
    }

    @Override
    public Object fromObject(Object value) throws GLException {
        return Integer.valueOf(value.toString());
    }
}
