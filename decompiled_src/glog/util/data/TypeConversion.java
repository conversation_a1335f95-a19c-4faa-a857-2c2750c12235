/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

public class TypeConversion {
    public static Class getJDBCTypeClass(int jdbcType) {
        switch (jdbcType) {
            case -1: 
            case 1: 
            case 12: {
                return String.class;
            }
            case -7: {
                return Boolean.class;
            }
            case -6: 
            case 2: 
            case 4: 
            case 5: {
                return Integer.class;
            }
            case -5: {
                return Long.class;
            }
            case 6: 
            case 8: {
                return Double.class;
            }
            case 91: {
                return Date.class;
            }
        }
        return Object.class;
    }

    public static Object getObjectFromResultSet(int column, ResultSet rs) {
        try {
            ResultSetMetaData rmd = rs.getMetaData();
            int type = rmd.getColumnType(column);
            switch (type) {
                case -1: 
                case 1: 
                case 12: {
                    return rs.getString(column);
                }
                case -7: {
                    return new Boolean(rs.getBoolean(column));
                }
                case -6: 
                case 2: 
                case 4: 
                case 5: {
                    return new Integer(rs.getInt(column));
                }
                case -5: {
                    return new Long(rs.getLong(column));
                }
                case 6: 
                case 8: {
                    return new Double(rs.getDouble(column));
                }
                case 91: {
                    return rs.getDate(column);
                }
            }
            return rs.getObject(column);
        }
        catch (SQLException e) {
            return null;
        }
    }
}
