/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import java.io.Serializable;

public class LobData
implements Serializable {
    private String colName;
    protected Object content;
    private boolean needModify = false;

    public LobData(String colName) {
        this.colName = colName;
        this.content = null;
        this.needModify = false;
    }

    public LobData(String colName, Object content) {
        this(colName, content, true);
    }

    public LobData(String colName, Object content, boolean needModify) {
        this.colName = colName;
        this.content = content;
        this.needModify = needModify;
    }

    public boolean needModify() {
        return this.needModify;
    }

    public void setNeedModify(boolean needModify) {
        this.needModify = needModify;
    }

    public boolean set(LobData lobData) {
        if (lobData == null) {
            this.setContent(null);
            return true;
        }
        if (this.colName.equalsIgnoreCase(lobData.getColName())) {
            this.setContent(lobData.getContent());
            return true;
        }
        return false;
    }

    public Object getContent() {
        return this.content;
    }

    public String getColName() {
        return this.colName;
    }

    public void setContent(Object content) {
        this.content = content;
        this.needModify = true;
    }

    public String toString() {
        return "LobData: colName=" + this.colName + ", content=" + this.content + ", needModify=" + this.needModify;
    }
}
