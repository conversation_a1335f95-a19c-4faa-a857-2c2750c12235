/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

public class Database.ColumnMetadata {
    String name;
    String type;
    int columnSize;
    int precision;

    Database.ColumnMetadata(String name, String type, int columnSize, int precision) {
        this.name = name;
        this.type = type;
        this.columnSize = columnSize;
        this.precision = precision;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }

    public int getColumnSize() {
        return this.columnSize;
    }

    public int getPrecision() {
        return this.precision;
    }

    public String getTypeString() {
        if (this.precision > 0) {
            return this.type + "(" + this.columnSize + "," + this.precision + ")";
        }
        return this.type + "(" + this.columnSize + ")";
    }
}
