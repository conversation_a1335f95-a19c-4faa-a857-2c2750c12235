/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.json.Json
 *  javax.json.JsonObject
 *  javax.json.JsonObjectBuilder
 *  javax.json.JsonValue
 */
package glog.util.data;

import glog.fusion.cil.common.sdo.DateType;
import glog.util.LocalTimestamp;
import glog.util.data.Converter;
import glog.util.exception.GLException;
import javax.json.Json;
import javax.json.JsonObject;
import javax.json.JsonObjectBuilder;
import javax.json.JsonValue;

private static class ConverterFactory.DateTypeConverter
implements Converter {
    private ConverterFactory.DateTypeConverter() {
    }

    @Override
    public String toString(Object value) {
        return value.toString();
    }

    @Override
    public void toJsonValue(JsonObjectBuilder builder, String key, Object value) {
        DateType date = (DateType)value;
        if (builder != null) {
            builder.add(key, Json.createObjectBuilder().add("date", date.toJsonString()));
        }
    }

    @Override
    public Object fromJsonValue(JsonValue value) throws GLException {
        JsonObject json = (JsonObject)value;
        try {
            return DateType.fromJsonString(json.getString("date"));
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    @Override
    public Object fromString(String value) throws GLException {
        try {
            return DateType.fromString(value);
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    @Override
    public Object fromObject(Object value) throws GLException {
        try {
            if (LocalTimestamp.class.isAssignableFrom(value.getClass())) {
                DateType date = DateType.fromCalender((LocalTimestamp)value);
                value = date;
            }
            return value;
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }
}
