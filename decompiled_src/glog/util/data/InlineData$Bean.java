/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.util.data.ComparableKeyedData;

private static class InlineData.<PERSON>
extends ComparableKeyedData<Integer> {
    private Integer i;

    public InlineData.Bean(Integer i) {
        this.i = i;
    }

    @Override
    public Integer getKey() {
        return this.i;
    }

    public String toString() {
        return String.valueOf(this.i);
    }
}
