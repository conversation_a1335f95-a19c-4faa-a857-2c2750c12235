/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import glog.util.data.FileData;
import java.lang.reflect.Method;

protected static class FileData.TokenType {
    private FileData.TypeClass typeClass;
    private Method valueOfMethod;
    private String defaultValue;
    private String token;

    public FileData.TokenType(FileData.TypeClass typeClass, Method valueOfMethod, String defaultValue, String token) {
        this.typeClass = typeClass;
        this.valueOfMethod = valueOfMethod;
        this.defaultValue = defaultValue;
        this.token = token;
    }

    public FileData.TypeClass getTypeClass() {
        return this.typeClass;
    }

    public Method getValueOfMethod() {
        return this.valueOfMethod;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }

    public String getToken() {
        return this.token;
    }

    public String toString() {
        return this.token + ":" + this.typeClass;
    }
}
