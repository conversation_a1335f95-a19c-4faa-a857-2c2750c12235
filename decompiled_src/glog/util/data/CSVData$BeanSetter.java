/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

private static class CSVData.BeanSetter {
    private Method setter;
    private Field field;
    private Method valueOf;
    private String defaultValue;

    public CSVData.BeanSetter(Method setter, Field field, Method valueOf, String defaultValue) {
        this.setter = setter;
        this.field = field;
        this.valueOf = valueOf;
        this.defaultValue = defaultValue;
    }

    public Method getSetter() {
        return this.setter;
    }

    public Field getField() {
        return this.field;
    }

    public Method getValueOf() {
        return this.valueOf;
    }

    public String getDefaultValue() {
        return this.defaultValue;
    }
}
