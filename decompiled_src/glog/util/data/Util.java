/*
 * Decompiled with CFR 0.152.
 */
package glog.util.data;

public class Util {
    public static String formatForLike(String value) {
        StringBuffer out = new StringBuffer();
        for (int i = 0; i < value.length(); ++i) {
            if (value.charAt(i) == '*') {
                out.append("%");
                continue;
            }
            out.append(value.charAt(i));
        }
        return out.toString();
    }
}
