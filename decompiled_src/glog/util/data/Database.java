/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  glog_deploy.build.codegen.DatabaseSchema
 *  glog_deploy.build.codegen.DatabaseSchema$Column
 *  glog_deploy.build.codegen.DatabaseSchema$Table
 */
package glog.util.data;

import glog.util.text.StringUtil;
import glog_deploy.build.codegen.DatabaseSchema;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.Statement;
import java.sql.Struct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;
import java.util.concurrent.Executor;

public class Database {
    static DatabaseSchema dbSchema = null;
    private String jdbcUrl;
    private String userName;
    private String pwd;
    private String schema;
    private Connection connection;
    HashMap tables = new HashMap();
    HashMap tableDefs = new HashMap();

    public Database(String jdbcUrl, String userName, String pwd, String schema) {
        this.jdbcUrl = jdbcUrl;
        this.userName = userName;
        this.pwd = pwd;
        this.schema = schema;
    }

    public void close() {
        try {
            this.connection.close();
        }
        catch (Exception exception) {
            // empty catch block
        }
    }

    public Vector listTables(boolean tablesOnly, boolean showVirtuals) {
        String[] types = tablesOnly ? new String[]{"TABLE"} : new String[]{"TABLE", "VIEW"};
        return this.listTables(types, showVirtuals);
    }

    public Vector listTables() {
        String[] types = new String[]{"TABLE", "VIEW"};
        return this.listTables(types);
    }

    public Vector listTables(String[] types) {
        return this.listTables(types, true);
    }

    public Vector listTables(String[] types, boolean showVirtuals) {
        Vector<String> out = new Vector<String>();
        ArrayList tableList = null;
        StringBuffer key = new StringBuffer();
        for (int i = 0; i < types.length; ++i) {
            key.append(types[i]);
            key.append("_");
        }
        if (!this.tables.containsKey(key.toString())) {
            String name;
            tableList = new ArrayList();
            if (dbSchema != null) {
                DatabaseSchema.Table[] tables = dbSchema.getTables();
                for (int i = 0; i < tables.length; ++i) {
                    name = tables[i].getName().toUpperCase();
                    if (name.startsWith("XX")) continue;
                    tableList.add(name);
                }
            } else {
                try {
                    DatabaseMetaData md = this.connection.getMetaData();
                    ResultSet rs = md.getTables(null, this.schema.toUpperCase(), null, types);
                    while (rs.next()) {
                        name = rs.getString("TABLE_NAME");
                        if (name.startsWith("XX")) continue;
                        tableList.add(name);
                    }
                    rs.close();
                }
                catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            this.tables.put(key.toString(), tableList);
        } else {
            tableList = (ArrayList)this.tables.get(key.toString());
        }
        if (!showVirtuals) {
            for (int i = 0; i < tableList.size(); ++i) {
                String name = (String)tableList.get(i);
                if (name.indexOf(36) >= 0) continue;
                out.addElement(name);
            }
        } else {
            out.addAll(tableList);
        }
        return out;
    }

    public Vector getColumns(String tableName) {
        Vector<String> out = new Vector<String>();
        try {
            ArrayList cols = this.getColumnMetadata(tableName);
            for (int i = 0; i < cols.size(); ++i) {
                out.add(((ColumnMetadata)cols.get(i)).getName());
            }
        }
        catch (SQLException e) {
            e.printStackTrace();
        }
        return out;
    }

    public String getColumnType(String columnName) {
        try {
            String[] s = StringUtil.split(columnName, '.');
            String tableName = "";
            String column = "";
            if (s.length == 2) {
                tableName = s[0].toUpperCase();
                column = s[1].toUpperCase();
            } else {
                column = s[0];
            }
            ArrayList cols = this.getColumnMetadata(tableName);
            for (int i = 0; i < cols.size(); ++i) {
                ColumnMetadata col = (ColumnMetadata)cols.get(i);
                if (!col.getName().equals(column)) continue;
                return col.getType().toUpperCase();
            }
        }
        catch (SQLException e) {
            e.printStackTrace();
        }
        return "";
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected ArrayList getColumnMetadata(String tableName) throws SQLException {
        if (this.tableDefs.containsKey(tableName.toUpperCase())) {
            return (ArrayList)this.tableDefs.get(tableName.toUpperCase());
        }
        ArrayList<ColumnMetadata> list = new ArrayList<ColumnMetadata>();
        if (dbSchema != null) {
            DatabaseSchema.Table table = dbSchema.getTable(null, tableName);
            DatabaseSchema.Column[] columns = table.getColumns();
            for (int i = 0; i < columns.length; ++i) {
                list.add(new ColumnMetadata(columns[i].getName().toUpperCase(), columns[i].getType(), columns[i].getSize(), columns[i].getDecimelDigits()));
            }
        } else {
            try (ResultSet rs = null;){
                DatabaseMetaData md = this.connection.getMetaData();
                rs = md.getColumns(null, this.schema.toUpperCase(), tableName, "%");
                while (rs.next()) {
                    String columnName = rs.getString(4);
                    int columnSize = rs.getInt(7);
                    int precision = rs.getInt(9);
                    list.add(new ColumnMetadata(columnName, rs.getString("TYPE_NAME"), columnSize, precision));
                }
            }
        }
        this.tableDefs.put(tableName.toUpperCase(), list);
        return list;
    }

    public ColumnMetadata[] getColumnMetadata(String tableName, ArrayList cols) throws SQLException {
        Object rs = null;
        ArrayList<String> columns = new ArrayList<String>();
        for (int i = 0; i < cols.size(); ++i) {
            columns.add(((String)cols.get(i)).toUpperCase());
        }
        ArrayList list = this.getColumnMetadata(tableName);
        ColumnMetadata[] result = new ColumnMetadata[columns.size()];
        block1: for (int i = 0; i < columns.size(); ++i) {
            String columnName = (String)columns.get(i);
            for (int j = 0; j < list.size(); ++j) {
                ColumnMetadata col = (ColumnMetadata)list.get(j);
                if (!col.getName().equals(columnName)) continue;
                result[i] = col;
                continue block1;
            }
        }
        return result;
    }

    public void createConnection() throws SQLException {
        if (dbSchema != null) {
            this.connection = new DummyConnection();
            return;
        }
        try {
            Class.forName("oracle.jdbc.driver.OracleDriver");
        }
        catch (ClassNotFoundException e) {
            e.printStackTrace();
            return;
        }
        this.connection = DriverManager.getConnection(this.jdbcUrl, this.userName, this.pwd);
        Statement stmt = this.connection.createStatement();
        stmt.executeUpdate("alter session set nls_date_format = 'YYYY-MM-DD HH24:MI:SS..'");
        stmt.close();
    }

    public Connection getConnection() {
        return this.connection;
    }

    public DatabaseMetaData getMetaData() {
        try {
            return this.connection.getMetaData();
        }
        catch (SQLException e) {
            return null;
        }
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String v) {
        this.userName = v;
    }

    public String getSchema() {
        return this.schema;
    }

    public void setSchema(String v) {
        this.schema = v;
    }

    public String getPwd() {
        return this.pwd;
    }

    public void setPwd(String v) {
        this.pwd = v;
    }

    public String getJdbcUrl() {
        return this.jdbcUrl;
    }

    public void setJdbcUrl(String v) {
        this.jdbcUrl = v;
    }

    static {
        if (DatabaseSchema.doesSchemaFileExist()) {
            try {
                DatabaseSchema.loadSchema();
                dbSchema = DatabaseSchema.get();
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    static class DummyConnection
    implements Connection {
        DummyConnection() {
        }

        @Override
        public void clearWarnings() throws SQLException {
        }

        @Override
        public void close() throws SQLException {
        }

        @Override
        public void commit() throws SQLException {
        }

        @Override
        public Array createArrayOf(String typeName, Object[] elements) throws SQLException {
            return null;
        }

        @Override
        public Blob createBlob() {
            return null;
        }

        @Override
        public Clob createClob() {
            return null;
        }

        @Override
        public NClob createNClob() {
            return null;
        }

        @Override
        public SQLXML createSQLXML() {
            return null;
        }

        @Override
        public Statement createStatement() throws SQLException {
            return null;
        }

        @Override
        public Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
            return null;
        }

        @Override
        public Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return null;
        }

        @Override
        public Struct createStruct(String typeName, Object[] attributes) throws SQLException {
            return null;
        }

        @Override
        public boolean getAutoCommit() throws SQLException {
            return false;
        }

        @Override
        public String getCatalog() throws SQLException {
            return null;
        }

        @Override
        public String getClientInfo(String name) throws SQLException {
            return null;
        }

        @Override
        public Properties getClientInfo() throws SQLException {
            return null;
        }

        @Override
        public int getHoldability() throws SQLException {
            return 0;
        }

        @Override
        public DatabaseMetaData getMetaData() throws SQLException {
            return null;
        }

        @Override
        public int getTransactionIsolation() throws SQLException {
            return 0;
        }

        public Map getTypeMap() throws SQLException {
            return null;
        }

        @Override
        public SQLWarning getWarnings() throws SQLException {
            return null;
        }

        @Override
        public boolean isClosed() throws SQLException {
            return true;
        }

        @Override
        public boolean isReadOnly() throws SQLException {
            return true;
        }

        @Override
        public boolean isValid(int timeout) throws SQLException {
            return false;
        }

        @Override
        public boolean isWrapperFor(Class<?> c) {
            return false;
        }

        @Override
        public String nativeSQL(String sql) throws SQLException {
            return null;
        }

        @Override
        public CallableStatement prepareCall(String sql) throws SQLException {
            return null;
        }

        @Override
        public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
            return null;
        }

        @Override
        public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
            return null;
        }

        @Override
        public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
            return null;
        }

        @Override
        public void releaseSavepoint(Savepoint savepoint) throws SQLException {
        }

        @Override
        public void rollback() throws SQLException {
        }

        @Override
        public void rollback(Savepoint savepoint) throws SQLException {
        }

        @Override
        public void setAutoCommit(boolean autoCommit) throws SQLException {
        }

        @Override
        public void setCatalog(String catalog) throws SQLException {
        }

        @Override
        public void setClientInfo(String name, String value) {
        }

        @Override
        public void setClientInfo(Properties properties) {
        }

        @Override
        public void setHoldability(int holdability) throws SQLException {
        }

        @Override
        public void setReadOnly(boolean readOnly) throws SQLException {
        }

        @Override
        public Savepoint setSavepoint() throws SQLException {
            return null;
        }

        @Override
        public Savepoint setSavepoint(String name) throws SQLException {
            return null;
        }

        @Override
        public void setTransactionIsolation(int level) throws SQLException {
        }

        public void setTypeMap(Map map) throws SQLException {
        }

        @Override
        public <T> T unwrap(Class<T> t) {
            return null;
        }

        @Override
        public int getNetworkTimeout() throws SQLException {
            return 0;
        }

        @Override
        public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
        }

        @Override
        public void abort(Executor executor) throws SQLException {
        }

        @Override
        public String getSchema() throws SQLException {
            return null;
        }

        @Override
        public void setSchema(String schema) throws SQLException {
        }
    }

    public class ColumnMetadata {
        String name;
        String type;
        int columnSize;
        int precision;

        ColumnMetadata(String name, String type, int columnSize, int precision) {
            this.name = name;
            this.type = type;
            this.columnSize = columnSize;
            this.precision = precision;
        }

        public String getName() {
            return this.name;
        }

        public String getType() {
            return this.type;
        }

        public int getColumnSize() {
            return this.columnSize;
        }

        public int getPrecision() {
            return this.precision;
        }

        public String getTypeString() {
            if (this.precision > 0) {
                return this.type + "(" + this.columnSize + "," + this.precision + ")";
            }
            return this.type + "(" + this.columnSize + ")";
        }
    }
}
