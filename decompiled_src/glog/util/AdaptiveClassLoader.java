/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Vector;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

public class AdaptiveClassLoader
extends ClassLoader {
    private static int generationCounter = 0;
    private int generation;
    private Hashtable cache = new Hashtable();
    private Vector repository;

    public AdaptiveClassLoader(Vector classRepository) throws IllegalArgumentException {
        Enumeration e = classRepository.elements();
        while (e.hasMoreElements()) {
            File file;
            Object o = e.nextElement();
            try {
                file = (File)o;
            }
            catch (ClassCastException objectIsNotFile) {
                throw new IllegalArgumentException("Object " + o + "is not a valid \"File\" instance");
            }
            if (!file.exists()) {
                throw new IllegalArgumentException("Repository " + file.getAbsolutePath() + " doesn't exist!");
            }
            if (!file.canRead()) {
                throw new IllegalArgumentException("Don't have read access for file " + file.getAbsolutePath());
            }
            if (file.isDirectory() || this.isZipOrJarArchive(file)) continue;
            throw new IllegalArgumentException(file.getAbsolutePath() + " is not a directory or zip/jar file" + " or if it's a zip/jar file then it is corrupted.");
        }
        this.repository = classRepository;
        this.generation = generationCounter++;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private boolean isZipOrJarArchive(File file) {
        boolean isArchive = true;
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(file);
        }
        catch (ZipException zipCurrupted) {
            isArchive = false;
        }
        catch (IOException anyIOError) {
            isArchive = false;
        }
        finally {
            if (zipFile != null) {
                try {
                    zipFile.close();
                }
                catch (IOException ignored) {}
            }
        }
        return isArchive;
    }

    public synchronized boolean shouldReload(String classname) {
        ClassCacheEntry entry = (ClassCacheEntry)this.cache.get(classname);
        if (entry == null) {
            return false;
        }
        if (entry.isSystemClass()) {
            return false;
        }
        boolean reload = entry.origin.lastModified() != entry.lastModified;
        return reload;
    }

    public synchronized boolean shouldReload() {
        Enumeration e = this.cache.elements();
        while (e.hasMoreElements()) {
            ClassCacheEntry entry = (ClassCacheEntry)e.nextElement();
            if (entry.isSystemClass()) continue;
            long msOrigin = entry.origin.lastModified();
            if (msOrigin == 0L) {
                return true;
            }
            if (msOrigin == entry.lastModified) continue;
            return true;
        }
        return false;
    }

    public AdaptiveClassLoader reinstantiate() {
        return new AdaptiveClassLoader(this.repository);
    }

    protected synchronized Class loadClass(String name, boolean resolve) throws ClassNotFoundException {
        Class<?> c = null;
        ClassCacheEntry entry = (ClassCacheEntry)this.cache.get(name);
        if (entry != null) {
            c = entry.loadedClass;
            if (resolve) {
                this.resolveClass(c);
            }
            return c;
        }
        Enumeration repEnum = this.repository.elements();
        ClassCacheEntry classCache = new ClassCacheEntry();
        while (repEnum.hasMoreElements()) {
            byte[] classData;
            File file = (File)repEnum.nextElement();
            try {
                classData = file.isDirectory() ? this.loadClassFromDirectory(file, name, classCache) : this.loadClassFromZipfile(file, name, classCache);
            }
            catch (IOException ioe) {
                classData = null;
            }
            if (classData == null) continue;
            classCache.loadedClass = c = this.defineClass(name, classData, 0, classData.length);
            classCache.lastModified = classCache.origin.lastModified();
            this.cache.put(name, classCache);
            if (resolve) {
                this.resolveClass(c);
            }
            return c;
        }
        if (!this.securityAllowsClass(name)) {
            return this.loadSystemClass(name, resolve);
        }
        try {
            c = this.loadSystemClass(name, resolve);
            if (c != null) {
                if (resolve) {
                    this.resolveClass(c);
                }
                return c;
            }
        }
        catch (Exception e) {
            c = null;
        }
        throw new ClassNotFoundException(name);
    }

    private Class loadSystemClass(String name, boolean resolve) throws NoClassDefFoundError, ClassNotFoundException {
        Class<?> c = this.findSystemClass(name);
        ClassCacheEntry cacheEntry = new ClassCacheEntry();
        cacheEntry.origin = null;
        cacheEntry.loadedClass = c;
        cacheEntry.lastModified = Long.MAX_VALUE;
        this.cache.put(name, cacheEntry);
        if (resolve) {
            this.resolveClass(c);
        }
        return c;
    }

    private boolean securityAllowsClass(String className) {
        try {
            SecurityManager security = System.getSecurityManager();
            if (security == null) {
                return true;
            }
            int lastDot = className.lastIndexOf(46);
            security.checkPackageDefinition(lastDot > -1 ? className.substring(0, lastDot) : "");
            return true;
        }
        catch (SecurityException e) {
            return false;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private byte[] loadClassFromDirectory(File dir, String name, ClassCacheEntry cache) throws IOException {
        File classFile;
        String classFileName = name.replace('.', File.separatorChar) + ".class";
        if (!Character.isJavaIdentifierStart(classFileName.charAt(0))) {
            int start = 1;
            while (!Character.isJavaIdentifierStart(classFileName.charAt(start++))) {
            }
            classFileName = classFileName.substring(start);
        }
        if ((classFile = new File(dir, classFileName)).exists()) {
            cache.origin = classFile;
            try (FileInputStream in = new FileInputStream(classFile);){
                byte[] byArray = this.loadBytesFromStream(in, (int)classFile.length());
                return byArray;
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private byte[] loadClassFromZipfile(File file, String name, ClassCacheEntry cache) throws IOException {
        String classFileName = name.replace('.', '/') + ".class";
        try (ZipFile zipfile = new ZipFile(file);){
            ZipEntry entry = zipfile.getEntry(classFileName);
            if (entry != null) {
                cache.origin = file;
                byte[] byArray = this.loadBytesFromStream(zipfile.getInputStream(entry), (int)entry.getSize());
                return byArray;
            }
            byte[] byArray = null;
            return byArray;
        }
    }

    private byte[] loadBytesFromStream(InputStream in, int length) throws IOException {
        int nRead;
        byte[] buf = new byte[length];
        int count = 0;
        while (length > 0 && (nRead = in.read(buf, count, length)) != -1) {
            count += nRead;
            length -= nRead;
        }
        return buf;
    }

    @Override
    public InputStream getResourceAsStream(String name) {
        InputStream s;
        block1: {
            File file;
            s = AdaptiveClassLoader.getSystemResourceAsStream(name);
            if (s != null) break block1;
            Enumeration repEnum = this.repository.elements();
            while (repEnum.hasMoreElements() && (s = (file = (File)repEnum.nextElement()).isDirectory() ? this.loadResourceFromDirectory(file, name) : this.loadResourceFromZipfile(file, name)) == null) {
            }
        }
        return s;
    }

    private InputStream loadResourceFromDirectory(File dir, String name) {
        String fileName = name.replace('/', File.separatorChar);
        File resFile = new File(dir, fileName);
        if (resFile.exists()) {
            try {
                return new FileInputStream(resFile);
            }
            catch (FileNotFoundException shouldnothappen) {
                return null;
            }
        }
        return null;
    }

    private InputStream loadResourceFromZipfile(File file, String name) {
        try {
            ZipFile zipfile = new ZipFile(file);
            ZipEntry entry = zipfile.getEntry(name);
            if (entry != null) {
                return zipfile.getInputStream(entry);
            }
            return null;
        }
        catch (IOException e) {
            return null;
        }
    }

    @Override
    public URL getResource(String name) {
        URL u = AdaptiveClassLoader.getSystemResource(name);
        if (u != null) {
            return u;
        }
        Enumeration repEnum = this.repository.elements();
        while (repEnum.hasMoreElements()) {
            String fileName;
            File resFile;
            File file = (File)repEnum.nextElement();
            if (!file.isDirectory() || !(resFile = new File(file, fileName = name.replace('/', File.separatorChar))).exists()) continue;
            try {
                return new URL("file://" + resFile.getAbsolutePath());
            }
            catch (MalformedURLException badurl) {
                badurl.printStackTrace();
                return null;
            }
        }
        return null;
    }

    private static class ClassCacheEntry {
        Class loadedClass;
        File origin;
        long lastModified;

        private ClassCacheEntry() {
        }

        public boolean isSystemClass() {
            return this.origin == null;
        }
    }
}
