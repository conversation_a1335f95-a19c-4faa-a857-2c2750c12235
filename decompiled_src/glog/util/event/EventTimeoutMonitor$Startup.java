/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.StartupShutdown;
import glog.util.event.EventTimeoutMonitor;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;

public static class EventTimeoutMonitor.Startup
implements StartupShutdown {
    @Override
    public void load(T2SharedConnection conn) throws GLException {
        theEventTimeoutMonitor = new EventTimeoutMonitor(null);
    }

    @Override
    public void unload() throws GLException {
    }

    @Override
    public void activate(T2SharedConnection conn) {
    }
}
