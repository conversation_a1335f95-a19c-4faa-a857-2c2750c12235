/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.GLProperties;
import glog.util.StartupShutdown;
import glog.util.event.EventQueue;
import glog.util.event.EventThread;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.log.Log;
import glog.util.log.LogIds;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeSet;

public class EventTimeoutMonitor
implements Runnable {
    private long monitorPollRate = Integer.parseInt(GLProperties.get().getProperty("glog.eventQueueMonitor.polling", "0")) * 1000;
    private Map groupsToMonitor;
    private Thread monitorThread;
    private boolean needsReset = true;
    private static EventTimeoutMonitor theEventTimeoutMonitor;
    private static ThreadGroup monitorThreadGroup;

    @Override
    public void run() {
        try {
            while (!Thread.interrupted()) {
                Thread.sleep(this.monitorPollRate);
                this.setMonitor();
                long now = System.currentTimeMillis();
                for (Map.Entry entry : this.groupsToMonitor.entrySet()) {
                    EventQueue eventQueue = (EventQueue)entry.getKey();
                    Long timeout = (Long)entry.getValue();
                    EventThread[] eventThreads = eventQueue.getThreads();
                    for (int i = 0; i < eventThreads.length; ++i) {
                        if (eventThreads[i].getState() != EventThread.ACTIVE || now - eventThreads[i].getSinceStamp() < timeout) continue;
                        if (Log.idOn[LogIds.GLOG.index]) {
                            Log.logID(LogIds.GLOG, Log.WARNING, "Thread {0} has timed out and will be interrupted", eventThreads[i]);
                        }
                        eventThreads[i].copy().start();
                        eventThreads[i].interrupt();
                    }
                }
            }
        }
        catch (InterruptedException ie) {
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
    }

    public static void reset() {
        if (theEventTimeoutMonitor != null) {
            EventTimeoutMonitor.theEventTimeoutMonitor.needsReset = true;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void setMonitor() {
        if (this.needsReset) {
            try {
                TreeSet<GLProperties.Entry> threadGroupsToTimeout = GLProperties.get().entrySet("glog.eventQueueMonitor.timeout.", false, null);
                if (!threadGroupsToTimeout.isEmpty()) {
                    Map threadGroups = EventQueue.getAllEventQueues();
                    this.groupsToMonitor = new HashMap();
                    for (GLProperties.Entry entry : threadGroupsToTimeout) {
                        String groupName = entry.getKey();
                        String timeoutSpec = entry.getValue();
                        Long timeout = new Long(Long.parseLong(timeoutSpec) * 1000L);
                        if (!threadGroups.keySet().contains(groupName)) continue;
                        EventQueue eventQueue = (EventQueue)threadGroups.get(groupName);
                        this.groupsToMonitor.put(eventQueue, timeout);
                    }
                }
            }
            finally {
                this.needsReset = false;
            }
        }
    }

    private EventTimeoutMonitor() {
        if (this.monitorPollRate != 0L) {
            this.monitorThread = new Thread(monitorThreadGroup, this, "Event Queue Monitor");
            this.monitorThread.start();
        }
    }

    static {
        monitorThreadGroup = new ThreadGroup("Event Monitor");
        GLProperties.addListener("glog.eventQueueMonitor.timeout.", new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                theEventTimeoutMonitor;
                EventTimeoutMonitor.reset();
            }
        });
    }

    public static class Startup
    implements StartupShutdown {
        @Override
        public void load(T2SharedConnection conn) throws GLException {
            theEventTimeoutMonitor = new EventTimeoutMonitor();
        }

        @Override
        public void unload() throws GLException {
        }

        @Override
        public void activate(T2SharedConnection conn) {
        }
    }
}
