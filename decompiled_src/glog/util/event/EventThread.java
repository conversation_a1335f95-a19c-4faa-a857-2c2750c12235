/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  commonj.work.Work
 *  commonj.work.WorkManager
 */
package glog.util.event;

import commonj.work.Work;
import commonj.work.WorkManager;
import glog.util.LocalTimestamp;
import glog.util.TypedConstant;
import glog.util.commonj.WorkManagerFactory;
import glog.util.event.Abortable;
import glog.util.event.EventQueue;
import glog.util.event.EventThreadIdentifier;
import glog.util.event.QueuedEvent;
import glog.util.exception.GLException;
import glog.util.j2ee.thread.ASResettableThreadLocalFactory;
import glog.util.j2ee.thread.ASWorkerThread;
import java.io.PrintWriter;
import java.util.List;

public class EventThread
implements Comparable,
Work {
    private Thread hostThread;
    private ASWorkerThread asWorkerThread;
    private static ThreadLocal eventThreadLocal = new ThreadLocal();
    protected EventThreadIdentifier identifier;
    protected State state;
    protected long since;
    protected EventQueue queue;
    protected Runnable runnable;
    private List localQueue;
    private transient QueuedEvent currentEvent;
    public static final State ABORTED = new State("ABORTED");
    public static final State INTERRUPTED = new State("INTERRUPTED");
    public static final State WAITING = new State("WAITING");
    public static final State ACTIVE = new State("ACTIVE");
    public static final State INITIALIZING = new State("INITIALIZING");

    public EventThread(EventQueue queue, Runnable runnable, String description, int index) {
        this.identifier = new EventThreadIdentifier(description, index);
        this.state = WAITING;
        this.since = System.currentTimeMillis();
        this.queue = queue;
        this.runnable = runnable;
        queue.addEventThread(this);
    }

    public String getName() {
        return this.identifier.toString();
    }

    public boolean isInterrupted() {
        return this.hostThread.isInterrupted() || this.state == INTERRUPTED;
    }

    public void interrupt() {
        this.setState(INTERRUPTED);
        this.hostThread.interrupt();
    }

    public void abort() {
        this.setState(ABORTED);
    }

    public void join(long millis) throws InterruptedException {
        this.hostThread.join(millis);
    }

    public boolean isAlive() {
        return this.hostThread.isAlive();
    }

    public void stop() {
        this.hostThread.stop();
    }

    public EventThread copy() {
        return new EventThread(this.queue, this.runnable, this.identifier.getDescription(), this.identifier.getIndex());
    }

    public boolean isDaemon() {
        return true;
    }

    public void release() {
    }

    public void run() {
        this.hostThread = Thread.currentThread();
        this.hostThread.setName(this.identifier.toString());
        if (this.hostThread instanceof ASWorkerThread) {
            this.asWorkerThread = (ASWorkerThread)((Object)this.hostThread);
        }
        eventThreadLocal.set(this);
        this.runnable.run();
    }

    public static EventThread getCurrentEventThread() {
        return (EventThread)eventThreadLocal.get();
    }

    public void start() {
        try {
            WorkManager wm = WorkManagerFactory.factory();
            wm.schedule((Work)this);
        }
        catch (Throwable t) {
            GLException.factory(t);
        }
    }

    public EventThreadIdentifier getIdentifier() {
        return this.identifier;
    }

    public State getState() {
        return this.state;
    }

    public String getStateString() {
        return this.getState().toString();
    }

    public void setState(State _state) {
        this.state = _state;
        this.since = System.currentTimeMillis();
    }

    public QueuedEvent getEvent() {
        return this.currentEvent;
    }

    public String getEventString() {
        return this.currentEvent != null ? this.currentEvent.toString() : null;
    }

    public long getEventSequence() {
        return this.currentEvent != null ? this.currentEvent.getFIFOSequence() : 0L;
    }

    public String getEventProcess() {
        return this.currentEvent != null ? this.currentEvent.getProcessId() : null;
    }

    public boolean isEventAbortable() {
        return this.currentEvent != null && this.currentEvent.getEvent() instanceof Abortable;
    }

    public void setEvent(QueuedEvent currentEvent) {
        this.currentEvent = currentEvent;
    }

    public LocalTimestamp getSince() {
        LocalTimestamp lt = new LocalTimestamp(this.since);
        lt.setTimeZone(LocalTimestamp.getDisplayTimeZone());
        return lt;
    }

    public long getSinceStamp() {
        return this.since;
    }

    public String getSinceString() {
        return this.getSince().toStringNoTZ();
    }

    public void diagnose(PrintWriter os, String tab) {
        os.println(tab + "Thread Id: " + this.identifier);
        tab = tab + tab;
        os.println(tab + "State: " + this.getState());
        os.println(tab + "Since: " + this.getSince());
        os.println(tab + "Event: " + this.getEvent());
    }

    public String toString() {
        return this.identifier.toString();
    }

    public int hashCode() {
        return this.identifier.hashCode();
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof EventThread)) {
            return false;
        }
        EventThread o = (EventThread)other;
        return this.identifier.equals(o.identifier);
    }

    public int compareTo(Object other) {
        if (this == other) {
            return 0;
        }
        if (!(other instanceof EventThread)) {
            return -1;
        }
        EventThread o = (EventThread)other;
        return this.identifier.compareTo(o.identifier);
    }

    public void resetThreadLocals() {
        if (this.asWorkerThread != null) {
            this.asWorkerThread.reset();
        } else {
            ASResettableThreadLocalFactory.resetAll();
        }
    }

    List getLocalQueue() {
        return this.localQueue;
    }

    public EventQueue getEventQueue() {
        return this.queue;
    }

    void setLocalQueue(List localQueue) {
        this.localQueue = localQueue;
    }

    public static class State
    extends TypedConstant {
        public State(String description) {
            super(description);
        }
    }
}
