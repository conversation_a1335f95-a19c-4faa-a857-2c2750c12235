/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.server.process.ProcessError;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.event.Event;
import glog.util.event.QueueSequence;
import glog.util.event.QueuedEventProcess;
import glog.util.exception.GLException;

public class QueuedEvent
implements Comparable {
    protected Event.Listener listener;
    protected Event event;
    protected long queueTime;
    protected long pollTime;
    protected long processTime;
    protected double wait;
    protected double execution;
    protected QueuedEventProcess process;
    protected QueueSequence eventSequence;
    protected long fifoSequence = ++staticFifoSequence;
    private static long staticFifoSequence = 0L;

    public QueuedEvent(Event.Listener l, Event e) {
        this.listener = l;
        this.event = e;
        this.eventSequence = e.getQueueSequence();
    }

    public QueuedEvent(QueuedEvent copy) {
        this.listener = copy.listener;
        this.event = copy.event;
        this.eventSequence = copy.eventSequence;
        this.fifoSequence = copy.fifoSequence;
        this.queueTime = copy.queueTime;
        this.pollTime = copy.pollTime;
        this.processTime = copy.processTime;
        this.wait = copy.wait;
        this.execution = copy.execution;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append(this.event);
        sb.append(" (").append(this.eventSequence).append('/').append(this.fifoSequence).append(')');
        return sb.toString();
    }

    public void open() {
        try {
            QueuedEventProcess queuedEventProcess = this.process = this.event.blockProcesses() ? new QueuedEventProcess(this.event) : null;
            if (this.process != null) {
                this.process.open();
            }
        }
        catch (GLException glex) {
            this.process = null;
        }
    }

    public void close() {
        if (this.process != null) {
            this.process.close();
        }
    }

    public void error(GLException glex) {
        if (this.process != null) {
            this.process.setError(new ProcessError(this.process, glex));
        }
    }

    public final void setQueueTime(long queueTime) {
        this.queueTime = queueTime;
    }

    public final long getQueueTime() {
        return this.queueTime;
    }

    public final String getQueueTimeString() {
        LocalTimestamp local = new LocalTimestamp(this.queueTime);
        local.setTimeZone(LocalTimestamp.getDisplayTimeZone());
        return local.toStringNoTZ();
    }

    public final void setPollTime(long pollTime) {
        this.pollTime = pollTime;
    }

    public final long getPollTime() {
        return this.pollTime;
    }

    public final void setProcessTime(long processTime) {
        this.processTime = processTime;
    }

    public final long getProcessTime() {
        return this.processTime;
    }

    public final void setWait(double wait) {
        this.wait = wait;
    }

    public final double getWait() {
        return this.wait;
    }

    public final void setExecution(double execution) {
        this.execution = execution;
    }

    public final double getExecution() {
        return this.execution;
    }

    public final String getEventDescription() {
        return this.event.toString();
    }

    public final QueueSequence getEventSequence() {
        return this.eventSequence;
    }

    public final String getEventSequenceDescription() {
        return String.valueOf(this.eventSequence);
    }

    public final void setEventSequence(QueueSequence eventSequence) {
        this.eventSequence = eventSequence;
    }

    public final long getFIFOSequence() {
        return this.fifoSequence;
    }

    public final void setFIFOSequence(long fifoSequence) {
        this.fifoSequence = fifoSequence;
    }

    public final Event.Listener getListener() {
        return this.listener;
    }

    public final Event getEvent() {
        return this.event;
    }

    public final String getProcessId() {
        return this.event.getProcessId();
    }

    public int compareTo(Object o) {
        if (this == o) {
            return 0;
        }
        QueuedEvent qeOther = (QueuedEvent)o;
        long result = Functions.compareTo(this.eventSequence, qeOther.eventSequence);
        if (result == 0L) {
            result = this.fifoSequence - qeOther.fifoSequence;
        }
        if (result == 0L && (result = (long)(System.identityHashCode(this) - System.identityHashCode(o))) == 0L) {
            result = 1L;
        }
        return result > 0L ? 1 : (result < 0L ? -1 : 0);
    }

    protected QueuedEvent() {
    }
}
