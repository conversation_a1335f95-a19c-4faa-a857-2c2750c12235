/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import java.io.Serializable;

public class QueueSequence
implements Comparable,
Serializable {
    private long queueSequenceNo;

    public QueueSequence(long queueSequenceNo) {
        this.queueSequenceNo = queueSequenceNo;
    }

    public QueueSequence(QueueSequence copy) {
        this.queueSequenceNo = copy.queueSequenceNo;
    }

    public String toString() {
        return String.valueOf(this.queueSequenceNo);
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof QueueSequence)) {
            return false;
        }
        QueueSequence other = (QueueSequence)o;
        return this.queueSequenceNo == other.queueSequenceNo;
    }

    public int hashCode() {
        return (int)this.queueSequenceNo;
    }

    public int compareTo(Object o) {
        if (this == o) {
            return 0;
        }
        if (!(o instanceof QueueSequence)) {
            return -1;
        }
        QueueSequence other = (QueueSequence)o;
        return (int)(this.queueSequenceNo - other.queueSequenceNo);
    }
}
