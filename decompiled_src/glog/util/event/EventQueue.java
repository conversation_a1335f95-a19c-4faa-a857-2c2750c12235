/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.regexp.RE
 */
package glog.util.event;

import glog.util.AvgStatistic;
import glog.util.DOMGenerator;
import glog.util.Functions;
import glog.util.GLProperties;
import glog.util.LocalTimestamp;
import glog.util.cache.StaticMap;
import glog.util.event.Abortable;
import glog.util.event.Event;
import glog.util.event.EventConstants;
import glog.util.event.EventIterator;
import glog.util.event.EventThread;
import glog.util.event.EventTimeoutMonitor;
import glog.util.event.QueuedEvent;
import glog.util.exception.GLException;
import glog.webserver.util.Reflector;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import org.apache.regexp.RE;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

public abstract class EventQueue
implements EventConstants {
    private Set threads = new TreeSet();
    private String name = null;
    protected static final String MEMORY_QUEUE = "Memory";
    protected static final String ORACLE_QUEUE = "Oracle";
    protected static final String DATA_QUEUE = "Data";
    protected static final String JMS_QUEUE = "Jms";
    protected static Map allEventQueues = new StaticMap("EventQueue.allEventQueues").get();
    protected static Reflector theReflector = new Reflector();
    protected static final String EVENTS_XML_MAJOR_VERSION = "5.5";
    protected static final String EVENTS_XML_MINOR_VERSION = "01";
    protected int threadCount = 1;
    protected int idleTime = 0;
    protected AvgStatistic queueSizeTracker;
    protected AvgStatistic waitTimeTracker;
    protected AvgStatistic processingTimeTracker;
    protected long totalThroughput = 0L;
    protected String maxProcessingEvent = null;
    protected static LocalTimestamp lastResetTime = EventQueue.now();
    protected boolean initialized = false;
    public static EventQueue theDefaultEventQueue;

    public EventQueue(String name, int threadCount) {
        this.name = name;
        this.threadCount = threadCount;
        this.initProperties();
        this.initStatistics();
        allEventQueues.put(name, this);
        GLProperties.addListener("glog.eventQueue.idleTime." + name, new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                EventQueue.this.initProperties();
            }
        });
    }

    protected EventQueue() {
        this.init();
    }

    public String toString() {
        return this.getName();
    }

    public String getName() {
        return this.name;
    }

    public void init() {
        if (!this.initialized) {
            for (int i = 0; i < this.threadCount; ++i) {
                EventThread thread = this.getEventThread(i + 1);
                thread.start();
            }
            this.initialized = true;
            EventTimeoutMonitor.reset();
        }
    }

    public void addEventThread(EventThread thread) {
        this.threads.add(thread);
    }

    public void removeEventThread(EventThread thread) {
        this.threads.remove(thread);
    }

    public synchronized void cleanup() {
        Iterator it = this.threads.iterator();
        while (it.hasNext()) {
            EventThread thread = (EventThread)it.next();
            thread.interrupt();
            it.remove();
        }
        allEventQueues.remove(this.getName());
        EventTimeoutMonitor.reset();
    }

    public boolean isInThreadGroup() {
        return this.threads.contains(Thread.currentThread());
    }

    public abstract String getType();

    public String getDescription() {
        return this.toString();
    }

    public int getThreadCount() {
        return this.threadCount;
    }

    public int getIdleTime() {
        return this.idleTime;
    }

    public EventThread[] getThreads() {
        return Functions.toArray(this.threads, EventThread.class);
    }

    public synchronized void increase() {
        EventThread[] eventThreads = this.getThreads();
        int index = 0;
        for (int i = 0; i < eventThreads.length; ++i) {
            index = Math.max(index, eventThreads[i].getIdentifier().getIndex());
        }
        this.getEventThread(index + 1).start();
        ++this.threadCount;
    }

    public synchronized void kill(int threadGid) {
        EventThread[] eventThreads = this.getThreads();
        for (int i = 0; i < eventThreads.length; ++i) {
            if (eventThreads[i].getIdentifier().getGid() != threadGid) continue;
            eventThreads[i].interrupt();
            this.threads.remove(eventThreads[i]);
            break;
        }
    }

    public synchronized void interrupt(int threadGid) {
        EventThread[] eventThreads = this.getThreads();
        for (int i = 0; i < eventThreads.length; ++i) {
            if (eventThreads[i].getIdentifier().getGid() != threadGid) continue;
            eventThreads[i].copy().start();
            eventThreads[i].interrupt();
            this.threads.remove(eventThreads[i]);
            --this.threadCount;
            break;
        }
    }

    public synchronized void setThreadCount(int threadCount) {
        block3: {
            EventThread[] eventThreads;
            block2: {
                eventThreads = this.getThreads();
                if (threadCount >= eventThreads.length) break block2;
                for (int i = eventThreads.length - 1; i >= threadCount; --i) {
                    eventThreads[i].interrupt();
                    this.threads.remove(eventThreads[i]);
                }
                break block3;
            }
            if (threadCount <= eventThreads.length) break block3;
            for (int i = eventThreads.length; i < threadCount; ++i) {
                this.increase();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public synchronized void preempt(int fifoSequence) throws GLException {
        QueuedEvent queuedEvent = null;
        try (EventIterator it = this.iterator();){
            while (it.hasNext() && (queuedEvent = (QueuedEvent)it.next()).getFIFOSequence() != (long)fifoSequence) {
            }
            if (queuedEvent != null) {
                this.preempt(queuedEvent);
            }
        }
    }

    public synchronized void abort(int threadGid, int fifoSequence) throws GLException {
        EventThread[] eventThreads = this.getThreads();
        for (int i = 0; i < eventThreads.length; ++i) {
            Event event;
            if (eventThreads[i].getIdentifier().getGid() != threadGid) continue;
            QueuedEvent queuedEvent = eventThreads[i].getEvent();
            if (queuedEvent == null || queuedEvent.getFIFOSequence() != (long)fifoSequence || !((event = queuedEvent.getEvent()) instanceof Abortable)) break;
            eventThreads[i].abort();
            Abortable abort = (Abortable)((Object)event);
            abort.abortIt();
            break;
        }
    }

    public synchronized boolean holdsProcess(String processPid) {
        EventThread[] eventThreads = this.getThreads();
        for (int i = 0; i < eventThreads.length; ++i) {
            String pid;
            Event event;
            QueuedEvent queuedEvent = eventThreads[i].getEvent();
            if (queuedEvent == null || (event = queuedEvent.getEvent()) == null || (pid = event.getProcessPid()) == null) continue;
            return pid.equals(processPid);
        }
        return false;
    }

    public abstract void add(QueuedEvent var1);

    public abstract void remove(Object var1);

    public abstract void preempt(QueuedEvent var1) throws GLException;

    public abstract EventIterator iterator();

    public abstract long size();

    protected abstract EventThread getEventThread(int var1);

    public AvgStatistic trackQueueSize() {
        return this.queueSizeTracker;
    }

    public AvgStatistic trackWaitTime() {
        return this.waitTimeTracker;
    }

    public AvgStatistic trackProcessingTime() {
        return this.processingTimeTracker;
    }

    public long getTotalThroughput() {
        return this.totalThroughput;
    }

    public String getMaxProcessingEvent() {
        return this.maxProcessingEvent;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public synchronized void diagnose(PrintWriter os, String tab, String processGrep, int flags, int maxEventsPerQueue) {
        if (!this.checkFlags(flags)) {
            return;
        }
        if (!this.checkProcess(processGrep)) {
            return;
        }
        os.println(tab + "Queue: " + this.getName());
        tab = tab + tab;
        os.println(tab + "Current Queue Size: " + this.size());
        os.println(tab + "Total Throughput: " + this.totalThroughput);
        os.println(tab + this.trackQueueSize());
        os.println(tab + this.trackWaitTime());
        os.println(tab + this.trackProcessingTime());
        os.println(tab + "Threads:");
        if ((flags & 2) != 0) {
            EventThread[] threads = this.getThreads();
            for (int i = 0; i < threads.length; ++i) {
                threads[i].diagnose(os, tab + tab);
            }
        }
        if ((flags & 1) != 0) {
            int count = 1;
            os.println(tab + "Max Processing Event: " + this.maxProcessingEvent);
            os.println(tab + "Events:");
            try (EventIterator it = this.iterator();){
                while (it.hasNext()) {
                    if (count++ > maxEventsPerQueue) {
                        os.println(tab + tab + "...");
                        break;
                    }
                    os.println(tab + tab + it.next());
                }
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public synchronized void diagnoseXML(Document d, Element topLevelElem, String processGrep, int flags, int maxEventsPerQueue) throws GLException {
        if (!this.checkFlags(flags)) {
            return;
        }
        if (!this.checkProcess(processGrep)) {
            return;
        }
        Element queueElem = theReflector.generate(d, this);
        topLevelElem.appendChild(queueElem);
        if ((flags & 2) != 0) {
            Element threadsElem = d.createElement("threads");
            queueElem.appendChild(threadsElem);
            EventThread[] threads = this.getThreads();
            for (int i = 0; i < threads.length; ++i) {
                threadsElem.appendChild(theReflector.generate(d, threads[i]));
            }
        }
        if ((flags & 1) != 0) {
            Element eventsElem = d.createElement("events");
            queueElem.appendChild(eventsElem);
            int count = 1;
            try (EventIterator it = this.iterator();){
                while (it.hasNext()) {
                    if (count++ > maxEventsPerQueue) {
                        eventsElem.setAttribute("has_more", "true");
                        break;
                    }
                    eventsElem.appendChild(theReflector.generate(d, it.next()));
                }
            }
        }
    }

    public void resetStatistics() throws GLException {
        this.initStatistics();
        this.totalThroughput = 0L;
        this.maxProcessingEvent = null;
        lastResetTime = EventQueue.now();
    }

    public static LocalTimestamp getLastResetTime() {
        return lastResetTime;
    }

    public void trackQueue(long size) {
        this.queueSizeTracker.cumulate(size);
    }

    public void trackWait(QueuedEvent queuedEvent) {
        this.trackWait(queuedEvent, System.currentTimeMillis());
    }

    public void trackProcess(QueuedEvent queuedEvent) {
        this.trackProcess(queuedEvent, System.currentTimeMillis());
    }

    protected void trackWait(QueuedEvent queuedEvent, long now) {
        long waitingSince = queuedEvent.getQueueTime();
        double wait = (double)(now - waitingSince) / 1000.0;
        queuedEvent.setWait(wait);
        this.waitTimeTracker.cumulate(wait);
        queuedEvent.setProcessTime(now);
    }

    protected void trackProcess(QueuedEvent queuedEvent, long now) {
        long processingSince = queuedEvent.getProcessTime();
        double execution = (double)(now - processingSince) / 1000.0;
        ++this.totalThroughput;
        if (execution > this.processingTimeTracker.getMaximum()) {
            this.maxProcessingEvent = queuedEvent.getEvent().toString();
        }
        this.processingTimeTracker.cumulate(execution);
        queuedEvent.setExecution(execution);
    }

    protected void trackBatchPoll(QueuedEvent queuedEvent, long now) {
        queuedEvent.setPollTime(now);
    }

    protected void trackBatchPoll(Collection queuedEvents, long now) {
        for (QueuedEvent queuedEvent : queuedEvents) {
            queuedEvent.setWait((double)(now - queuedEvent.getQueueTime()) / 1000.0);
            queuedEvent.setPollTime(now);
        }
    }

    protected void trackBatchProcessing(QueuedEvent queuedEvent, long now) {
        double wait = (double)(now - queuedEvent.getQueueTime()) / 1000.0;
        this.waitTimeTracker.cumulate(wait);
        queuedEvent.setWait(wait);
        queuedEvent.setProcessTime(now);
    }

    protected void trackBatchProcessing(Collection queuedEvents, long now) {
    }

    protected void trackBatchProcessed(QueuedEvent queuedEvent, long now) {
        this.trackProcess(queuedEvent, now);
    }

    protected double trackBatchProcessed(Collection queuedEvents, long now) {
        int size = queuedEvents.size();
        if (size == 0) {
            return 0.0;
        }
        QueuedEvent event = (QueuedEvent)queuedEvents.iterator().next();
        double totalProcessingTime = (double)(now - event.getPollTime()) / 1000.0;
        double averageWaitTime = totalProcessingTime * (double)(size - 1) / (double)(size * 2);
        double averageProcessingTime = totalProcessingTime / (double)size;
        this.totalThroughput += (long)size;
        if (averageProcessingTime > this.processingTimeTracker.getMaximum()) {
            this.maxProcessingEvent = event.toString();
        }
        for (QueuedEvent queuedEvent : queuedEvents) {
            double wait = queuedEvent.getWait() + averageWaitTime;
            queuedEvent.setWait(wait);
            queuedEvent.setExecution(averageProcessingTime);
            this.waitTimeTracker.cumulate(wait);
            this.processingTimeTracker.cumulate(averageProcessingTime);
        }
        return averageWaitTime;
    }

    private void initProperties() {
        String strIdleTime = GLProperties.get().getProperty("glog.eventQueue.idleTime." + this.getName());
        this.idleTime = strIdleTime != null ? Integer.parseInt(strIdleTime) * 1000 : 0;
    }

    private void initStatistics() {
        this.queueSizeTracker = new AvgStatistic("Queue Size", 2);
        this.waitTimeTracker = new AvgStatistic("Wait Time", 2);
        this.processingTimeTracker = new AvgStatistic("Processing Time", 2);
    }

    private boolean checkFlags(int flags) {
        String type = this.getType();
        if (MEMORY_QUEUE.equals(type) && (flags & 4) == 0) {
            return false;
        }
        if (DATA_QUEUE.equals(type) && (flags & 8) == 0) {
            return false;
        }
        if (ORACLE_QUEUE.equals(type) && (flags & 0x10) == 0) {
            return false;
        }
        if (JMS_QUEUE.equals(type) && (flags & 0x40) == 0) {
            return false;
        }
        if ((flags & 0x20) == 0) {
            return true;
        }
        EventThread[] threads = this.getThreads();
        for (int i = 0; i < threads.length; ++i) {
            if (threads[i].getEvent() == null) continue;
            return true;
        }
        return this.iterator().hasNext();
    }

    private boolean checkProcess(String processGrep) {
        if (processGrep == null) {
            return true;
        }
        try {
            QueuedEvent queuedEvent;
            RE processRE = new RE(processGrep);
            EventThread[] threads = this.getThreads();
            for (int i = 0; i < threads.length; ++i) {
                queuedEvent = threads[i].getEvent();
                if (queuedEvent == null || queuedEvent.getProcessId() == null || !processRE.match(queuedEvent.getProcessId())) continue;
                return true;
            }
            EventIterator it = this.iterator();
            while (it.hasNext()) {
                queuedEvent = (QueuedEvent)it.next();
                if (queuedEvent.getProcessId() == null || !processRE.match(queuedEvent.getProcessId())) continue;
                return true;
            }
            return false;
        }
        catch (Throwable t) {
            GLException.factory(t);
            return false;
        }
    }

    private static LocalTimestamp now() {
        LocalTimestamp result = new LocalTimestamp();
        result.setTimeZone(LocalTimestamp.getDisplayTimeZone());
        return result;
    }

    public static Map getAllEventQueues() {
        return allEventQueues;
    }

    public static void cleanupAllEventQueues() {
        for (EventQueue queue : EventQueue.getAllEventQueues().values()) {
            queue.cleanup();
        }
        theDefaultEventQueue.cleanup();
    }

    public static EventQueue findProcess(String processPid) {
        for (EventQueue queue : EventQueue.getAllEventQueues().values()) {
            if (!queue.holdsProcess(processPid)) continue;
            return queue;
        }
        return null;
    }

    public static void diagnose(PrintWriter pw, boolean reset, int flags, int maxEventsPerQueue) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(null)) {
            pw.println(queue.getName() + " Statistics");
            queue.diagnose(pw, "\t", null, flags, maxEventsPerQueue);
            if (!reset) continue;
            queue.resetStatistics();
        }
    }

    public static String diagnose(String queueGrep, String processGrep, int flags, int maxEventsPerQueue) throws GLException {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        pw.println("ServerTime: " + EventQueue.now());
        pw.println("Version: 5.5.01");
        pw.println("Last Reset: " + lastResetTime);
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.diagnose(pw, "\t", processGrep, flags, maxEventsPerQueue);
        }
        return sw.toString();
    }

    public static void diagnoseXML(String queueGrep, String processGrep, Document d, int flags, int maxEventsPerQueue) throws GLException {
        Element topLevelElement = d.createElement("diagnostics");
        d.appendChild(topLevelElement);
        DOMGenerator.insertTypeUnit(d, "server_time", topLevelElement, EventQueue.now());
        DOMGenerator.insertTypeUnit(d, "last_reset_time", topLevelElement, lastResetTime);
        Node versionElement = DOMGenerator.insertElement(d, "version", topLevelElement);
        DOMGenerator.insertTagValue(d, "major", versionElement, EVENTS_XML_MAJOR_VERSION);
        DOMGenerator.insertTagValue(d, "minor", versionElement, EVENTS_XML_MINOR_VERSION);
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.diagnoseXML(d, topLevelElement, processGrep, flags, maxEventsPerQueue);
        }
    }

    public static void resetStatistics(String queueGrep) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.resetStatistics();
        }
    }

    public static void increase(String queueGrep) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.increase();
        }
    }

    public static void kill(String queueGrep, int thread) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.kill(thread);
        }
    }

    public static void preempt(String queueGrep, int sequence) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.preempt(sequence);
        }
    }

    public static void abort(String queueGrep, int thread, int sequence) throws GLException {
        for (EventQueue queue : EventQueue.getQueues(queueGrep)) {
            queue.abort(thread, sequence);
        }
    }

    private static Collection getQueues(String queueGrep) throws GLException {
        try {
            Map allQueues = EventQueue.getAllEventQueues();
            if (queueGrep == null) {
                return allQueues.values();
            }
            RE queueRE = new RE(queueGrep);
            LinkedList<EventQueue> queues = new LinkedList<EventQueue>();
            for (Map.Entry entry : EventQueue.getAllEventQueues().entrySet()) {
                String queueName = (String)entry.getKey();
                EventQueue queue = (EventQueue)entry.getValue();
                if (!queueRE.match(queueName)) continue;
                queues.add(queue);
            }
            return queues;
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }
}
