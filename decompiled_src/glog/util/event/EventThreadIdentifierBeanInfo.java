/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventThreadIdentifier;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class EventThreadIdentifierBeanInfo
extends SimpleBeanInfo {
    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("description", EventThreadIdentifier.class, "getDescription", null), new PropertyDescriptor("index", EventThreadIdentifier.class, "getIndex", null), new PropertyDescriptor("gid", EventThreadIdentifier.class, "getGid", null)};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
