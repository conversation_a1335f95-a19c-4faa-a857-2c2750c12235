/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.QueuedEvent;
import java.beans.BeanDescriptor;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class QueuedEventBeanInfo
extends SimpleBeanInfo {
    private static BeanDescriptor theQueuedEventBeanDescriptor = new BeanDescriptor(QueuedEvent.class);

    @Override
    public BeanDescriptor getBeanDescriptor() {
        return theQueuedEventBeanDescriptor;
    }

    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("event", QueuedEvent.class, "getEventDescription", null), new PropertyDescriptor("eventSequence", QueuedEvent.class, "getEventSequenceDescription", null), new PropertyDescriptor("fifoSequence", QueuedEvent.class, "getFIFOSequence", null), new PropertyDescriptor("queueTime", QueuedEvent.class, "getQueueTimeString", null), new PropertyDescriptor("processId", QueuedEvent.class, "getProcessId", null)};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
