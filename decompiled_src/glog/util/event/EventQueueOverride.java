/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventQueue;

public class EventQueueOverride {
    private static ThreadLocal overrideEventQueueForThread = new ThreadLocal();

    public static EventQueue get() {
        return (EventQueue)overrideEventQueueForThread.get();
    }

    public static void set(EventQueue eventQueue) {
        overrideEventQueueForThread.set(eventQueue);
    }
}
