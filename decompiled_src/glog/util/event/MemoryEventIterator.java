/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventIterator;
import java.util.Iterator;

public class MemoryEventIterator
implements EventIterator {
    private Iterator it;

    public MemoryEventIterator(Iterator it) {
        this.it = it;
    }

    @Override
    public boolean hasNext() {
        return this.it.hasNext();
    }

    public Object next() {
        return this.it.next();
    }

    @Override
    public void remove() {
        this.it.remove();
    }

    @Override
    public void close() {
    }
}
