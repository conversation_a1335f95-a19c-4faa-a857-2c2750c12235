/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.GLProperties;
import glog.util.event.Event;
import glog.util.event.EventParameterization;
import glog.util.event.EventSpecification;
import glog.util.event.UnexpectedEventException;
import glog.util.event.UnexpectedListenerException;
import glog.util.exception.GLException;
import glog.util.log.Log;
import glog.util.log.LogIds;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class EventHandler {
    Map listenerMap = concurrentListeners ? new ConcurrentHashMap() : new HashMap();
    Class eventClass;
    Class listenerClass;
    private static boolean fairLocks = GLProperties.get().getBooleanProperty("glog.eventHandler.fairLocks", false);
    private static boolean concurrentListeners = GLProperties.get().getBooleanProperty("glog.eventHandler.concurrentListeners", false);
    protected ReentrantReadWriteLock rwLock = concurrentListeners ? null : new ReentrantReadWriteLock(fairLocks);

    public EventHandler() {
        this.eventClass = Event.class;
        this.listenerClass = Event.Listener.class;
    }

    public EventHandler(Class eventClass, Class listenerClass) {
        this.eventClass = eventClass;
        this.listenerClass = listenerClass;
    }

    public void raiseEvent(Event event, Event.Context transientContext) throws UnexpectedEventException {
        this.checkEvent(event);
        try {
            this.triggerEvent(event, transientContext, false);
        }
        catch (GLException gLException) {
            // empty catch block
        }
    }

    public void raiseEvent(Event event) throws UnexpectedEventException {
        this.raiseEvent(event, null);
    }

    public void cleanup() throws GLException {
        this.listenerMap.clear();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void addListener(Event.Listener l, EventSpecification spec) throws UnexpectedListenerException {
        if (this.rwLock != null) {
            this.rwLock.writeLock().lock();
        }
        try {
            Set<Object> listeners = (Set<Object>)this.listenerMap.get(spec);
            if (listeners == null) {
                listeners = concurrentListeners ? Collections.newSetFromMap(new ConcurrentHashMap()) : new HashSet();
                this.listenerMap.put(spec, listeners);
            }
            listeners.remove(l);
            listeners.add(l);
        }
        finally {
            if (this.rwLock != null) {
                this.rwLock.writeLock().unlock();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void removeListener(Event.Listener l, EventSpecification spec) {
        if (this.rwLock != null) {
            this.rwLock.writeLock().lock();
        }
        try {
            Set listeners = (Set)this.listenerMap.get(spec);
            if (listeners != null) {
                listeners.remove(l);
                if (listeners.size() == 0) {
                    this.listenerMap.remove(spec);
                }
            }
        }
        finally {
            if (this.rwLock != null) {
                this.rwLock.writeLock().unlock();
            }
        }
    }

    public Collection getClassListeners(EventSpecification e) {
        return (Collection)this.listenerMap.get(e);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Collection getAllListeners(Event e) {
        if (this.rwLock != null) {
            this.rwLock.readLock().lock();
        }
        try {
            HashSet listeners = new HashSet();
            EventSpecification copy = e.getSpecification().copy();
            this.getAllListenersRecurse(listeners, copy);
            HashSet<Event.Listener> undecomposedSet = new HashSet<Event.Listener>();
            for (Event.Listener listener : listeners) {
                if (listener instanceof Event.DecomposedListener) {
                    Event.Listener undecomposed = ((Event.DecomposedListener)listener).getUndecomposed();
                    undecomposedSet.add(undecomposed);
                    continue;
                }
                undecomposedSet.add(listener);
            }
            HashSet<Event.Listener> hashSet = undecomposedSet;
            return hashSet;
        }
        finally {
            if (this.rwLock != null) {
                this.rwLock.readLock().unlock();
            }
        }
    }

    private void getSpecificListeners(Collection listeners, EventSpecification copy) {
        Collection specificListeners = (Collection)this.listenerMap.get(copy);
        if (specificListeners != null) {
            listeners.addAll(specificListeners);
        }
    }

    private void getAllListenersRecurse(Collection listeners, EventSpecification copy) {
        Class eventClass = copy.getEventClass();
        if (eventClass != null && eventClass != Object.class) {
            this.getSpecificListeners(listeners, copy);
            EventParameterization eventParameterization = copy.getEventParameterization();
            if (eventParameterization != null) {
                Iterator it = eventParameterization.parameterizations();
                while (it.hasNext()) {
                    copy.setEventParameterization((EventParameterization)it.next());
                    this.getSpecificListeners(listeners, copy);
                }
                Collection generalParams = eventParameterization.getGeneralParams();
                if (generalParams != null) {
                    for (EventParameterization generalParam : generalParams) {
                        Iterator it2 = generalParam.parameterizations();
                        while (it2.hasNext()) {
                            copy.setEventParameterization((EventParameterization)it2.next());
                            this.getSpecificListeners(listeners, copy);
                        }
                    }
                }
                copy.setEventParameterization(null);
                this.getSpecificListeners(listeners, copy);
                copy.setEventParameterization(eventParameterization);
            }
        }
    }

    protected void triggerEvent(Event event, Event.Context transientContext, boolean rethrow) throws GLException {
        for (Event.Listener listener : this.getAllListeners(event)) {
            try {
                listener.trigger(event, transientContext);
            }
            catch (Throwable t) {
                EventHandler.logEventListenerError(event, listener, t, rethrow);
            }
        }
    }

    protected static void logEventListenerError(Event event, Event.Listener listener, Throwable t, boolean rethrow) throws GLException {
        if (Log.idOn[LogIds.PROCESS.index]) {
            Log.logID(LogIds.PROCESS, "Listener <{0}> failure on <{1}>:", listener, event);
            if (!(t instanceof GLException)) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                t.printStackTrace(pw);
                Log.logID(LogIds.PROCESS, sw.toString());
                t = GLException.factory(t);
            }
        }
        if (rethrow) {
            throw (GLException)t;
        }
    }

    protected void checkEvent(Event event) throws UnexpectedEventException {
        if (!this.eventClass.isInstance(event)) {
            throw new UnexpectedEventException(this, event);
        }
    }

    protected void checkListener(Event.Listener listener) throws UnexpectedListenerException {
        if (!this.listenerClass.isInstance(listener)) {
            throw new UnexpectedListenerException(this, listener);
        }
    }
}
