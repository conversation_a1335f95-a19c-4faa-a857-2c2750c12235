/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.ContinuousEventQueue;
import glog.util.event.Event;
import glog.util.event.EventHandler;
import glog.util.event.EventQueueOverride;
import glog.util.event.EventThread;
import glog.util.event.MemoryEventQueueRunnable;
import glog.util.event.QueuedEvent;
import glog.util.exception.GLException;

public class ContinuousEventQueueRunnable
extends MemoryEventQueueRunnable {
    @Override
    protected boolean processEvent(QueuedEvent queuedEvent) throws GLException {
        EventThread thread = EventThread.getCurrentEventThread();
        ContinuousEventQueue queue = (ContinuousEventQueue)thread.getEventQueue();
        EventQueueOverride.set(queue);
        queue.addLocal(queuedEvent);
        while (!queue.isEmptyLocal()) {
            QueuedEvent localEvent = queue.getFirstLocal();
            queue.removeFirstLocal();
            Event.Listener listener = localEvent.getListener();
            Event event = localEvent.getEvent();
            try {
                thread.resetThreadLocals();
                listener.trigger(event, null);
                if (!thread.isInterrupted()) continue;
                queue.clearLocal();
                return true;
            }
            catch (Throwable t) {
                try {
                    EventHandler.logEventListenerError(event, listener, t, false);
                }
                catch (Throwable t2) {}
            }
        }
        return false;
    }
}
