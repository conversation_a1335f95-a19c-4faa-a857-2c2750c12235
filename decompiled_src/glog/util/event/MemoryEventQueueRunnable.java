/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.database.security.SubjectUtil;
import glog.util.event.Event;
import glog.util.event.EventHandler;
import glog.util.event.EventQueueRunnable;
import glog.util.event.EventThread;
import glog.util.event.MemoryEventQueue;
import glog.util.event.QueuedEvent;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.remote.NamingDirectory;

public class MemoryEventQueueRunnable
extends EventQueueRunnable {
    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public void run() {
        this.waitUntilReady();
        EventThread thread = EventThread.getCurrentEventThread();
        thread.setState(EventThread.ACTIVE);
        MemoryEventQueue queue = (MemoryEventQueue)thread.getEventQueue();
        try {
            NamingDirectory.pushAppServerContext();
            boolean canceled = false;
            boolean lastIdleHadActivity = false;
            while (!canceled) {
                QueuedEvent queuedEvent;
                if (NamingDirectory.isAppServer() && !SubjectUtil.isAppServerReady()) {
                    try {
                        Thread.sleep(500L);
                    }
                    catch (InterruptedException ie) {
                        canceled = true;
                    }
                    continue;
                }
                thread.resetThreadLocals();
                MemoryEventQueue ie = queue;
                synchronized (ie) {
                    if (queue.isEmpty()) {
                        int idleTime = queue.getIdleTime();
                        try {
                            thread.setState(EventThread.WAITING);
                            if (idleTime == 0) {
                                queue.wait();
                            } else {
                                queue.wait(idleTime);
                            }
                        }
                        catch (InterruptedException e) {
                            canceled = true;
                        }
                        if (queue.isEmpty()) {
                            if (idleTime != 0 && lastIdleHadActivity) {
                                lastIdleHadActivity = false;
                                EventThread replacement = thread.copy();
                                replacement.start();
                                return;
                            }
                            continue;
                        }
                        if (queue.isEmpty()) {
                            continue;
                        }
                    }
                    if (canceled |= thread.isInterrupted()) {
                        continue;
                    }
                    queuedEvent = queue.getFirst();
                    queue.removeFirst();
                    queue.trackWait(queuedEvent);
                }
                thread.setState(EventThread.ACTIVE);
                thread.setEvent(queuedEvent);
                Event.Listener listener = queuedEvent.getListener();
                Event event = queuedEvent.getEvent();
                try {
                    canceled |= this.processEvent(queuedEvent);
                }
                catch (ThreadDeath td) {
                    throw td;
                }
                catch (Throwable t) {
                    try {
                        GLException glex = GLException.factory(t);
                        queuedEvent.error(glex);
                        EventHandler.logEventListenerError(event, listener, glex, false);
                    }
                    catch (Throwable t2) {
                        // empty catch block
                    }
                }
                finally {
                    thread.setEvent(null);
                    queue.trackProcess(queuedEvent);
                    queuedEvent.close();
                }
                queuedEvent = null;
                event = null;
                listener = null;
            }
            return;
        }
        catch (ThreadDeath td) {
            throw td;
        }
        catch (Throwable t) {
            GLException.factory((Cause)new GLException.CausedBy("cause.Queue_Error", null, new Object[][]{{"queue", queue}}), t);
            if (queue == null) return;
            queue.clear();
            return;
        }
        finally {
            NamingDirectory.popAppServerContext();
        }
    }

    protected boolean processEvent(QueuedEvent queuedEvent) throws GLException {
        Event.Listener listener = queuedEvent.getListener();
        Event event = queuedEvent.getEvent();
        listener.trigger(event, null);
        return EventThread.getCurrentEventThread().isInterrupted();
    }
}
