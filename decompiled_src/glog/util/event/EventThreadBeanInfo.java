/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventThread;
import java.beans.BeanDescriptor;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class EventThreadBeanInfo
extends SimpleBeanInfo {
    private static BeanDescriptor theThreadBeanDescriptor = new BeanDescriptor(EventThread.class);

    @Override
    public BeanDescriptor getBeanDescriptor() {
        return theThreadBeanDescriptor;
    }

    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("identifier", EventThread.class, "getIdentifier", null), new PropertyDescriptor("currentState", EventThread.class, "getStateString", null), new PropertyDescriptor("currentEvent", EventThread.class, "getEventString", null), new PropertyDescriptor("currentProcess", EventThread.class, "getEventProcess", null), new PropertyDescriptor("currentSeq", EventThread.class, "getEventSequence", null), new PropertyDescriptor("abortable", EventThread.class, "isEventAbortable", null), new PropertyDescriptor("since", EventThread.class, "getSinceString", null)};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
