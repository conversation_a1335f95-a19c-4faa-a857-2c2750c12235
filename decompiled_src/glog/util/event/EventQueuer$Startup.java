/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.StartupShutdown;
import glog.util.event.EventQueuer;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;

public static class EventQueuer.Startup
implements StartupShutdown {
    @Override
    public void load(T2SharedConnection conn) throws GLException {
    }

    @Override
    public void activate(T2SharedConnection conn) throws GLException {
    }

    @Override
    public void unload() throws GLException {
        for (EventQueuer queuer : eventQueuerList) {
            queuer.cleanup();
        }
    }
}
