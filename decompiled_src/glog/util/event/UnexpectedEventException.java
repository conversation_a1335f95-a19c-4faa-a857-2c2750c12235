/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.Event;
import glog.util.event.EventHandler;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.exception.GLExceptionProd;

public class UnexpectedEventException
extends GLExceptionProd {
    UnexpectedEventException(EventHandler handler, Event event) {
        super(new Cause("cause.eventUnexpected", null, new Object[][]{{"expected", handler.eventClass.getName()}, {"actual", event.getClass().getName()}}), null);
    }

    public UnexpectedEventException(GLException ex) {
        super(ex);
    }
}
