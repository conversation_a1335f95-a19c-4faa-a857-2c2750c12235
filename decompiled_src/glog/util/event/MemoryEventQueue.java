/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventIterator;
import glog.util.event.EventQueue;
import glog.util.event.EventThread;
import glog.util.event.MemoryEventIterator;
import glog.util.event.MemoryEventQueueRunnable;
import glog.util.event.QueuedEvent;
import java.util.TreeSet;

public class MemoryEventQueue
extends EventQueue {
    private TreeSet queue = new TreeSet();

    public MemoryEventQueue(String name, int threadCount) {
        super(name, threadCount);
    }

    protected MemoryEventQueue() {
    }

    @Override
    public String getType() {
        return "Memory";
    }

    @Override
    public void add(QueuedEvent queuedEvent) {
        long now = System.currentTimeMillis();
        queuedEvent.setQueueTime(now);
        this.queue.add(queuedEvent);
        this.trackQueue(this.queue.size());
    }

    @Override
    public synchronized void remove(Object event) {
        EventIterator it = this.iterator();
        while (it.hasNext()) {
            QueuedEvent queuedEvent = (QueuedEvent)it.next();
            if (event != queuedEvent.getEvent()) continue;
            it.remove();
        }
    }

    @Override
    public synchronized void preempt(QueuedEvent withEvent) {
        QueuedEvent firstEvent;
        this.queue.remove(withEvent);
        long fifo = -1L;
        if (!this.queue.isEmpty() && (firstEvent = (QueuedEvent)this.queue.first()).getEventSequence() == null) {
            fifo = firstEvent.getFIFOSequence() - 1L;
        }
        withEvent.setFIFOSequence(fifo);
        withEvent.setEventSequence(null);
        this.queue.add(withEvent);
    }

    @Override
    public EventIterator iterator() {
        return new MemoryEventIterator(this.queue.iterator());
    }

    @Override
    public long size() {
        return this.queue.size();
    }

    public void clear() {
        this.queue.clear();
    }

    public boolean isEmpty() {
        return this.queue.isEmpty();
    }

    public QueuedEvent getFirst() {
        return (QueuedEvent)this.queue.first();
    }

    public void removeFirst() {
        this.queue.remove(this.queue.first());
    }

    @Override
    public synchronized boolean holdsProcess(String processPid) {
        if (super.holdsProcess(processPid)) {
            return true;
        }
        EventIterator it = this.iterator();
        while (it.hasNext()) {
            QueuedEvent queuedEvent = (QueuedEvent)it.next();
            if (!queuedEvent.getEvent().getProcessPid().equals(processPid)) continue;
            return true;
        }
        return false;
    }

    @Override
    protected EventThread getEventThread(int index) {
        return new EventThread(this, new MemoryEventQueueRunnable(), this.getName(), index);
    }

    static {
        theDefaultEventQueue = new MemoryEventQueue();
    }
}
