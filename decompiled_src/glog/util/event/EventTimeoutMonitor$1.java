/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.GLProperties;
import glog.util.event.EventTimeoutMonitor;
import glog.util.exception.GLException;

static final class EventTimeoutMonitor.1
extends GLProperties.Listener {
    EventTimeoutMonitor.1() {
    }

    @Override
    public void onPropertyChange(GLProperties.Change change) throws GLException {
        theEventTimeoutMonitor;
        EventTimeoutMonitor.reset();
    }
}
