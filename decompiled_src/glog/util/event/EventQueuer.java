/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.StartupShutdown;
import glog.util.cache.StaticList;
import glog.util.event.Event;
import glog.util.event.EventHandler;
import glog.util.event.EventQueue;
import glog.util.event.EventQueueOverride;
import glog.util.event.QueuedEvent;
import glog.util.event.UnexpectedEventException;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

public class EventQueuer
extends EventHandler {
    protected Collection eventQueues = new LinkedList();
    private static List eventQueuerList = new StaticList("EventQueuer.eventQueuerList").get();
    public static final EventQueuer theDefaultEventQueuer = new EventQueuer(null);

    public EventQueuer(Collection queues) {
        this.init(queues);
    }

    public EventQueuer(Class eventClass, Class listenerClass, Collection queues) {
        super(eventClass, listenerClass);
        eventQueuerList.add(this);
        this.init(queues);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void raiseEvent(Event event) throws UnexpectedEventException {
        this.checkEvent(event);
        EventQueue override = EventQueueOverride.get();
        EventQueue queue = null;
        for (Event.Listener listener : this.getAllListeners(event)) {
            QueuedEvent queuedEvent = new QueuedEvent(listener, event);
            queuedEvent.open();
            try {
                if (override != null) {
                    queue = override;
                } else if (listener instanceof Event.ListenerWithQueueAssigment) {
                    queue = ((Event.ListenerWithQueueAssigment)listener).getQueue();
                }
                if (queue == null) {
                    queue = event.getQueue();
                }
                EventQueue eventQueue = queue;
                synchronized (eventQueue) {
                    queue.add(queuedEvent);
                    queue.notify();
                }
            }
            catch (Throwable t) {
                queuedEvent.close();
            }
        }
    }

    public void removeEvent(Event event) {
        EventQueue queue = event.getQueue();
        queue.remove(event);
    }

    public void triggerEvent(Event event) throws GLException {
        super.triggerEvent(event, null, true);
    }

    @Override
    public void cleanup() throws GLException {
        super.cleanup();
        for (EventQueue eventQueue : this.eventQueues) {
            eventQueue.cleanup();
        }
        this.eventQueues.clear();
    }

    public void add(EventQueue eventQueue) {
        this.eventQueues.add(eventQueue);
        eventQueue.init();
    }

    public void remove(EventQueue eventQueue) {
        this.eventQueues.remove(eventQueue);
        eventQueue.cleanup();
    }

    protected void init(Collection eventQueues) {
        if (eventQueues == null) {
            return;
        }
        this.eventQueues.addAll(eventQueues);
        for (EventQueue eventQueue : eventQueues) {
            eventQueue.init();
        }
    }

    public static class Startup
    implements StartupShutdown {
        @Override
        public void load(T2SharedConnection conn) throws GLException {
        }

        @Override
        public void activate(T2SharedConnection conn) throws GLException {
        }

        @Override
        public void unload() throws GLException {
            for (EventQueuer queuer : eventQueuerList) {
                queuer.cleanup();
            }
        }
    }
}
