/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventQueue;
import java.beans.BeanDescriptor;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.beans.SimpleBeanInfo;

public class EventQueueBeanInfo
extends SimpleBeanInfo {
    private static BeanDescriptor theQueueBeanDescriptor = new BeanDescriptor(EventQueue.class);

    @Override
    public BeanDescriptor getBeanDescriptor() {
        return theQueueBeanDescriptor;
    }

    @Override
    public PropertyDescriptor[] getPropertyDescriptors() {
        try {
            return new PropertyDescriptor[]{new PropertyDescriptor("name", EventQueue.class, "getName", null), new PropertyDescriptor("type", EventQueue.class, "getType", null), new PropertyDescriptor("currentSize", EventQueue.class, "size", null), new PropertyDescriptor("queueSize", EventQueue.class, "trackQueueSize", null), new PropertyDescriptor("throughput", EventQueue.class, "getTotalThroughput", null), new PropertyDescriptor("waitTime", EventQueue.class, "trackWaitTime", null), new PropertyDescriptor("processingTime", EventQueue.class, "trackProcessingTime", null), new PropertyDescriptor("maxProcessEvent", EventQueue.class, "getMaxProcessingEvent", null)};
        }
        catch (IntrospectionException ie) {
            throw new Error(ie.toString());
        }
    }
}
