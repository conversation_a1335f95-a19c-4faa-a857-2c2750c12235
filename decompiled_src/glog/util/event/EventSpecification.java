/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.Functions;
import glog.util.OTMMessageFormat;
import glog.util.event.EventParameterization;
import glog.util.event.EventQueueGroup;
import java.io.Serializable;

public class EventSpecification
implements Serializable {
    private int hash = -1;
    protected Class eventClass;
    protected EventParameterization eventParameterization;
    protected EventQueueGroup eventQueueGroup = EventQueueGroup.theMainEventQueueGroup;

    public EventSpecification(Class eventClass, EventParameterization eventParameterization, EventQueueGroup eventQueueGroup) {
        this.eventClass = eventClass;
        this.eventParameterization = eventParameterization;
        this.eventQueueGroup = eventQueueGroup;
    }

    public EventSpecification copy() {
        return new EventSpecification(this.eventClass, this.eventParameterization, this.eventQueueGroup);
    }

    public Class getEventClass() {
        return this.eventClass;
    }

    public EventParameterization getEventParameterization() {
        return this.eventParameterization;
    }

    public EventQueueGroup getEventQueueGroup() {
        return this.eventQueueGroup;
    }

    void setEventClass(Class eventClass) {
        this.eventClass = eventClass;
        this.hash = -1;
    }

    void setEventParameterization(EventParameterization eventParameterization) {
        this.eventParameterization = eventParameterization;
        this.hash = -1;
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof EventSpecification)) {
            return false;
        }
        EventSpecification otherSpec = (EventSpecification)other;
        return this.eventClass == otherSpec.eventClass && Functions.equals(this.eventParameterization, otherSpec.eventParameterization) && Functions.equals(this.eventQueueGroup, otherSpec.eventQueueGroup);
    }

    public int hashCode() {
        if (this.hash == -1) {
            this.hash = this.eventClass.getName().hashCode() + (this.eventQueueGroup == null ? 0 : this.eventQueueGroup.hashCode());
        }
        return this.hash;
    }

    public String toString() {
        return OTMMessageFormat.format("(class={0}/param={1}/queue={2})", this.eventClass.getName(), this.eventParameterization, this.eventQueueGroup);
    }
}
