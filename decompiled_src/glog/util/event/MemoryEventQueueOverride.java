/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.MemoryEventQueue;

public class MemoryEventQueueOverride {
    private static ThreadLocal overrideEventQueueForThread = new ThreadLocal();

    public static MemoryEventQueue get() {
        return (MemoryEventQueue)overrideEventQueueForThread.get();
    }

    public static void set(MemoryEventQueue eventQueue) {
        overrideEventQueueForThread.set(eventQueue);
    }
}
