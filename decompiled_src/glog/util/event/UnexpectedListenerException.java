/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.Event;
import glog.util.event.EventHandler;
import glog.util.exception.Cause;
import glog.util.exception.GLExceptionProd;

public class UnexpectedListenerException
extends GLExceptionProd {
    UnexpectedListenerException(EventHandler handler, Event.Listener listener) {
        super(new Cause("cause.unexpectedListener", null, new Object[][]{{"expected", handler.listenerClass.getName()}, {"actual", listener.getClass().getName()}}), null);
    }
}
