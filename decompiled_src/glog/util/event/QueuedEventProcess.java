/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.server.process.BusinessProcess;
import glog.server.process.ProcessType;
import glog.util.event.Event;
import glog.util.exception.GLException;

public class QueuedEventProcess
extends BusinessProcess {
    public QueuedEventProcess(Event event) throws GLException {
        super(processStack.peek(), ProcessType.TOPIC, event.toString());
        this.init();
    }

    public void init() {
        this.setMaintainProcessStack(false);
        this.setDelayEventDelivery(true);
        this.setCreated();
    }
}
