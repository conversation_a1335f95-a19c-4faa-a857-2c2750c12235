/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.StartupShutdown;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;

public abstract class EventQueueRunnable
implements Runnable {
    public static StartupShutdown.IsReady isReady = new StartupShutdown.IsReady();

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void waitUntilReady() {
        StartupShutdown.IsReady isReady = EventQueueRunnable.isReady;
        synchronized (isReady) {
            if (!EventQueueRunnable.isReady.get()) {
                try {
                    EventQueueRunnable.isReady.wait();
                }
                catch (InterruptedException interruptedException) {
                    // empty catch block
                }
            }
        }
    }

    public static class Startup
    implements StartupShutdown {
        @Override
        public void load(T2SharedConnection conn) throws GLException {
        }

        /*
         * WARNING - Removed try catching itself - possible behaviour change.
         */
        @Override
        public void activate(T2SharedConnection conn) throws GLException {
            StartupShutdown.IsReady isReady = EventQueueRunnable.isReady;
            synchronized (isReady) {
                EventQueueRunnable.isReady.notifyAll();
                EventQueueRunnable.isReady.set(true);
            }
        }

        @Override
        public void unload() throws GLException {
        }
    }
}
