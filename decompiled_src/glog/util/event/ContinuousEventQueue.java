/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.ContinuousEventQueueRunnable;
import glog.util.event.EventThread;
import glog.util.event.MemoryEventQueue;
import glog.util.event.QueuedEvent;
import java.util.LinkedList;
import java.util.List;

public class ContinuousEventQueue
extends MemoryEventQueue {
    public ContinuousEventQueue(String name, int threadCount) {
        super(name, threadCount);
    }

    @Override
    public void add(QueuedEvent queuedEvent) {
        List localQueue = this.getLocalQueue();
        if (localQueue != null) {
            localQueue.add(queuedEvent);
        } else {
            super.add(queuedEvent);
        }
    }

    @Override
    public void clear() {
        super.clear();
        EventThread[] eventThreads = this.getThreads();
        for (int i = 0; i < eventThreads.length; ++i) {
            eventThreads[i].getLocalQueue().clear();
        }
    }

    void clearLocal() {
        List localQueue = this.getLocalQueue();
        localQueue.clear();
    }

    void addLocal(QueuedEvent queuedEvent) {
        List localQueue = this.getLocalQueue();
        localQueue.add(queuedEvent);
    }

    boolean isEmptyLocal() {
        List localQueue = this.getLocalQueue();
        return localQueue.isEmpty();
    }

    void removeFirstLocal() {
        List localQueue = this.getLocalQueue();
        localQueue.remove(0);
    }

    QueuedEvent getFirstLocal() {
        List localQueue = this.getLocalQueue();
        return (QueuedEvent)localQueue.get(0);
    }

    @Override
    protected EventThread getEventThread(int index) {
        EventThread thread = new EventThread(this, new ContinuousEventQueueRunnable(), this.getName(), index);
        thread.setLocalQueue(new LinkedList());
        return thread;
    }

    private List getLocalQueue() {
        EventThread eventThread = EventThread.getCurrentEventThread();
        if (eventThread != null) {
            return eventThread.getLocalQueue();
        }
        return null;
    }
}
