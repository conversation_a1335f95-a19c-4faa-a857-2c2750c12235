/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.StartupShutdown;
import glog.util.event.EventQueueRunnable;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;

public static class EventQueueRunnable.Startup
implements StartupShutdown {
    @Override
    public void load(T2SharedConnection conn) throws GLException {
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void activate(T2SharedConnection conn) throws GLException {
        StartupShutdown.IsReady isReady = EventQueueRunnable.isReady;
        synchronized (isReady) {
            EventQueueRunnable.isReady.notifyAll();
            EventQueueRunnable.isReady.set(true);
        }
    }

    @Override
    public void unload() throws GLException {
    }
}
