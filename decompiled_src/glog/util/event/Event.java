/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import glog.util.event.EventQueue;
import glog.util.event.EventSpecification;
import glog.util.event.QueueSequence;
import glog.util.exception.GLException;
import java.io.Serializable;

public interface Event
extends Serializable {
    public EventQueue getQueue();

    public QueueSequence getQueueSequence();

    public EventSpecification getSpecification();

    public String getProcessId();

    public String getProcessPid();

    public boolean blockProcesses();

    public static interface ListenerWithQueueAssigment
    extends Listener {
        public EventQueue getQueue();
    }

    public static interface DecomposedListener
    extends Listener {
        public Listener getUndecomposed();
    }

    public static interface Listener {
        public void trigger(Event var1, Context var2) throws GLException;
    }

    public static interface Context {
    }
}
