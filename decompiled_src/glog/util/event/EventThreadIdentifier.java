/*
 * Decompiled with CFR 0.152.
 */
package glog.util.event;

import java.io.Serializable;

public class EventThreadIdentifier
implements Serializable,
Comparable {
    private String description;
    private int index;
    private int gid;
    private static int threadGid = 0;

    public EventThreadIdentifier(String description, int index) {
        this.description = description;
        this.index = index;
        this.gid = ++threadGid;
    }

    public String getDescription() {
        return this.description;
    }

    public int getIndex() {
        return this.index;
    }

    public int getGid() {
        return this.gid;
    }

    public String toString() {
        return this.description + " - " + this.index;
    }

    public int hashCode() {
        return this.description.hashCode() + this.index;
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof EventThreadIdentifier)) {
            return false;
        }
        EventThreadIdentifier o = (EventThreadIdentifier)other;
        return this.description.equals(o.description) && this.index == o.index && this.gid == o.gid;
    }

    public int compareTo(Object other) {
        if (this == other) {
            return 0;
        }
        if (!(other instanceof EventThreadIdentifier)) {
            return -1;
        }
        EventThreadIdentifier o = (EventThreadIdentifier)other;
        int cmp = this.description.compareTo(o.description);
        if (cmp == 0) {
            cmp = this.index - o.index;
        }
        if (cmp == 0) {
            cmp = this.gid - o.gid;
        }
        return cmp;
    }
}
