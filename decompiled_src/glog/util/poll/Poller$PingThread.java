/*
 * Decompiled with CFR 0.152.
 */
package glog.util.poll;

import glog.util.exception.GLException;
import glog.util.poll.Poll;
import java.util.ArrayList;

private class Poller.PingThread
extends Thread {
    private boolean canceled;

    public Poller.PingThread(String name) {
        super(name);
        this.setDaemon(true);
    }

    @Override
    public void run() {
        long count = 0L;
        while (!this.canceled) {
            try {
                Thread.sleep(Poller.this.pingInterval);
            }
            catch (InterruptedException ie) {
                break;
            }
            ++count;
            ArrayList<Poll> pings = new ArrayList<Poll>(Poller.this.polls.size());
            for (Poll poll : Poller.this.polls) {
                if (count % poll.pingCount != 0L) continue;
                pings.add(poll);
            }
            for (Poll poll : pings) {
                try {
                    poll.ping();
                }
                catch (Throwable t) {
                    GLException.factory(t);
                }
            }
        }
    }

    public final void cancel() {
        this.canceled = true;
    }
}
