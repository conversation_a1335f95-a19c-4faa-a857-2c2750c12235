/*
 * Decompiled with CFR 0.152.
 */
package glog.util.poll;

import glog.util.exception.GLException;
import glog.util.poll.Poll;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;

public class Poller {
    private HashSet polls = new HashSet();
    private long pingInterval;
    private PingThread pingThread;
    private boolean canceled;
    private static Poller thePoller = null;

    public synchronized void register(Poll poll) {
        this.polls.remove(poll);
        if (poll.pingInterval != 0L) {
            this.polls.add(poll);
        }
        this.updatePing();
    }

    public synchronized void unregister(Poll poll) {
        this.polls.remove(poll);
        this.updatePing();
    }

    protected Poller() {
    }

    private void updatePing() {
        if (this.pingThread != null) {
            try {
                this.pingThread.cancel();
                this.pingThread.interrupt();
                this.pingThread.join(10000L);
            }
            catch (InterruptedException interruptedException) {
                // empty catch block
            }
        }
        if (!this.polls.isEmpty()) {
            this.setPollRates();
            this.pingThread = new PingThread("GC3 Poller");
            this.pingThread.start();
        }
    }

    private void setPollRates() {
        Iterator it = this.polls.iterator();
        Poll poll2 = (Poll)it.next();
        this.pingInterval = poll2.pingInterval;
        while (it.hasNext()) {
            poll2 = (Poll)it.next();
            this.pingInterval = this.euclid(poll2.pingInterval, this.pingInterval);
        }
        for (Poll poll2 : this.polls) {
            poll2.pingCount = poll2.pingInterval / this.pingInterval;
        }
    }

    private long euclid(long a, long b) {
        if (b == 0L) {
            return a;
        }
        return this.euclid(b, a % b);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    public static Poller get() {
        if (thePoller != null) return thePoller;
        Class<Poller> clazz = Poller.class;
        synchronized (Poller.class) {
            if (thePoller != null) return thePoller;
            thePoller = new Poller();
            // ** MonitorExit[var0] (shouldn't be in output)
            return thePoller;
        }
    }

    private class PingThread
    extends Thread {
        private boolean canceled;

        public PingThread(String name) {
            super(name);
            this.setDaemon(true);
        }

        @Override
        public void run() {
            long count = 0L;
            while (!this.canceled) {
                try {
                    Thread.sleep(Poller.this.pingInterval);
                }
                catch (InterruptedException ie) {
                    break;
                }
                ++count;
                ArrayList<Poll> pings = new ArrayList<Poll>(Poller.this.polls.size());
                for (Poll poll : Poller.this.polls) {
                    if (count % poll.pingCount != 0L) continue;
                    pings.add(poll);
                }
                for (Poll poll : pings) {
                    try {
                        poll.ping();
                    }
                    catch (Throwable t) {
                        GLException.factory(t);
                    }
                }
            }
        }

        public final void cancel() {
            this.canceled = true;
        }
    }
}
