/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import glog.database.security.SubjectUtil;
import glog.util.GLProperties;
import glog.util.LocalTimestamp;
import glog.util.OTMMessageFormat;
import glog.util.local.LocalContext;
import java.io.Serializable;

public static class GLProperties.History
implements Serializable {
    private GLProperties.HistoricalAction action;
    private String oldValue;
    private String newValue;
    private String user;
    private LocalTimestamp time;

    public GLProperties.History(String oldValue, String newValue) {
        this.action = oldValue != null ? (newValue != null ? MODIFIED : REMOVED) : ADDED;
        this.oldValue = this.checkMask(oldValue);
        this.newValue = this.checkMask(newValue);
        this.user = LocalContext.isAppServerContext() ? SubjectUtil.getCurrentUser() : null;
        this.time = new LocalTimestamp();
    }

    public GLProperties.HistoricalAction getAction() {
        return this.action;
    }

    public String getOldValue() {
        return this.oldValue;
    }

    public String getNewValue() {
        return this.newValue;
    }

    public String getUser() {
        return this.user;
    }

    public LocalTimestamp getTime() {
        return this.time;
    }

    public String toString() {
        return OTMMessageFormat.format(this.action.getFormat(), this.time, this.user, this.oldValue, this.newValue);
    }

    private String checkMask(String value) {
        if (value == null) {
            return null;
        }
        if (displayProtection != MASK_PROTECTION) {
            return value;
        }
        return GLProperties.isEncoded(value) ? "********" : value;
    }
}
