/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

public class DisposableFile
extends File {
    private int useCount;
    private static Map disposableFiles = new HashMap();

    public DisposableFile() {
        super("reserved");
    }

    protected DisposableFile(String filename) {
        super(filename);
    }

    protected DisposableFile(File file) {
        super(file.toString());
    }

    @Override
    public boolean delete() {
        --this.useCount;
        if (this.useCount == 0) {
            return super.delete();
        }
        return true;
    }

    public void finalize() {
        super.delete();
    }

    public static DisposableFile get(File file) {
        String filename = file.toString();
        DisposableFile disposableFile = (DisposableFile)disposableFiles.get(filename);
        if (disposableFile == null) {
            disposableFile = new DisposableFile(file);
            disposableFiles.put(filename, disposableFile);
        }
        ++disposableFile.useCount;
        return disposableFile;
    }

    public static DisposableFile get(String filename) {
        DisposableFile disposableFile = (DisposableFile)disposableFiles.get(filename);
        if (disposableFile == null) {
            disposableFile = new DisposableFile(filename);
            disposableFiles.put(filename, disposableFile);
        }
        ++disposableFile.useCount;
        return disposableFile;
    }
}
