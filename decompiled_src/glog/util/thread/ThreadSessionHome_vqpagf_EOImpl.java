/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.EJBObject
 *  weblogic.ejb.container.interfaces.Invokable
 *  weblogic.ejb.container.internal.BaseRemoteObject
 *  weblogic.ejb.container.internal.InvocationWrapper
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.SessionRemoteMethodInvoker
 *  weblogic.ejb.container.internal.StatelessEJBObject
 */
package glog.util.thread;

import glog.util.exception.GLException;
import glog.util.thread.ThreadSession;
import glog.util.thread.ThreadSessionHome_vqpagf_Intf;
import glog.util.uom.data.Duration;
import java.rmi.RemoteException;
import javax.ejb.EJBHome;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.ejb.EJBObject;
import weblogic.ejb.container.interfaces.Invokable;
import weblogic.ejb.container.internal.BaseRemoteObject;
import weblogic.ejb.container.internal.InvocationWrapper;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.SessionRemoteMethodInvoker;
import weblogic.ejb.container.internal.StatelessEJBObject;

public final class ThreadSessionHome_vqpagf_EOImpl
extends StatelessEJBObject
implements ThreadSession,
EJBObject,
Invokable {
    public static MethodDescriptor md_eo_diagnoseXML_SLLSglog_util_uom_data_DurationSSSSSi;
    public static MethodDescriptor md_eo_stop_l;
    public static MethodDescriptor md_eo_remove;
    public static MethodDescriptor md_eo_getEJBHome;
    public static MethodDescriptor md_eo_getHandle;
    public static MethodDescriptor md_eo_getPrimaryKey;
    public static MethodDescriptor md_eo_isIdentical_javax_ejb_EJBObject;

    public void stop(long l) throws RemoteException, GLException {
        SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_stop_l), (Object[])new Object[]{l}, (int)0);
    }

    public String diagnoseXML(String string, Long l, Long l2, String string2, Duration duration, String string3, String string4, String string5, String string6, String string7, int n) throws RemoteException, GLException {
        return (String)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_diagnoseXML_SLLSglog_util_uom_data_DurationSSSSSi), (Object[])new Object[]{string, l, l2, string2, duration, string3, string4, string5, string6, string7, n}, (int)1);
    }

    public Object __WL_invoke(Object object, Object[] objectArray, int n) throws Throwable {
        switch (n) {
            case 0: {
                ((ThreadSessionHome_vqpagf_Intf)object).stop((Long)objectArray[0]);
                return null;
            }
            case 1: {
                return ((ThreadSessionHome_vqpagf_Intf)object).diagnoseXML((String)objectArray[0], (Long)objectArray[1], (Long)objectArray[2], (String)objectArray[3], (Duration)objectArray[4], (String)objectArray[5], (String)objectArray[6], (String)objectArray[7], (String)objectArray[8], (String)objectArray[9], (Integer)objectArray[10]);
            }
        }
        throw new IllegalArgumentException("No method found for index : " + n);
    }

    public void __WL_handleException(int n, Throwable throwable) throws Throwable {
        switch (n) {
            case 0: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 1: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            default: {
                throw new IllegalArgumentException("No method found for index : " + n);
            }
        }
    }

    public void remove() throws RemoveException, RemoteException {
        super.remove(md_eo_remove);
    }

    public EJBHome getEJBHome() throws RemoteException {
        return super.getEJBHome(md_eo_getEJBHome);
    }

    public Handle getHandle() throws RemoteException {
        return super.getHandle(md_eo_getHandle);
    }

    public Object getPrimaryKey() throws RemoteException {
        return super.getPrimaryKey(md_eo_getPrimaryKey);
    }

    public boolean isIdentical(javax.ejb.EJBObject eJBObject) throws RemoteException {
        return super.isIdentical(md_eo_isIdentical_javax_ejb_EJBObject, eJBObject);
    }

    public void operationsComplete() {
    }
}
