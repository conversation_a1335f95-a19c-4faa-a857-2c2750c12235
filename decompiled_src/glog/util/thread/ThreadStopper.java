/*
 * Decompiled with CFR 0.152.
 */
package glog.util.thread;

import glog.util.GLProperties;
import glog.util.exception.GLException;
import glog.util.thread.ThreadStoppedException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.StringTokenizer;
import java.util.concurrent.ConcurrentHashMap;

public class ThreadStopper {
    private static ConcurrentHashMap stopThread = new ConcurrentHashMap();
    public static final int STOP_THREAD_ON_SQL = 0;
    public static final int STOP_THREAD_ON_CACHE_WRITE = 1;
    public static final int STOP_THREAD_ON_CACHE_READ = 2;
    public static final int STOP_THREAD_ON_JNDI = 3;
    public static final int STOP_THREAD_ON_LOGGING = 4;
    public static final int STOP_THREAD_ON_SESSION_CALL = 5;
    public static final int STOP_THREAD_ON_COUNT = 6;
    private static HashMap<String, Integer> stopThreadOnMap = new HashMap();
    public static boolean[] stopThreadOn;

    public static boolean isCurrentThreadStopped() {
        return ThreadStopper.isThreadStopped(Thread.currentThread().getId());
    }

    public static void checkCurrentThreadStopped() {
        if (ThreadStopper.isCurrentThreadStopped()) {
            ThreadStopper.unstopCurrentThread();
            throw new ThreadStoppedException();
        }
    }

    public static void unstopCurrentThread() {
        ThreadStopper.unstopThread(Thread.currentThread().getId());
    }

    public static boolean isThreadStopped(long threadId) {
        return Boolean.TRUE == stopThread.get(new Long(threadId));
    }

    public static void stopThread(long threadId) {
        stopThread.put(new Long(threadId), Boolean.TRUE);
    }

    public static void unstopThread(long threadId) {
        stopThread.put(new Long(threadId), Boolean.FALSE);
    }

    private static ThreadGroup getTopLevelThreadGroup() {
        ThreadGroup parent = null;
        ThreadGroup threadGroup = Thread.currentThread().getThreadGroup();
        while ((parent = threadGroup.getParent()) != null) {
            threadGroup = parent;
        }
        return threadGroup;
    }

    private static Thread findThread(ThreadGroup threadGroup, long threadId) {
        int size;
        Thread[] threads = null;
        for (int cnt = 1000; (size = threadGroup.enumerate(threads = new Thread[cnt], true)) >= cnt; cnt *= 2) {
        }
        for (int i = 0; i < threads.length; ++i) {
            if (threads[i].getId() != threadId) continue;
            return threads[i];
        }
        return null;
    }

    private static void init() {
        String stopOnStr = GLProperties.get().getProperty("glog.thread.stopOn", "sql,jndi");
        StringTokenizer tokenizer = new StringTokenizer(stopOnStr, ",");
        Arrays.fill(stopThreadOn, false);
        while (tokenizer.hasMoreTokens()) {
            String token = tokenizer.nextToken();
            ThreadStopper.stopThreadOn[ThreadStopper.stopThreadOnMap.get((Object)token).intValue()] = true;
        }
    }

    static {
        stopThreadOnMap.put("sql", 0);
        stopThreadOnMap.put("cacheWrite", 1);
        stopThreadOnMap.put("cacheRead", 2);
        stopThreadOnMap.put("jndi", 3);
        stopThreadOnMap.put("logging", 4);
        stopThreadOnMap.put("sessionCall", 5);
        stopThreadOn = new boolean[6];
        ThreadStopper.init();
        GLProperties.get();
        GLProperties.addListener("glog.thread.stopOn", new GLProperties.Listener(){

            @Override
            public void onPropertyChange(GLProperties.Change change) throws GLException {
                ThreadStopper.init();
            }
        });
    }
}
