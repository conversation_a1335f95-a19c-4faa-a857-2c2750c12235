/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 *  weblogic.ejb.container.interfaces.EJBCreateInvoker
 *  weblogic.ejb.container.interfaces.WLEnterpriseBean
 */
package glog.util.thread;

import glog.util.exception.GLException;
import glog.util.thread.ThreadSessionHome_vqpagf_Intf;
import glog.util.thread.ThreadSessionServerSideEJBWrapper;
import javax.ejb.EJBContext;
import weblogic.ejb.container.interfaces.EJBCreateInvoker;
import weblogic.ejb.container.interfaces.WLEnterpriseBean;

public class ThreadSessionHome_vqpagf_Impl
extends ThreadSessionServerSideEJBWrapper
implements ThreadSessionHome_vqpagf_Intf,
WLEnterpriseBean,
EJBCreateInvoker {
    private int __WL_method_state;
    private EJBContext __WL_EJBContext;

    public int __WL_getMethodState() {
        return this.__WL_method_state;
    }

    public void __WL_setMethodState(int n) {
        this.__WL_method_state = n;
    }

    public EJBContext __WL_getEJBContext() {
        return this.__WL_EJBContext;
    }

    public void __WL_setEJBContext(EJBContext eJBContext) {
        this.__WL_EJBContext = eJBContext;
    }
}
