/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.apache.regexp.RE
 */
package glog.util.thread;

import glog.server.process.BusinessProcess;
import glog.server.process.LifetimeContext;
import glog.server.process.walker.ProcessWalker;
import glog.util.DOMGenerator;
import glog.util.Functions;
import glog.util.exception.GLException;
import glog.util.j2ee.jvm.ASJVMDiagnostics;
import glog.util.j2ee.jvm.JVMThreadState;
import glog.util.remote.BaseStatelessBean;
import glog.util.thread.ThreadStopper;
import glog.util.uom.data.Duration;
import glog.webserver.util.XMLHelper;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.apache.regexp.RE;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

public class ThreadSessionBean
extends BaseStatelessBean {
    public String diagnoseXML(String threadGrep, Long threadIdMatch, Long processGid, String processPid, Duration processOlderThan, String processDescGrep, String mediationGrep, String lockGrep, String entryGrep, String traceGrep, int flags) throws GLException {
        try {
            XMLHelper xmlHelper = XMLHelper.instance();
            Document d = xmlHelper.createEmptyXMLDocument();
            Element e = (Element)DOMGenerator.insertElement(d, "threads", d);
            DOMGenerator.insertTypeUnit(d, "server_time", e, Functions.now());
            ASJVMDiagnostics diag = ASJVMDiagnostics.newInstance();
            ProcessWalker processWalker = processGid != null || processPid != null || processOlderThan != null || processDescGrep != null || mediationGrep != null ? new ProcessWalker(processGid, processPid, null, 1, processOlderThan, processDescGrep, mediationGrep) : null;
            for (JVMThreadState threadState : this.filterThreads(diag.getThreadState(), threadGrep, threadIdMatch, lockGrep, entryGrep, traceGrep)) {
                if (!this.checkThread(threadState, processWalker)) continue;
                this.addThread(d, threadState, flags, e);
            }
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            xmlHelper.display(d, (Writer)pw);
            return sw.toString();
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    private List<JVMThreadState> filterThreads(Map<JVMThreadState.Key, JVMThreadState> threads, String threadGrep, Long idMatch, String lockGrep, String entryGrep, String traceGrep) throws GLException {
        try {
            LinkedList<JVMThreadState> filteredThreads = new LinkedList<JVMThreadState>();
            RE threadRE = threadGrep != null ? new RE(threadGrep) : null;
            RE lockRE = lockGrep != null ? new RE(lockGrep) : null;
            RE entryRE = entryGrep != null ? new RE(entryGrep) : null;
            RE traceRE = traceGrep != null ? new RE(traceGrep) : null;
            for (JVMThreadState.Key key : threads.keySet()) {
                JVMThreadState thread;
                if (idMatch != null && idMatch.longValue() != key.getId() || !this.match(threadRE, key.getName()) || !this.match(entryRE, (thread = threads.get(key)).getTrace().getEntry()) || !this.match(traceRE, thread.getTrace().getTrace())) continue;
                if (lockRE != null) {
                    boolean matched;
                    JVMThreadState.Lock wait = thread.getWait();
                    boolean bl = matched = wait != null ? this.match(lockRE, wait.getName()) : false;
                    if (!matched) {
                        for (JVMThreadState.Lock owned : thread.getOwned()) {
                            if (matched |= this.match(lockRE, owned.getName())) break;
                        }
                    }
                    if (!matched) continue;
                }
                filteredThreads.add(thread);
            }
            return filteredThreads;
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    private boolean match(RE re, String s) {
        if (re == null) {
            return true;
        }
        if (s == null) {
            return false;
        }
        return re.match(s);
    }

    private boolean checkThread(JVMThreadState threadState, ProcessWalker processWalker) throws GLException {
        if (processWalker == null) {
            return true;
        }
        Thread thread = threadState.getThread();
        BusinessProcess process = BusinessProcess.getCurrentProcess(thread);
        if (process == null) {
            return false;
        }
        LifetimeContext context = LifetimeContext.get(this.getTopLevel(process).getPid());
        return processWalker.filterProcess(context);
    }

    private void addThread(Document d, JVMThreadState threadState, int flags, Element e) throws GLException {
        Element threadElem = (Element)DOMGenerator.insertElement(d, "thread", e);
        threadElem.setAttribute("id", String.valueOf(threadState.getId()));
        DOMGenerator.insertTagValue(d, "name", threadElem, threadState.getName());
        DOMGenerator.insertTagValue(d, "state", threadElem, (Object)threadState.getState());
        if ((flags & 1) != 0) {
            DOMGenerator.insertTagValue(d, "trace", threadElem, threadState.getTrace().getTrace());
        }
        DOMGenerator.insertTagValue(d, "entry", threadElem, threadState.getTrace().getEntry());
        JVMThreadState.Lock waitLock = threadState.getWait();
        if (waitLock != null) {
            this.addLock(d, "lockWait", threadElem, waitLock, true);
        }
        for (JVMThreadState.Lock ownedLock : threadState.getOwned()) {
            this.addLock(d, "lockOwned", threadElem, ownedLock, false);
        }
        Thread thread = threadState.getThread();
        BusinessProcess process = BusinessProcess.getCurrentProcess(thread);
        if (process != null) {
            process = this.getTopLevel(process);
            String pid = process.getPid();
            LifetimeContext context = LifetimeContext.get(pid);
            ProcessWalker processWalker = new ProcessWalker(pid);
            if ((flags & 2) == 0) {
                processWalker.setFlags(2);
            }
            processWalker.addProcess(context, d, threadElem);
        }
    }

    private BusinessProcess getTopLevel(BusinessProcess process) {
        if (process == null) {
            return null;
        }
        BusinessProcess parent = null;
        while ((parent = process.getParent()) != null) {
            process = parent;
        }
        return process;
    }

    private void addLock(Document d, String tag, Element e, JVMThreadState.Lock lock, boolean withOwner) throws GLException {
        Element lockElem = (Element)DOMGenerator.insertElement(d, tag, e);
        DOMGenerator.insertTagValue(d, "type", lockElem, (Object)lock.getType());
        DOMGenerator.insertTagValue(d, "name", lockElem, lock.getName());
        if (withOwner) {
            DOMGenerator.insertTagValue(d, "owner", lockElem, lock.getOwnerThreadId());
        }
    }

    public void stop(long threadId) throws GLException {
        try {
            ThreadStopper.stopThread(threadId);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
