/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 */
package glog.util.thread;

import glog.util.thread.ThreadSession;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;

public interface ThreadSessionHome
extends EJBHome {
    public static final String NAME = "ThreadSessionHome";

    public ThreadSession create() throws CreateException, RemoteException;
}
