/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBMetaData
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.StatelessEJBHome
 */
package glog.util.thread;

import glog.util.thread.ThreadSession;
import glog.util.thread.ThreadSessionHome;
import glog.util.thread.ThreadSessionHome_vqpagf_EOImpl;
import glog.util.thread.ThreadSessionHome_vqpagf_Intf;
import java.lang.reflect.Method;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.EJBMetaData;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.StatelessEJBHome;

public final class ThreadSessionHome_vqpagf_HomeImpl
extends StatelessEJBHome
implements ThreadSessionHome {
    private static final Method mth_ejbCreate;
    public MethodDescriptor md_ejbCreate;
    public MethodDescriptor md_getEJBMetaData;
    public MethodDescriptor md_getHomeHandle;
    public MethodDescriptor md_ejbRemove_O;
    public MethodDescriptor md_ejbRemove_javax_ejb_Handle;

    static {
        try {
            mth_ejbCreate = ThreadSessionHome_vqpagf_Intf.class.getMethod("ejbCreate", new Class[0]);
        }
        catch (Exception exception) {
            throw new AssertionError((Object)("Unable to find expected methods.  Please check your classpath for stale versions of your ejb classes and re-run weblogic.appc.\n If this is a java.io.FilePermission exception and you are running under JACC security, then check your security policy file.\n  Exception: '" + exception.getMessage() + "'"));
        }
    }

    public ThreadSessionHome_vqpagf_HomeImpl() {
        super(ThreadSessionHome_vqpagf_EOImpl.class);
    }

    public ThreadSession create() throws CreateException, RemoteException {
        try {
            return (ThreadSession)super.create(this.md_ejbCreate);
        }
        catch (Exception exception) {
            if (exception instanceof RemoteException) {
                throw (RemoteException)exception;
            }
            if (exception instanceof CreateException) {
                throw (CreateException)exception;
            }
            if (exception instanceof RuntimeException && this.deploymentInfo.getExceptionInfo(this.md_ejbCreate.getMethod(), (Throwable)exception).isAppException()) {
                throw (RuntimeException)exception;
            }
            throw new CreateException("Error while creating bean: " + exception.toString());
        }
    }

    public EJBMetaData getEJBMetaData() throws RemoteException {
        return super.getEJBMetaData(this.md_getEJBMetaData);
    }

    public HomeHandle getHomeHandle() throws RemoteException {
        return super.getHomeHandle(this.md_getHomeHandle);
    }

    public void remove(Object object) throws RemoteException, RemoveException {
        super.remove(this.md_ejbRemove_O, object);
    }

    public void remove(Handle handle) throws RemoteException, RemoveException {
        super.remove(this.md_ejbRemove_javax_ejb_Handle, handle);
    }
}
