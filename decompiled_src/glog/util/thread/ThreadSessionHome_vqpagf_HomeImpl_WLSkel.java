/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  weblogic.ejb20.interfaces.RemoteHome
 *  weblogic.rmi.internal.Skeleton
 *  weblogic.rmi.spi.InboundRequest
 *  weblogic.rmi.spi.MsgInput
 *  weblogic.rmi.spi.OutboundResponse
 */
package glog.util.thread;

import glog.util.thread.ThreadSession;
import glog.util.thread.ThreadSessionHome;
import java.io.IOException;
import java.rmi.MarshalException;
import java.rmi.UnmarshalException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import weblogic.ejb20.interfaces.RemoteHome;
import weblogic.rmi.internal.Skeleton;
import weblogic.rmi.spi.InboundRequest;
import weblogic.rmi.spi.MsgInput;
import weblogic.rmi.spi.OutboundResponse;

public final class ThreadSessionHome_vqpagf_HomeImpl_WLSkel
extends Skeleton {
    private static /* synthetic */ Class class$java$lang$Object;
    private static /* synthetic */ Class class$javax$ejb$HomeHandle;
    private static /* synthetic */ Class class$javax$ejb$Handle;
    private static /* synthetic */ Class class$glog$util$thread$ThreadSession;
    private static /* synthetic */ Class class$javax$ejb$EJBObject;
    private static /* synthetic */ Class class$javax$ejb$EJBMetaData;
    private static /* synthetic */ Class class$java$lang$String;

    public OutboundResponse invoke(int n, InboundRequest inboundRequest, OutboundResponse outboundResponse, Object object) throws Exception {
        switch (n) {
            case 0: {
                EJBObject eJBObject = ((RemoteHome)object).allocateEJBObject();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBObject, class$javax$ejb$EJBObject == null ? (class$javax$ejb$EJBObject = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("javax.ejb.EJBObject")) : class$javax$ejb$EJBObject);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 1: {
                Object object2;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    object2 = msgInput.readObject(class$java$lang$Object == null ? (class$java$lang$Object = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("java.lang.Object")) : class$java$lang$Object);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                EJBObject eJBObject = ((RemoteHome)object).allocateEJBObject(object2);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBObject, class$javax$ejb$EJBObject == null ? (class$javax$ejb$EJBObject = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("javax.ejb.EJBObject")) : class$javax$ejb$EJBObject);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 2: {
                ThreadSession threadSession = ((ThreadSessionHome)object).create();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)threadSession, class$glog$util$thread$ThreadSession == null ? (class$glog$util$thread$ThreadSession = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("glog.util.thread.ThreadSession")) : class$glog$util$thread$ThreadSession);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 3: {
                EJBMetaData eJBMetaData = ((EJBHome)object).getEJBMetaData();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBMetaData, class$javax$ejb$EJBMetaData == null ? (class$javax$ejb$EJBMetaData = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("javax.ejb.EJBMetaData")) : class$javax$ejb$EJBMetaData);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 4: {
                HomeHandle homeHandle = ((EJBHome)object).getHomeHandle();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)homeHandle, class$javax$ejb$HomeHandle == null ? (class$javax$ejb$HomeHandle = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("javax.ejb.HomeHandle")) : class$javax$ejb$HomeHandle);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 5: {
                String string = ((RemoteHome)object).getIsIdenticalKey();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)string, class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 6: {
                Object object3;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    object3 = msgInput.readObject(class$java$lang$Object == null ? (class$java$lang$Object = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("java.lang.Object")) : class$java$lang$Object);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                ((EJBHome)object).remove(object3);
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            case 7: {
                Handle handle;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    handle = (Handle)msgInput.readObject(class$javax$ejb$Handle == null ? (class$javax$ejb$Handle = ThreadSessionHome_vqpagf_HomeImpl_WLSkel.class$("javax.ejb.Handle")) : class$javax$ejb$Handle);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                ((EJBHome)object).remove(handle);
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            case 8: {
                ((RemoteHome)object).undeploy();
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            case 9: {
                boolean bl = ((RemoteHome)object).usesBeanManagedTx();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeBoolean(bl);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            default: {
                throw new UnmarshalException("Method identifier [" + n + "] out of range");
            }
        }
        return outboundResponse;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public Object invoke(int n, Object[] objectArray, Object object) throws Exception {
        switch (n) {
            case 0: {
                return ((RemoteHome)object).allocateEJBObject();
            }
            case 1: {
                return ((RemoteHome)object).allocateEJBObject(objectArray[0]);
            }
            case 2: {
                return ((ThreadSessionHome)object).create();
            }
            case 3: {
                return ((EJBHome)object).getEJBMetaData();
            }
            case 4: {
                return ((EJBHome)object).getHomeHandle();
            }
            case 5: {
                return ((RemoteHome)object).getIsIdenticalKey();
            }
            case 6: {
                ((EJBHome)object).remove(objectArray[0]);
                return null;
            }
            case 7: {
                ((EJBHome)object).remove((Handle)objectArray[0]);
                return null;
            }
            case 8: {
                ((RemoteHome)object).undeploy();
                return null;
            }
            case 9: {
                return new Boolean(((RemoteHome)object).usesBeanManagedTx());
            }
        }
        throw new UnmarshalException("Method identifier [" + n + "] out of range");
    }
}
