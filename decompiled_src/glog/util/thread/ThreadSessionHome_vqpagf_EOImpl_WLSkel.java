/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  weblogic.rmi.internal.Skeleton
 *  weblogic.rmi.spi.InboundRequest
 *  weblogic.rmi.spi.MsgInput
 *  weblogic.rmi.spi.OutboundResponse
 */
package glog.util.thread;

import glog.util.thread.ThreadSession;
import glog.util.uom.data.Duration;
import java.io.IOException;
import java.rmi.MarshalException;
import java.rmi.UnmarshalException;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import weblogic.rmi.internal.Skeleton;
import weblogic.rmi.spi.InboundRequest;
import weblogic.rmi.spi.MsgInput;
import weblogic.rmi.spi.OutboundResponse;

public final class ThreadSessionHome_vqpagf_EOImpl_WLSkel
extends Skeleton {
    private static /* synthetic */ Class class$glog$util$uom$data$Duration;
    private static /* synthetic */ Class class$javax$ejb$EJBObject;
    private static /* synthetic */ Class class$javax$ejb$EJBHome;
    private static /* synthetic */ Class class$javax$ejb$Handle;
    private static /* synthetic */ Class class$java$lang$Long;
    private static /* synthetic */ Class class$java$lang$Object;
    private static /* synthetic */ Class class$java$lang$String;

    public OutboundResponse invoke(int n, InboundRequest inboundRequest, OutboundResponse outboundResponse, Object object) throws Exception {
        switch (n) {
            case 0: {
                int n2;
                String string;
                String string2;
                String string3;
                String string4;
                String string5;
                Duration duration;
                String string6;
                Long l;
                Long l2;
                String string7;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    string7 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    l2 = (Long)msgInput.readObject(class$java$lang$Long == null ? (class$java$lang$Long = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.Long")) : class$java$lang$Long);
                    l = (Long)msgInput.readObject(class$java$lang$Long == null ? (class$java$lang$Long = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.Long")) : class$java$lang$Long);
                    string6 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    duration = (Duration)msgInput.readObject(class$glog$util$uom$data$Duration == null ? (class$glog$util$uom$data$Duration = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("glog.util.uom.data.Duration")) : class$glog$util$uom$data$Duration);
                    string5 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    string4 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    string3 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    string2 = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    string = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    n2 = msgInput.readInt();
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                String string8 = ((ThreadSession)object).diagnoseXML(string7, l2, l, string6, duration, string5, string4, string3, string2, string, n2);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)string8, class$java$lang$String == null ? (class$java$lang$String = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 1: {
                EJBHome eJBHome = ((EJBObject)object).getEJBHome();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBHome, class$javax$ejb$EJBHome == null ? (class$javax$ejb$EJBHome = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("javax.ejb.EJBHome")) : class$javax$ejb$EJBHome);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 2: {
                Handle handle = ((EJBObject)object).getHandle();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)handle, class$javax$ejb$Handle == null ? (class$javax$ejb$Handle = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("javax.ejb.Handle")) : class$javax$ejb$Handle);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 3: {
                Object object2 = ((EJBObject)object).getPrimaryKey();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(object2, class$java$lang$Object == null ? (class$java$lang$Object = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("java.lang.Object")) : class$java$lang$Object);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 4: {
                EJBObject eJBObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    eJBObject = (EJBObject)msgInput.readObject(class$javax$ejb$EJBObject == null ? (class$javax$ejb$EJBObject = ThreadSessionHome_vqpagf_EOImpl_WLSkel.class$("javax.ejb.EJBObject")) : class$javax$ejb$EJBObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                boolean bl = ((EJBObject)object).isIdentical(eJBObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeBoolean(bl);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 5: {
                ((EJBObject)object).remove();
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            case 6: {
                long l;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    l = msgInput.readLong();
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                ((ThreadSession)object).stop(l);
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            default: {
                throw new UnmarshalException("Method identifier [" + n + "] out of range");
            }
        }
        return outboundResponse;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public Object invoke(int n, Object[] objectArray, Object object) throws Exception {
        switch (n) {
            case 0: {
                return ((ThreadSession)object).diagnoseXML((String)objectArray[0], (Long)objectArray[1], (Long)objectArray[2], (String)objectArray[3], (Duration)objectArray[4], (String)objectArray[5], (String)objectArray[6], (String)objectArray[7], (String)objectArray[8], (String)objectArray[9], (Integer)objectArray[10]);
            }
            case 1: {
                return ((EJBObject)object).getEJBHome();
            }
            case 2: {
                return ((EJBObject)object).getHandle();
            }
            case 3: {
                return ((EJBObject)object).getPrimaryKey();
            }
            case 4: {
                return new Boolean(((EJBObject)object).isIdentical((EJBObject)objectArray[0]));
            }
            case 5: {
                ((EJBObject)object).remove();
                return null;
            }
            case 6: {
                ((ThreadSession)object).stop((Long)objectArray[0]);
                return null;
            }
        }
        throw new UnmarshalException("Method identifier [" + n + "] out of range");
    }
}
