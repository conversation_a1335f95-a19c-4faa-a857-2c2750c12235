/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.SessionContext
 */
package glog.util.thread;

import glog.util.exception.GLException;
import glog.util.local.LocalSessionContext;
import glog.util.local.SessionEnterExit;
import glog.util.thread.ThreadSession;
import glog.util.thread.ThreadSessionBean;
import glog.util.uom.data.Duration;
import java.rmi.RemoteException;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.SessionContext;

public class ThreadSessionStub
implements ThreadSession {
    private SessionContext context;

    public ThreadSessionStub(EJBHome home) {
        this.context = new LocalSessionContext(home, this);
    }

    @Override
    public String diagnoseXML(String p1, Long p2, Long p3, String p4, Duration p5, String p6, String p7, String p8, String p9, String p10, int p11) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ThreadSession", "diagnoseXML");
            ThreadSessionBean bean = new ThreadSessionBean();
            bean.setSessionContext(this.context);
            String string = bean.diagnoseXML(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11);
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public void stop(long p1) throws RemoteException, GLException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ThreadSession", "stop");
            ThreadSessionBean bean = new ThreadSessionBean();
            bean.setSessionContext(this.context);
            bean.stop(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() {
        return null;
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    public void remove() {
    }
}
