/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.util.thread;

import glog.server.sessionperf.SessionPerf;
import glog.util.NTierResponse;
import glog.util.exception.GLException;
import glog.util.remote.NamingDirectory;
import glog.util.thread.ThreadSessionBean;
import glog.util.uom.data.Duration;
import javax.ejb.EJBContext;

public class ThreadSessionServerSideEJBWrapper
extends ThreadSessionBean {
    @Override
    public String diagnoseXML(String p1, Long p2, Long p3, String p4, Duration p5, String p6, String p7, String p8, String p9, String p10, int p11) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("ThreadSession.diagnoseXML")) != null ? System.currentTimeMillis() : 0L;
        try {
            String string = super.diagnoseXML(p1, p2, p3, p4, p5, p6, p7, p8, p9, p10, p11);
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }

    @Override
    public void stop(long p1) throws GLException {
        SessionPerf perf;
        NamingDirectory.pushAppServerContext();
        NTierResponse ntierResponse = NTierResponse.get();
        if (ntierResponse != null) {
            ntierResponse.pushSessionCall();
        }
        long start = (perf = SessionPerf.get("ThreadSession.stop")) != null ? System.currentTimeMillis() : 0L;
        try {
            super.stop(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw GLException.ejbFactory(exc2);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            if (perf != null) {
                perf.collect(start);
            }
            if (ntierResponse != null) {
                ntierResponse.popSessionCall();
            }
            NamingDirectory.popAppServerContext();
        }
    }
}
