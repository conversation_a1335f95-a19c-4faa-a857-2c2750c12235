/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.SessionContext
 *  weblogic.ejb.container.interfaces.WLEnterpriseBean
 */
package glog.util.thread;

import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.uom.data.Duration;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.SessionContext;
import weblogic.ejb.container.interfaces.WLEnterpriseBean;

public interface ThreadSessionHome_vqpagf_Intf
extends WLEnterpriseBean {
    public void ejbCreate() throws CreateException;

    public void ejbRemove() throws RemoteException;

    public void ejbActivate() throws RemoteException;

    public void stop(long var1) throws GLException;

    public T2SharedConnection getConnection() throws GLException;

    public SessionContext getSessionContext();

    public String diagnoseXML(String var1, Long var2, Long var3, String var4, Duration var5, String var6, String var7, String var8, String var9, String var10, int var11) throws GLException;

    public T2SharedConnection getConnectionRemote() throws RemoteException;

    public void ejbPassivate() throws RemoteException;

    public void setSessionContext(SessionContext var1);
}
