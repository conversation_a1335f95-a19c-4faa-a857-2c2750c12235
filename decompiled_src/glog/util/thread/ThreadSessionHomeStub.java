/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBMetaData
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 */
package glog.util.thread;

import glog.util.thread.ThreadSession;
import glog.util.thread.ThreadSessionHome;
import glog.util.thread.ThreadSessionStub;
import javax.ejb.EJBMetaData;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;

public class ThreadSessionHomeStub
implements ThreadSessionHome {
    @Override
    public ThreadSession create() {
        return new ThreadSessionStub(this);
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(<PERSON>le handle) {
    }

    public void remove(Object o) {
    }
}
