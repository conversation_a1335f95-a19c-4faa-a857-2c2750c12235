/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.util.thread;

import glog.util.exception.GLException;
import glog.util.uom.data.Duration;
import java.rmi.RemoteException;
import javax.ejb.EJBObject;

public interface ThreadSession
extends EJBObject {
    public static final int SHOW_TRACE = 1;
    public static final int SHOW_PROCESS_TREE = 2;

    public String diagnoseXML(String var1, Long var2, Long var3, String var4, Duration var5, String var6, String var7, String var8, String var9, String var10, int var11) throws <PERSON>moteException, GLException;

    public void stop(long var1) throws RemoteException, GLException;
}
