/*
 * Decompiled with CFR 0.152.
 */
package glog.util.thread;

import glog.util.GLProperties;
import glog.util.exception.GLException;
import glog.util.thread.ThreadStopper;

static final class ThreadStopper.1
extends GLProperties.Listener {
    ThreadStopper.1() {
    }

    @Override
    public void onPropertyChange(GLProperties.Change change) throws GLException {
        ThreadStopper.init();
    }
}
