/*
 * Decompiled with CFR 0.152.
 */
package glog.util;

import java.security.AccessController;
import java.security.PrivilegedAction;

public abstract class ThrowablePrivilegedAction
implements PrivilegedAction {
    private Throwable error;

    public abstract Object runAndThrow() throws Throwable;

    public Object run() {
        try {
            return this.runAndThrow();
        }
        catch (Throwable t) {
            this.error = t;
            return null;
        }
    }

    public Throwable getError() {
        return this.error;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Object doPrivileged() throws Throwable {
        try {
            Object t = AccessController.doPrivileged(this);
            return t;
        }
        finally {
            if (this.error != null) {
                throw this.error;
            }
        }
    }
}
