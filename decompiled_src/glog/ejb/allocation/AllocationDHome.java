/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationDHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationDHome
extends AllocationDHomeDB {
    public Enumeration findByAllocSeqNo(String var1) throws FinderException, RemoteException;

    public Enumeration findByShipment(String var1) throws FinderException, RemoteException;
}
