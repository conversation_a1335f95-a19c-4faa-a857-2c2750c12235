/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.ejb.allocation;

import glog.ejb.allocation.AllocationDBean;
import glog.ejb.allocation.db.AllocationDData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBContext;

public class AllocationDServerSideEJBWrapper
extends AllocationDBean {
    @Override
    public AllocationDData getData() throws GLException {
        try {
            AllocationDData allocationDData = super.getData();
            return allocationDData;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setData(AllocationDData p1) throws GLException {
        try {
            super.setData(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setLinks(Vector p1, Vector p2) throws GLException {
        try {
            super.setLinks(p1, p2);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Pk getPK() {
        Pk pk = super.getPK();
        return pk;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() {
        super.markForCacheRemove();
    }

    @Override
    public void reload() throws GLException {
        try {
            super.reload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void unload() throws GLException {
        try {
            super.unload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Map verify() throws GLException {
        try {
            Map map = super.verify();
            return map;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Integer getAllocSeqNo() throws GLException {
        try {
            Integer n = super.getAllocSeqNo();
            return n;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setAllocSeqNo(Integer p1) throws GLException {
        try {
            super.setAllocSeqNo(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getOrderReleaseLineGid() throws GLException {
        try {
            String string = super.getOrderReleaseLineGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setOrderReleaseLineGid(String p1) throws GLException {
        try {
            super.setOrderReleaseLineGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getShipmentGid() throws GLException {
        try {
            String string = super.getShipmentGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setShipmentGid(String p1) throws GLException {
        try {
            super.setShipmentGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getOrderReleaseGid() throws GLException {
        try {
            String string = super.getOrderReleaseGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setOrderReleaseGid(String p1) throws GLException {
        try {
            super.setOrderReleaseGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Currency getPrivateCost() throws GLException {
        try {
            Currency currency = super.getPrivateCost();
            return currency;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setPrivateCost(Currency p1) throws GLException {
        try {
            super.setPrivateCost(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Currency getBaseCost() throws GLException {
        try {
            Currency currency = super.getBaseCost();
            return currency;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setBaseCost(Currency p1) throws GLException {
        try {
            super.setBaseCost(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Currency getTotalAllocLineCost() throws GLException {
        try {
            Currency currency = super.getTotalAllocLineCost();
            return currency;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setTotalAllocLineCost(Currency p1) throws GLException {
        try {
            super.setTotalAllocLineCost(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getPackagedItemGid() throws GLException {
        try {
            String string = super.getPackagedItemGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setPackagedItemGid(String p1) throws GLException {
        try {
            super.setPackagedItemGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public LocalDate getExchangeRateDate() throws GLException {
        try {
            LocalDate localDate = super.getExchangeRateDate();
            return localDate;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateDate(LocalDate p1) throws GLException {
        try {
            super.setExchangeRateDate(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getExchangeRateGid() throws GLException {
        try {
            String string = super.getExchangeRateGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateGid(String p1) throws GLException {
        try {
            super.setExchangeRateGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDomainName() throws GLException {
        try {
            String string = super.getDomainName();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDomainName(String p1) throws GLException {
        try {
            super.setDomainName(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }
}
