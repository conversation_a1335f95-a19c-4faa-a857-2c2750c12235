/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.Allocation;
import glog.ejb.allocation.AllocationD;
import glog.ejb.allocation.AllocationDHome;
import glog.ejb.allocation.AllocationHome;
import glog.ejb.allocation.AllocationOrLineD;
import glog.ejb.allocation.AllocationOrLineDHome;
import glog.ejb.allocation.AllocationOrderReleaseD;
import glog.ejb.allocation.AllocationOrderReleaseDHome;
import glog.ejb.allocation.db.AllocationBaseBeanDB;
import glog.ejb.allocation.db.AllocationBasePK;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.VoucherPK;
import glog.ejb.shipment.ShipmentCalculatedTransCostFieldsSync;
import glog.ejb.shipment.db.ShipmentPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.server.total.RecalcSyncHelper;
import glog.util.exception.Cause;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.log.Log;
import glog.util.log.LogIds;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.GLNoMoreRecords;
import glog.util.remote.NamingDirectory;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.FinderException;

public class AllocationBaseBean
extends AllocationBaseBeanDB {
    private static final String sql_PLANNING = "select ab.alloc_seq_no from shipment_status ss, status_value sv, allocation_base ab where ss.shipment_gid = ab.shipment_gid and sv.status_value_xid = ? and ss.status_type_gid = sv.status_type_gid and sv.status_value_gid = ss.status_value_gid and ab.alloc_type_qual_gid = ? ";
    private static final String sql_VOUCHER = "select ab.alloc_seq_no from voucher_status vs, status_value sv, allocation_base ab where vs.voucher_gid = ab.voucher_gid and sv.status_value_xid = ? and vs.status_type_gid = sv.status_type_gid and vs.status_value_gid = sv.status_value_gid and ab.alloc_type_qual_gid = ? ";
    private static final String sql_BILL = "select ab.alloc_seq_no from invoice_status bs, status_value sv, allocation_base ab, invoice i where bs.invoice_gid = ab.invoice_gid and ab.invoice_gid = i.invoice_gid and i.invoice_type='B' and sv.status_value_xid = ? and bs.status_type_gid = sv.status_type_gid and bs.status_value_gid = sv.status_value_gid and ab.alloc_type_qual_gid = ? ";
    private static final String SELECT_TRANS_SHIPMENT = "select original_shipment_gid from shipment where shipment_gid = ? and shipment_type_gid = 'SECONDARY CHARGE'";

    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    public Enumeration ejbFindByParentInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("parent_invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    public Enumeration ejbFindByShipment(String ShipmentGid) throws FinderException {
        return this.ejbFind("shipment_gid = ?", new Object[]{ShipmentGid});
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Enumeration ejbFindIssueable(String strAllocType) throws FinderException {
        Enumeration enumeration;
        SqlQuery sql = null;
        if ("PLANNING".equals(strAllocType) || "PLANNING_SELL".equals(strAllocType)) {
            sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sql_PLANNING), new Object[]{"ALLOCATION_SHIPMENT_ALLOCATED", strAllocType}, "AllocationBase::ejbFindIssueable(1)");
        } else if ("VOUCHER".equalsIgnoreCase(strAllocType)) {
            sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sql_VOUCHER), new Object[]{"ALLOCATION_VOUCHER_ALLOCATED", strAllocType}, "AllocationBase::ejbFindIssueable(2)");
        } else if ("BILL".equalsIgnoreCase(strAllocType)) {
            sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sql_BILL), new Object[]{"ALLOCATION_BILL_ALLOCATED", strAllocType}, "AllocationBase::ejbFindIssueable(3)");
        } else {
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "AllocationBaseBean::findIssueable - invalid Alloc_Type passed in -" + strAllocType);
            }
            throw GLException.factory((Cause)new GLException.CausedBy("cause.AllocationBaseBean.0001", "solution.AllocationBaseBean.0002", new Object[][]{{"strAllocType", strAllocType}}), null);
        }
        if (Log.idOn[LogIds.PLANNING.index]) {
            Log.logID(LogIds.PLANNING, "SQL Find Issueable Allocs: {0}.", sql.toString());
        }
        T2SharedConnection conn = this.getConnection();
        Vector<AllocationBasePK> v = new Vector<AllocationBasePK>();
        try {
            conn.open();
            sql.open(conn.get());
            while (sql.next()) {
                v.add(new AllocationBasePK(1, sql.getObject(1)));
            }
            if (v.size() == 0) {
                throw new GLNoMoreRecords(this);
            }
            enumeration = v.elements();
        }
        catch (Throwable throwable) {
            try {
                sql.close();
                conn.close();
                throw throwable;
            }
            catch (Throwable t) {
                throw FinderExceptionWrapper.ejbFactory(t);
            }
        }
        sql.close();
        conn.close();
        return enumeration;
    }

    public Enumeration ejbFindByVoucherPK(VoucherPK voucherPK) throws FinderException {
        return this.ejbFind("voucher_gid = ?", new Object[]{voucherPK.getDbValue(0)});
    }

    @Override
    protected boolean preRemove() throws GLException {
        try {
            if (this.markedForCacheRemove) {
                return true;
            }
            super.preRemove();
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocOrderReleaseD");
            }
            Enumeration eAllocOrderD = null;
            AllocationOrderReleaseDHome allocOrderDHome = (AllocationOrderReleaseDHome)NamingDirectory.get().lookup("ejb.AllocationOrderReleaseD");
            try {
                eAllocOrderD = allocOrderDHome.findByAllocSeqNo(this.getData().allocSeqNo.toString());
            }
            catch (FinderNoMoreRecords nmr) {
                // empty catch block
            }
            if (eAllocOrderD != null) {
                while (eAllocOrderD.hasMoreElements()) {
                    AllocationOrderReleaseD allocOrderD = (AllocationOrderReleaseD)eAllocOrderD.nextElement();
                    if (Log.idOn[LogIds.PLANNING.index]) {
                        Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocOrderReleaseD - " + allocOrderD.getAllocSeqNo() + ", " + allocOrderD.getOrderReleaseGid());
                    }
                    allocOrderD.remove();
                }
            }
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocOrLineD");
            }
            Enumeration eAllocORLineD = null;
            AllocationOrLineDHome allocOrLineDHome = (AllocationOrLineDHome)NamingDirectory.get().lookup("ejb.AllocationOrLineD");
            try {
                eAllocORLineD = allocOrLineDHome.findByAllocSeqNo(this.getData().allocSeqNo.toString());
            }
            catch (FinderNoMoreRecords nmr) {
                // empty catch block
            }
            if (eAllocORLineD != null) {
                while (eAllocORLineD.hasMoreElements()) {
                    AllocationOrLineD allocOrLineD = (AllocationOrLineD)eAllocORLineD.nextElement();
                    if (Log.idOn[LogIds.PLANNING.index]) {
                        Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocOrLineD - " + allocOrLineD.getAllocSeqNo() + ", " + allocOrLineD.getOrderReleaseLineGid());
                    }
                    allocOrLineD.remove();
                }
            }
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocationD - ");
            }
            Enumeration eAllocD = null;
            AllocationDHome allocDHome = (AllocationDHome)NamingDirectory.get().lookup("ejb.AllocationD");
            try {
                eAllocD = allocDHome.findByAllocSeqNo(this.getData().allocSeqNo.toString());
            }
            catch (FinderNoMoreRecords nmr) {
                // empty catch block
            }
            if (eAllocD != null) {
                while (eAllocD.hasMoreElements()) {
                    AllocationD allocD = (AllocationD)eAllocD.nextElement();
                    if (Log.idOn[LogIds.PLANNING.index]) {
                        Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing AllocationD - " + allocD.getAllocSeqNo() + ", " + allocD.getOrderReleaseLineGid());
                    }
                    allocD.remove();
                }
            }
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing Allocation - ");
            }
            Enumeration eAlloc = null;
            AllocationHome allocHome = (AllocationHome)NamingDirectory.get().lookup("ejb.Allocation");
            try {
                eAlloc = allocHome.findByAllocSeqNo(this.getData().allocSeqNo.toString());
            }
            catch (FinderNoMoreRecords nmr) {
                // empty catch block
            }
            if (eAlloc != null) {
                while (eAlloc.hasMoreElements()) {
                    Allocation alloc = (Allocation)eAlloc.nextElement();
                    if (Log.idOn[LogIds.PLANNING.index]) {
                        Log.logID(LogIds.PLANNING, "AllocationBaseBean::preRemove - Removing Allocation - " + alloc.getAllocSeqNo() + ", " + alloc.getOrderReleaseGid());
                    }
                    alloc.remove();
                }
            }
        }
        catch (Throwable t) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.AllocationBaseBean.0003", null, new Object[][]{{"pK", this.getPK()}}), t);
        }
        return true;
    }

    @Override
    protected void postRemove() throws GLException {
        super.postRemove();
        this.registerShipmentTransCostSync();
    }

    @Override
    protected void postCreate() throws GLException {
        super.postCreate();
        this.registerShipmentTransCostSync();
    }

    @Override
    protected void postStore() throws GLException {
        super.postStore();
        if (!BeanDataProcessor.doTrack() || RecalcSyncHelper.shouldPublish(this.getPK())) {
            this.registerShipmentTransCostSync();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private void registerShipmentTransCostSync() throws GLException {
        if (Log.idOn[LogIds.PLANNING.index]) {
            Log.logID(LogIds.PLANNING, "Allocation base change for shipment: {0}.", this.shipmentGid);
        }
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_TRANS_SHIPMENT), new Object[]{this.shipmentGid}, "AllocationBase::registerShipmentTransCostSync");
        T2SharedConnection conn = this.getConnection();
        ShipmentPK shipmentPK = null;
        try {
            conn.open();
            sql.open(conn.get());
            if (sql.next() && sql.getString("original_shipment_gid") != null && !"".equals(sql.getString("original_shipment_gid"))) {
                shipmentPK = new ShipmentPK(sql.getString("original_shipment_gid"));
            }
        }
        finally {
            sql.close();
            conn.close();
        }
        if (shipmentPK != null) {
            if (Log.idOn[LogIds.PLANNING.index]) {
                Log.logID(LogIds.PLANNING, "Publishing recalc on related transportation shipment {0}", shipmentPK);
            }
        } else {
            shipmentPK = new ShipmentPK((String)this.shipmentGid);
        }
        ShipmentCalculatedTransCostFieldsSync.register(shipmentPK);
    }
}
