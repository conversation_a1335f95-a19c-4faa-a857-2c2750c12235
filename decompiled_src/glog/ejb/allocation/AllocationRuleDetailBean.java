/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationRuleDetailBeanDB;
import glog.ejb.allocation.db.AllocationRuleDetailData;
import glog.ejb.allocation.db.AllocationRuleDetailPK;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import java.util.Enumeration;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationRuleDetailBean
extends AllocationRuleDetailBeanDB {
    private static final String SELECT_ALLOCATION_RULE_DETAIL_SEQ = "SELECT nvl(MAX(ALLOC_RULE_DETAIL_SEQ) , 0)+1 from ALLOCATION_RULE_DETAIL WHERE ALLOCATION_RULE_GID = ?";

    public Enumeration ejbFindByAllocationRule(String strAllocationRuleGid) throws FinderException {
        return this.ejbFind("allocation_rule_gid like ?", new Object[]{strAllocationRuleGid});
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private Double findNextSequence(String allocationRuleGid) throws GLException {
        SqlQuery sequenceQuery = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_ALLOCATION_RULE_DETAIL_SEQ), new Object[]{allocationRuleGid});
        T2SharedConnection conn = this.getConnection();
        try {
            conn.open();
            sequenceQuery.open(conn.get());
            if (sequenceQuery.next()) {
                Double d = new Double(sequenceQuery.getDouble(1));
                return d;
            }
        }
        finally {
            sequenceQuery.close();
            conn.close();
        }
        return null;
    }

    @Override
    public AllocationRuleDetailPK ejbCreate(AllocationRuleDetailData ardData) throws CreateException {
        try {
            Double nextSequence;
            if (ardData.allocRuleDetailSeq == null && null != (nextSequence = this.findNextSequence(ardData.allocationRuleGid))) {
                ardData.allocRuleDetailSeq = nextSequence;
            }
            return super.ejbCreate(ardData);
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }
}
