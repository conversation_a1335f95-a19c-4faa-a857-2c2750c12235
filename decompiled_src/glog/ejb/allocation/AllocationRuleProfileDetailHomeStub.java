/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.AllocationRuleProfileDetail;
import glog.ejb.allocation.AllocationRuleProfileDetailBean;
import glog.ejb.allocation.AllocationRuleProfileDetailHome;
import glog.ejb.allocation.AllocationRuleProfileDetailStub;
import glog.ejb.allocation.db.AllocationRuleProfileDetailData;
import glog.ejb.allocation.db.AllocationRuleProfileDetailPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class AllocationRuleProfileDetailHomeStub
implements AllocationRuleProfileDetailHome {
    static LRUCache cache = LocalEntityCache.register("AllocationRuleProfileDetail");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AllocationRuleProfileDetail create(AllocationRuleProfileDetailData p1) throws CreateException, RemoteException {
        AllocationRuleProfileDetailStub allocationRuleProfileDetailStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "create", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            allocationRuleProfileDetailStub = new AllocationRuleProfileDetailStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return allocationRuleProfileDetailStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public AllocationRuleProfileDetail findByPrimaryKey(AllocationRuleProfileDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AllocationRuleProfileDetailBean bean;
                ee.enterMethod("AllocationRuleProfileDetailHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (AllocationRuleProfileDetailBean)cache.get(p1)) != null) {
                    AllocationRuleProfileDetailStub allocationRuleProfileDetailStub = new AllocationRuleProfileDetailStub((EJBHome)this, bean);
                    return allocationRuleProfileDetailStub;
                }
                bean = new AllocationRuleProfileDetailBean();
                AllocationRuleProfileDetailPK pk = bean.ejbFindByPrimaryKey(p1);
                AllocationRuleProfileDetailStub allocationRuleProfileDetailStub = new AllocationRuleProfileDetailStub((EJBHome)this, pk);
                return allocationRuleProfileDetailStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "findAll", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "findAll", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "findByPrimaryKeys", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "findByPrimaryKeys", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(AllocationRuleProfileDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AllocationRuleProfileDetailBean bean;
                ee.enterMethod("AllocationRuleProfileDetailHome", "findInCache", true);
                if (cache != null && (bean = (AllocationRuleProfileDetailBean)cache.get(p1)) != null) {
                    Vector<AllocationRuleProfileDetailPK> v = new Vector<AllocationRuleProfileDetailPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), AllocationRuleProfileDetailStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(AllocationRuleProfileDetailPK p1, AllocationRuleProfileDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "onCreate", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(AllocationRuleProfileDetailPK p1, AllocationRuleProfileDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "onUpdate", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(AllocationRuleProfileDetailPK p1, AllocationRuleProfileDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "onRemove", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "listBeansInCache", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "listLocks", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "unlock", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "getDataNoLock", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "getLockData", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "getMaxCacheSize", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileDetailHome", "updateMaxCacheSize", true);
            AllocationRuleProfileDetailBean bean = new AllocationRuleProfileDetailBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new AllocationRuleProfileDetailStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
