/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationHome
extends AllocationHomeDB {
    public Enumeration findByAllocSeqNo(String var1) throws FinderException, RemoteException;

    public Enumeration findByOrderReleaseGid(String var1) throws FinderException, RemoteException;
}
