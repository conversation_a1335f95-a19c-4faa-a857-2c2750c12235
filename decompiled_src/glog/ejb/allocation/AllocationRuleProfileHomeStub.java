/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.AllocationRuleProfile;
import glog.ejb.allocation.AllocationRuleProfileBean;
import glog.ejb.allocation.AllocationRuleProfileHome;
import glog.ejb.allocation.AllocationRuleProfileStub;
import glog.ejb.allocation.db.AllocationRuleProfileData;
import glog.ejb.allocation.db.AllocationRuleProfilePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class AllocationRuleProfileHomeStub
implements AllocationRuleProfileHome {
    static LRUCache cache = LocalEntityCache.register("AllocationRuleProfile");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findDefaultProfile() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "findDefaultProfile", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Enumeration e = bean.ejbFindDefaultProfile();
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findDefaultProfile", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AllocationRuleProfile create(AllocationRuleProfileData p1) throws CreateException, RemoteException {
        AllocationRuleProfileStub allocationRuleProfileStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "create", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            allocationRuleProfileStub = new AllocationRuleProfileStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return allocationRuleProfileStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public AllocationRuleProfile findByPrimaryKey(AllocationRuleProfilePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AllocationRuleProfileBean bean;
                ee.enterMethod("AllocationRuleProfileHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (AllocationRuleProfileBean)cache.get(p1)) != null) {
                    AllocationRuleProfileStub allocationRuleProfileStub = new AllocationRuleProfileStub((EJBHome)this, bean);
                    return allocationRuleProfileStub;
                }
                bean = new AllocationRuleProfileBean();
                AllocationRuleProfilePK pk = bean.ejbFindByPrimaryKey(p1);
                AllocationRuleProfileStub allocationRuleProfileStub = new AllocationRuleProfileStub((EJBHome)this, pk);
                return allocationRuleProfileStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "findAll", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "findAll", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "findByPrimaryKeys", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "findByPrimaryKeys", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, AllocationRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(AllocationRuleProfilePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AllocationRuleProfileBean bean;
                ee.enterMethod("AllocationRuleProfileHome", "findInCache", true);
                if (cache != null && (bean = (AllocationRuleProfileBean)cache.get(p1)) != null) {
                    Vector<AllocationRuleProfilePK> v = new Vector<AllocationRuleProfilePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), AllocationRuleProfileStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(AllocationRuleProfilePK p1, AllocationRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "onCreate", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(AllocationRuleProfilePK p1, AllocationRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "onUpdate", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(AllocationRuleProfilePK p1, AllocationRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "onRemove", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "listBeansInCache", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "listLocks", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "unlock", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "getDataNoLock", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "getLockData", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "getMaxCacheSize", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AllocationRuleProfileHome", "updateMaxCacheSize", true);
            AllocationRuleProfileBean bean = new AllocationRuleProfileBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new AllocationRuleProfileStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
