/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationRuleProfileColumns;
import glog.ejb.allocation.db.AllocationRuleProfileData;
import glog.ejb.allocation.db.AllocationRuleProfilePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationRuleProfileBeanDB
extends BeanManagedEntityBean {
    public Object allocationRuleProfileGid;
    public Object allocationRuleProfileXid;
    public Object description;
    public Object isDefault;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationRuleProfilePK pk;
    protected transient AllocationRuleProfileData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleProfileBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationRuleProfilePK.Callback theCall = new AllocationRuleProfilePK.Callback();

    public AllocationRuleProfileBeanDB() {
        super(false);
    }

    public AllocationRuleProfilePK ejbCreate(AllocationRuleProfileData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocationRuleProfilePK newPK() throws GLException {
        return AllocationRuleProfilePK.newPK(this.getDomainName(), this.getAllocationRuleProfileXid(), this.getConnection());
    }

    public void ejbPostCreate(AllocationRuleProfileData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationRuleProfilePK ejbFindByPrimaryKey(AllocationRuleProfilePK pk) throws FinderException {
        return (AllocationRuleProfilePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationRuleProfilePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationRuleProfilePK> v = new Vector<AllocationRuleProfilePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationRuleProfilePK pk, AllocationRuleProfileData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationRuleProfilePK pk, AllocationRuleProfileData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationRuleProfilePK pk, AllocationRuleProfileData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationRuleProfileColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationRuleProfileColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationRuleProfileColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationRuleProfileColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationRuleProfilePK pk = (AllocationRuleProfilePK)genericPk;
        this.allocationRuleProfileGid = pk.allocationRuleProfileGid;
        this.domainName = pk.domainName;
        this.allocationRuleProfileXid = pk.allocationRuleProfileXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationRuleProfilePK pk = new AllocationRuleProfilePK(0, this.allocationRuleProfileGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationRuleProfile";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationRuleProfileData getData() throws GLException {
        try {
            AllocationRuleProfileData retval = new AllocationRuleProfileData();
            retval.getFromBean(this, AllocationRuleProfileColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationRuleProfileData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationRuleProfileData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationRuleProfileData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationRuleProfileColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationRuleProfileData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationRuleProfilePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocationRuleProfileGid() throws GLException {
        try {
            return (String)AllocationRuleProfileColumns.allocationRuleProfileGid.convertFromDB(this.allocationRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleProfileGid(String allocationRuleProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleProfileGid;
            AllocationRuleProfileData data = this.getData();
            this.allocationRuleProfileGid = AllocationRuleProfileColumns.allocationRuleProfileGid.convertToDB(allocationRuleProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleProfileGid", String.class, oldValue, this.allocationRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocationRuleProfileXid() throws GLException {
        try {
            return (String)AllocationRuleProfileColumns.allocationRuleProfileXid.convertFromDB(this.allocationRuleProfileXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleProfileXid(String allocationRuleProfileXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleProfileXid;
            AllocationRuleProfileData data = this.getData();
            this.allocationRuleProfileXid = AllocationRuleProfileColumns.allocationRuleProfileXid.convertToDB(allocationRuleProfileXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleProfileXid", String.class, oldValue, this.allocationRuleProfileXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)AllocationRuleProfileColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            AllocationRuleProfileData data = this.getData();
            this.description = AllocationRuleProfileColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsDefault() throws GLException {
        try {
            return (Boolean)AllocationRuleProfileColumns.isDefault.convertFromDB(this.isDefault);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsDefault(Boolean isDefault) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isDefault;
            AllocationRuleProfileData data = this.getData();
            this.isDefault = AllocationRuleProfileColumns.isDefault.convertToDB(isDefault);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isDefault", Boolean.class, oldValue, this.isDefault);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationRuleProfileColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationRuleProfileData data = this.getData();
            this.domainName = AllocationRuleProfileColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
