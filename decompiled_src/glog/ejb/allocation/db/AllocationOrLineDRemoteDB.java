/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationOrLineDData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationOrLineDRemoteDB
extends EJBObject {
    public AllocationOrLineDData getData() throws RemoteException, GLException;

    public void setData(AllocationOrLineDData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public Integer getAllocSeqNo() throws RemoteException, GLException;

    public void setAllocSeqNo(Integer var1) throws RemoteException, GLException;

    public Integer getAllocCostSeqno() throws RemoteException, GLException;

    public void setAllocCostSeqno(Integer var1) throws RemoteException, GLException;

    public String getOrderReleaseLineGid() throws RemoteException, GLException;

    public void setOrderReleaseLineGid(String var1) throws RemoteException, GLException;

    public String getCostDescription() throws RemoteException, GLException;

    public void setCostDescription(String var1) throws RemoteException, GLException;

    public String getAccessorialCodeGid() throws RemoteException, GLException;

    public void setAccessorialCodeGid(String var1) throws RemoteException, GLException;

    public Currency getCost() throws RemoteException, GLException;

    public void setCost(Currency var1) throws RemoteException, GLException;

    public String getGeneralLedgerGid() throws RemoteException, GLException;

    public void setGeneralLedgerGid(String var1) throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Integer var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getCostTypeGid() throws RemoteException, GLException;

    public void setCostTypeGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
