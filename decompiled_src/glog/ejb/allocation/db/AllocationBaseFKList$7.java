/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationBaseFKList.7
implements Fk {
    AllocationBaseFKList.7() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationBaseData";
    }

    @Override
    public String getPkField() {
        return "allocTypeQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocTypeQualData";
    }

    @Override
    public String getFkDataField() {
        return "allocTypeQualGid";
    }
}
