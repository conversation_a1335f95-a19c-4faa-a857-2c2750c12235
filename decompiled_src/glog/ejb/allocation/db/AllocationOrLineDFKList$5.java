/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationOrLineDFKList.5
implements Fk {
    AllocationOrLineDFKList.5() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationOrLineDData";
    }

    @Override
    public String getPkField() {
        return "allocSeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocationBaseData";
    }

    @Override
    public String getFkDataField() {
        return "allocSeqNo";
    }
}
