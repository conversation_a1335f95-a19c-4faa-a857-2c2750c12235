/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class AllocationOrderReleaseDColumns {
    public static SqlColumn allocSeqNo = new SqlColumn(Integer.class, "alloc_seq_no", 8, 0, 1, null, 0, null);
    public static SqlColumn allocCostSeqno = new SqlColumn(Integer.class, "alloc_cost_seqno", 8, 0, 1, null, 1, null);
    public static SqlColumn orderReleaseGid = new SqlColumn(String.class, "order_release_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn costDescription = new SqlColumn(String.class, "cost_description", 120, 0, 0, null, 3, null);
    public static SqlColumn accessorialCodeGid = new SqlColumn(String.class, "accessorial_code_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn cost = new SqlColumn(Currency.class, "cost", 22, 2, 1, null, 5, "cost_base");
    public static SqlColumn generalLedgerGid = new SqlColumn(String.class, "general_ledger_gid", 101, 0, 0, null, 6, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 7, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 8, null);
    public static SqlColumn costTypeGid = new SqlColumn(String.class, "cost_type_gid", 1, 0, 0, null, 9, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
}
