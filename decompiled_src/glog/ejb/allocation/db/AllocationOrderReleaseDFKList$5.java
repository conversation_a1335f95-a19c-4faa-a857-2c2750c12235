/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationOrderReleaseDFKList.5
implements Fk {
    AllocationOrderReleaseDFKList.5() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationOrderReleaseDData";
    }

    @Override
    public String getPkField() {
        return "orderReleaseGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.order.db";
    }

    @Override
    public String getFkDataClass() {
        return "OrderReleaseData";
    }

    @Override
    public String getFkDataField() {
        return "orderReleaseGid";
    }
}
