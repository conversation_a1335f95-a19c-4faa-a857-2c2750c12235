/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleDetailColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocationRuleDetailPK
extends Pk {
    public Object allocationRuleGid;
    public Object allocRuleDetailSeq;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationRuleDetailPK() {
    }

    public AllocationRuleDetailPK(String allocationRuleGid, Double allocRuleDetailSeq) {
        this.allocationRuleGid = this.notNull(AllocationRuleDetailColumns.allocationRuleGid.convertToDB(allocationRuleGid), "allocationRuleGid");
        this.allocRuleDetailSeq = this.notNull(AllocationRuleDetailColumns.allocRuleDetailSeq.convertToDB(allocRuleDetailSeq), "allocRuleDetailSeq");
    }

    public AllocationRuleDetailPK(int dummy, Object allocationRuleGid, Object allocRuleDetailSeq) {
        this(dummy, allocationRuleGid, allocRuleDetailSeq, null);
    }

    public AllocationRuleDetailPK(int dummy, Object allocationRuleGid, Object allocRuleDetailSeq, Object transaction) {
        this.allocationRuleGid = allocationRuleGid;
        this.allocRuleDetailSeq = allocRuleDetailSeq;
        this.transaction = transaction;
    }

    public AllocationRuleDetailPK(AllocationRuleDetailPK otherPk, Object transaction) {
        this.allocationRuleGid = otherPk.allocationRuleGid;
        this.allocRuleDetailSeq = otherPk.allocRuleDetailSeq;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocationRuleGid != null ? String.valueOf(this.allocationRuleGid) : "") + " " + (this.allocRuleDetailSeq != null ? String.valueOf(this.allocRuleDetailSeq) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocationRuleGid != null ? String.valueOf(this.allocationRuleGid) : "") + "|" + (this.allocRuleDetailSeq != null ? String.valueOf(this.allocRuleDetailSeq) : "");
    }

    public static AllocationRuleDetailPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationRuleDetailPK(gids[0], Double.valueOf(gids[1])) : null;
    }

    public static AllocationRuleDetailPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationRuleDetailPK(gids[0], Double.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.allocationRuleGid.hashCode() + this.allocRuleDetailSeq.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationRuleDetailPK)) {
            return false;
        }
        AllocationRuleDetailPK otherPk = (AllocationRuleDetailPK)other;
        return Functions.equals(otherPk.allocationRuleGid, this.allocationRuleGid) && Functions.equals(otherPk.allocRuleDetailSeq, this.allocRuleDetailSeq) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationRuleDetailHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocationRuleGid;
            }
            case 1: {
                return this.allocRuleDetailSeq;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationRuleDetailColumns.allocationRuleGid.convertFromDB(this.allocationRuleGid);
            }
            case 1: {
                return AllocationRuleDetailColumns.allocRuleDetailSeq.convertFromDB(this.allocRuleDetailSeq);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationRuleDetailPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationRuleDetail";
        }

        @Override
        public final String getTableName() {
            return "allocation_rule_detail";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"allocation_rule_gid", "alloc_rule_detail_seq"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationRuleDetailPK result = new AllocationRuleDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
