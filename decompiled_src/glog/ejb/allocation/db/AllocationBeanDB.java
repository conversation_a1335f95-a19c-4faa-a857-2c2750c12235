/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationColumns;
import glog.ejb.allocation.db.AllocationData;
import glog.ejb.allocation.db.AllocationPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationBeanDB
extends BeanManagedEntityBean {
    public Object allocSeqNo;
    public Object orderReleaseGid;
    public Object shipmentGid;
    public Object privateCost;
    public Object baseCost;
    public Object totalAllocCost;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationPK pk;
    protected transient AllocationData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationPK.Callback theCall = new AllocationPK.Callback();

    public AllocationBeanDB() {
        super(false);
    }

    public AllocationPK ejbCreate(AllocationData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationPK ejbFindByPrimaryKey(AllocationPK pk) throws FinderException {
        return (AllocationPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationPK> v = new Vector<AllocationPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationPK pk, AllocationData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationPK pk, AllocationData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationPK pk, AllocationData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationPK pk = (AllocationPK)genericPk;
        this.allocSeqNo = pk.allocSeqNo;
        this.orderReleaseGid = pk.orderReleaseGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationPK pk = new AllocationPK(0, this.allocSeqNo, this.orderReleaseGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.Allocation";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationData getData() throws GLException {
        try {
            AllocationData retval = new AllocationData();
            retval.getFromBean(this, AllocationColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getAllocSeqNo() throws GLException {
        try {
            return (Integer)AllocationColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocSeqNo(Integer allocSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocSeqNo;
            AllocationData data = this.getData();
            this.allocSeqNo = AllocationColumns.allocSeqNo.convertToDB(allocSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocSeqNo", Integer.class, oldValue, this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOrderReleaseGid() throws GLException {
        try {
            return (String)AllocationColumns.orderReleaseGid.convertFromDB(this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOrderReleaseGid(String orderReleaseGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.orderReleaseGid;
            AllocationData data = this.getData();
            this.orderReleaseGid = AllocationColumns.orderReleaseGid.convertToDB(orderReleaseGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "orderReleaseGid", String.class, oldValue, this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getShipmentGid() throws GLException {
        try {
            return (String)AllocationColumns.shipmentGid.convertFromDB(this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setShipmentGid(String shipmentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.shipmentGid;
            AllocationData data = this.getData();
            this.shipmentGid = AllocationColumns.shipmentGid.convertToDB(shipmentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "shipmentGid", String.class, oldValue, this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getPrivateCost() throws GLException {
        try {
            return (Currency)AllocationColumns.privateCost.convertFromDB(this.privateCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPrivateCost(Currency privateCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.privateCost;
            AllocationData data = this.getData();
            this.privateCost = AllocationColumns.privateCost.convertToDB(privateCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "privateCost", Currency.class, oldValue, this.privateCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getBaseCost() throws GLException {
        try {
            return (Currency)AllocationColumns.baseCost.convertFromDB(this.baseCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBaseCost(Currency baseCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.baseCost;
            AllocationData data = this.getData();
            this.baseCost = AllocationColumns.baseCost.convertToDB(baseCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "baseCost", Currency.class, oldValue, this.baseCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTotalAllocCost() throws GLException {
        try {
            return (Currency)AllocationColumns.totalAllocCost.convertFromDB(this.totalAllocCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalAllocCost(Currency totalAllocCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalAllocCost;
            AllocationData data = this.getData();
            this.totalAllocCost = AllocationColumns.totalAllocCost.convertToDB(totalAllocCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalAllocCost", Currency.class, oldValue, this.totalAllocCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)AllocationColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            AllocationData data = this.getData();
            this.exchangeRateDate = AllocationColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)AllocationColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            AllocationData data = this.getData();
            this.exchangeRateGid = AllocationColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationData data = this.getData();
            this.domainName = AllocationColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
