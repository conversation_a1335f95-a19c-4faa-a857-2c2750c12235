/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocTypeQualColumns {
    public static SqlColumn allocTypeQualGid = new SqlColumn(String.class, "alloc_type_qual_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocTypeQualXid = new SqlColumn(String.class, "alloc_type_qual_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn allocTypeQualDesc = new SqlColumn(String.class, "alloc_type_qual_desc", 255, 0, 0, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
