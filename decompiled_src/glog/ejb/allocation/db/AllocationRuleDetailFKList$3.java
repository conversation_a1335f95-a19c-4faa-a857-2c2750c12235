/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleDetailFKList.3
implements Fk {
    AllocationRuleDetailFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleDetailData";
    }

    @Override
    public String getPkField() {
        return "costTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "CostTypeData";
    }

    @Override
    public String getFkDataField() {
        return "costTypeGid";
    }
}
