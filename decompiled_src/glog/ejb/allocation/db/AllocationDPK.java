/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationDColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocationDPK
extends Pk {
    public Object allocSeqNo;
    public Object orderReleaseLineGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationDPK() {
    }

    public AllocationDPK(Integer allocSeqNo, String orderReleaseLineGid) {
        this.allocSeqNo = this.notNull(AllocationDColumns.allocSeqNo.convertToDB(allocSeqNo), "allocSeqNo");
        this.orderReleaseLineGid = this.notNull(AllocationDColumns.orderReleaseLineGid.convertToDB(orderReleaseLineGid), "orderReleaseLineGid");
    }

    public AllocationDPK(int dummy, Object allocSeqNo, Object orderReleaseLineGid) {
        this(dummy, allocSeqNo, orderReleaseLineGid, null);
    }

    public AllocationDPK(int dummy, Object allocSeqNo, Object orderReleaseLineGid, Object transaction) {
        this.allocSeqNo = allocSeqNo;
        this.orderReleaseLineGid = orderReleaseLineGid;
        this.transaction = transaction;
    }

    public AllocationDPK(AllocationDPK otherPk, Object transaction) {
        this.allocSeqNo = otherPk.allocSeqNo;
        this.orderReleaseLineGid = otherPk.orderReleaseLineGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "") + " " + (this.orderReleaseLineGid != null ? String.valueOf(this.orderReleaseLineGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "") + "|" + (this.orderReleaseLineGid != null ? String.valueOf(this.orderReleaseLineGid) : "");
    }

    public static AllocationDPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationDPK(Integer.valueOf(gids[0]), gids[1]) : null;
    }

    public static AllocationDPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationDPK(Integer.valueOf(gids[0]), gids[1]) : null;
    }

    public int hashCode() {
        return this.allocSeqNo.hashCode() + this.orderReleaseLineGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationDPK)) {
            return false;
        }
        AllocationDPK otherPk = (AllocationDPK)other;
        return Functions.equals(otherPk.allocSeqNo, this.allocSeqNo) && Functions.equals(otherPk.orderReleaseLineGid, this.orderReleaseLineGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationDHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocSeqNo;
            }
            case 1: {
                return this.orderReleaseLineGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationDColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
            }
            case 1: {
                return AllocationDColumns.orderReleaseLineGid.convertFromDB(this.orderReleaseLineGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationDPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationD";
        }

        @Override
        public final String getTableName() {
            return "allocation_d";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_seq_no", "order_release_line_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationDPK result = new AllocationDPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
