/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodDetailData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocMethodDetailRemoteDB
extends EJBObject {
    public AllocMethodDetailData getData() throws RemoteException, GLException;

    public void setData(AllocMethodDetailData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteEx<PERSON>, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getAllocMethodGid() throws <PERSON>moteException, GLException;

    public void setAllocMethodGid(String var1) throws RemoteException, GLException;

    public String getAllocMetricGid() throws RemoteException, GLException;

    public void setAllocMetricGid(String var1) throws RemoteException, GLException;

    public String getAllocTargetGid() throws RemoteException, GLException;

    public void setAllocTargetGid(String var1) throws RemoteException, GLException;

    public Double getPercentage() throws RemoteException, GLException;

    public void setPercentage(Double var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
