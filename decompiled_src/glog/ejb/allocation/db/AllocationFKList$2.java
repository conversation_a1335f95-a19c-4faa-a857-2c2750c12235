/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationFKList.2
implements Fk {
    AllocationFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationData";
    }

    @Override
    public String getPkField() {
        return "allocSeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocationBaseData";
    }

    @Override
    public String getFkDataField() {
        return "allocSeqNo";
    }
}
