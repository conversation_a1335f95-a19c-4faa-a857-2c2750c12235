/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileDetailColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocationRuleProfileDetailPK
extends Pk {
    public Object allocationRuleProfileGid;
    public Object allocationRuleSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationRuleProfileDetailPK() {
    }

    public AllocationRuleProfileDetailPK(String allocationRuleProfileGid, Integer allocationRuleSeqNo) {
        this.allocationRuleProfileGid = this.notNull(AllocationRuleProfileDetailColumns.allocationRuleProfileGid.convertToDB(allocationRuleProfileGid), "allocationRuleProfileGid");
        this.allocationRuleSeqNo = this.notNull(AllocationRuleProfileDetailColumns.allocationRuleSeqNo.convertToDB(allocationRuleSeqNo), "allocationRuleSeqNo");
    }

    public AllocationRuleProfileDetailPK(int dummy, Object allocationRuleProfileGid, Object allocationRuleSeqNo) {
        this(dummy, allocationRuleProfileGid, allocationRuleSeqNo, null);
    }

    public AllocationRuleProfileDetailPK(int dummy, Object allocationRuleProfileGid, Object allocationRuleSeqNo, Object transaction) {
        this.allocationRuleProfileGid = allocationRuleProfileGid;
        this.allocationRuleSeqNo = allocationRuleSeqNo;
        this.transaction = transaction;
    }

    public AllocationRuleProfileDetailPK(AllocationRuleProfileDetailPK otherPk, Object transaction) {
        this.allocationRuleProfileGid = otherPk.allocationRuleProfileGid;
        this.allocationRuleSeqNo = otherPk.allocationRuleSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocationRuleProfileGid != null ? String.valueOf(this.allocationRuleProfileGid) : "") + " " + (this.allocationRuleSeqNo != null ? String.valueOf(this.allocationRuleSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocationRuleProfileGid != null ? String.valueOf(this.allocationRuleProfileGid) : "") + "|" + (this.allocationRuleSeqNo != null ? String.valueOf(this.allocationRuleSeqNo) : "");
    }

    public static AllocationRuleProfileDetailPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationRuleProfileDetailPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public static AllocationRuleProfileDetailPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationRuleProfileDetailPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.allocationRuleProfileGid.hashCode() + this.allocationRuleSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationRuleProfileDetailPK)) {
            return false;
        }
        AllocationRuleProfileDetailPK otherPk = (AllocationRuleProfileDetailPK)other;
        return Functions.equals(otherPk.allocationRuleProfileGid, this.allocationRuleProfileGid) && Functions.equals(otherPk.allocationRuleSeqNo, this.allocationRuleSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationRuleProfileDetailHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocationRuleProfileGid;
            }
            case 1: {
                return this.allocationRuleSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationRuleProfileDetailColumns.allocationRuleProfileGid.convertFromDB(this.allocationRuleProfileGid);
            }
            case 1: {
                return AllocationRuleProfileDetailColumns.allocationRuleSeqNo.convertFromDB(this.allocationRuleSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationRuleProfileDetailPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationRuleProfileDetail";
        }

        @Override
        public final String getTableName() {
            return "allocation_rule_profile_detail";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"allocation_rule_profile_gid", "allocation_rule_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationRuleProfileDetailPK result = new AllocationRuleProfileDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
