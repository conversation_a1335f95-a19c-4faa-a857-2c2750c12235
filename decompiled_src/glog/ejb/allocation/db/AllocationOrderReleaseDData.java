/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationOrderReleaseDPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationOrderReleaseDData
extends BeanData {
    public Integer allocSeqNo;
    public Integer allocCostSeqno;
    public String orderReleaseGid;
    public String costDescription;
    public String accessorialCodeGid;
    public Currency cost;
    public String generalLedgerGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String costTypeGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationOrderReleaseDData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocSeqNoField = beanDataFields[0];
    public static Field allocCostSeqnoField = beanDataFields[1];
    public static Field orderReleaseGidField = beanDataFields[2];
    public static Field costDescriptionField = beanDataFields[3];
    public static Field accessorialCodeGidField = beanDataFields[4];
    public static Field costField = beanDataFields[5];
    public static Field generalLedgerGidField = beanDataFields[6];
    public static Field exchangeRateDateField = beanDataFields[7];
    public static Field exchangeRateGidField = beanDataFields[8];
    public static Field costTypeGidField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationOrderReleaseDData() {
    }

    public AllocationOrderReleaseDData(AllocationOrderReleaseDData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationOrderReleaseDPK();
    }

    @Legacy
    public AllocationOrderReleaseDPK getAllocationOrderReleaseDPK() {
        if (this.allocCostSeqno == null) {
            return null;
        }
        if (this.allocSeqNo == null) {
            return null;
        }
        return new AllocationOrderReleaseDPK(this.allocCostSeqno, this.allocSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationOrderReleaseDPK((AllocationOrderReleaseDPK)pk);
    }

    @Legacy
    public void setAllocationOrderReleaseDPK(AllocationOrderReleaseDPK pk) {
        this.allocCostSeqno = (Integer)pk.getAppValue(0);
        this.allocSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationOrderReleaseDQueryGen";
    }

    public static AllocationOrderReleaseDData load(Connection conn, AllocationOrderReleaseDPK pk) throws GLException {
        return (AllocationOrderReleaseDData)AllocationOrderReleaseDData.load(conn, pk, AllocationOrderReleaseDData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationOrderReleaseDData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationOrderReleaseDData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationOrderReleaseDData.load(conn, whereClause, prepareArguments, fetchSize, AllocationOrderReleaseDData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationOrderReleaseDData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationOrderReleaseDData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationOrderReleaseDData.class);
    }
}
