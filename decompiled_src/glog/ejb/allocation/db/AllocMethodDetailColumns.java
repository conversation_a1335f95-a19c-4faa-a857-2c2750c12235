/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocMethodDetailColumns {
    public static SqlColumn allocMethodGid = new SqlColumn(String.class, "alloc_method_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocMetricGid = new SqlColumn(String.class, "alloc_metric_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn allocTargetGid = new SqlColumn(String.class, "alloc_target_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn percentage = new SqlColumn(Double.class, "percentage", 22, 3, 1, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
