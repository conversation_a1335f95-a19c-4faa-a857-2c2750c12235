/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocMethodColumns {
    public static SqlColumn allocMethodGid = new SqlColumn(String.class, "alloc_method_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocMethodXid = new SqlColumn(String.class, "alloc_method_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn allocTargetGid = new SqlColumn(String.class, "alloc_target_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 101, 0, 0, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
    public static SqlColumn isUseShipmentShipUnit = new SqlColumn(Boolean.class, "is_use_shipment_ship_unit", 1, 0, 0, Boolean.valueOf("false"), 5, null);
}
