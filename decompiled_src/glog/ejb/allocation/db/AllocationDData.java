/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationDPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationDData
extends BeanData {
    public Integer allocSeqNo;
    public String orderReleaseLineGid;
    public String shipmentGid;
    public String orderReleaseGid;
    public Currency privateCost;
    public Currency baseCost;
    public Currency totalAllocLineCost;
    public String packagedItemGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationDData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocSeqNoField = beanDataFields[0];
    public static Field orderReleaseLineGidField = beanDataFields[1];
    public static Field shipmentGidField = beanDataFields[2];
    public static Field orderReleaseGidField = beanDataFields[3];
    public static Field privateCostField = beanDataFields[4];
    public static Field baseCostField = beanDataFields[5];
    public static Field totalAllocLineCostField = beanDataFields[6];
    public static Field packagedItemGidField = beanDataFields[7];
    public static Field exchangeRateDateField = beanDataFields[8];
    public static Field exchangeRateGidField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationDData() {
    }

    public AllocationDData(AllocationDData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationDPK();
    }

    @Legacy
    public AllocationDPK getAllocationDPK() {
        if (this.allocSeqNo == null) {
            return null;
        }
        if (this.orderReleaseLineGid == null) {
            return null;
        }
        return new AllocationDPK(this.allocSeqNo, this.orderReleaseLineGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationDPK((AllocationDPK)pk);
    }

    @Legacy
    public void setAllocationDPK(AllocationDPK pk) {
        this.allocSeqNo = (Integer)pk.getAppValue(0);
        this.orderReleaseLineGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationDQueryGen";
    }

    public static AllocationDData load(Connection conn, AllocationDPK pk) throws GLException {
        return (AllocationDData)AllocationDData.load(conn, pk, AllocationDData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationDData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationDData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationDData.load(conn, whereClause, prepareArguments, fetchSize, AllocationDData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationDData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationDData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationDData.class);
    }
}
