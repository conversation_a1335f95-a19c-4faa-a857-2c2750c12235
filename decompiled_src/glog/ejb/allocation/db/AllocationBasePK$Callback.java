/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationBasePK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class AllocationBasePK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return AllocationBasePK.class;
    }

    @Override
    public final String getEntity() {
        return "AllocationBase";
    }

    @Override
    public final String getTableName() {
        return "allocation_base";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"alloc_seq_no"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        AllocationBasePK result = new AllocationBasePK(0, q.getObject(pkOffset + 0 + 1), transaction);
        return result;
    }
}
