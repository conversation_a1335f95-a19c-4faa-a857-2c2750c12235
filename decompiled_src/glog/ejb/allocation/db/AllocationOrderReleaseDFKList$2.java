/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationOrderReleaseDFKList.2
implements Fk {
    AllocationOrderReleaseDFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationOrderReleaseDData";
    }

    @Override
    public String getPkField() {
        return "generalLedgerGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "GeneralLedgerCodeData";
    }

    @Override
    public String getFkDataField() {
        return "generalLedgerGid";
    }
}
