/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationRuleColumns;
import glog.ejb.allocation.db.AllocationRuleData;
import glog.ejb.allocation.db.AllocationRulePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationRuleBeanDB
extends BeanManagedEntityBean {
    public Object allocationRuleGid;
    public Object allocationRuleXid;
    public Object locationProfileGid;
    public Object consInvAllocType;
    public Object modeProfileGid;
    public Object domainName;
    public Object isAllocShipment;
    public Object isAllocVoucher;
    public Object isAllocBill;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationRulePK pk;
    protected transient AllocationRuleData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationRulePK.Callback theCall = new AllocationRulePK.Callback();

    public AllocationRuleBeanDB() {
        super(false);
    }

    public AllocationRulePK ejbCreate(AllocationRuleData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocationRulePK newPK() throws GLException {
        return AllocationRulePK.newPK(this.getDomainName(), this.getAllocationRuleXid(), this.getConnection());
    }

    public void ejbPostCreate(AllocationRuleData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationRulePK ejbFindByPrimaryKey(AllocationRulePK pk) throws FinderException {
        return (AllocationRulePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationRulePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationRulePK> v = new Vector<AllocationRulePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationRulePK pk, AllocationRuleData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationRulePK pk, AllocationRuleData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationRulePK pk, AllocationRuleData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationRuleColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationRuleColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationRuleColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationRuleColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationRulePK pk = (AllocationRulePK)genericPk;
        this.allocationRuleGid = pk.allocationRuleGid;
        this.domainName = pk.domainName;
        this.allocationRuleXid = pk.allocationRuleXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationRulePK pk = new AllocationRulePK(0, this.allocationRuleGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationRule";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationRuleData getData() throws GLException {
        try {
            AllocationRuleData retval = new AllocationRuleData();
            retval.getFromBean(this, AllocationRuleColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationRuleData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationRuleData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationRuleData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationRuleColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationRuleData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationRulePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocationRuleGid() throws GLException {
        try {
            return (String)AllocationRuleColumns.allocationRuleGid.convertFromDB(this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleGid(String allocationRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleGid;
            AllocationRuleData data = this.getData();
            this.allocationRuleGid = AllocationRuleColumns.allocationRuleGid.convertToDB(allocationRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleGid", String.class, oldValue, this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocationRuleXid() throws GLException {
        try {
            return (String)AllocationRuleColumns.allocationRuleXid.convertFromDB(this.allocationRuleXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleXid(String allocationRuleXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleXid;
            AllocationRuleData data = this.getData();
            this.allocationRuleXid = AllocationRuleColumns.allocationRuleXid.convertToDB(allocationRuleXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleXid", String.class, oldValue, this.allocationRuleXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getLocationProfileGid() throws GLException {
        try {
            return (String)AllocationRuleColumns.locationProfileGid.convertFromDB(this.locationProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLocationProfileGid(String locationProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.locationProfileGid;
            AllocationRuleData data = this.getData();
            this.locationProfileGid = AllocationRuleColumns.locationProfileGid.convertToDB(locationProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "locationProfileGid", String.class, oldValue, this.locationProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getConsInvAllocType() throws GLException {
        try {
            return (String)AllocationRuleColumns.consInvAllocType.convertFromDB(this.consInvAllocType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setConsInvAllocType(String consInvAllocType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.consInvAllocType;
            AllocationRuleData data = this.getData();
            this.consInvAllocType = AllocationRuleColumns.consInvAllocType.convertToDB(consInvAllocType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "consInvAllocType", String.class, oldValue, this.consInvAllocType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getModeProfileGid() throws GLException {
        try {
            return (String)AllocationRuleColumns.modeProfileGid.convertFromDB(this.modeProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setModeProfileGid(String modeProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.modeProfileGid;
            AllocationRuleData data = this.getData();
            this.modeProfileGid = AllocationRuleColumns.modeProfileGid.convertToDB(modeProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "modeProfileGid", String.class, oldValue, this.modeProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationRuleColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationRuleData data = this.getData();
            this.domainName = AllocationRuleColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsAllocShipment() throws GLException {
        try {
            return (Boolean)AllocationRuleColumns.isAllocShipment.convertFromDB(this.isAllocShipment);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsAllocShipment(Boolean isAllocShipment) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isAllocShipment;
            AllocationRuleData data = this.getData();
            this.isAllocShipment = AllocationRuleColumns.isAllocShipment.convertToDB(isAllocShipment);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isAllocShipment", Boolean.class, oldValue, this.isAllocShipment);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsAllocVoucher() throws GLException {
        try {
            return (Boolean)AllocationRuleColumns.isAllocVoucher.convertFromDB(this.isAllocVoucher);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsAllocVoucher(Boolean isAllocVoucher) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isAllocVoucher;
            AllocationRuleData data = this.getData();
            this.isAllocVoucher = AllocationRuleColumns.isAllocVoucher.convertToDB(isAllocVoucher);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isAllocVoucher", Boolean.class, oldValue, this.isAllocVoucher);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsAllocBill() throws GLException {
        try {
            return (Boolean)AllocationRuleColumns.isAllocBill.convertFromDB(this.isAllocBill);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsAllocBill(Boolean isAllocBill) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isAllocBill;
            AllocationRuleData data = this.getData();
            this.isAllocBill = AllocationRuleColumns.isAllocBill.convertToDB(isAllocBill);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isAllocBill", Boolean.class, oldValue, this.isAllocBill);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
