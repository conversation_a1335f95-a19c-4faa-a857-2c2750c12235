/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AllocationOrderReleaseDFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("costCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "costCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("generalLedgerGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "generalLedgerGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getFkDataField() {
                return "generalLedgerGid";
            }
        });
        fks.put("accessorialCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "accessorialCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.rates.db";
            }

            @Override
            public String getFkDataClass() {
                return "AccessorialCodeData";
            }

            @Override
            public String getFkDataField() {
                return "accessorialCodeGid";
            }
        });
        fks.put("allocSeqNo", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "allocSeqNo";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getFkDataClass() {
                return "AllocationBaseData";
            }

            @Override
            public String getFkDataField() {
                return "allocSeqNo";
            }
        });
        fks.put("orderReleaseGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "orderReleaseGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.order.db";
            }

            @Override
            public String getFkDataClass() {
                return "OrderReleaseData";
            }

            @Override
            public String getFkDataField() {
                return "orderReleaseGid";
            }
        });
        fks.put("exchangeRateGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationOrderReleaseDData";
            }

            @Override
            public String getPkField() {
                return "exchangeRateGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "ExchangeRateData";
            }

            @Override
            public String getFkDataField() {
                return "exchangeRateGid";
            }
        });
    }
}
