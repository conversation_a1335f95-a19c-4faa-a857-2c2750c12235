/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocMetricColumns;
import glog.ejb.allocation.db.AllocMetricData;
import glog.ejb.allocation.db.AllocMetricPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocMetricBeanDB
extends BeanManagedEntityBean {
    public Object allocMetricGid;
    public Object allocationBasis;
    public Object allocMetricXid;
    public Object className;
    public Object singleOriginOnly;
    public Object singleDestinationOnly;
    public Object singleOrderOnly;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocMetricPK pk;
    protected transient AllocMetricData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMetricBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocMetricPK.Callback theCall = new AllocMetricPK.Callback();

    public AllocMetricBeanDB() {
        super(false);
    }

    public AllocMetricPK ejbCreate(AllocMetricData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocMetricData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocMetricPK ejbFindByPrimaryKey(AllocMetricPK pk) throws FinderException {
        return (AllocMetricPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocMetricPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocMetricPK> v = new Vector<AllocMetricPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocMetricPK pk, AllocMetricData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocMetricPK pk, AllocMetricData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocMetricPK pk, AllocMetricData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocMetricPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocMetricColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocMetricColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocMetricColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocMetricColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocMetricPK pk = (AllocMetricPK)genericPk;
        this.allocationBasis = pk.allocationBasis;
        this.allocMetricGid = pk.allocMetricGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocMetricPK pk = new AllocMetricPK(0, this.allocationBasis, this.allocMetricGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocMetric";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocMetricData getData() throws GLException {
        try {
            AllocMetricData retval = new AllocMetricData();
            retval.getFromBean(this, AllocMetricColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocMetricData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocMetricData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocMetricData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocMetricColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocMetricData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocMetricPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocMetricGid() throws GLException {
        try {
            return (String)AllocMetricColumns.allocMetricGid.convertFromDB(this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMetricGid(String allocMetricGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMetricGid;
            AllocMetricData data = this.getData();
            this.allocMetricGid = AllocMetricColumns.allocMetricGid.convertToDB(allocMetricGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMetricGid", String.class, oldValue, this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocationBasis() throws GLException {
        try {
            return (String)AllocMetricColumns.allocationBasis.convertFromDB(this.allocationBasis);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationBasis(String allocationBasis) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationBasis;
            AllocMetricData data = this.getData();
            this.allocationBasis = AllocMetricColumns.allocationBasis.convertToDB(allocationBasis);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationBasis", String.class, oldValue, this.allocationBasis);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocMetricXid() throws GLException {
        try {
            return (String)AllocMetricColumns.allocMetricXid.convertFromDB(this.allocMetricXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMetricXid(String allocMetricXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMetricXid;
            AllocMetricData data = this.getData();
            this.allocMetricXid = AllocMetricColumns.allocMetricXid.convertToDB(allocMetricXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMetricXid", String.class, oldValue, this.allocMetricXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getClassName() throws GLException {
        try {
            return (String)AllocMetricColumns.className.convertFromDB(this.className);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setClassName(String className) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.className;
            AllocMetricData data = this.getData();
            this.className = AllocMetricColumns.className.convertToDB(className);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "className", String.class, oldValue, this.className);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getSingleOriginOnly() throws GLException {
        try {
            return (Boolean)AllocMetricColumns.singleOriginOnly.convertFromDB(this.singleOriginOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSingleOriginOnly(Boolean singleOriginOnly) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.singleOriginOnly;
            AllocMetricData data = this.getData();
            this.singleOriginOnly = AllocMetricColumns.singleOriginOnly.convertToDB(singleOriginOnly);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "singleOriginOnly", Boolean.class, oldValue, this.singleOriginOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getSingleDestinationOnly() throws GLException {
        try {
            return (Boolean)AllocMetricColumns.singleDestinationOnly.convertFromDB(this.singleDestinationOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSingleDestinationOnly(Boolean singleDestinationOnly) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.singleDestinationOnly;
            AllocMetricData data = this.getData();
            this.singleDestinationOnly = AllocMetricColumns.singleDestinationOnly.convertToDB(singleDestinationOnly);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "singleDestinationOnly", Boolean.class, oldValue, this.singleDestinationOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getSingleOrderOnly() throws GLException {
        try {
            return (Boolean)AllocMetricColumns.singleOrderOnly.convertFromDB(this.singleOrderOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSingleOrderOnly(Boolean singleOrderOnly) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.singleOrderOnly;
            AllocMetricData data = this.getData();
            this.singleOrderOnly = AllocMetricColumns.singleOrderOnly.convertToDB(singleOrderOnly);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "singleOrderOnly", Boolean.class, oldValue, this.singleOrderOnly);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocMetricColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocMetricData data = this.getData();
            this.domainName = AllocMetricColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
