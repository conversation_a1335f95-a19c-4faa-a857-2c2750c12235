/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.AllocationRule;
import glog.ejb.allocation.db.AllocationRuleData;
import glog.ejb.allocation.db.AllocationRulePK;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.FinderException;

public interface AllocationRuleHomeDB
extends EJBHome {
    public static final String NAME = "ejb.AllocationRule";
    public static final String dataClass = "glog.ejb.allocation.db.AllocationRuleData";
    public static final String primaryKeyClass = "glog.ejb.allocation.db.AllocationRulePK";

    public AllocationRule create(AllocationRuleData var1) throws CreateException, RemoteException;

    public AllocationRule findByPrimaryKey(AllocationRulePK var1) throws FinderException, RemoteException;

    public Enumeration findAll() throws FinderException, RemoteException;

    public Enumeration findAll(Object var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1, Object var2) throws FinderException, RemoteException;

    public Enumeration findInCache(AllocationRulePK var1) throws FinderException, RemoteException;

    public void onCreate(AllocationRulePK var1, AllocationRuleData var2) throws GLException, RemoteException;

    public void onUpdate(AllocationRulePK var1, AllocationRuleData var2) throws GLException, RemoteException;

    public void onRemove(AllocationRulePK var1, AllocationRuleData var2) throws GLException, RemoteException;

    public Collection listBeansInCache() throws GLException, RemoteException;

    public Collection listLocks() throws GLException, RemoteException;

    public void unlock(Pk var1) throws GLException, RemoteException;

    public BeanData getDataNoLock(Pk var1) throws GLException, RemoteException;

    public LockData getLockData(Pk var1) throws GLException, RemoteException;

    public int getMaxCacheSize() throws GLException, RemoteException;

    public void updateMaxCacheSize(int var1) throws GLException, RemoteException;
}
