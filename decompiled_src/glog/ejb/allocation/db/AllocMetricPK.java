/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMetricColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocMetricPK
extends Pk {
    public Object allocationBasis;
    public Object allocMetricGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocMetricPK() {
    }

    public AllocMetricPK(String allocationBasis, String allocMetricGid) {
        this.allocationBasis = this.notNull(AllocMetricColumns.allocationBasis.convertToDB(allocationBasis), "allocationBasis");
        this.allocMetricGid = this.notNull(AllocMetricColumns.allocMetricGid.convertToDB(allocMetricGid), "allocMetricGid");
    }

    public AllocMetricPK(int dummy, Object allocationBasis, Object allocMetricGid) {
        this(dummy, allocationBasis, allocMetricGid, null);
    }

    public AllocMetricPK(int dummy, Object allocationBasis, Object allocMetricGid, Object transaction) {
        this.allocationBasis = allocationBasis;
        this.allocMetricGid = allocMetricGid;
        this.transaction = transaction;
    }

    public AllocMetricPK(AllocMetricPK otherPk, Object transaction) {
        this.allocationBasis = otherPk.allocationBasis;
        this.allocMetricGid = otherPk.allocMetricGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocationBasis != null ? String.valueOf(this.allocationBasis) : "") + " " + (this.allocMetricGid != null ? String.valueOf(this.allocMetricGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocationBasis != null ? String.valueOf(this.allocationBasis) : "") + "|" + (this.allocMetricGid != null ? String.valueOf(this.allocMetricGid) : "");
    }

    public static AllocMetricPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocMetricPK(gids[0], gids[1]) : null;
    }

    public static AllocMetricPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocMetricPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.allocationBasis.hashCode() + this.allocMetricGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocMetricPK)) {
            return false;
        }
        AllocMetricPK otherPk = (AllocMetricPK)other;
        return Functions.equals(otherPk.allocationBasis, this.allocationBasis) && Functions.equals(otherPk.allocMetricGid, this.allocMetricGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocMetricHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocationBasis;
            }
            case 1: {
                return this.allocMetricGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocMetricColumns.allocationBasis.convertFromDB(this.allocationBasis);
            }
            case 1: {
                return AllocMetricColumns.allocMetricGid.convertFromDB(this.allocMetricGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocMetricPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocMetric";
        }

        @Override
        public final String getTableName() {
            return "alloc_metric";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"allocation_basis", "alloc_metric_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocMetricPK result = new AllocMetricPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
