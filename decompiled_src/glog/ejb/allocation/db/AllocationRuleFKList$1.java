/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleFKList.1
implements Fk {
    AllocationRuleFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleData";
    }

    @Override
    public String getPkField() {
        return "modeProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.modeprofile.db";
    }

    @Override
    public String getFkDataClass() {
        return "ModeProfileData";
    }

    @Override
    public String getFkDataField() {
        return "modeProfileGid";
    }
}
