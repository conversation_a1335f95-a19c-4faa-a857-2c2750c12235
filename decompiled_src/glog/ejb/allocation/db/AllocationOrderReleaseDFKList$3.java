/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationOrderReleaseDFKList.3
implements Fk {
    AllocationOrderReleaseDFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationOrderReleaseDData";
    }

    @Override
    public String getPkField() {
        return "accessorialCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.rates.db";
    }

    @Override
    public String getFkDataClass() {
        return "AccessorialCodeData";
    }

    @Override
    public String getFkDataField() {
        return "accessorialCodeGid";
    }
}
