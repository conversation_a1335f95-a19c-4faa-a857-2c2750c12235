/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationData
extends BeanData {
    public Integer allocSeqNo;
    public String orderReleaseGid;
    public String shipmentGid;
    public Currency privateCost;
    public Currency baseCost;
    public Currency totalAllocCost;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocSeqNoField = beanDataFields[0];
    public static Field orderReleaseGidField = beanDataFields[1];
    public static Field shipmentGidField = beanDataFields[2];
    public static Field privateCostField = beanDataFields[3];
    public static Field baseCostField = beanDataFields[4];
    public static Field totalAllocCostField = beanDataFields[5];
    public static Field exchangeRateDateField = beanDataFields[6];
    public static Field exchangeRateGidField = beanDataFields[7];
    public static Field domainNameField = beanDataFields[8];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationData() {
    }

    public AllocationData(AllocationData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationPK();
    }

    @Legacy
    public AllocationPK getAllocationPK() {
        if (this.allocSeqNo == null) {
            return null;
        }
        if (this.orderReleaseGid == null) {
            return null;
        }
        return new AllocationPK(this.allocSeqNo, this.orderReleaseGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationPK((AllocationPK)pk);
    }

    @Legacy
    public void setAllocationPK(AllocationPK pk) {
        this.allocSeqNo = (Integer)pk.getAppValue(0);
        this.orderReleaseGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationQueryGen";
    }

    public static AllocationData load(Connection conn, AllocationPK pk) throws GLException {
        return (AllocationData)AllocationData.load(conn, pk, AllocationData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationData.load(conn, whereClause, prepareArguments, fetchSize, AllocationData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationData.class);
    }
}
