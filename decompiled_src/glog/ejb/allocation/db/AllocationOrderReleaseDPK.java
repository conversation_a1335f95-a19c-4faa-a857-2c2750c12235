/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationOrderReleaseDColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocationOrderReleaseDPK
extends Pk {
    public Object allocCostSeqno;
    public Object allocSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationOrderReleaseDPK() {
    }

    public AllocationOrderReleaseDPK(Integer allocCostSeqno, Integer allocSeqNo) {
        this.allocCostSeqno = this.notNull(AllocationOrderReleaseDColumns.allocCostSeqno.convertToDB(allocCostSeqno), "allocCostSeqno");
        this.allocSeqNo = this.notNull(AllocationOrderReleaseDColumns.allocSeqNo.convertToDB(allocSeqNo), "allocSeqNo");
    }

    public AllocationOrderReleaseDPK(int dummy, Object allocCostSeqno, Object allocSeqNo) {
        this(dummy, allocCostSeqno, allocSeqNo, null);
    }

    public AllocationOrderReleaseDPK(int dummy, Object allocCostSeqno, Object allocSeqNo, Object transaction) {
        this.allocCostSeqno = allocCostSeqno;
        this.allocSeqNo = allocSeqNo;
        this.transaction = transaction;
    }

    public AllocationOrderReleaseDPK(AllocationOrderReleaseDPK otherPk, Object transaction) {
        this.allocCostSeqno = otherPk.allocCostSeqno;
        this.allocSeqNo = otherPk.allocSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocCostSeqno != null ? String.valueOf(this.allocCostSeqno) : "") + " " + (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocCostSeqno != null ? String.valueOf(this.allocCostSeqno) : "") + "|" + (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "");
    }

    public static AllocationOrderReleaseDPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationOrderReleaseDPK(Integer.valueOf(gids[0]), Integer.valueOf(gids[1])) : null;
    }

    public static AllocationOrderReleaseDPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationOrderReleaseDPK(Integer.valueOf(gids[0]), Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.allocCostSeqno.hashCode() + this.allocSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationOrderReleaseDPK)) {
            return false;
        }
        AllocationOrderReleaseDPK otherPk = (AllocationOrderReleaseDPK)other;
        return Functions.equals(otherPk.allocCostSeqno, this.allocCostSeqno) && Functions.equals(otherPk.allocSeqNo, this.allocSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationOrderReleaseDHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocCostSeqno;
            }
            case 1: {
                return this.allocSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationOrderReleaseDColumns.allocCostSeqno.convertFromDB(this.allocCostSeqno);
            }
            case 1: {
                return AllocationOrderReleaseDColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationOrderReleaseDPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationOrderReleaseD";
        }

        @Override
        public final String getTableName() {
            return "allocation_order_release_d";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_cost_seqno", "alloc_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationOrderReleaseDPK result = new AllocationOrderReleaseDPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
