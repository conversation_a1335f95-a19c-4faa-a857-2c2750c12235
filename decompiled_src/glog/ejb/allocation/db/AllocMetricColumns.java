/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocMetricColumns {
    public static SqlColumn allocMetricGid = new SqlColumn(String.class, "alloc_metric_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocationBasis = new SqlColumn(String.class, "allocation_basis", 10, 0, 1, null, 1, null);
    public static SqlColumn allocMetricXid = new SqlColumn(String.class, "alloc_metric_xid", 50, 0, 1, null, 2, null);
    public static SqlColumn className = new SqlColumn(String.class, "class_name", 255, 0, 1, null, 3, null);
    public static SqlColumn singleOriginOnly = new SqlColumn(Boolean.class, "single_origin_only", 1, 0, 1, null, 4, null);
    public static SqlColumn singleDestinationOnly = new SqlColumn(Boolean.class, "single_destination_only", 1, 0, 1, null, 5, null);
    public static SqlColumn singleOrderOnly = new SqlColumn(Boolean.class, "single_order_only", 1, 0, 1, null, 6, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 7, null);
}
