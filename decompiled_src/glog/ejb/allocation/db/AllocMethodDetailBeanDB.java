/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocMethodDetailColumns;
import glog.ejb.allocation.db.AllocMethodDetailData;
import glog.ejb.allocation.db.AllocMethodDetailPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocMethodDetailBeanDB
extends BeanManagedEntityBean {
    public Object allocMethodGid;
    public Object allocMetricGid;
    public Object allocTargetGid;
    public Object percentage;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocMethodDetailPK pk;
    protected transient AllocMethodDetailData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMethodDetailBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocMethodDetailPK.Callback theCall = new AllocMethodDetailPK.Callback();

    public AllocMethodDetailBeanDB() {
        super(false);
    }

    public AllocMethodDetailPK ejbCreate(AllocMethodDetailData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocMethodDetailData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocMethodDetailPK ejbFindByPrimaryKey(AllocMethodDetailPK pk) throws FinderException {
        return (AllocMethodDetailPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocMethodDetailPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocMethodDetailPK> v = new Vector<AllocMethodDetailPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocMethodDetailPK pk, AllocMethodDetailData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocMethodDetailPK pk, AllocMethodDetailData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocMethodDetailPK pk, AllocMethodDetailData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocMethodDetailPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocMethodDetailColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocMethodDetailColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocMethodDetailColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocMethodDetailColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocMethodDetailPK pk = (AllocMethodDetailPK)genericPk;
        this.allocMethodGid = pk.allocMethodGid;
        this.allocMetricGid = pk.allocMetricGid;
        this.allocTargetGid = pk.allocTargetGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocMethodDetailPK pk = new AllocMethodDetailPK(0, this.allocMethodGid, this.allocMetricGid, this.allocTargetGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocMethodDetail";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocMethodDetailData getData() throws GLException {
        try {
            AllocMethodDetailData retval = new AllocMethodDetailData();
            retval.getFromBean(this, AllocMethodDetailColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocMethodDetailData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocMethodDetailData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocMethodDetailData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocMethodDetailColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocMethodDetailData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocMethodDetailPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocMethodGid() throws GLException {
        try {
            return (String)AllocMethodDetailColumns.allocMethodGid.convertFromDB(this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMethodGid(String allocMethodGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMethodGid;
            AllocMethodDetailData data = this.getData();
            this.allocMethodGid = AllocMethodDetailColumns.allocMethodGid.convertToDB(allocMethodGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMethodGid", String.class, oldValue, this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocMetricGid() throws GLException {
        try {
            return (String)AllocMethodDetailColumns.allocMetricGid.convertFromDB(this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMetricGid(String allocMetricGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMetricGid;
            AllocMethodDetailData data = this.getData();
            this.allocMetricGid = AllocMethodDetailColumns.allocMetricGid.convertToDB(allocMetricGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMetricGid", String.class, oldValue, this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTargetGid() throws GLException {
        try {
            return (String)AllocMethodDetailColumns.allocTargetGid.convertFromDB(this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTargetGid(String allocTargetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTargetGid;
            AllocMethodDetailData data = this.getData();
            this.allocTargetGid = AllocMethodDetailColumns.allocTargetGid.convertToDB(allocTargetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTargetGid", String.class, oldValue, this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getPercentage() throws GLException {
        try {
            return (Double)AllocMethodDetailColumns.percentage.convertFromDB(this.percentage);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPercentage(Double percentage) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.percentage;
            AllocMethodDetailData data = this.getData();
            this.percentage = AllocMethodDetailColumns.percentage.convertToDB(percentage);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "percentage", Double.class, oldValue, this.percentage);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocMethodDetailColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocMethodDetailData data = this.getData();
            this.domainName = AllocMethodDetailColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
