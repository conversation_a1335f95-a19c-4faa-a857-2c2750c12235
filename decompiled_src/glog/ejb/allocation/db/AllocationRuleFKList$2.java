/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleFKList.2
implements Fk {
    AllocationRuleFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleData";
    }

    @Override
    public String getPkField() {
        return "locationProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "LocationProfileData";
    }

    @Override
    public String getFkDataField() {
        return "locationProfileGid";
    }
}
