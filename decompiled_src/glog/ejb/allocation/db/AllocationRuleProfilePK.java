/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocationRuleProfilePK
extends Pk {
    public Object allocationRuleProfileGid;
    public transient Object allocationRuleProfileXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationRuleProfilePK() {
    }

    public AllocationRuleProfilePK(String allocationRuleProfileGid) {
        this.allocationRuleProfileGid = this.notNull(AllocationRuleProfileColumns.allocationRuleProfileGid.convertToDB(allocationRuleProfileGid), "allocationRuleProfileGid");
    }

    public AllocationRuleProfilePK(String domainName, String allocationRuleProfileXid) {
        this.domainName = domainName;
        this.allocationRuleProfileXid = allocationRuleProfileXid;
        this.allocationRuleProfileGid = AllocationRuleProfilePK.concatForGid(domainName, allocationRuleProfileXid);
    }

    public AllocationRuleProfilePK(int dummy, Object allocationRuleProfileGid) {
        this(dummy, allocationRuleProfileGid, null);
    }

    public AllocationRuleProfilePK(int dummy, Object allocationRuleProfileGid, Object transaction) {
        this.allocationRuleProfileGid = allocationRuleProfileGid;
        this.transaction = transaction;
    }

    public AllocationRuleProfilePK(AllocationRuleProfilePK otherPk, Object transaction) {
        this.allocationRuleProfileGid = otherPk.allocationRuleProfileGid;
        this.allocationRuleProfileXid = otherPk.allocationRuleProfileXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocationRuleProfileGid != null ? String.valueOf(this.allocationRuleProfileGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocationRuleProfileGid != null ? String.valueOf(this.allocationRuleProfileGid) : "";
    }

    public static AllocationRuleProfilePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationRuleProfilePK(gids[0]) : null;
    }

    public static AllocationRuleProfilePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationRuleProfilePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.allocationRuleProfileGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationRuleProfilePK)) {
            return false;
        }
        AllocationRuleProfilePK otherPk = (AllocationRuleProfilePK)other;
        return Functions.equals(otherPk.allocationRuleProfileGid, this.allocationRuleProfileGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationRuleProfileHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocationRuleProfileGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationRuleProfileColumns.allocationRuleProfileGid.convertFromDB(this.allocationRuleProfileGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationRuleProfilePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AllocationRuleProfilePK requires a non-null XID to generate a GID");
            }
            AllocationRuleProfilePK allocationRuleProfilePK = new AllocationRuleProfilePK(domain, xid);
            return allocationRuleProfilePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationRuleProfilePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocationRuleProfilePK allocationRuleProfilePK = AllocationRuleProfilePK.newPK(domainName, xid, connection);
            return allocationRuleProfilePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationRuleProfilePK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationRuleProfile";
        }

        @Override
        public final String getTableName() {
            return "allocation_rule_profile";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"allocation_rule_profile_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationRuleProfilePK result = new AllocationRuleProfilePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
