/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationDFKList.2
implements Fk {
    AllocationDFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationDData";
    }

    @Override
    public String getPkField() {
        return "orderReleaseLineGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.order.db";
    }

    @Override
    public String getFkDataClass() {
        return "OrderReleaseLineData";
    }

    @Override
    public String getFkDataField() {
        return "orderReleaseLineGid";
    }
}
