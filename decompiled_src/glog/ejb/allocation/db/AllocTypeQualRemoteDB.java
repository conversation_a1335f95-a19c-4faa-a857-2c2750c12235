/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTypeQualData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocTypeQualRemoteDB
extends EJBObject {
    public AllocTypeQualData getData() throws RemoteException, GLException;

    public void setData(AllocTypeQualData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getAllocTypeQualGid() throws RemoteException, GLException;

    public void setAllocTypeQualGid(String var1) throws RemoteException, GLException;

    public String getAllocTypeQualXid() throws RemoteException, GLException;

    public void setAllocTypeQualXid(String var1) throws RemoteException, GLException;

    public String getAllocTypeQualDesc() throws RemoteException, GLException;

    public void setAllocTypeQualDesc(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
