/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocMethodFKList.1
implements Fk {
    AllocMethodFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocMethodData";
    }

    @Override
    public String getPkField() {
        return "allocTargetGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocTargetData";
    }

    @Override
    public String getFkDataField() {
        return "allocTargetGid";
    }
}
