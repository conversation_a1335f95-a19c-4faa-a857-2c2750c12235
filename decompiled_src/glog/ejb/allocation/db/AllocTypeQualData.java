/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTypeQualPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocTypeQualData
extends BeanData {
    public String allocTypeQualGid;
    public String allocTypeQualXid;
    public String allocTypeQualDesc;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocTypeQualData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocTypeQualGidField = beanDataFields[0];
    public static Field allocTypeQualXidField = beanDataFields[1];
    public static Field allocTypeQualDescField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocTypeQualData() {
    }

    public AllocTypeQualData(AllocTypeQualData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocTypeQualPK();
    }

    @Legacy
    public AllocTypeQualPK getAllocTypeQualPK() {
        if (this.allocTypeQualGid == null) {
            return null;
        }
        return new AllocTypeQualPK(this.allocTypeQualGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocTypeQualPK((AllocTypeQualPK)pk);
    }

    @Legacy
    public void setAllocTypeQualPK(AllocTypeQualPK pk) {
        this.allocTypeQualGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocTypeQualQueryGen";
    }

    public static AllocTypeQualData load(Connection conn, AllocTypeQualPK pk) throws GLException {
        return (AllocTypeQualData)AllocTypeQualData.load(conn, pk, AllocTypeQualData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocTypeQualData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocTypeQualData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocTypeQualData.load(conn, whereClause, prepareArguments, fetchSize, AllocTypeQualData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocTypeQualData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocTypeQualData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocTypeQualData.class);
    }
}
