/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocMethodColumns;
import glog.ejb.allocation.db.AllocMethodData;
import glog.ejb.allocation.db.AllocMethodPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocMethodBeanDB
extends BeanManagedEntityBean {
    public Object allocMethodGid;
    public Object allocMethodXid;
    public Object allocTargetGid;
    public Object description;
    public Object domainName;
    public Object isUseShipmentShipUnit;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocMethodPK pk;
    protected transient AllocMethodData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMethodBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocMethodPK.Callback theCall = new AllocMethodPK.Callback();

    public AllocMethodBeanDB() {
        super(false);
    }

    public AllocMethodPK ejbCreate(AllocMethodData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocMethodPK newPK() throws GLException {
        return AllocMethodPK.newPK(this.getDomainName(), this.getAllocMethodXid(), this.getConnection());
    }

    public void ejbPostCreate(AllocMethodData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocMethodPK ejbFindByPrimaryKey(AllocMethodPK pk) throws FinderException {
        return (AllocMethodPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocMethodPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocMethodPK> v = new Vector<AllocMethodPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocMethodPK pk, AllocMethodData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocMethodPK pk, AllocMethodData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocMethodPK pk, AllocMethodData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocMethodColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocMethodColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocMethodColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocMethodColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocMethodPK pk = (AllocMethodPK)genericPk;
        this.allocMethodGid = pk.allocMethodGid;
        this.domainName = pk.domainName;
        this.allocMethodXid = pk.allocMethodXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocMethodPK pk = new AllocMethodPK(0, this.allocMethodGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocMethod";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocMethodData getData() throws GLException {
        try {
            AllocMethodData retval = new AllocMethodData();
            retval.getFromBean(this, AllocMethodColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocMethodData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocMethodData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocMethodData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocMethodColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocMethodData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocMethodPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocMethodGid() throws GLException {
        try {
            return (String)AllocMethodColumns.allocMethodGid.convertFromDB(this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMethodGid(String allocMethodGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMethodGid;
            AllocMethodData data = this.getData();
            this.allocMethodGid = AllocMethodColumns.allocMethodGid.convertToDB(allocMethodGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMethodGid", String.class, oldValue, this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocMethodXid() throws GLException {
        try {
            return (String)AllocMethodColumns.allocMethodXid.convertFromDB(this.allocMethodXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMethodXid(String allocMethodXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMethodXid;
            AllocMethodData data = this.getData();
            this.allocMethodXid = AllocMethodColumns.allocMethodXid.convertToDB(allocMethodXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMethodXid", String.class, oldValue, this.allocMethodXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTargetGid() throws GLException {
        try {
            return (String)AllocMethodColumns.allocTargetGid.convertFromDB(this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTargetGid(String allocTargetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTargetGid;
            AllocMethodData data = this.getData();
            this.allocTargetGid = AllocMethodColumns.allocTargetGid.convertToDB(allocTargetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTargetGid", String.class, oldValue, this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)AllocMethodColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            AllocMethodData data = this.getData();
            this.description = AllocMethodColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocMethodColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocMethodData data = this.getData();
            this.domainName = AllocMethodColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsUseShipmentShipUnit() throws GLException {
        try {
            return (Boolean)AllocMethodColumns.isUseShipmentShipUnit.convertFromDB(this.isUseShipmentShipUnit);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsUseShipmentShipUnit(Boolean isUseShipmentShipUnit) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isUseShipmentShipUnit;
            AllocMethodData data = this.getData();
            this.isUseShipmentShipUnit = AllocMethodColumns.isUseShipmentShipUnit.convertToDB(isUseShipmentShipUnit);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isUseShipmentShipUnit", Boolean.class, oldValue, this.isUseShipmentShipUnit);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
