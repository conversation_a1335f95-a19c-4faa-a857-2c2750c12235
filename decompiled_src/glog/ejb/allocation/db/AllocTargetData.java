/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTargetPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocTargetData
extends BeanData {
    public String allocTargetGid;
    public String allocTargetXid;
    public String description;
    public String className;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocTargetData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocTargetGidField = beanDataFields[0];
    public static Field allocTargetXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field classNameField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocTargetData() {
    }

    public AllocTargetData(AllocTargetData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocTargetPK();
    }

    @Legacy
    public AllocTargetPK getAllocTargetPK() {
        if (this.allocTargetGid == null) {
            return null;
        }
        return new AllocTargetPK(this.allocTargetGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocTargetPK((AllocTargetPK)pk);
    }

    @Legacy
    public void setAllocTargetPK(AllocTargetPK pk) {
        this.allocTargetGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocTargetQueryGen";
    }

    public static AllocTargetData load(Connection conn, AllocTargetPK pk) throws GLException {
        return (AllocTargetData)AllocTargetData.load(conn, pk, AllocTargetData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocTargetData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocTargetData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocTargetData.load(conn, whereClause, prepareArguments, fetchSize, AllocTargetData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocTargetData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocTargetData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocTargetData.class);
    }
}
