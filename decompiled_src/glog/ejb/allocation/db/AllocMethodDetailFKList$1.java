/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocMethodDetailFKList.1
implements Fk {
    AllocMethodDetailFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocMethodDetailData";
    }

    @Override
    public String getPkField() {
        return "allocMetricGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocMetricData";
    }

    @Override
    public String getFkDataField() {
        return "allocMetricGid";
    }
}
