/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRulePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationRuleData
extends BeanData {
    public String allocationRuleGid;
    public String allocationRuleXid;
    public String locationProfileGid;
    public String consInvAllocType;
    public String modeProfileGid;
    public String domainName;
    public Boolean isAllocShipment = Boolean.valueOf("true");
    public Boolean isAllocVoucher = Boolean.valueOf("true");
    public Boolean isAllocBill = Boolean.valueOf("true");
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocationRuleGidField = beanDataFields[0];
    public static Field allocationRuleXidField = beanDataFields[1];
    public static Field locationProfileGidField = beanDataFields[2];
    public static Field consInvAllocTypeField = beanDataFields[3];
    public static Field modeProfileGidField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];
    public static Field isAllocShipmentField = beanDataFields[6];
    public static Field isAllocVoucherField = beanDataFields[7];
    public static Field isAllocBillField = beanDataFields[8];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationRuleData() {
    }

    public AllocationRuleData(AllocationRuleData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationRulePK();
    }

    @Legacy
    public AllocationRulePK getAllocationRulePK() {
        if (this.allocationRuleGid == null) {
            return null;
        }
        return new AllocationRulePK(this.allocationRuleGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationRulePK((AllocationRulePK)pk);
    }

    @Legacy
    public void setAllocationRulePK(AllocationRulePK pk) {
        this.allocationRuleGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationRuleQueryGen";
    }

    public static AllocationRuleData load(Connection conn, AllocationRulePK pk) throws GLException {
        return (AllocationRuleData)AllocationRuleData.load(conn, pk, AllocationRuleData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationRuleData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationRuleData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleData.load(conn, whereClause, prepareArguments, fetchSize, AllocationRuleData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationRuleData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationRuleData.class);
    }
}
