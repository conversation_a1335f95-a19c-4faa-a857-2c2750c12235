/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationFKList.6
implements Fk {
    AllocationFKList.6() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationData";
    }

    @Override
    public String getPkField() {
        return "exchangeRateGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "ExchangeRateData";
    }

    @Override
    public String getFkDataField() {
        return "exchangeRateGid";
    }
}
