/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocationRulePK
extends Pk {
    public Object allocationRuleGid;
    public transient Object allocationRuleXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationRulePK() {
    }

    public AllocationRulePK(String allocationRuleGid) {
        this.allocationRuleGid = this.notNull(AllocationRuleColumns.allocationRuleGid.convertToDB(allocationRuleGid), "allocationRuleGid");
    }

    public AllocationRulePK(String domainName, String allocationRuleXid) {
        this.domainName = domainName;
        this.allocationRuleXid = allocationRuleXid;
        this.allocationRuleGid = AllocationRulePK.concatForGid(domainName, allocationRuleXid);
    }

    public AllocationRulePK(int dummy, Object allocationRuleGid) {
        this(dummy, allocationRuleGid, null);
    }

    public AllocationRulePK(int dummy, Object allocationRuleGid, Object transaction) {
        this.allocationRuleGid = allocationRuleGid;
        this.transaction = transaction;
    }

    public AllocationRulePK(AllocationRulePK otherPk, Object transaction) {
        this.allocationRuleGid = otherPk.allocationRuleGid;
        this.allocationRuleXid = otherPk.allocationRuleXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocationRuleGid != null ? String.valueOf(this.allocationRuleGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocationRuleGid != null ? String.valueOf(this.allocationRuleGid) : "";
    }

    public static AllocationRulePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationRulePK(gids[0]) : null;
    }

    public static AllocationRulePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationRulePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.allocationRuleGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationRulePK)) {
            return false;
        }
        AllocationRulePK otherPk = (AllocationRulePK)other;
        return Functions.equals(otherPk.allocationRuleGid, this.allocationRuleGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationRuleHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocationRuleGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationRuleColumns.allocationRuleGid.convertFromDB(this.allocationRuleGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationRulePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AllocationRulePK requires a non-null XID to generate a GID");
            }
            AllocationRulePK allocationRulePK = new AllocationRulePK(domain, xid);
            return allocationRulePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationRulePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocationRulePK allocationRulePK = AllocationRulePK.newPK(domainName, xid, connection);
            return allocationRulePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationRulePK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationRule";
        }

        @Override
        public final String getTableName() {
            return "allocation_rule";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"allocation_rule_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationRulePK result = new AllocationRulePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
