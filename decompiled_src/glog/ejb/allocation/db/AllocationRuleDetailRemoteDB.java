/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleDetailData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationRuleDetailRemoteDB
extends EJBObject {
    public AllocationRuleDetailData getData() throws RemoteException, GLException;

    public void setData(AllocationRuleDetailData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON><PERSON>Ex<PERSON>, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getAllocationRuleGid() throws RemoteException, GLException;

    public void setAllocationRuleGid(String var1) throws RemoteException, GLException;

    public Double getAllocRuleDetailSeq() throws RemoteException, GLException;

    public void setAllocRuleDetailSeq(Double var1) throws RemoteException, GLException;

    public String getBasis() throws RemoteException, GLException;

    public void setBasis(String var1) throws RemoteException, GLException;

    public String getAllocType() throws RemoteException, GLException;

    public void setAllocType(String var1) throws RemoteException, GLException;

    public String getAllocMetricGid() throws RemoteException, GLException;

    public void setAllocMetricGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getCostTypeGid() throws RemoteException, GLException;

    public void setCostTypeGid(String var1) throws RemoteException, GLException;

    public String getAccessorialCodeGid() throws RemoteException, GLException;

    public void setAccessorialCodeGid(String var1) throws RemoteException, GLException;

    public String getAllocMethodGid() throws RemoteException, GLException;

    public void setAllocMethodGid(String var1) throws RemoteException, GLException;
}
