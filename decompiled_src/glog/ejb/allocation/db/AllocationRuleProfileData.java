/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfilePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationRuleProfileData
extends BeanData {
    public String allocationRuleProfileGid;
    public String allocationRuleProfileXid;
    public String description;
    public Boolean isDefault = Boolean.valueOf("false");
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleProfileData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocationRuleProfileGidField = beanDataFields[0];
    public static Field allocationRuleProfileXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field isDefaultField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationRuleProfileData() {
    }

    public AllocationRuleProfileData(AllocationRuleProfileData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationRuleProfilePK();
    }

    @Legacy
    public AllocationRuleProfilePK getAllocationRuleProfilePK() {
        if (this.allocationRuleProfileGid == null) {
            return null;
        }
        return new AllocationRuleProfilePK(this.allocationRuleProfileGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationRuleProfilePK((AllocationRuleProfilePK)pk);
    }

    @Legacy
    public void setAllocationRuleProfilePK(AllocationRuleProfilePK pk) {
        this.allocationRuleProfileGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationRuleProfileQueryGen";
    }

    public static AllocationRuleProfileData load(Connection conn, AllocationRuleProfilePK pk) throws GLException {
        return (AllocationRuleProfileData)AllocationRuleProfileData.load(conn, pk, AllocationRuleProfileData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationRuleProfileData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationRuleProfileData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleProfileData.load(conn, whereClause, prepareArguments, fetchSize, AllocationRuleProfileData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationRuleProfileData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleProfileData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationRuleProfileData.class);
    }
}
