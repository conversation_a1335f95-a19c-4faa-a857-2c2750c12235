/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationDColumns;
import glog.ejb.allocation.db.AllocationDData;
import glog.ejb.allocation.db.AllocationDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationDBeanDB
extends BeanManagedEntityBean {
    public Object allocSeqNo;
    public Object orderReleaseLineGid;
    public Object shipmentGid;
    public Object orderReleaseGid;
    public Object privateCost;
    public Object baseCost;
    public Object totalAllocLineCost;
    public Object packagedItemGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationDPK pk;
    protected transient AllocationDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationDPK.Callback theCall = new AllocationDPK.Callback();

    public AllocationDBeanDB() {
        super(false);
    }

    public AllocationDPK ejbCreate(AllocationDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationDPK ejbFindByPrimaryKey(AllocationDPK pk) throws FinderException {
        return (AllocationDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationDPK> v = new Vector<AllocationDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationDPK pk, AllocationDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationDPK pk, AllocationDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationDPK pk, AllocationDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationDPK pk = (AllocationDPK)genericPk;
        this.allocSeqNo = pk.allocSeqNo;
        this.orderReleaseLineGid = pk.orderReleaseLineGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationDPK pk = new AllocationDPK(0, this.allocSeqNo, this.orderReleaseLineGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationDData getData() throws GLException {
        try {
            AllocationDData retval = new AllocationDData();
            retval.getFromBean(this, AllocationDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationDData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationDData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getAllocSeqNo() throws GLException {
        try {
            return (Integer)AllocationDColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocSeqNo(Integer allocSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocSeqNo;
            AllocationDData data = this.getData();
            this.allocSeqNo = AllocationDColumns.allocSeqNo.convertToDB(allocSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocSeqNo", Integer.class, oldValue, this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOrderReleaseLineGid() throws GLException {
        try {
            return (String)AllocationDColumns.orderReleaseLineGid.convertFromDB(this.orderReleaseLineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOrderReleaseLineGid(String orderReleaseLineGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.orderReleaseLineGid;
            AllocationDData data = this.getData();
            this.orderReleaseLineGid = AllocationDColumns.orderReleaseLineGid.convertToDB(orderReleaseLineGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "orderReleaseLineGid", String.class, oldValue, this.orderReleaseLineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getShipmentGid() throws GLException {
        try {
            return (String)AllocationDColumns.shipmentGid.convertFromDB(this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setShipmentGid(String shipmentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.shipmentGid;
            AllocationDData data = this.getData();
            this.shipmentGid = AllocationDColumns.shipmentGid.convertToDB(shipmentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "shipmentGid", String.class, oldValue, this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOrderReleaseGid() throws GLException {
        try {
            return (String)AllocationDColumns.orderReleaseGid.convertFromDB(this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOrderReleaseGid(String orderReleaseGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.orderReleaseGid;
            AllocationDData data = this.getData();
            this.orderReleaseGid = AllocationDColumns.orderReleaseGid.convertToDB(orderReleaseGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "orderReleaseGid", String.class, oldValue, this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getPrivateCost() throws GLException {
        try {
            return (Currency)AllocationDColumns.privateCost.convertFromDB(this.privateCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPrivateCost(Currency privateCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.privateCost;
            AllocationDData data = this.getData();
            this.privateCost = AllocationDColumns.privateCost.convertToDB(privateCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "privateCost", Currency.class, oldValue, this.privateCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getBaseCost() throws GLException {
        try {
            return (Currency)AllocationDColumns.baseCost.convertFromDB(this.baseCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBaseCost(Currency baseCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.baseCost;
            AllocationDData data = this.getData();
            this.baseCost = AllocationDColumns.baseCost.convertToDB(baseCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "baseCost", Currency.class, oldValue, this.baseCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTotalAllocLineCost() throws GLException {
        try {
            return (Currency)AllocationDColumns.totalAllocLineCost.convertFromDB(this.totalAllocLineCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalAllocLineCost(Currency totalAllocLineCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalAllocLineCost;
            AllocationDData data = this.getData();
            this.totalAllocLineCost = AllocationDColumns.totalAllocLineCost.convertToDB(totalAllocLineCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalAllocLineCost", Currency.class, oldValue, this.totalAllocLineCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getPackagedItemGid() throws GLException {
        try {
            return (String)AllocationDColumns.packagedItemGid.convertFromDB(this.packagedItemGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPackagedItemGid(String packagedItemGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.packagedItemGid;
            AllocationDData data = this.getData();
            this.packagedItemGid = AllocationDColumns.packagedItemGid.convertToDB(packagedItemGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "packagedItemGid", String.class, oldValue, this.packagedItemGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)AllocationDColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            AllocationDData data = this.getData();
            this.exchangeRateDate = AllocationDColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)AllocationDColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            AllocationDData data = this.getData();
            this.exchangeRateGid = AllocationDColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationDData data = this.getData();
            this.domainName = AllocationDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
