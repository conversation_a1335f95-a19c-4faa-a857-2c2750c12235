/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleDetailFKList.2
implements Fk {
    AllocationRuleDetailFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleDetailData";
    }

    @Override
    public String getPkField() {
        return "accessorialCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.rates.db";
    }

    @Override
    public String getFkDataClass() {
        return "AccessorialCodeData";
    }

    @Override
    public String getFkDataField() {
        return "accessorialCodeGid";
    }
}
