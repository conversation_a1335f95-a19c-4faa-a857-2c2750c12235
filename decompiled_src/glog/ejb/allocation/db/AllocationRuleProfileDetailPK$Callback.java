/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileDetailPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class AllocationRuleProfileDetailPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return AllocationRuleProfileDetailPK.class;
    }

    @Override
    public final String getEntity() {
        return "AllocationRuleProfileDetail";
    }

    @Override
    public final String getTableName() {
        return "allocation_rule_profile_detail";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"allocation_rule_profile_gid", "allocation_rule_seq_no"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        AllocationRuleProfileDetailPK result = new AllocationRuleProfileDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
        return result;
    }
}
