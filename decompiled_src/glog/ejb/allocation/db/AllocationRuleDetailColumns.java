/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocationRuleDetailColumns {
    public static SqlColumn allocationRuleGid = new SqlColumn(String.class, "allocation_rule_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocRuleDetailSeq = new SqlColumn(Double.class, "alloc_rule_detail_seq", 22, 3, 1, null, 1, null);
    public static SqlColumn basis = new SqlColumn(String.class, "basis", 50, 0, 0, null, 2, null);
    public static SqlColumn allocType = new SqlColumn(String.class, "alloc_type", 20, 0, 0, null, 3, null);
    public static SqlColumn allocMetricGid = new SqlColumn(String.class, "alloc_metric_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 5, null);
    public static SqlColumn costTypeGid = new SqlColumn(String.class, "cost_type_gid", 101, 0, 0, null, 6, null);
    public static SqlColumn accessorialCodeGid = new SqlColumn(String.class, "accessorial_code_gid", 101, 0, 0, null, 7, null);
    public static SqlColumn allocMethodGid = new SqlColumn(String.class, "alloc_method_gid", 101, 0, 1, null, 8, null);
}
