/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AllocationRuleFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("modeProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationRuleData";
            }

            @Override
            public String getPkField() {
                return "modeProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.modeprofile.db";
            }

            @Override
            public String getFkDataClass() {
                return "ModeProfileData";
            }

            @Override
            public String getFkDataField() {
                return "modeProfileGid";
            }
        });
        fks.put("locationProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationRuleData";
            }

            @Override
            public String getPkField() {
                return "locationProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.location.db";
            }

            @Override
            public String getFkDataClass() {
                return "LocationProfileData";
            }

            @Override
            public String getFkDataField() {
                return "locationProfileGid";
            }
        });
    }
}
