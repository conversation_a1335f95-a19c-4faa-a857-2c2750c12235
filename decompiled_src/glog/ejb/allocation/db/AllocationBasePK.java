/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationBaseColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.PKSequence;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocationBasePK
extends Pk {
    public Object allocSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationBasePK() {
    }

    public AllocationBasePK(Integer allocSeqNo) {
        this.allocSeqNo = this.notNull(AllocationBaseColumns.allocSeqNo.convertToDB(allocSeqNo), "allocSeqNo");
    }

    public AllocationBasePK(int l) {
        this.allocSeqNo = AllocationBaseColumns.allocSeqNo.convertToDB(new Integer(l));
    }

    public AllocationBasePK(String s) {
        this.allocSeqNo = AllocationBaseColumns.allocSeqNo.convertToDB(new Integer(s));
    }

    public AllocationBasePK(int dummy, Object allocSeqNo) {
        this(dummy, allocSeqNo, null);
    }

    public AllocationBasePK(int dummy, Object allocSeqNo, Object transaction) {
        this.allocSeqNo = allocSeqNo;
        this.transaction = transaction;
    }

    public AllocationBasePK(AllocationBasePK otherPk, Object transaction) {
        this.allocSeqNo = otherPk.allocSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "";
    }

    public static AllocationBasePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationBasePK(Integer.valueOf(gids[0])) : null;
    }

    public static AllocationBasePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationBasePK(Integer.valueOf(gids[0])) : null;
    }

    public int hashCode() {
        return this.allocSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationBasePK)) {
            return false;
        }
        AllocationBasePK otherPk = (AllocationBasePK)other;
        return Functions.equals(otherPk.allocSeqNo, this.allocSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationBaseHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationBaseColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationBasePK newPK(T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            AllocationBasePK allocationBasePK = new AllocationBasePK((Integer)PKSequence.next(conn.get(), "allocation_base_sequence", Integer.class));
            return allocationBasePK;
        }
        finally {
            conn.close();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocationBasePK newPK() throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocationBasePK allocationBasePK = AllocationBasePK.newPK(connection);
            return allocationBasePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationBasePK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocationBase";
        }

        @Override
        public final String getTableName() {
            return "allocation_base";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationBasePK result = new AllocationBasePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
