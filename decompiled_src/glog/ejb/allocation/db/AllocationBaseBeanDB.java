/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationBaseColumns;
import glog.ejb.allocation.db.AllocationBaseData;
import glog.ejb.allocation.db.AllocationBasePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationBaseBeanDB
extends BeanManagedEntityBean {
    public Object allocSeqNo;
    public Object shipmentGid;
    public Object allocTypeQualGid;
    public Object invoiceGid;
    public Object timestamp;
    public Object allocatedCost;
    public Object parentInvoiceGid;
    public Object voucherGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationBasePK pk;
    protected transient AllocationBaseData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationBaseBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationBasePK.Callback theCall = new AllocationBasePK.Callback();

    public AllocationBaseBeanDB() {
        super(false);
    }

    public AllocationBasePK ejbCreate(AllocationBaseData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocationBasePK newPK() throws GLException {
        return AllocationBasePK.newPK(this.getConnection());
    }

    public void ejbPostCreate(AllocationBaseData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationBasePK ejbFindByPrimaryKey(AllocationBasePK pk) throws FinderException {
        return (AllocationBasePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationBasePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationBasePK> v = new Vector<AllocationBasePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationBasePK pk, AllocationBaseData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationBasePK pk, AllocationBaseData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationBasePK pk, AllocationBaseData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationBaseColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationBaseColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationBaseColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationBaseColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationBasePK pk = (AllocationBasePK)genericPk;
        this.allocSeqNo = pk.allocSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationBasePK pk = new AllocationBasePK(0, this.allocSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationBase";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationBaseData getData() throws GLException {
        try {
            AllocationBaseData retval = new AllocationBaseData();
            retval.getFromBean(this, AllocationBaseColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationBaseData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationBaseData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationBaseData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationBaseColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationBaseData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationBasePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getAllocSeqNo() throws GLException {
        try {
            return (Integer)AllocationBaseColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocSeqNo(Integer allocSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocSeqNo;
            AllocationBaseData data = this.getData();
            this.allocSeqNo = AllocationBaseColumns.allocSeqNo.convertToDB(allocSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocSeqNo", Integer.class, oldValue, this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getShipmentGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.shipmentGid.convertFromDB(this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setShipmentGid(String shipmentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.shipmentGid;
            AllocationBaseData data = this.getData();
            this.shipmentGid = AllocationBaseColumns.shipmentGid.convertToDB(shipmentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "shipmentGid", String.class, oldValue, this.shipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTypeQualGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.allocTypeQualGid.convertFromDB(this.allocTypeQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTypeQualGid(String allocTypeQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTypeQualGid;
            AllocationBaseData data = this.getData();
            this.allocTypeQualGid = AllocationBaseColumns.allocTypeQualGid.convertToDB(allocTypeQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTypeQualGid", String.class, oldValue, this.allocTypeQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            AllocationBaseData data = this.getData();
            this.invoiceGid = AllocationBaseColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Timestamp getTimestamp() throws GLException {
        try {
            return (Timestamp)AllocationBaseColumns.timestamp.convertFromDB(this.timestamp);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTimestamp(Timestamp timestamp) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.timestamp;
            AllocationBaseData data = this.getData();
            this.timestamp = AllocationBaseColumns.timestamp.convertToDB(timestamp);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "timestamp", Timestamp.class, oldValue, this.timestamp);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getAllocatedCost() throws GLException {
        try {
            return (Currency)AllocationBaseColumns.allocatedCost.convertFromDB(this.allocatedCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocatedCost(Currency allocatedCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocatedCost;
            AllocationBaseData data = this.getData();
            this.allocatedCost = AllocationBaseColumns.allocatedCost.convertToDB(allocatedCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocatedCost", Currency.class, oldValue, this.allocatedCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getParentInvoiceGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.parentInvoiceGid.convertFromDB(this.parentInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setParentInvoiceGid(String parentInvoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.parentInvoiceGid;
            AllocationBaseData data = this.getData();
            this.parentInvoiceGid = AllocationBaseColumns.parentInvoiceGid.convertToDB(parentInvoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "parentInvoiceGid", String.class, oldValue, this.parentInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVoucherGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.voucherGid.convertFromDB(this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherGid(String voucherGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherGid;
            AllocationBaseData data = this.getData();
            this.voucherGid = AllocationBaseColumns.voucherGid.convertToDB(voucherGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherGid", String.class, oldValue, this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)AllocationBaseColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            AllocationBaseData data = this.getData();
            this.exchangeRateDate = AllocationBaseColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)AllocationBaseColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            AllocationBaseData data = this.getData();
            this.exchangeRateGid = AllocationBaseColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationBaseColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationBaseData data = this.getData();
            this.domainName = AllocationBaseColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
