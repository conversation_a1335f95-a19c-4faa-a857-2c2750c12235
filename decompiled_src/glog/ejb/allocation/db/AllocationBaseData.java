/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationBasePK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationBaseData
extends BeanData {
    public Integer allocSeqNo;
    public String shipmentGid;
    public String allocTypeQualGid;
    public String invoiceGid;
    public Timestamp timestamp;
    public Currency allocatedCost;
    public String parentInvoiceGid;
    public String voucherGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationBaseData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocSeqNoField = beanDataFields[0];
    public static Field shipmentGidField = beanDataFields[1];
    public static Field allocTypeQualGidField = beanDataFields[2];
    public static Field invoiceGidField = beanDataFields[3];
    public static Field timestampField = beanDataFields[4];
    public static Field allocatedCostField = beanDataFields[5];
    public static Field parentInvoiceGidField = beanDataFields[6];
    public static Field voucherGidField = beanDataFields[7];
    public static Field exchangeRateDateField = beanDataFields[8];
    public static Field exchangeRateGidField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationBaseData() {
    }

    public AllocationBaseData(AllocationBaseData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationBasePK();
    }

    @Legacy
    public AllocationBasePK getAllocationBasePK() {
        if (this.allocSeqNo == null) {
            return null;
        }
        return new AllocationBasePK(this.allocSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationBasePK((AllocationBasePK)pk);
    }

    @Legacy
    public void setAllocationBasePK(AllocationBasePK pk) {
        this.allocSeqNo = (Integer)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationBaseQueryGen";
    }

    public static AllocationBaseData load(Connection conn, AllocationBasePK pk) throws GLException {
        return (AllocationBaseData)AllocationBaseData.load(conn, pk, AllocationBaseData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationBaseData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationBaseData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationBaseData.load(conn, whereClause, prepareArguments, fetchSize, AllocationBaseData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationBaseData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationBaseData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationBaseData.class);
    }
}
