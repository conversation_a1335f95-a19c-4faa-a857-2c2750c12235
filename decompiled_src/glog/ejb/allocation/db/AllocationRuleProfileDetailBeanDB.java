/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationRuleProfileDetailColumns;
import glog.ejb.allocation.db.AllocationRuleProfileDetailData;
import glog.ejb.allocation.db.AllocationRuleProfileDetailPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationRuleProfileDetailBeanDB
extends BeanManagedEntityBean {
    public Object allocationRuleProfileGid;
    public Object allocationRuleSeqNo;
    public Object allocationRuleGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationRuleProfileDetailPK pk;
    protected transient AllocationRuleProfileDetailData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleProfileDetailBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationRuleProfileDetailPK.Callback theCall = new AllocationRuleProfileDetailPK.Callback();

    public AllocationRuleProfileDetailBeanDB() {
        super(false);
    }

    public AllocationRuleProfileDetailPK ejbCreate(AllocationRuleProfileDetailData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationRuleProfileDetailData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationRuleProfileDetailPK ejbFindByPrimaryKey(AllocationRuleProfileDetailPK pk) throws FinderException {
        return (AllocationRuleProfileDetailPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationRuleProfileDetailPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationRuleProfileDetailPK> v = new Vector<AllocationRuleProfileDetailPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationRuleProfileDetailPK pk, AllocationRuleProfileDetailData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationRuleProfileDetailPK pk, AllocationRuleProfileDetailData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationRuleProfileDetailPK pk, AllocationRuleProfileDetailData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationRuleProfileDetailPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationRuleProfileDetailColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationRuleProfileDetailColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationRuleProfileDetailColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationRuleProfileDetailColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationRuleProfileDetailPK pk = (AllocationRuleProfileDetailPK)genericPk;
        this.allocationRuleProfileGid = pk.allocationRuleProfileGid;
        this.allocationRuleSeqNo = pk.allocationRuleSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationRuleProfileDetailPK pk = new AllocationRuleProfileDetailPK(0, this.allocationRuleProfileGid, this.allocationRuleSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationRuleProfileDetail";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationRuleProfileDetailData getData() throws GLException {
        try {
            AllocationRuleProfileDetailData retval = new AllocationRuleProfileDetailData();
            retval.getFromBean(this, AllocationRuleProfileDetailColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationRuleProfileDetailData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationRuleProfileDetailData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationRuleProfileDetailData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationRuleProfileDetailColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationRuleProfileDetailData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationRuleProfileDetailPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocationRuleProfileGid() throws GLException {
        try {
            return (String)AllocationRuleProfileDetailColumns.allocationRuleProfileGid.convertFromDB(this.allocationRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleProfileGid(String allocationRuleProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleProfileGid;
            AllocationRuleProfileDetailData data = this.getData();
            this.allocationRuleProfileGid = AllocationRuleProfileDetailColumns.allocationRuleProfileGid.convertToDB(allocationRuleProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleProfileGid", String.class, oldValue, this.allocationRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getAllocationRuleSeqNo() throws GLException {
        try {
            return (Integer)AllocationRuleProfileDetailColumns.allocationRuleSeqNo.convertFromDB(this.allocationRuleSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleSeqNo(Integer allocationRuleSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleSeqNo;
            AllocationRuleProfileDetailData data = this.getData();
            this.allocationRuleSeqNo = AllocationRuleProfileDetailColumns.allocationRuleSeqNo.convertToDB(allocationRuleSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleSeqNo", Integer.class, oldValue, this.allocationRuleSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocationRuleGid() throws GLException {
        try {
            return (String)AllocationRuleProfileDetailColumns.allocationRuleGid.convertFromDB(this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleGid(String allocationRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleGid;
            AllocationRuleProfileDetailData data = this.getData();
            this.allocationRuleGid = AllocationRuleProfileDetailColumns.allocationRuleGid.convertToDB(allocationRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleGid", String.class, oldValue, this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationRuleProfileDetailColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationRuleProfileDetailData data = this.getData();
            this.domainName = AllocationRuleProfileDetailColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
