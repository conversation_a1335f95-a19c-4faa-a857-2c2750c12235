/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocMethodPK
extends Pk {
    public Object allocMethodGid;
    public transient Object allocMethodXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocMethodPK() {
    }

    public AllocMethodPK(String allocMethodGid) {
        this.allocMethodGid = this.notNull(AllocMethodColumns.allocMethodGid.convertToDB(allocMethodGid), "allocMethodGid");
    }

    public AllocMethodPK(String domainName, String allocMethodXid) {
        this.domainName = domainName;
        this.allocMethodXid = allocMethodXid;
        this.allocMethodGid = AllocMethodPK.concatForGid(domainName, allocMethodXid);
    }

    public AllocMethodPK(int dummy, Object allocMethodGid) {
        this(dummy, allocMethodGid, null);
    }

    public AllocMethodPK(int dummy, Object allocMethodGid, Object transaction) {
        this.allocMethodGid = allocMethodGid;
        this.transaction = transaction;
    }

    public AllocMethodPK(AllocMethodPK otherPk, Object transaction) {
        this.allocMethodGid = otherPk.allocMethodGid;
        this.allocMethodXid = otherPk.allocMethodXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocMethodGid != null ? String.valueOf(this.allocMethodGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocMethodGid != null ? String.valueOf(this.allocMethodGid) : "";
    }

    public static AllocMethodPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocMethodPK(gids[0]) : null;
    }

    public static AllocMethodPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocMethodPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.allocMethodGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocMethodPK)) {
            return false;
        }
        AllocMethodPK otherPk = (AllocMethodPK)other;
        return Functions.equals(otherPk.allocMethodGid, this.allocMethodGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocMethodHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocMethodGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocMethodColumns.allocMethodGid.convertFromDB(this.allocMethodGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocMethodPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AllocMethodPK requires a non-null XID to generate a GID");
            }
            AllocMethodPK allocMethodPK = new AllocMethodPK(domain, xid);
            return allocMethodPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocMethodPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocMethodPK allocMethodPK = AllocMethodPK.newPK(domainName, xid, connection);
            return allocMethodPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocMethodPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocMethod";
        }

        @Override
        public final String getTableName() {
            return "alloc_method";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_method_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocMethodPK result = new AllocMethodPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
