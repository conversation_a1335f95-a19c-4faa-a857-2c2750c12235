/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;
import java.sql.Timestamp;

public class AllocationBaseColumns {
    public static SqlColumn allocSeqNo = new SqlColumn(Integer.class, "alloc_seq_no", 8, 0, 1, null, 0, null);
    public static SqlColumn shipmentGid = new SqlColumn(String.class, "shipment_gid", 101, 0, 0, null, 1, null);
    public static SqlColumn allocTypeQualGid = new SqlColumn(String.class, "alloc_type_qual_gid", 50, 0, 1, null, 2, null);
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 0, null, 3, null);
    public static SqlColumn timestamp = new SqlColumn(Timestamp.class, "timestamp", 7, 0, 1, null, 4, null);
    public static SqlColumn allocatedCost = new SqlColumn(Currency.class, "allocated_cost", 22, 2, 0, null, 5, "allocated_cost_base");
    public static SqlColumn parentInvoiceGid = new SqlColumn(String.class, "parent_invoice_gid", 101, 0, 0, null, 6, null);
    public static SqlColumn voucherGid = new SqlColumn(String.class, "voucher_gid", 101, 0, 0, null, 7, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 8, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 9, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
}
