/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocMethodData
extends BeanData {
    public String allocMethodGid;
    public String allocMethodXid;
    public String allocTargetGid;
    public String description;
    public String domainName;
    public Boolean isUseShipmentShipUnit = Boolean.valueOf("false");
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMethodData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocMethodGidField = beanDataFields[0];
    public static Field allocMethodXidField = beanDataFields[1];
    public static Field allocTargetGidField = beanDataFields[2];
    public static Field descriptionField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];
    public static Field isUseShipmentShipUnitField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocMethodData() {
    }

    public AllocMethodData(AllocMethodData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocMethodPK();
    }

    @Legacy
    public AllocMethodPK getAllocMethodPK() {
        if (this.allocMethodGid == null) {
            return null;
        }
        return new AllocMethodPK(this.allocMethodGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocMethodPK((AllocMethodPK)pk);
    }

    @Legacy
    public void setAllocMethodPK(AllocMethodPK pk) {
        this.allocMethodGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocMethodQueryGen";
    }

    public static AllocMethodData load(Connection conn, AllocMethodPK pk) throws GLException {
        return (AllocMethodData)AllocMethodData.load(conn, pk, AllocMethodData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocMethodData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocMethodData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMethodData.load(conn, whereClause, prepareArguments, fetchSize, AllocMethodData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocMethodData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMethodData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocMethodData.class);
    }
}
