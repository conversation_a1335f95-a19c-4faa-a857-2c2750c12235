/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationBaseFKList.3
implements Fk {
    AllocationBaseFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationBaseData";
    }

    @Override
    public String getPkField() {
        return "parentInvoiceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceGid";
    }
}
