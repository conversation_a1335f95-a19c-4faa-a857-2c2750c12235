/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleDetailPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationRuleDetailData
extends BeanData {
    public String allocationRuleGid;
    public Double allocRuleDetailSeq;
    public String basis;
    public String allocType;
    public String allocMetricGid;
    public String domainName;
    public String costTypeGid;
    public String accessorialCodeGid;
    public String allocMethodGid;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocationRuleGidField = beanDataFields[0];
    public static Field allocRuleDetailSeqField = beanDataFields[1];
    public static Field basisField = beanDataFields[2];
    public static Field allocTypeField = beanDataFields[3];
    public static Field allocMetricGidField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];
    public static Field costTypeGidField = beanDataFields[6];
    public static Field accessorialCodeGidField = beanDataFields[7];
    public static Field allocMethodGidField = beanDataFields[8];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationRuleDetailData() {
    }

    public AllocationRuleDetailData(AllocationRuleDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationRuleDetailPK();
    }

    @Legacy
    public AllocationRuleDetailPK getAllocationRuleDetailPK() {
        if (this.allocationRuleGid == null) {
            return null;
        }
        if (this.allocRuleDetailSeq == null) {
            return null;
        }
        return new AllocationRuleDetailPK(this.allocationRuleGid, this.allocRuleDetailSeq);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationRuleDetailPK((AllocationRuleDetailPK)pk);
    }

    @Legacy
    public void setAllocationRuleDetailPK(AllocationRuleDetailPK pk) {
        this.allocationRuleGid = (String)pk.getAppValue(0);
        this.allocRuleDetailSeq = (Double)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationRuleDetailQueryGen";
    }

    public static AllocationRuleDetailData load(Connection conn, AllocationRuleDetailPK pk) throws GLException {
        return (AllocationRuleDetailData)AllocationRuleDetailData.load(conn, pk, AllocationRuleDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationRuleDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationRuleDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleDetailData.load(conn, whereClause, prepareArguments, fetchSize, AllocationRuleDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationRuleDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationRuleDetailData.class);
    }
}
