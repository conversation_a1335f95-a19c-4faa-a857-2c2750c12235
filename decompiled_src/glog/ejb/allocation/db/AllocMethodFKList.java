/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AllocMethodFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("allocTargetGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocMethodData";
            }

            @Override
            public String getPkField() {
                return "allocTargetGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getFkDataClass() {
                return "AllocTargetData";
            }

            @Override
            public String getFkDataField() {
                return "allocTargetGid";
            }
        });
    }
}
