/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationBaseData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationBaseRemoteDB
extends EJBObject {
    public AllocationBaseData getData() throws RemoteException, GLException;

    public void setData(AllocationBaseData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteEx<PERSON>, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public Integer getAllocSeqNo() throws RemoteException, GLException;

    public void setAllocSeqNo(Integer var1) throws RemoteException, GLException;

    public String getShipmentGid() throws RemoteException, GLException;

    public void setShipmentGid(String var1) throws RemoteException, GLException;

    public String getAllocTypeQualGid() throws RemoteException, GLException;

    public void setAllocTypeQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Timestamp getTimestamp() throws RemoteException, GLException;

    public void setTimestamp(Timestamp var1) throws RemoteException, GLException;

    public Currency getAllocatedCost() throws RemoteException, GLException;

    public void setAllocatedCost(Currency var1) throws RemoteException, GLException;

    public String getParentInvoiceGid() throws RemoteException, GLException;

    public void setParentInvoiceGid(String var1) throws RemoteException, GLException;

    public String getVoucherGid() throws RemoteException, GLException;

    public void setVoucherGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
