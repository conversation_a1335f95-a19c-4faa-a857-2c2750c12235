/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationOrLineDFKList.1
implements Fk {
    AllocationOrLineDFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationOrLineDData";
    }

    @Override
    public String getPkField() {
        return "lineitemSeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getFkDataField() {
        return "lineitemSeqNo";
    }
}
