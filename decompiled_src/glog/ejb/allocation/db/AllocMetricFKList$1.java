/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocMetricFKList.1
implements Fk {
    AllocMetricFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocMetricData";
    }

    @Override
    public String getPkField() {
        return "allocationBasis";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocTargetData";
    }

    @Override
    public String getFkDataField() {
        return "allocTargetGid";
    }
}
