/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMetricData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocMetricRemoteDB
extends EJBObject {
    public AllocMetricData getData() throws RemoteException, GLException;

    public void setData(AllocMetricData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getAllocMetricGid() throws RemoteException, GLException;

    public void setAllocMetricGid(String var1) throws RemoteException, GLException;

    public String getAllocationBasis() throws RemoteException, GLException;

    public void setAllocationBasis(String var1) throws RemoteException, GLException;

    public String getAllocMetricXid() throws RemoteException, GLException;

    public void setAllocMetricXid(String var1) throws RemoteException, GLException;

    public String getClassName() throws RemoteException, GLException;

    public void setClassName(String var1) throws RemoteException, GLException;

    public Boolean getSingleOriginOnly() throws RemoteException, GLException;

    public void setSingleOriginOnly(Boolean var1) throws RemoteException, GLException;

    public Boolean getSingleDestinationOnly() throws RemoteException, GLException;

    public void setSingleDestinationOnly(Boolean var1) throws RemoteException, GLException;

    public Boolean getSingleOrderOnly() throws RemoteException, GLException;

    public void setSingleOrderOnly(Boolean var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
