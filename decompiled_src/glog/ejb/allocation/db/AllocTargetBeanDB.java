/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocTargetColumns;
import glog.ejb.allocation.db.AllocTargetData;
import glog.ejb.allocation.db.AllocTargetPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocTargetBeanDB
extends BeanManagedEntityBean {
    public Object allocTargetGid;
    public Object allocTargetXid;
    public Object description;
    public Object className;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocTargetPK pk;
    protected transient AllocTargetData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocTargetBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocTargetPK.Callback theCall = new AllocTargetPK.Callback();

    public AllocTargetBeanDB() {
        super(false);
    }

    public AllocTargetPK ejbCreate(AllocTargetData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocTargetPK newPK() throws GLException {
        return AllocTargetPK.newPK(this.getDomainName(), this.getAllocTargetXid(), this.getConnection());
    }

    public void ejbPostCreate(AllocTargetData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocTargetPK ejbFindByPrimaryKey(AllocTargetPK pk) throws FinderException {
        return (AllocTargetPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocTargetPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocTargetPK> v = new Vector<AllocTargetPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocTargetPK pk, AllocTargetData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocTargetPK pk, AllocTargetData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocTargetPK pk, AllocTargetData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocTargetColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocTargetColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocTargetColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocTargetColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocTargetPK pk = (AllocTargetPK)genericPk;
        this.allocTargetGid = pk.allocTargetGid;
        this.domainName = pk.domainName;
        this.allocTargetXid = pk.allocTargetXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocTargetPK pk = new AllocTargetPK(0, this.allocTargetGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocTarget";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocTargetData getData() throws GLException {
        try {
            AllocTargetData retval = new AllocTargetData();
            retval.getFromBean(this, AllocTargetColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocTargetData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocTargetData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocTargetData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocTargetColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocTargetData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocTargetPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocTargetGid() throws GLException {
        try {
            return (String)AllocTargetColumns.allocTargetGid.convertFromDB(this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTargetGid(String allocTargetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTargetGid;
            AllocTargetData data = this.getData();
            this.allocTargetGid = AllocTargetColumns.allocTargetGid.convertToDB(allocTargetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTargetGid", String.class, oldValue, this.allocTargetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTargetXid() throws GLException {
        try {
            return (String)AllocTargetColumns.allocTargetXid.convertFromDB(this.allocTargetXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTargetXid(String allocTargetXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTargetXid;
            AllocTargetData data = this.getData();
            this.allocTargetXid = AllocTargetColumns.allocTargetXid.convertToDB(allocTargetXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTargetXid", String.class, oldValue, this.allocTargetXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)AllocTargetColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            AllocTargetData data = this.getData();
            this.description = AllocTargetColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getClassName() throws GLException {
        try {
            return (String)AllocTargetColumns.className.convertFromDB(this.className);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setClassName(String className) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.className;
            AllocTargetData data = this.getData();
            this.className = AllocTargetColumns.className.convertToDB(className);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "className", String.class, oldValue, this.className);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocTargetColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocTargetData data = this.getData();
            this.domainName = AllocTargetColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
