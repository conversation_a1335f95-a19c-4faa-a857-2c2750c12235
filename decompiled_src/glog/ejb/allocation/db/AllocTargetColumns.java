/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocTargetColumns {
    public static SqlColumn allocTargetGid = new SqlColumn(String.class, "alloc_target_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocTargetXid = new SqlColumn(String.class, "alloc_target_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 101, 0, 0, null, 2, null);
    public static SqlColumn className = new SqlColumn(String.class, "class_name", 101, 0, 0, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
