/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMetricPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocMetricData
extends BeanData {
    public String allocMetricGid;
    public String allocationBasis;
    public String allocMetricXid;
    public String className;
    public Boolean singleOriginOnly;
    public Boolean singleDestinationOnly;
    public Boolean singleOrderOnly;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMetricData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocMetricGidField = beanDataFields[0];
    public static Field allocationBasisField = beanDataFields[1];
    public static Field allocMetricXidField = beanDataFields[2];
    public static Field classNameField = beanDataFields[3];
    public static Field singleOriginOnlyField = beanDataFields[4];
    public static Field singleDestinationOnlyField = beanDataFields[5];
    public static Field singleOrderOnlyField = beanDataFields[6];
    public static Field domainNameField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocMetricData() {
    }

    public AllocMetricData(AllocMetricData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocMetricPK();
    }

    @Legacy
    public AllocMetricPK getAllocMetricPK() {
        if (this.allocationBasis == null) {
            return null;
        }
        if (this.allocMetricGid == null) {
            return null;
        }
        return new AllocMetricPK(this.allocationBasis, this.allocMetricGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocMetricPK((AllocMetricPK)pk);
    }

    @Legacy
    public void setAllocMetricPK(AllocMetricPK pk) {
        this.allocationBasis = (String)pk.getAppValue(0);
        this.allocMetricGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocMetricQueryGen";
    }

    public static AllocMetricData load(Connection conn, AllocMetricPK pk) throws GLException {
        return (AllocMetricData)AllocMetricData.load(conn, pk, AllocMetricData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocMetricData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocMetricData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMetricData.load(conn, whereClause, prepareArguments, fetchSize, AllocMetricData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocMetricData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMetricData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocMetricData.class);
    }
}
