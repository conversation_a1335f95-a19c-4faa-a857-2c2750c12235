/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationRuleProfileRemoteDB
extends EJBObject {
    public AllocationRuleProfileData getData() throws RemoteException, GLException;

    public void setData(AllocationRuleProfileData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteEx<PERSON>, GLException;

    public Map verify() throws <PERSON>moteEx<PERSON>, GLEx<PERSON>;

    public String getAllocationRuleProfileGid() throws RemoteException, GLException;

    public void setAllocationRuleProfileGid(String var1) throws RemoteException, GLException;

    public String getAllocationRuleProfileXid() throws RemoteException, GLException;

    public void setAllocationRuleProfileXid(String var1) throws RemoteException, GLException;

    public String getDescription() throws RemoteException, GLException;

    public void setDescription(String var1) throws RemoteException, GLException;

    public Boolean getIsDefault() throws RemoteException, GLException;

    public void setIsDefault(Boolean var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
