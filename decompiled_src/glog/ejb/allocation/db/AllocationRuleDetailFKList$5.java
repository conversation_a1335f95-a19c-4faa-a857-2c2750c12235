/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleDetailFKList.5
implements Fk {
    AllocationRuleDetailFKList.5() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleDetailData";
    }

    @Override
    public String getPkField() {
        return "basis";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocTargetData";
    }

    @Override
    public String getFkDataField() {
        return "allocTargetGid";
    }
}
