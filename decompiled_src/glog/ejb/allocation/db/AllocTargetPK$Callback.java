/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTargetPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class AllocTargetPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return AllocTargetPK.class;
    }

    @Override
    public final String getEntity() {
        return "AllocTarget";
    }

    @Override
    public final String getTableName() {
        return "alloc_target";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"alloc_target_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        AllocTargetPK result = new AllocTargetPK(0, q.getObject(pkOffset + 0 + 1), transaction);
        return result;
    }
}
