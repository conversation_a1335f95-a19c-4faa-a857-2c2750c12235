/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTargetColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocTargetPK
extends Pk {
    public Object allocTargetGid;
    public transient Object allocTargetXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocTargetPK() {
    }

    public AllocTargetPK(String allocTargetGid) {
        this.allocTargetGid = this.notNull(AllocTargetColumns.allocTargetGid.convertToDB(allocTargetGid), "allocTargetGid");
    }

    public AllocTargetPK(String domainName, String allocTargetXid) {
        this.domainName = domainName;
        this.allocTargetXid = allocTargetXid;
        this.allocTargetGid = AllocTargetPK.concatForGid(domainName, allocTargetXid);
    }

    public AllocTargetPK(int dummy, Object allocTargetGid) {
        this(dummy, allocTargetGid, null);
    }

    public AllocTargetPK(int dummy, Object allocTargetGid, Object transaction) {
        this.allocTargetGid = allocTargetGid;
        this.transaction = transaction;
    }

    public AllocTargetPK(AllocTargetPK otherPk, Object transaction) {
        this.allocTargetGid = otherPk.allocTargetGid;
        this.allocTargetXid = otherPk.allocTargetXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocTargetGid != null ? String.valueOf(this.allocTargetGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocTargetGid != null ? String.valueOf(this.allocTargetGid) : "";
    }

    public static AllocTargetPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocTargetPK(gids[0]) : null;
    }

    public static AllocTargetPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocTargetPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.allocTargetGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocTargetPK)) {
            return false;
        }
        AllocTargetPK otherPk = (AllocTargetPK)other;
        return Functions.equals(otherPk.allocTargetGid, this.allocTargetGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocTargetHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocTargetGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocTargetColumns.allocTargetGid.convertFromDB(this.allocTargetGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocTargetPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AllocTargetPK requires a non-null XID to generate a GID");
            }
            AllocTargetPK allocTargetPK = new AllocTargetPK(domain, xid);
            return allocTargetPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocTargetPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocTargetPK allocTargetPK = AllocTargetPK.newPK(domainName, xid, connection);
            return allocTargetPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocTargetPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocTarget";
        }

        @Override
        public final String getTableName() {
            return "alloc_target";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_target_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocTargetPK result = new AllocTargetPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
