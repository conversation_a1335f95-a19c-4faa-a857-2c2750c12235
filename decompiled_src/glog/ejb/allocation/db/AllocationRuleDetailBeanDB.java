/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationRuleDetailColumns;
import glog.ejb.allocation.db.AllocationRuleDetailData;
import glog.ejb.allocation.db.AllocationRuleDetailPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationRuleDetailBeanDB
extends BeanManagedEntityBean {
    public Object allocationRuleGid;
    public Object allocRuleDetailSeq;
    public Object basis;
    public Object allocType;
    public Object allocMetricGid;
    public Object domainName;
    public Object costTypeGid;
    public Object accessorialCodeGid;
    public Object allocMethodGid;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationRuleDetailPK pk;
    protected transient AllocationRuleDetailData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleDetailBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationRuleDetailPK.Callback theCall = new AllocationRuleDetailPK.Callback();

    public AllocationRuleDetailBeanDB() {
        super(false);
    }

    public AllocationRuleDetailPK ejbCreate(AllocationRuleDetailData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationRuleDetailData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationRuleDetailPK ejbFindByPrimaryKey(AllocationRuleDetailPK pk) throws FinderException {
        return (AllocationRuleDetailPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationRuleDetailPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationRuleDetailPK> v = new Vector<AllocationRuleDetailPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationRuleDetailPK pk, AllocationRuleDetailData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationRuleDetailPK pk, AllocationRuleDetailData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationRuleDetailPK pk, AllocationRuleDetailData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationRuleDetailPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationRuleDetailColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationRuleDetailPK pk = (AllocationRuleDetailPK)genericPk;
        this.allocationRuleGid = pk.allocationRuleGid;
        this.allocRuleDetailSeq = pk.allocRuleDetailSeq;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationRuleDetailPK pk = new AllocationRuleDetailPK(0, this.allocationRuleGid, this.allocRuleDetailSeq, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationRuleDetail";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationRuleDetailData getData() throws GLException {
        try {
            AllocationRuleDetailData retval = new AllocationRuleDetailData();
            retval.getFromBean(this, AllocationRuleDetailColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationRuleDetailData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationRuleDetailData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationRuleDetailData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationRuleDetailColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationRuleDetailData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationRuleDetailPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocationRuleGid() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.allocationRuleGid.convertFromDB(this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocationRuleGid(String allocationRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocationRuleGid;
            AllocationRuleDetailData data = this.getData();
            this.allocationRuleGid = AllocationRuleDetailColumns.allocationRuleGid.convertToDB(allocationRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocationRuleGid", String.class, oldValue, this.allocationRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getAllocRuleDetailSeq() throws GLException {
        try {
            return (Double)AllocationRuleDetailColumns.allocRuleDetailSeq.convertFromDB(this.allocRuleDetailSeq);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocRuleDetailSeq(Double allocRuleDetailSeq) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocRuleDetailSeq;
            AllocationRuleDetailData data = this.getData();
            this.allocRuleDetailSeq = AllocationRuleDetailColumns.allocRuleDetailSeq.convertToDB(allocRuleDetailSeq);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocRuleDetailSeq", Double.class, oldValue, this.allocRuleDetailSeq);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getBasis() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.basis.convertFromDB(this.basis);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBasis(String basis) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.basis;
            AllocationRuleDetailData data = this.getData();
            this.basis = AllocationRuleDetailColumns.basis.convertToDB(basis);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "basis", String.class, oldValue, this.basis);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocType() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.allocType.convertFromDB(this.allocType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocType(String allocType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocType;
            AllocationRuleDetailData data = this.getData();
            this.allocType = AllocationRuleDetailColumns.allocType.convertToDB(allocType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocType", String.class, oldValue, this.allocType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocMetricGid() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.allocMetricGid.convertFromDB(this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMetricGid(String allocMetricGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMetricGid;
            AllocationRuleDetailData data = this.getData();
            this.allocMetricGid = AllocationRuleDetailColumns.allocMetricGid.convertToDB(allocMetricGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMetricGid", String.class, oldValue, this.allocMetricGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationRuleDetailData data = this.getData();
            this.domainName = AllocationRuleDetailColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCostTypeGid() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.costTypeGid.convertFromDB(this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCostTypeGid(String costTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.costTypeGid;
            AllocationRuleDetailData data = this.getData();
            this.costTypeGid = AllocationRuleDetailColumns.costTypeGid.convertToDB(costTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "costTypeGid", String.class, oldValue, this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAccessorialCodeGid() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.accessorialCodeGid.convertFromDB(this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAccessorialCodeGid(String accessorialCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.accessorialCodeGid;
            AllocationRuleDetailData data = this.getData();
            this.accessorialCodeGid = AllocationRuleDetailColumns.accessorialCodeGid.convertToDB(accessorialCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "accessorialCodeGid", String.class, oldValue, this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocMethodGid() throws GLException {
        try {
            return (String)AllocationRuleDetailColumns.allocMethodGid.convertFromDB(this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocMethodGid(String allocMethodGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocMethodGid;
            AllocationRuleDetailData data = this.getData();
            this.allocMethodGid = AllocationRuleDetailColumns.allocMethodGid.convertToDB(allocMethodGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocMethodGid", String.class, oldValue, this.allocMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
