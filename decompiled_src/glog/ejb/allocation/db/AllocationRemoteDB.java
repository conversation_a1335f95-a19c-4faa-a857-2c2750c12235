/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationRemoteDB
extends EJBObject {
    public AllocationData getData() throws RemoteException, GLException;

    public void setData(AllocationData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteEx<PERSON>, GLEx<PERSON>;

    public Integer getAllocSeqNo() throws RemoteException, GLException;

    public void setAllocSeqNo(Integer var1) throws RemoteException, GLException;

    public String getOrderReleaseGid() throws RemoteException, GLException;

    public void setOrderReleaseGid(String var1) throws RemoteException, GLException;

    public String getShipmentGid() throws RemoteException, GLException;

    public void setShipmentGid(String var1) throws RemoteException, GLException;

    public Currency getPrivateCost() throws RemoteException, GLException;

    public void setPrivateCost(Currency var1) throws RemoteException, GLException;

    public Currency getBaseCost() throws RemoteException, GLException;

    public void setBaseCost(Currency var1) throws RemoteException, GLException;

    public Currency getTotalAllocCost() throws RemoteException, GLException;

    public void setTotalAllocCost(Currency var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
