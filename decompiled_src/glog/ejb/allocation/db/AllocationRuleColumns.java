/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocationRuleColumns {
    public static SqlColumn allocationRuleGid = new SqlColumn(String.class, "allocation_rule_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocationRuleXid = new SqlColumn(String.class, "allocation_rule_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn locationProfileGid = new SqlColumn(String.class, "location_profile_gid", 101, 0, 0, null, 2, null);
    public static SqlColumn consInvAllocType = new SqlColumn(String.class, "cons_inv_alloc_type", 20, 0, 0, null, 3, null);
    public static SqlColumn modeProfileGid = new SqlColumn(String.class, "mode_profile_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 5, null);
    public static SqlColumn isAllocShipment = new SqlColumn(Boolean.class, "is_alloc_shipment", 1, 0, 1, Boolean.valueOf("true"), 6, null);
    public static SqlColumn isAllocVoucher = new SqlColumn(Boolean.class, "is_alloc_voucher", 1, 0, 1, Boolean.valueOf("true"), 7, null);
    public static SqlColumn isAllocBill = new SqlColumn(Boolean.class, "is_alloc_bill", 1, 0, 1, Boolean.valueOf("true"), 8, null);
}
