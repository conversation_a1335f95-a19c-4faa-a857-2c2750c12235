/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocTypeQualColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AllocTypeQualPK
extends Pk {
    public Object allocTypeQualGid;
    public transient Object allocTypeQualXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocTypeQualPK() {
    }

    public AllocTypeQualPK(String allocTypeQualGid) {
        this.allocTypeQualGid = this.notNull(AllocTypeQualColumns.allocTypeQualGid.convertToDB(allocTypeQualGid), "allocTypeQualGid");
    }

    public AllocTypeQualPK(String domainName, String allocTypeQualXid) {
        this.domainName = domainName;
        this.allocTypeQualXid = allocTypeQualXid;
        this.allocTypeQualGid = AllocTypeQualPK.concatForGid(domainName, allocTypeQualXid);
    }

    public AllocTypeQualPK(int dummy, Object allocTypeQualGid) {
        this(dummy, allocTypeQualGid, null);
    }

    public AllocTypeQualPK(int dummy, Object allocTypeQualGid, Object transaction) {
        this.allocTypeQualGid = allocTypeQualGid;
        this.transaction = transaction;
    }

    public AllocTypeQualPK(AllocTypeQualPK otherPk, Object transaction) {
        this.allocTypeQualGid = otherPk.allocTypeQualGid;
        this.allocTypeQualXid = otherPk.allocTypeQualXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.allocTypeQualGid != null ? String.valueOf(this.allocTypeQualGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.allocTypeQualGid != null ? String.valueOf(this.allocTypeQualGid) : "";
    }

    public static AllocTypeQualPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocTypeQualPK(gids[0]) : null;
    }

    public static AllocTypeQualPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocTypeQualPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.allocTypeQualGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocTypeQualPK)) {
            return false;
        }
        AllocTypeQualPK otherPk = (AllocTypeQualPK)other;
        return Functions.equals(otherPk.allocTypeQualGid, this.allocTypeQualGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocTypeQualHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocTypeQualGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocTypeQualColumns.allocTypeQualGid.convertFromDB(this.allocTypeQualGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocTypeQualPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AllocTypeQualPK requires a non-null XID to generate a GID");
            }
            AllocTypeQualPK allocTypeQualPK = new AllocTypeQualPK(domain, xid);
            return allocTypeQualPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AllocTypeQualPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AllocTypeQualPK allocTypeQualPK = AllocTypeQualPK.newPK(domainName, xid, connection);
            return allocTypeQualPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocTypeQualPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocTypeQual";
        }

        @Override
        public final String getTableName() {
            return "alloc_type_qual";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_type_qual_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocTypeQualPK result = new AllocTypeQualPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
