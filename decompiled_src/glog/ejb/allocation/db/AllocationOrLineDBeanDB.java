/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationOrLineDColumns;
import glog.ejb.allocation.db.AllocationOrLineDData;
import glog.ejb.allocation.db.AllocationOrLineDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationOrLineDBeanDB
extends BeanManagedEntityBean {
    public Object allocSeqNo;
    public Object allocCostSeqno;
    public Object orderReleaseLineGid;
    public Object costDescription;
    public Object accessorialCodeGid;
    public Object cost;
    public Object generalLedgerGid;
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object costTypeGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationOrLineDPK pk;
    protected transient AllocationOrLineDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationOrLineDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationOrLineDPK.Callback theCall = new AllocationOrLineDPK.Callback();

    public AllocationOrLineDBeanDB() {
        super(false);
    }

    public AllocationOrLineDPK ejbCreate(AllocationOrLineDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationOrLineDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationOrLineDPK ejbFindByPrimaryKey(AllocationOrLineDPK pk) throws FinderException {
        return (AllocationOrLineDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationOrLineDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationOrLineDPK> v = new Vector<AllocationOrLineDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationOrLineDPK pk, AllocationOrLineDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationOrLineDPK pk, AllocationOrLineDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationOrLineDPK pk, AllocationOrLineDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationOrLineDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationOrLineDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationOrLineDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationOrLineDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationOrLineDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationOrLineDPK pk = (AllocationOrLineDPK)genericPk;
        this.allocCostSeqno = pk.allocCostSeqno;
        this.allocSeqNo = pk.allocSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationOrLineDPK pk = new AllocationOrLineDPK(0, this.allocCostSeqno, this.allocSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationOrLineD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationOrLineDData getData() throws GLException {
        try {
            AllocationOrLineDData retval = new AllocationOrLineDData();
            retval.getFromBean(this, AllocationOrLineDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationOrLineDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationOrLineDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationOrLineDData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationOrLineDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationOrLineDData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationOrLineDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getAllocSeqNo() throws GLException {
        try {
            return (Integer)AllocationOrLineDColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocSeqNo(Integer allocSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocSeqNo;
            AllocationOrLineDData data = this.getData();
            this.allocSeqNo = AllocationOrLineDColumns.allocSeqNo.convertToDB(allocSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocSeqNo", Integer.class, oldValue, this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getAllocCostSeqno() throws GLException {
        try {
            return (Integer)AllocationOrLineDColumns.allocCostSeqno.convertFromDB(this.allocCostSeqno);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocCostSeqno(Integer allocCostSeqno) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocCostSeqno;
            AllocationOrLineDData data = this.getData();
            this.allocCostSeqno = AllocationOrLineDColumns.allocCostSeqno.convertToDB(allocCostSeqno);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocCostSeqno", Integer.class, oldValue, this.allocCostSeqno);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOrderReleaseLineGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.orderReleaseLineGid.convertFromDB(this.orderReleaseLineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOrderReleaseLineGid(String orderReleaseLineGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.orderReleaseLineGid;
            AllocationOrLineDData data = this.getData();
            this.orderReleaseLineGid = AllocationOrLineDColumns.orderReleaseLineGid.convertToDB(orderReleaseLineGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "orderReleaseLineGid", String.class, oldValue, this.orderReleaseLineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCostDescription() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.costDescription.convertFromDB(this.costDescription);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCostDescription(String costDescription) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.costDescription;
            AllocationOrLineDData data = this.getData();
            this.costDescription = AllocationOrLineDColumns.costDescription.convertToDB(costDescription);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "costDescription", String.class, oldValue, this.costDescription);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAccessorialCodeGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.accessorialCodeGid.convertFromDB(this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAccessorialCodeGid(String accessorialCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.accessorialCodeGid;
            AllocationOrLineDData data = this.getData();
            this.accessorialCodeGid = AllocationOrLineDColumns.accessorialCodeGid.convertToDB(accessorialCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "accessorialCodeGid", String.class, oldValue, this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getCost() throws GLException {
        try {
            return (Currency)AllocationOrLineDColumns.cost.convertFromDB(this.cost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCost(Currency cost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cost;
            AllocationOrLineDData data = this.getData();
            this.cost = AllocationOrLineDColumns.cost.convertToDB(cost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cost", Currency.class, oldValue, this.cost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGeneralLedgerGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.generalLedgerGid.convertFromDB(this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGeneralLedgerGid(String generalLedgerGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.generalLedgerGid;
            AllocationOrLineDData data = this.getData();
            this.generalLedgerGid = AllocationOrLineDColumns.generalLedgerGid.convertToDB(generalLedgerGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "generalLedgerGid", String.class, oldValue, this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            AllocationOrLineDData data = this.getData();
            this.invoiceGid = AllocationOrLineDColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getLineitemSeqNo() throws GLException {
        try {
            return (Integer)AllocationOrLineDColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineitemSeqNo(Integer lineitemSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineitemSeqNo;
            AllocationOrLineDData data = this.getData();
            this.lineitemSeqNo = AllocationOrLineDColumns.lineitemSeqNo.convertToDB(lineitemSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineitemSeqNo", Integer.class, oldValue, this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)AllocationOrLineDColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            AllocationOrLineDData data = this.getData();
            this.exchangeRateDate = AllocationOrLineDColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            AllocationOrLineDData data = this.getData();
            this.exchangeRateGid = AllocationOrLineDColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCostTypeGid() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.costTypeGid.convertFromDB(this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCostTypeGid(String costTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.costTypeGid;
            AllocationOrLineDData data = this.getData();
            this.costTypeGid = AllocationOrLineDColumns.costTypeGid.convertToDB(costTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "costTypeGid", String.class, oldValue, this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationOrLineDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationOrLineDData data = this.getData();
            this.domainName = AllocationOrLineDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
