/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocationOrderReleaseDColumns;
import glog.ejb.allocation.db.AllocationOrderReleaseDData;
import glog.ejb.allocation.db.AllocationOrderReleaseDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocationOrderReleaseDBeanDB
extends BeanManagedEntityBean {
    public Object allocSeqNo;
    public Object allocCostSeqno;
    public Object orderReleaseGid;
    public Object costDescription;
    public Object accessorialCodeGid;
    public Object cost;
    public Object generalLedgerGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object costTypeGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocationOrderReleaseDPK pk;
    protected transient AllocationOrderReleaseDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationOrderReleaseDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocationOrderReleaseDPK.Callback theCall = new AllocationOrderReleaseDPK.Callback();

    public AllocationOrderReleaseDBeanDB() {
        super(false);
    }

    public AllocationOrderReleaseDPK ejbCreate(AllocationOrderReleaseDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AllocationOrderReleaseDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocationOrderReleaseDPK ejbFindByPrimaryKey(AllocationOrderReleaseDPK pk) throws FinderException {
        return (AllocationOrderReleaseDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocationOrderReleaseDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocationOrderReleaseDPK> v = new Vector<AllocationOrderReleaseDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocationOrderReleaseDPK pk, AllocationOrderReleaseDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocationOrderReleaseDPK pk, AllocationOrderReleaseDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocationOrderReleaseDPK pk, AllocationOrderReleaseDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AllocationOrderReleaseDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocationOrderReleaseDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocationOrderReleaseDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocationOrderReleaseDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocationOrderReleaseDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocationOrderReleaseDPK pk = (AllocationOrderReleaseDPK)genericPk;
        this.allocCostSeqno = pk.allocCostSeqno;
        this.allocSeqNo = pk.allocSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocationOrderReleaseDPK pk = new AllocationOrderReleaseDPK(0, this.allocCostSeqno, this.allocSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocationOrderReleaseD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocationOrderReleaseDData getData() throws GLException {
        try {
            AllocationOrderReleaseDData retval = new AllocationOrderReleaseDData();
            retval.getFromBean(this, AllocationOrderReleaseDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocationOrderReleaseDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocationOrderReleaseDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocationOrderReleaseDData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocationOrderReleaseDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocationOrderReleaseDData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocationOrderReleaseDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getAllocSeqNo() throws GLException {
        try {
            return (Integer)AllocationOrderReleaseDColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocSeqNo(Integer allocSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocSeqNo;
            AllocationOrderReleaseDData data = this.getData();
            this.allocSeqNo = AllocationOrderReleaseDColumns.allocSeqNo.convertToDB(allocSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocSeqNo", Integer.class, oldValue, this.allocSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getAllocCostSeqno() throws GLException {
        try {
            return (Integer)AllocationOrderReleaseDColumns.allocCostSeqno.convertFromDB(this.allocCostSeqno);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocCostSeqno(Integer allocCostSeqno) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocCostSeqno;
            AllocationOrderReleaseDData data = this.getData();
            this.allocCostSeqno = AllocationOrderReleaseDColumns.allocCostSeqno.convertToDB(allocCostSeqno);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocCostSeqno", Integer.class, oldValue, this.allocCostSeqno);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOrderReleaseGid() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.orderReleaseGid.convertFromDB(this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOrderReleaseGid(String orderReleaseGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.orderReleaseGid;
            AllocationOrderReleaseDData data = this.getData();
            this.orderReleaseGid = AllocationOrderReleaseDColumns.orderReleaseGid.convertToDB(orderReleaseGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "orderReleaseGid", String.class, oldValue, this.orderReleaseGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCostDescription() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.costDescription.convertFromDB(this.costDescription);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCostDescription(String costDescription) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.costDescription;
            AllocationOrderReleaseDData data = this.getData();
            this.costDescription = AllocationOrderReleaseDColumns.costDescription.convertToDB(costDescription);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "costDescription", String.class, oldValue, this.costDescription);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAccessorialCodeGid() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.accessorialCodeGid.convertFromDB(this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAccessorialCodeGid(String accessorialCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.accessorialCodeGid;
            AllocationOrderReleaseDData data = this.getData();
            this.accessorialCodeGid = AllocationOrderReleaseDColumns.accessorialCodeGid.convertToDB(accessorialCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "accessorialCodeGid", String.class, oldValue, this.accessorialCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getCost() throws GLException {
        try {
            return (Currency)AllocationOrderReleaseDColumns.cost.convertFromDB(this.cost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCost(Currency cost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cost;
            AllocationOrderReleaseDData data = this.getData();
            this.cost = AllocationOrderReleaseDColumns.cost.convertToDB(cost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cost", Currency.class, oldValue, this.cost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGeneralLedgerGid() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.generalLedgerGid.convertFromDB(this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGeneralLedgerGid(String generalLedgerGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.generalLedgerGid;
            AllocationOrderReleaseDData data = this.getData();
            this.generalLedgerGid = AllocationOrderReleaseDColumns.generalLedgerGid.convertToDB(generalLedgerGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "generalLedgerGid", String.class, oldValue, this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)AllocationOrderReleaseDColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            AllocationOrderReleaseDData data = this.getData();
            this.exchangeRateDate = AllocationOrderReleaseDColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            AllocationOrderReleaseDData data = this.getData();
            this.exchangeRateGid = AllocationOrderReleaseDColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCostTypeGid() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.costTypeGid.convertFromDB(this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCostTypeGid(String costTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.costTypeGid;
            AllocationOrderReleaseDData data = this.getData();
            this.costTypeGid = AllocationOrderReleaseDColumns.costTypeGid.convertToDB(costTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "costTypeGid", String.class, oldValue, this.costTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocationOrderReleaseDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocationOrderReleaseDData data = this.getData();
            this.domainName = AllocationOrderReleaseDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
