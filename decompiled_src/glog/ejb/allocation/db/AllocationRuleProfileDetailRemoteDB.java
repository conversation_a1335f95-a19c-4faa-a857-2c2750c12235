/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileDetailData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationRuleProfileDetailRemoteDB
extends EJBObject {
    public AllocationRuleProfileDetailData getData() throws RemoteException, GLException;

    public void setData(AllocationRuleProfileDetailData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getAllocationRuleProfileGid() throws RemoteException, GLException;

    public void setAllocationRuleProfileGid(String var1) throws RemoteException, GLException;

    public Integer getAllocationRuleSeqNo() throws RemoteException, GLException;

    public void setAllocationRuleSeqNo(Integer var1) throws RemoteException, GLException;

    public String getAllocationRuleGid() throws RemoteException, GLException;

    public void setAllocationRuleGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
