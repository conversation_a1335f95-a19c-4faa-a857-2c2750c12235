/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocationRuleProfileColumns {
    public static SqlColumn allocationRuleProfileGid = new SqlColumn(String.class, "allocation_rule_profile_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocationRuleProfileXid = new SqlColumn(String.class, "allocation_rule_profile_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 255, 0, 0, null, 2, null);
    public static SqlColumn isDefault = new SqlColumn(Boolean.class, "is_default", 1, 0, 1, Boolean.valueOf("false"), 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
