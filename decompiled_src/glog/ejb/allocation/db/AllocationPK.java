/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocationPK
extends Pk {
    public Object allocSeqNo;
    public Object orderReleaseGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocationPK() {
    }

    public AllocationPK(Integer allocSeqNo, String orderReleaseGid) {
        this.allocSeqNo = this.notNull(AllocationColumns.allocSeqNo.convertToDB(allocSeqNo), "allocSeqNo");
        this.orderReleaseGid = this.notNull(AllocationColumns.orderReleaseGid.convertToDB(orderReleaseGid), "orderReleaseGid");
    }

    public AllocationPK(int dummy, Object allocSeqNo, Object orderReleaseGid) {
        this(dummy, allocSeqNo, orderReleaseGid, null);
    }

    public AllocationPK(int dummy, Object allocSeqNo, Object orderReleaseGid, Object transaction) {
        this.allocSeqNo = allocSeqNo;
        this.orderReleaseGid = orderReleaseGid;
        this.transaction = transaction;
    }

    public AllocationPK(AllocationPK otherPk, Object transaction) {
        this.allocSeqNo = otherPk.allocSeqNo;
        this.orderReleaseGid = otherPk.orderReleaseGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "") + " " + (this.orderReleaseGid != null ? String.valueOf(this.orderReleaseGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocSeqNo != null ? String.valueOf(this.allocSeqNo) : "") + "|" + (this.orderReleaseGid != null ? String.valueOf(this.orderReleaseGid) : "");
    }

    public static AllocationPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocationPK(Integer.valueOf(gids[0]), gids[1]) : null;
    }

    public static AllocationPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocationPK(Integer.valueOf(gids[0]), gids[1]) : null;
    }

    public int hashCode() {
        return this.allocSeqNo.hashCode() + this.orderReleaseGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocationPK)) {
            return false;
        }
        AllocationPK otherPk = (AllocationPK)other;
        return Functions.equals(otherPk.allocSeqNo, this.allocSeqNo) && Functions.equals(otherPk.orderReleaseGid, this.orderReleaseGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocationHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocSeqNo;
            }
            case 1: {
                return this.orderReleaseGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocationColumns.allocSeqNo.convertFromDB(this.allocSeqNo);
            }
            case 1: {
                return AllocationColumns.orderReleaseGid.convertFromDB(this.orderReleaseGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocationPK.class;
        }

        @Override
        public final String getEntity() {
            return "Allocation";
        }

        @Override
        public final String getTableName() {
            return "allocation";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_seq_no", "order_release_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocationPK result = new AllocationPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
