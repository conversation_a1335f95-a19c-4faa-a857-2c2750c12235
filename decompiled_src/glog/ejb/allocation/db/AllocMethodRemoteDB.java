/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocMethodRemoteDB
extends EJBObject {
    public AllocMethodData getData() throws RemoteException, GLException;

    public void setData(AllocMethodData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getAllocMethodGid() throws RemoteException, GLException;

    public void setAllocMethodGid(String var1) throws RemoteException, GLException;

    public String getAllocMethodXid() throws RemoteException, GLException;

    public void setAllocMethodXid(String var1) throws RemoteException, GLException;

    public String getAllocTargetGid() throws RemoteException, GLException;

    public void setAllocTargetGid(String var1) throws RemoteException, GLException;

    public String getDescription() throws RemoteException, GLException;

    public void setDescription(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public Boolean getIsUseShipmentShipUnit() throws RemoteException, GLException;

    public void setIsUseShipmentShipUnit(Boolean var1) throws RemoteException, GLException;
}
