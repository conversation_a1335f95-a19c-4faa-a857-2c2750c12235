/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocMethodDetailFKList.3
implements Fk {
    AllocMethodDetailFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocMethodDetailData";
    }

    @Override
    public String getPkField() {
        return "allocTargetGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocMetricData";
    }

    @Override
    public String getFkDataField() {
        return "allocationBasis";
    }
}
