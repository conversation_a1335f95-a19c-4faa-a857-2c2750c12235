/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleProfileDetailPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocationRuleProfileDetailData
extends BeanData {
    public String allocationRuleProfileGid;
    public Integer allocationRuleSeqNo;
    public String allocationRuleGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocationRuleProfileDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocationRuleProfileGidField = beanDataFields[0];
    public static Field allocationRuleSeqNoField = beanDataFields[1];
    public static Field allocationRuleGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocationRuleProfileDetailData() {
    }

    public AllocationRuleProfileDetailData(AllocationRuleProfileDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocationRuleProfileDetailPK();
    }

    @Legacy
    public AllocationRuleProfileDetailPK getAllocationRuleProfileDetailPK() {
        if (this.allocationRuleProfileGid == null) {
            return null;
        }
        if (this.allocationRuleSeqNo == null) {
            return null;
        }
        return new AllocationRuleProfileDetailPK(this.allocationRuleProfileGid, this.allocationRuleSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocationRuleProfileDetailPK((AllocationRuleProfileDetailPK)pk);
    }

    @Legacy
    public void setAllocationRuleProfileDetailPK(AllocationRuleProfileDetailPK pk) {
        this.allocationRuleProfileGid = (String)pk.getAppValue(0);
        this.allocationRuleSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocationRuleProfileDetailQueryGen";
    }

    public static AllocationRuleProfileDetailData load(Connection conn, AllocationRuleProfileDetailPK pk) throws GLException {
        return (AllocationRuleProfileDetailData)AllocationRuleProfileDetailData.load(conn, pk, AllocationRuleProfileDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocationRuleProfileDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocationRuleProfileDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleProfileDetailData.load(conn, whereClause, prepareArguments, fetchSize, AllocationRuleProfileDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocationRuleProfileDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocationRuleProfileDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocationRuleProfileDetailData.class);
    }
}
