/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodDetailColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AllocMethodDetailPK
extends Pk {
    public Object allocMethodGid;
    public Object allocMetricGid;
    public Object allocTargetGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AllocMethodDetailPK() {
    }

    public AllocMethodDetailPK(String allocMethodGid, String allocMetricGid, String allocTargetGid) {
        this.allocMethodGid = this.notNull(AllocMethodDetailColumns.allocMethodGid.convertToDB(allocMethodGid), "allocMethodGid");
        this.allocMetricGid = this.notNull(AllocMethodDetailColumns.allocMetricGid.convertToDB(allocMetricGid), "allocMetricGid");
        this.allocTargetGid = this.notNull(AllocMethodDetailColumns.allocTargetGid.convertToDB(allocTargetGid), "allocTargetGid");
    }

    public AllocMethodDetailPK(int dummy, Object allocMethodGid, Object allocMetricGid, Object allocTargetGid) {
        this(dummy, allocMethodGid, allocMetricGid, allocTargetGid, null);
    }

    public AllocMethodDetailPK(int dummy, Object allocMethodGid, Object allocMetricGid, Object allocTargetGid, Object transaction) {
        this.allocMethodGid = allocMethodGid;
        this.allocMetricGid = allocMetricGid;
        this.allocTargetGid = allocTargetGid;
        this.transaction = transaction;
    }

    public AllocMethodDetailPK(AllocMethodDetailPK otherPk, Object transaction) {
        this.allocMethodGid = otherPk.allocMethodGid;
        this.allocMetricGid = otherPk.allocMetricGid;
        this.allocTargetGid = otherPk.allocTargetGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.allocMethodGid != null ? String.valueOf(this.allocMethodGid) : "") + " " + (this.allocMetricGid != null ? String.valueOf(this.allocMetricGid) : "") + " " + (this.allocTargetGid != null ? String.valueOf(this.allocTargetGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.allocMethodGid != null ? String.valueOf(this.allocMethodGid) : "") + "|" + (this.allocMetricGid != null ? String.valueOf(this.allocMetricGid) : "") + "|" + (this.allocTargetGid != null ? String.valueOf(this.allocTargetGid) : "");
    }

    public static AllocMethodDetailPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AllocMethodDetailPK(gids[0], gids[1], gids[2]) : null;
    }

    public static AllocMethodDetailPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AllocMethodDetailPK(gids[0], gids[1], gids[2]) : null;
    }

    public int hashCode() {
        return this.allocMethodGid.hashCode() + this.allocMetricGid.hashCode() + this.allocTargetGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AllocMethodDetailPK)) {
            return false;
        }
        AllocMethodDetailPK otherPk = (AllocMethodDetailPK)other;
        return Functions.equals(otherPk.allocMethodGid, this.allocMethodGid) && Functions.equals(otherPk.allocMetricGid, this.allocMetricGid) && Functions.equals(otherPk.allocTargetGid, this.allocTargetGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.allocation.AllocMethodDetailHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.allocMethodGid;
            }
            case 1: {
                return this.allocMetricGid;
            }
            case 2: {
                return this.allocTargetGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AllocMethodDetailColumns.allocMethodGid.convertFromDB(this.allocMethodGid);
            }
            case 1: {
                return AllocMethodDetailColumns.allocMetricGid.convertFromDB(this.allocMetricGid);
            }
            case 2: {
                return AllocMethodDetailColumns.allocTargetGid.convertFromDB(this.allocTargetGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AllocMethodDetailPK.class;
        }

        @Override
        public final String getEntity() {
            return "AllocMethodDetail";
        }

        @Override
        public final String getTableName() {
            return "alloc_method_detail";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"alloc_method_gid", "alloc_metric_gid", "alloc_target_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AllocMethodDetailPK result = new AllocMethodDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
