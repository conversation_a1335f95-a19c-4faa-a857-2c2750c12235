/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class AllocationDColumns {
    public static SqlColumn allocSeqNo = new SqlColumn(Integer.class, "alloc_seq_no", 8, 0, 1, null, 0, null);
    public static SqlColumn orderReleaseLineGid = new SqlColumn(String.class, "order_release_line_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn shipmentGid = new SqlColumn(String.class, "shipment_gid", 101, 0, 0, null, 2, null);
    public static SqlColumn orderReleaseGid = new SqlColumn(String.class, "order_release_gid", 101, 0, 1, null, 3, null);
    public static SqlColumn privateCost = new SqlColumn(Currency.class, "private_cost", 22, 2, 1, null, 4, "private_cost_base");
    public static SqlColumn baseCost = new SqlColumn(Currency.class, "base_cost", 22, 2, 1, null, 5, "base_cost_base");
    public static SqlColumn totalAllocLineCost = new SqlColumn(Currency.class, "total_alloc_line_cost", 22, 2, 1, null, 6, "total_alloc_line_cost_base");
    public static SqlColumn packagedItemGid = new SqlColumn(String.class, "packaged_item_gid", 101, 0, 1, null, 7, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 8, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 9, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
}
