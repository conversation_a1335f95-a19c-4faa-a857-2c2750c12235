/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.SqlColumn;

public class AllocationRuleProfileDetailColumns {
    public static SqlColumn allocationRuleProfileGid = new SqlColumn(String.class, "allocation_rule_profile_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn allocationRuleSeqNo = new SqlColumn(Integer.class, "allocation_rule_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn allocationRuleGid = new SqlColumn(String.class, "allocation_rule_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
