/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationDFKList.8
implements Fk {
    AllocationDFKList.8() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationDData";
    }

    @Override
    public String getPkField() {
        return "packagedItemGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.item.db";
    }

    @Override
    public String getFkDataClass() {
        return "PackagedItemData";
    }

    @Override
    public String getFkDataField() {
        return "packagedItemGid";
    }
}
