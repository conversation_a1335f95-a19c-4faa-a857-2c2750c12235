/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocMethodDetailPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AllocMethodDetailData
extends BeanData {
    public String allocMethodGid;
    public String allocMetricGid;
    public String allocTargetGid;
    public Double percentage;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocMethodDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field allocMethodGidField = beanDataFields[0];
    public static Field allocMetricGidField = beanDataFields[1];
    public static Field allocTargetGidField = beanDataFields[2];
    public static Field percentageField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AllocMethodDetailData() {
    }

    public AllocMethodDetailData(AllocMethodDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAllocMethodDetailPK();
    }

    @Legacy
    public AllocMethodDetailPK getAllocMethodDetailPK() {
        if (this.allocMethodGid == null) {
            return null;
        }
        if (this.allocMetricGid == null) {
            return null;
        }
        if (this.allocTargetGid == null) {
            return null;
        }
        return new AllocMethodDetailPK(this.allocMethodGid, this.allocMetricGid, this.allocTargetGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAllocMethodDetailPK((AllocMethodDetailPK)pk);
    }

    @Legacy
    public void setAllocMethodDetailPK(AllocMethodDetailPK pk) {
        this.allocMethodGid = (String)pk.getAppValue(0);
        this.allocMetricGid = (String)pk.getAppValue(1);
        this.allocTargetGid = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.allocation.gen.AllocMethodDetailQueryGen";
    }

    public static AllocMethodDetailData load(Connection conn, AllocMethodDetailPK pk) throws GLException {
        return (AllocMethodDetailData)AllocMethodDetailData.load(conn, pk, AllocMethodDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AllocMethodDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AllocMethodDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMethodDetailData.load(conn, whereClause, prepareArguments, fetchSize, AllocMethodDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AllocMethodDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AllocMethodDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AllocMethodDetailData.class);
    }
}
