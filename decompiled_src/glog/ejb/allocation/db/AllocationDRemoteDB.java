/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationDData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationDRemoteDB
extends EJBObject {
    public AllocationDData getData() throws RemoteException, GLException;

    public void setData(AllocationDData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteEx<PERSON>, <PERSON>LEx<PERSON>;

    public Integer getAllocSeqNo() throws RemoteException, GLException;

    public void setAllocSeqNo(Integer var1) throws RemoteException, GLException;

    public String getOrderReleaseLineGid() throws RemoteException, GLException;

    public void setOrderReleaseLineGid(String var1) throws RemoteException, GLException;

    public String getShipmentGid() throws RemoteException, GLException;

    public void setShipmentGid(String var1) throws RemoteException, GLException;

    public String getOrderReleaseGid() throws RemoteException, GLException;

    public void setOrderReleaseGid(String var1) throws RemoteException, GLException;

    public Currency getPrivateCost() throws RemoteException, GLException;

    public void setPrivateCost(Currency var1) throws RemoteException, GLException;

    public Currency getBaseCost() throws RemoteException, GLException;

    public void setBaseCost(Currency var1) throws RemoteException, GLException;

    public Currency getTotalAllocLineCost() throws RemoteException, GLException;

    public void setTotalAllocLineCost(Currency var1) throws RemoteException, GLException;

    public String getPackagedItemGid() throws RemoteException, GLException;

    public void setPackagedItemGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
