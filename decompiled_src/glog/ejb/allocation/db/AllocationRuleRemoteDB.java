/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.allocation.db;

import glog.ejb.allocation.db.AllocationRuleData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AllocationRuleRemoteDB
extends EJBObject {
    public AllocationRuleData getData() throws RemoteException, GLException;

    public void setData(AllocationRuleData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getAllocationRuleGid() throws RemoteException, GLException;

    public void setAllocationRuleGid(String var1) throws RemoteException, GLException;

    public String getAllocationRuleXid() throws RemoteException, GLException;

    public void setAllocationRuleXid(String var1) throws RemoteException, GLException;

    public String getLocationProfileGid() throws RemoteException, GLException;

    public void setLocationProfileGid(String var1) throws RemoteException, GLException;

    public String getConsInvAllocType() throws RemoteException, GLException;

    public void setConsInvAllocType(String var1) throws RemoteException, GLException;

    public String getModeProfileGid() throws RemoteException, GLException;

    public void setModeProfileGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public Boolean getIsAllocShipment() throws RemoteException, GLException;

    public void setIsAllocShipment(Boolean var1) throws RemoteException, GLException;

    public Boolean getIsAllocVoucher() throws RemoteException, GLException;

    public void setIsAllocVoucher(Boolean var1) throws RemoteException, GLException;

    public Boolean getIsAllocBill() throws RemoteException, GLException;

    public void setIsAllocBill(Boolean var1) throws RemoteException, GLException;
}
