/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationBaseFKList.2
implements Fk {
    AllocationBaseFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationBaseData";
    }

    @Override
    public String getPkField() {
        return "shipmentGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.shipment.db";
    }

    @Override
    public String getFkDataClass() {
        return "ShipmentData";
    }

    @Override
    public String getFkDataField() {
        return "shipmentGid";
    }
}
