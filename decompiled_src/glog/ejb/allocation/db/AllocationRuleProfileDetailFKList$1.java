/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;

static final class AllocationRuleProfileDetailFKList.1
implements Fk {
    AllocationRuleProfileDetailFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getPkDataClass() {
        return "AllocationRuleProfileDetailData";
    }

    @Override
    public String getPkField() {
        return "allocationRuleGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.allocation.db";
    }

    @Override
    public String getFkDataClass() {
        return "AllocationRuleData";
    }

    @Override
    public String getFkDataField() {
        return "allocationRuleGid";
    }
}
