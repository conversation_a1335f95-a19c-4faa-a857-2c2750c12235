/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.allocation.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AllocationDFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("privateCostCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "privateCostCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("orderReleaseLineGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "orderReleaseLineGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.order.db";
            }

            @Override
            public String getFkDataClass() {
                return "OrderReleaseLineData";
            }

            @Override
            public String getFkDataField() {
                return "orderReleaseLineGid";
            }
        });
        fks.put("allocSeqNo", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "allocSeqNo";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getFkDataClass() {
                return "AllocationBaseData";
            }

            @Override
            public String getFkDataField() {
                return "allocSeqNo";
            }
        });
        fks.put("tLineCostCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "tLineCostCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("orderReleaseGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "orderReleaseGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.order.db";
            }

            @Override
            public String getFkDataClass() {
                return "OrderReleaseData";
            }

            @Override
            public String getFkDataField() {
                return "orderReleaseGid";
            }
        });
        fks.put("exchangeRateGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "exchangeRateGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "ExchangeRateData";
            }

            @Override
            public String getFkDataField() {
                return "exchangeRateGid";
            }
        });
        fks.put("shipmentGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "shipmentGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.shipment.db";
            }

            @Override
            public String getFkDataClass() {
                return "ShipmentData";
            }

            @Override
            public String getFkDataField() {
                return "shipmentGid";
            }
        });
        fks.put("packagedItemGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "packagedItemGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.item.db";
            }

            @Override
            public String getFkDataClass() {
                return "PackagedItemData";
            }

            @Override
            public String getFkDataField() {
                return "packagedItemGid";
            }
        });
        fks.put("baseCostCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.allocation.db";
            }

            @Override
            public String getPkDataClass() {
                return "AllocationDData";
            }

            @Override
            public String getPkField() {
                return "baseCostCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
    }
}
