/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation.db;

import glog.database.security.SecurityUtil;
import glog.ejb.allocation.db.AllocTypeQualColumns;
import glog.ejb.allocation.db.AllocTypeQualData;
import glog.ejb.allocation.db.AllocTypeQualPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AllocTypeQualBeanDB
extends BeanManagedEntityBean {
    public Object allocTypeQualGid;
    public Object allocTypeQualXid;
    public Object allocTypeQualDesc;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AllocTypeQualPK pk;
    protected transient AllocTypeQualData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AllocTypeQualBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AllocTypeQualPK.Callback theCall = new AllocTypeQualPK.Callback();

    public AllocTypeQualBeanDB() {
        super(false);
    }

    public AllocTypeQualPK ejbCreate(AllocTypeQualData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AllocTypeQualPK newPK() throws GLException {
        return AllocTypeQualPK.newPK(this.getDomainName(), this.getAllocTypeQualXid(), this.getConnection());
    }

    public void ejbPostCreate(AllocTypeQualData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AllocTypeQualPK ejbFindByPrimaryKey(AllocTypeQualPK pk) throws FinderException {
        return (AllocTypeQualPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AllocTypeQualPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AllocTypeQualPK> v = new Vector<AllocTypeQualPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AllocTypeQualPK pk, AllocTypeQualData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AllocTypeQualPK pk, AllocTypeQualData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AllocTypeQualPK pk, AllocTypeQualData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AllocTypeQualColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AllocTypeQualColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AllocTypeQualColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AllocTypeQualColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AllocTypeQualPK pk = (AllocTypeQualPK)genericPk;
        this.allocTypeQualGid = pk.allocTypeQualGid;
        this.domainName = pk.domainName;
        this.allocTypeQualXid = pk.allocTypeQualXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AllocTypeQualPK pk = new AllocTypeQualPK(0, this.allocTypeQualGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AllocTypeQual";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AllocTypeQualData getData() throws GLException {
        try {
            AllocTypeQualData retval = new AllocTypeQualData();
            retval.getFromBean(this, AllocTypeQualColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AllocTypeQualData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AllocTypeQualData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AllocTypeQualData oldData = modified ? this.getData() : null;
            data.setToBean(this, AllocTypeQualColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AllocTypeQualData.class;
    }

    @Override
    public Class getPkClass() {
        return AllocTypeQualPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAllocTypeQualGid() throws GLException {
        try {
            return (String)AllocTypeQualColumns.allocTypeQualGid.convertFromDB(this.allocTypeQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTypeQualGid(String allocTypeQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTypeQualGid;
            AllocTypeQualData data = this.getData();
            this.allocTypeQualGid = AllocTypeQualColumns.allocTypeQualGid.convertToDB(allocTypeQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTypeQualGid", String.class, oldValue, this.allocTypeQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTypeQualXid() throws GLException {
        try {
            return (String)AllocTypeQualColumns.allocTypeQualXid.convertFromDB(this.allocTypeQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTypeQualXid(String allocTypeQualXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTypeQualXid;
            AllocTypeQualData data = this.getData();
            this.allocTypeQualXid = AllocTypeQualColumns.allocTypeQualXid.convertToDB(allocTypeQualXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTypeQualXid", String.class, oldValue, this.allocTypeQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAllocTypeQualDesc() throws GLException {
        try {
            return (String)AllocTypeQualColumns.allocTypeQualDesc.convertFromDB(this.allocTypeQualDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllocTypeQualDesc(String allocTypeQualDesc) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allocTypeQualDesc;
            AllocTypeQualData data = this.getData();
            this.allocTypeQualDesc = AllocTypeQualColumns.allocTypeQualDesc.convertToDB(allocTypeQualDesc);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allocTypeQualDesc", String.class, oldValue, this.allocTypeQualDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AllocTypeQualColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AllocTypeQualData data = this.getData();
            this.domainName = AllocTypeQualColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
