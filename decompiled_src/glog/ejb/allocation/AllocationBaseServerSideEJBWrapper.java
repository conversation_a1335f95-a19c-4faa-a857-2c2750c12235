/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.ejb.allocation;

import glog.ejb.allocation.AllocationBaseBean;
import glog.ejb.allocation.db.AllocationBaseData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.sql.Timestamp;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBContext;

public class AllocationBaseServerSideEJBWrapper
extends AllocationBaseBean {
    @Override
    public AllocationBaseData getData() throws GLException {
        try {
            AllocationBaseData allocationBaseData = super.getData();
            return allocationBaseData;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setData(AllocationBaseData p1) throws GLException {
        try {
            super.setData(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setLinks(Vector p1, Vector p2) throws GLException {
        try {
            super.setLinks(p1, p2);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Pk getPK() {
        Pk pk = super.getPK();
        return pk;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() {
        super.markForCacheRemove();
    }

    @Override
    public void reload() throws GLException {
        try {
            super.reload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void unload() throws GLException {
        try {
            super.unload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Map verify() throws GLException {
        try {
            Map map = super.verify();
            return map;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Integer getAllocSeqNo() throws GLException {
        try {
            Integer n = super.getAllocSeqNo();
            return n;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setAllocSeqNo(Integer p1) throws GLException {
        try {
            super.setAllocSeqNo(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getShipmentGid() throws GLException {
        try {
            String string = super.getShipmentGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setShipmentGid(String p1) throws GLException {
        try {
            super.setShipmentGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getAllocTypeQualGid() throws GLException {
        try {
            String string = super.getAllocTypeQualGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setAllocTypeQualGid(String p1) throws GLException {
        try {
            super.setAllocTypeQualGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getInvoiceGid() throws GLException {
        try {
            String string = super.getInvoiceGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setInvoiceGid(String p1) throws GLException {
        try {
            super.setInvoiceGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Timestamp getTimestamp() throws GLException {
        try {
            Timestamp timestamp = super.getTimestamp();
            return timestamp;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setTimestamp(Timestamp p1) throws GLException {
        try {
            super.setTimestamp(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Currency getAllocatedCost() throws GLException {
        try {
            Currency currency = super.getAllocatedCost();
            return currency;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setAllocatedCost(Currency p1) throws GLException {
        try {
            super.setAllocatedCost(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getParentInvoiceGid() throws GLException {
        try {
            String string = super.getParentInvoiceGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setParentInvoiceGid(String p1) throws GLException {
        try {
            super.setParentInvoiceGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getVoucherGid() throws GLException {
        try {
            String string = super.getVoucherGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setVoucherGid(String p1) throws GLException {
        try {
            super.setVoucherGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public LocalDate getExchangeRateDate() throws GLException {
        try {
            LocalDate localDate = super.getExchangeRateDate();
            return localDate;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateDate(LocalDate p1) throws GLException {
        try {
            super.setExchangeRateDate(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getExchangeRateGid() throws GLException {
        try {
            String string = super.getExchangeRateGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateGid(String p1) throws GLException {
        try {
            super.setExchangeRateGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDomainName() throws GLException {
        try {
            String string = super.getDomainName();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDomainName(String p1) throws GLException {
        try {
            super.setDomainName(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }
}
