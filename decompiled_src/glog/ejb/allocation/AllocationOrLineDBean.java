/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationOrLineDBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AllocationOrLineDBean
extends AllocationOrLineDBeanDB {
    public Enumeration ejbFindByAllocSeqNo(String strSeqNo) throws FinderException {
        return this.ejbFindSQL(this.getFindSelect() + " WHERE alloc_seq_no = ? ORDER BY order_release_line_gid", new Object[]{strSeqNo});
    }
}
