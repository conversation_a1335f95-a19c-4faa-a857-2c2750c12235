/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationRuleProfileHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationRuleProfileHome
extends AllocationRuleProfileHomeDB {
    public Enumeration findDefaultProfile() throws FinderException, RemoteException;
}
