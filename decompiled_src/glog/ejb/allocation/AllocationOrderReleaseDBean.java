/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationOrderReleaseDBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AllocationOrderReleaseDBean
extends AllocationOrderReleaseDBeanDB {
    public Enumeration ejbFindByAllocSeqNo(String strSeqNo) throws FinderException {
        return this.ejbFind("alloc_seq_no = ?", new Object[]{strSeqNo});
    }
}
