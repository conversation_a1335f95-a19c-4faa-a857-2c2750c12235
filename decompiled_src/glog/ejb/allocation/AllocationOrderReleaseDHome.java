/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationOrderReleaseDHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationOrderReleaseDHome
extends AllocationOrderReleaseDHomeDB {
    public Enumeration findByAllocSeqNo(String var1) throws FinderException, RemoteException;
}
