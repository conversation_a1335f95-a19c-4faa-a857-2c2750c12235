/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationDBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AllocationDBean
extends AllocationDBeanDB {
    public Enumeration ejbFindByAllocSeqNo(String strSeqNo) throws FinderException {
        return this.ejbFindSQL(this.getFindSelect() + " WHERE alloc_seq_no = ? ORDER BY order_release_line_gid", new Object[]{strSeqNo});
    }

    public Enumeration ejbFindByShipment(String ShipmentGid) throws FinderException {
        return this.ejbFind("shipment_gid = ?", new Object[]{ShipmentGid});
    }
}
