/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationRuleHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationRuleHome
extends AllocationRuleHomeDB {
    public Enumeration findByProfileGid(String var1) throws FinderException, RemoteException;
}
