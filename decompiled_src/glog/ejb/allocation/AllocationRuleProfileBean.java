/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationRuleProfileBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AllocationRuleProfileBean
extends AllocationRuleProfileBeanDB {
    public Enumeration ejbFindDefaultProfile() throws FinderException {
        return this.ejbFind("is_default = ?", new Object[]{"Y"});
    }
}
