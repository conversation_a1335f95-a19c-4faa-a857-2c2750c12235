/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AllocationBean
extends AllocationBeanDB {
    public Enumeration ejbFindByAllocSeqNo(String strSeqNo) throws FinderException {
        return this.ejbFind("alloc_seq_no = ?", new Object[]{strSeqNo});
    }

    public Enumeration ejbFindByOrderReleaseGid(String strOrderReleaseGid) throws FinderException {
        return this.ejbFindSQL(this.getFindSelect() + " WHERE order_release_gid = ? ORDER BY order_release_gid", new Object[]{strOrderReleaseGid});
    }
}
