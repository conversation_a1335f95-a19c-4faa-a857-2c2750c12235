/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.EntityContext
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.AllocationRule;
import glog.ejb.allocation.AllocationRuleBean;
import glog.ejb.allocation.AllocationRuleHomeStub;
import glog.ejb.allocation.db.AllocationRuleData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityContext;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.EntityContext;
import javax.ejb.Handle;
import javax.ejb.RemoveException;

public class AllocationRuleStub
implements AllocationRule {
    private AllocationRuleBean entityBean;
    private Pk pk;
    private EntityContext context;

    public AllocationRuleStub(EJBHome ejbHome, AllocationRuleBean bean) {
        this.entityBean = bean;
        this.pk = bean.getPK();
        this.context = new LocalEntityContext(ejbHome, this, this.pk);
    }

    public AllocationRuleStub(EJBHome ejbHome, Pk pk) {
        this.pk = pk;
        this.context = new LocalEntityContext(ejbHome, this, pk);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AllocationRuleData getData() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getData", false);
            this.entityBean = this.loadOnDemand();
            AllocationRuleData allocationRuleData = this.entityBean.getData();
            return allocationRuleData;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setData(AllocationRuleData p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setData", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setData(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLinks(Vector p1, Vector p2) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setLinks", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLinks(p1, p2);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Pk getPK() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("AllocationRule", "getPK", false);
                this.entityBean = this.loadOnDemand();
                Pk pk = this.entityBean.getPK();
                return pk;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("getPK", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("AllocationRule", "markForCacheRemove", false);
                this.entityBean = this.loadOnDemand();
                this.entityBean.markForCacheRemove();
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("markForCacheRemove", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void reload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "reload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.reload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "unload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.unload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Map verify() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "verify", false);
            this.entityBean = this.loadOnDemand();
            Map map = this.entityBean.verify();
            return map;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getAllocationRuleGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getAllocationRuleGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getAllocationRuleGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setAllocationRuleGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setAllocationRuleGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setAllocationRuleGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getAllocationRuleXid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getAllocationRuleXid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getAllocationRuleXid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setAllocationRuleXid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setAllocationRuleXid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setAllocationRuleXid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getLocationProfileGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getLocationProfileGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getLocationProfileGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLocationProfileGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setLocationProfileGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLocationProfileGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getConsInvAllocType() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getConsInvAllocType", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getConsInvAllocType();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setConsInvAllocType(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setConsInvAllocType", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setConsInvAllocType(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getModeProfileGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getModeProfileGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getModeProfileGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setModeProfileGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setModeProfileGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setModeProfileGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getDomainName() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getDomainName", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getDomainName();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setDomainName(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setDomainName", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setDomainName(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Boolean getIsAllocShipment() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getIsAllocShipment", false);
            this.entityBean = this.loadOnDemand();
            Boolean bl = this.entityBean.getIsAllocShipment();
            return bl;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setIsAllocShipment(Boolean p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setIsAllocShipment", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setIsAllocShipment(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Boolean getIsAllocVoucher() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getIsAllocVoucher", false);
            this.entityBean = this.loadOnDemand();
            Boolean bl = this.entityBean.getIsAllocVoucher();
            return bl;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setIsAllocVoucher(Boolean p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setIsAllocVoucher", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setIsAllocVoucher(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Boolean getIsAllocBill() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "getIsAllocBill", false);
            this.entityBean = this.loadOnDemand();
            Boolean bl = this.entityBean.getIsAllocBill();
            return bl;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setIsAllocBill(Boolean p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("AllocationRule", "setIsAllocBill", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setIsAllocBill(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    public void remove() throws RemoteException, RemoveException {
        try {
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("AllocationRule", "remove", false);
                this.entityBean = this.loadOnDemand();
                this.remove(this.entityBean);
                this.entityBean = null;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                throw ex;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("remove", glex);
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() throws RemoteException {
        return this.getPK();
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    private void store(AllocationRuleBean entityBean) throws RemoteException {
        if (AllocationRuleHomeStub.cache != null) {
            AllocationRuleHomeStub.cache.put(this.pk, entityBean);
        }
        entityBean.ejbStore();
    }

    private void remove(AllocationRuleBean entityBean) throws RemoveException {
        if (AllocationRuleHomeStub.cache != null) {
            AllocationRuleHomeStub.cache.remove(this.pk);
        }
        entityBean.ejbRemove();
    }

    private AllocationRuleBean loadOnDemand() throws RemoteException {
        if (this.entityBean == null && (AllocationRuleHomeStub.cache == null || (this.entityBean = (AllocationRuleBean)AllocationRuleHomeStub.cache.get(this.pk)) == null)) {
            this.entityBean = new AllocationRuleBean();
            this.entityBean.setPK(this.pk);
            this.entityBean.setEntityContext(this.context);
            this.entityBean.ejbLoad();
            if (AllocationRuleHomeStub.cache != null) {
                AllocationRuleHomeStub.cache.put(this.pk, this.entityBean);
            }
        }
        return this.entityBean;
    }
}
