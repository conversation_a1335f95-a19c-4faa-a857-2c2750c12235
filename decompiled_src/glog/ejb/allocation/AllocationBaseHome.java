/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationBaseHomeDB;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.VoucherPK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface AllocationBaseHome
extends AllocationBaseHomeDB {
    public Enumeration findByInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findByParentInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findByShipment(String var1) throws FinderException, RemoteException;

    public Enumeration findIssueable(String var1) throws FinderException, RemoteException;

    public Enumeration findByVoucherPK(VoucherPK var1) throws FinderException, RemoteException;
}
