/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.allocation;

import glog.ejb.allocation.db.AllocationRuleBeanDB;
import glog.ejb.allocation.db.AllocationRulePK;
import glog.util.exception.Cause;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.remote.GLNoMoreRecords;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.FinderException;

public class AllocationRuleBean
extends AllocationRuleBeanDB {
    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Enumeration ejbFindByProfileGid(String strProfileGid) throws FinderException {
        Enumeration enumeration;
        T2SharedConnection conn = this.getConnection();
        String sqlText = "select allocation_rule_gid from allocation_rule_profile_detail where ALLOCATION_RULE_PROFILE_GID = ? order by ALLOCATION_RULE_SEQ_NO";
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlText), new Object[]{strProfileGid}, "AllocationRuleBean::ejbfindByProfileGid");
        Vector<AllocationRulePK> v = new Vector<AllocationRulePK>();
        try {
            conn.open();
            sql.open(conn.get());
            while (sql.next()) {
                v.add(new AllocationRulePK(1, sql.getObject(1)));
            }
            if (v.size() == 0) {
                throw new GLNoMoreRecords(this);
            }
            enumeration = v.elements();
        }
        catch (Throwable throwable) {
            try {
                sql.close();
                conn.close();
                throw throwable;
            }
            catch (Throwable t) {
                throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.AllocationRuleBean.0001", null, new Object[][]{{"strProfileGid", strProfileGid}}), t));
            }
        }
        sql.close();
        conn.close();
        return enumeration;
    }
}
