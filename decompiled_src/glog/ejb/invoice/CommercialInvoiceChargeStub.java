/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.EntityContext
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.CommercialInvoiceCharge;
import glog.ejb.invoice.CommercialInvoiceChargeBean;
import glog.ejb.invoice.CommercialInvoiceChargeHomeStub;
import glog.ejb.invoice.db.CommercialInvoiceChargeData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityContext;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.EntityContext;
import javax.ejb.Handle;
import javax.ejb.RemoveException;

public class CommercialInvoiceChargeStub
implements CommercialInvoiceCharge {
    private CommercialInvoiceChargeBean entityBean;
    private Pk pk;
    private EntityContext context;

    public CommercialInvoiceChargeStub(EJBHome ejbHome, CommercialInvoiceChargeBean bean) {
        this.entityBean = bean;
        this.pk = bean.getPK();
        this.context = new LocalEntityContext(ejbHome, this, this.pk);
    }

    public CommercialInvoiceChargeStub(EJBHome ejbHome, Pk pk) {
        this.pk = pk;
        this.context = new LocalEntityContext(ejbHome, this, pk);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public CommercialInvoiceChargeData getData() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getData", false);
            this.entityBean = this.loadOnDemand();
            CommercialInvoiceChargeData commercialInvoiceChargeData = this.entityBean.getData();
            return commercialInvoiceChargeData;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setData(CommercialInvoiceChargeData p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setData", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setData(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLinks(Vector p1, Vector p2) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setLinks", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLinks(p1, p2);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Pk getPK() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("CommercialInvoiceCharge", "getPK", false);
                this.entityBean = this.loadOnDemand();
                Pk pk = this.entityBean.getPK();
                return pk;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("getPK", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("CommercialInvoiceCharge", "markForCacheRemove", false);
                this.entityBean = this.loadOnDemand();
                this.entityBean.markForCacheRemove();
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("markForCacheRemove", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void reload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "reload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.reload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "unload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.unload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Map verify() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "verify", false);
            this.entityBean = this.loadOnDemand();
            Map map = this.entityBean.verify();
            return map;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getCommercialInvoiceGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getCommercialInvoiceGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getCommercialInvoiceGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setCommercialInvoiceGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setCommercialInvoiceGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setCommercialInvoiceGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Integer getSequenceNo() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getSequenceNo", false);
            this.entityBean = this.loadOnDemand();
            Integer n = this.entityBean.getSequenceNo();
            return n;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setSequenceNo(Integer p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setSequenceNo", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setSequenceNo(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Currency getChargeAmount() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getChargeAmount", false);
            this.entityBean = this.loadOnDemand();
            Currency currency = this.entityBean.getChargeAmount();
            return currency;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setChargeAmount(Currency p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setChargeAmount", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setChargeAmount(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getChargeActivity() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getChargeActivity", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getChargeActivity();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setChargeActivity(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setChargeActivity", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setChargeActivity(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getCommercialInvChargeCodeGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getCommercialInvChargeCodeGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getCommercialInvChargeCodeGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setCommercialInvChargeCodeGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setCommercialInvChargeCodeGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setCommercialInvChargeCodeGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LocalDate getExchangeRateDate() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getExchangeRateDate", false);
            this.entityBean = this.loadOnDemand();
            LocalDate localDate = this.entityBean.getExchangeRateDate();
            return localDate;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setExchangeRateDate(LocalDate p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setExchangeRateDate", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setExchangeRateDate(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getExchangeRateGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getExchangeRateGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getExchangeRateGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setExchangeRateGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setExchangeRateGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setExchangeRateGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getDomainName() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "getDomainName", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getDomainName();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setDomainName(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("CommercialInvoiceCharge", "setDomainName", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setDomainName(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    public void remove() throws RemoteException, RemoveException {
        try {
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("CommercialInvoiceCharge", "remove", false);
                this.entityBean = this.loadOnDemand();
                this.remove(this.entityBean);
                this.entityBean = null;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                throw ex;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("remove", glex);
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() throws RemoteException {
        return this.getPK();
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    private void store(CommercialInvoiceChargeBean entityBean) throws RemoteException {
        if (CommercialInvoiceChargeHomeStub.cache != null) {
            CommercialInvoiceChargeHomeStub.cache.put(this.pk, entityBean);
        }
        entityBean.ejbStore();
    }

    private void remove(CommercialInvoiceChargeBean entityBean) throws RemoveException {
        if (CommercialInvoiceChargeHomeStub.cache != null) {
            CommercialInvoiceChargeHomeStub.cache.remove(this.pk);
        }
        entityBean.ejbRemove();
    }

    private CommercialInvoiceChargeBean loadOnDemand() throws RemoteException {
        if (this.entityBean == null && (CommercialInvoiceChargeHomeStub.cache == null || (this.entityBean = (CommercialInvoiceChargeBean)CommercialInvoiceChargeHomeStub.cache.get(this.pk)) == null)) {
            this.entityBean = new CommercialInvoiceChargeBean();
            this.entityBean.setPK(this.pk);
            this.entityBean.setEntityContext(this.context);
            this.entityBean.ejbLoad();
            if (CommercialInvoiceChargeHomeStub.cache != null) {
                CommercialInvoiceChargeHomeStub.cache.put(this.pk, this.entityBean);
            }
        }
        return this.entityBean;
    }
}
