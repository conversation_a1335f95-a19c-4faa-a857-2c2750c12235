/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.LineApproveTolerance;
import glog.ejb.invoice.LineApproveToleranceBean;
import glog.ejb.invoice.LineApproveToleranceHome;
import glog.ejb.invoice.LineApproveToleranceStub;
import glog.ejb.invoice.db.LineApproveToleranceData;
import glog.ejb.invoice.db.LineApproveTolerancePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class LineApproveToleranceHomeStub
implements LineApproveToleranceHome {
    static LRUCache cache = LocalEntityCache.register("LineApproveTolerance");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LineApproveTolerance create(LineApproveToleranceData p1) throws CreateException, RemoteException {
        LineApproveToleranceStub lineApproveToleranceStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "create", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            lineApproveToleranceStub = new LineApproveToleranceStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return lineApproveToleranceStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public LineApproveTolerance findByPrimaryKey(LineApproveTolerancePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                LineApproveToleranceBean bean;
                ee.enterMethod("LineApproveToleranceHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (LineApproveToleranceBean)cache.get(p1)) != null) {
                    LineApproveToleranceStub lineApproveToleranceStub = new LineApproveToleranceStub((EJBHome)this, bean);
                    return lineApproveToleranceStub;
                }
                bean = new LineApproveToleranceBean();
                LineApproveTolerancePK pk = bean.ejbFindByPrimaryKey(p1);
                LineApproveToleranceStub lineApproveToleranceStub = new LineApproveToleranceStub((EJBHome)this, pk);
                return lineApproveToleranceStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "findAll", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveToleranceStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "findAll", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveToleranceStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "findByPrimaryKeys", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveToleranceStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "findByPrimaryKeys", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveToleranceStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(LineApproveTolerancePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                LineApproveToleranceBean bean;
                ee.enterMethod("LineApproveToleranceHome", "findInCache", true);
                if (cache != null && (bean = (LineApproveToleranceBean)cache.get(p1)) != null) {
                    Vector<LineApproveTolerancePK> v = new Vector<LineApproveTolerancePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), LineApproveToleranceStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(LineApproveTolerancePK p1, LineApproveToleranceData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "onCreate", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(LineApproveTolerancePK p1, LineApproveToleranceData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "onUpdate", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(LineApproveTolerancePK p1, LineApproveToleranceData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "onRemove", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "listBeansInCache", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "listLocks", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "unlock", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "getDataNoLock", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "getLockData", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "getMaxCacheSize", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveToleranceHome", "updateMaxCacheSize", true);
            LineApproveToleranceBean bean = new LineApproveToleranceBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new LineApproveToleranceStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
