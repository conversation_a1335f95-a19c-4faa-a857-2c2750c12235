/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class InvoiceStopRefnumPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return InvoiceStopRefnumPK.class;
    }

    @Override
    public final String getEntity() {
        return "InvoiceStopRefnum";
    }

    @Override
    public final String getTableName() {
        return "invoice_stop_refnum";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"invoice_gid", "invoice_stop_refnum_qual_gid", "stop_seq_no"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        InvoiceStopRefnumPK result = new InvoiceStopRefnumPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
        return result;
    }
}
