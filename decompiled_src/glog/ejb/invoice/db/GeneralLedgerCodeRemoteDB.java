/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GeneralLedgerCodeData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface GeneralLedgerCodeRemoteDB
extends EJBObject {
    public GeneralLedgerCodeData getData() throws RemoteException, GLException;

    public void setData(GeneralLedgerCodeData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getGeneralLedgerGid() throws RemoteException, GLException;

    public void setGeneralLedgerGid(String var1) throws RemoteException, GLException;

    public String getGeneralLedgerXid() throws RemoteException, GLException;

    public void setGeneralLedgerXid(String var1) throws RemoteException, GLException;

    public String getDescription() throws RemoteException, GLException;

    public void setDescription(String var1) throws RemoteException, GLException;

    public Boolean getIsActive() throws RemoteException, GLException;

    public void setIsActive(Boolean var1) throws RemoteException, GLException;

    public String getBuyOrderGlLookupKeyGid() throws RemoteException, GLException;

    public void setBuyOrderGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public String getSellOrderGlLookupKeyGid() throws RemoteException, GLException;

    public void setSellOrderGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public String getBuyShipGlLookupKeyGid() throws RemoteException, GLException;

    public void setBuyShipGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public String getSellShipGlLookupKeyGid() throws RemoteException, GLException;

    public void setSellShipGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
