/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceLineitemRefnumColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineitemSeqNo = new SqlColumn(Integer.class, "lineitem_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn invoiceLiRefnumQualGid = new SqlColumn(String.class, "invoice_li_refnum_qual_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn referenceNumber = new SqlColumn(String.class, "reference_number", 240, 0, 1, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
