/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceTextFKList.3
implements Fk {
    InvoiceTextFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceTextData";
    }

    @Override
    public String getPkField() {
        return "textTemplateGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.text.db";
    }

    @Override
    public String getFkDataClass() {
        return "TextTemplateData";
    }

    @Override
    public String getFkDataField() {
        return "textTemplateGid";
    }
}
