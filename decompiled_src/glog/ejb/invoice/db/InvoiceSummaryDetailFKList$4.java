/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceSummaryDetailFKList.4
implements Fk {
    InvoiceSummaryDetailFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceSummaryDetailData";
    }

    @Override
    public String getPkField() {
        return "billedAsQtyFnCurrencyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "CurrencyData";
    }

    @Override
    public String getFkDataField() {
        return "currencyGid";
    }
}
