/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.9
implements Fk {
    InvoiceLineitemFKList.9() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "adjustmentReasonCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "AdjustmentReasonData";
    }

    @Override
    public String getFkDataField() {
        return "adjustmentReasonGid";
    }
}
