/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopSealColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceStopSealPK
extends Pk {
    public Object invoiceGid;
    public Object sealSequence;
    public Object stopSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceStopSealPK() {
    }

    public InvoiceStopSealPK(String invoiceGid, Integer sealSequence, String stopSeqNo) {
        this.invoiceGid = this.notNull(InvoiceStopSealColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.sealSequence = this.notNull(InvoiceStopSealColumns.sealSequence.convertToDB(sealSequence), "sealSequence");
        this.stopSeqNo = this.notNull(InvoiceStopSealColumns.stopSeqNo.convertToDB(stopSeqNo), "stopSeqNo");
    }

    public InvoiceStopSealPK(int dummy, Object invoiceGid, Object sealSequence, Object stopSeqNo) {
        this(dummy, invoiceGid, sealSequence, stopSeqNo, null);
    }

    public InvoiceStopSealPK(int dummy, Object invoiceGid, Object sealSequence, Object stopSeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.sealSequence = sealSequence;
        this.stopSeqNo = stopSeqNo;
        this.transaction = transaction;
    }

    public InvoiceStopSealPK(InvoiceStopSealPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.sealSequence = otherPk.sealSequence;
        this.stopSeqNo = otherPk.stopSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.sealSequence != null ? String.valueOf(this.sealSequence) : "") + " " + (this.stopSeqNo != null ? String.valueOf(this.stopSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.sealSequence != null ? String.valueOf(this.sealSequence) : "") + "|" + (this.stopSeqNo != null ? String.valueOf(this.stopSeqNo) : "");
    }

    public static InvoiceStopSealPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceStopSealPK(gids[0], Integer.valueOf(gids[1]), gids[2]) : null;
    }

    public static InvoiceStopSealPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceStopSealPK(gids[0], Integer.valueOf(gids[1]), gids[2]) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.sealSequence.hashCode() + this.stopSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceStopSealPK)) {
            return false;
        }
        InvoiceStopSealPK otherPk = (InvoiceStopSealPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.sealSequence, this.sealSequence) && Functions.equals(otherPk.stopSeqNo, this.stopSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceStopSealHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.sealSequence;
            }
            case 2: {
                return this.stopSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceStopSealColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceStopSealColumns.sealSequence.convertFromDB(this.sealSequence);
            }
            case 2: {
                return InvoiceStopSealColumns.stopSeqNo.convertFromDB(this.stopSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceStopSealPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceStopSeal";
        }

        @Override
        public final String getTableName() {
            return "invoice_stop_seal";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "seal_sequence", "stop_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceStopSealPK result = new InvoiceStopSealPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
