/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;
import glog.util.uom.data.Temperature;

public class InvoiceProtectiveServiceColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn protSrvSeqNo = new SqlColumn(Integer.class, "prot_srv_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn protSrvCode = new SqlColumn(String.class, "prot_srv_code", 4, 0, 0, null, 2, null);
    public static SqlColumn protSrvRuleCode = new SqlColumn(String.class, "prot_srv_rule_code", 9, 0, 0, null, 3, null);
    public static SqlColumn protSrvTemp = new SqlColumn(Temperature.class, "prot_srv_temp", 22, 3, 0, null, 4, "prot_srv_temp_base");
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, String.valueOf("PUBLIC"), 5, null);
}
