/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.AutoApproveRuleDetailColumns;
import glog.ejb.invoice.db.AutoApproveRuleDetailData;
import glog.ejb.invoice.db.AutoApproveRuleDetailPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AutoApproveRuleDetailBeanDB
extends BeanManagedEntityBean {
    public Object sequence;
    public Object autoApproveRuleGid;
    public Object type;
    public Object allowablePercentAbove;
    public Object allowablePercentBelow;
    public Object approveToAmount;
    public Object approveToWeight;
    public Object allowCurrAbove;
    public Object allowCurrBelow;
    public Object allowWeightAbove;
    public Object allowWeightBelow;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AutoApproveRuleDetailPK pk;
    protected transient AutoApproveRuleDetailData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleDetailBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AutoApproveRuleDetailPK.Callback theCall = new AutoApproveRuleDetailPK.Callback();

    public AutoApproveRuleDetailBeanDB() {
        super(false);
    }

    public AutoApproveRuleDetailPK ejbCreate(AutoApproveRuleDetailData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AutoApproveRuleDetailData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AutoApproveRuleDetailPK ejbFindByPrimaryKey(AutoApproveRuleDetailPK pk) throws FinderException {
        return (AutoApproveRuleDetailPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AutoApproveRuleDetailPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AutoApproveRuleDetailPK> v = new Vector<AutoApproveRuleDetailPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AutoApproveRuleDetailPK pk, AutoApproveRuleDetailData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AutoApproveRuleDetailPK pk, AutoApproveRuleDetailData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AutoApproveRuleDetailPK pk, AutoApproveRuleDetailData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AutoApproveRuleDetailPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AutoApproveRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AutoApproveRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AutoApproveRuleDetailColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AutoApproveRuleDetailColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AutoApproveRuleDetailPK pk = (AutoApproveRuleDetailPK)genericPk;
        this.autoApproveRuleGid = pk.autoApproveRuleGid;
        this.sequence = pk.sequence;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AutoApproveRuleDetailPK pk = new AutoApproveRuleDetailPK(0, this.autoApproveRuleGid, this.sequence, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AutoApproveRuleDetail";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AutoApproveRuleDetailData getData() throws GLException {
        try {
            AutoApproveRuleDetailData retval = new AutoApproveRuleDetailData();
            retval.getFromBean(this, AutoApproveRuleDetailColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AutoApproveRuleDetailData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AutoApproveRuleDetailData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AutoApproveRuleDetailData oldData = modified ? this.getData() : null;
            data.setToBean(this, AutoApproveRuleDetailColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AutoApproveRuleDetailData.class;
    }

    @Override
    public Class getPkClass() {
        return AutoApproveRuleDetailPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public Integer getSequence() throws GLException {
        try {
            return (Integer)AutoApproveRuleDetailColumns.sequence.convertFromDB(this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSequence(Integer sequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sequence;
            AutoApproveRuleDetailData data = this.getData();
            this.sequence = AutoApproveRuleDetailColumns.sequence.convertToDB(sequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sequence", Integer.class, oldValue, this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAutoApproveRuleGid() throws GLException {
        try {
            return (String)AutoApproveRuleDetailColumns.autoApproveRuleGid.convertFromDB(this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleGid(String autoApproveRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleGid;
            AutoApproveRuleDetailData data = this.getData();
            this.autoApproveRuleGid = AutoApproveRuleDetailColumns.autoApproveRuleGid.convertToDB(autoApproveRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleGid", String.class, oldValue, this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getType() throws GLException {
        try {
            return (String)AutoApproveRuleDetailColumns.type.convertFromDB(this.type);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setType(String type) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.type;
            AutoApproveRuleDetailData data = this.getData();
            this.type = AutoApproveRuleDetailColumns.type.convertToDB(type);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "type", String.class, oldValue, this.type);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getAllowablePercentAbove() throws GLException {
        try {
            return (Double)AutoApproveRuleDetailColumns.allowablePercentAbove.convertFromDB(this.allowablePercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowablePercentAbove(Double allowablePercentAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowablePercentAbove;
            AutoApproveRuleDetailData data = this.getData();
            this.allowablePercentAbove = AutoApproveRuleDetailColumns.allowablePercentAbove.convertToDB(allowablePercentAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowablePercentAbove", Double.class, oldValue, this.allowablePercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getAllowablePercentBelow() throws GLException {
        try {
            return (Double)AutoApproveRuleDetailColumns.allowablePercentBelow.convertFromDB(this.allowablePercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowablePercentBelow(Double allowablePercentBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowablePercentBelow;
            AutoApproveRuleDetailData data = this.getData();
            this.allowablePercentBelow = AutoApproveRuleDetailColumns.allowablePercentBelow.convertToDB(allowablePercentBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowablePercentBelow", Double.class, oldValue, this.allowablePercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getApproveToAmount() throws GLException {
        try {
            return (Currency)AutoApproveRuleDetailColumns.approveToAmount.convertFromDB(this.approveToAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApproveToAmount(Currency approveToAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.approveToAmount;
            AutoApproveRuleDetailData data = this.getData();
            this.approveToAmount = AutoApproveRuleDetailColumns.approveToAmount.convertToDB(approveToAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "approveToAmount", Currency.class, oldValue, this.approveToAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getApproveToWeight() throws GLException {
        try {
            return (Weight)AutoApproveRuleDetailColumns.approveToWeight.convertFromDB(this.approveToWeight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApproveToWeight(Weight approveToWeight) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.approveToWeight;
            AutoApproveRuleDetailData data = this.getData();
            this.approveToWeight = AutoApproveRuleDetailColumns.approveToWeight.convertToDB(approveToWeight);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "approveToWeight", Weight.class, oldValue, this.approveToWeight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getAllowCurrAbove() throws GLException {
        try {
            return (Currency)AutoApproveRuleDetailColumns.allowCurrAbove.convertFromDB(this.allowCurrAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowCurrAbove(Currency allowCurrAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowCurrAbove;
            AutoApproveRuleDetailData data = this.getData();
            this.allowCurrAbove = AutoApproveRuleDetailColumns.allowCurrAbove.convertToDB(allowCurrAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowCurrAbove", Currency.class, oldValue, this.allowCurrAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getAllowCurrBelow() throws GLException {
        try {
            return (Currency)AutoApproveRuleDetailColumns.allowCurrBelow.convertFromDB(this.allowCurrBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowCurrBelow(Currency allowCurrBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowCurrBelow;
            AutoApproveRuleDetailData data = this.getData();
            this.allowCurrBelow = AutoApproveRuleDetailColumns.allowCurrBelow.convertToDB(allowCurrBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowCurrBelow", Currency.class, oldValue, this.allowCurrBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getAllowWeightAbove() throws GLException {
        try {
            return (Weight)AutoApproveRuleDetailColumns.allowWeightAbove.convertFromDB(this.allowWeightAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowWeightAbove(Weight allowWeightAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowWeightAbove;
            AutoApproveRuleDetailData data = this.getData();
            this.allowWeightAbove = AutoApproveRuleDetailColumns.allowWeightAbove.convertToDB(allowWeightAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowWeightAbove", Weight.class, oldValue, this.allowWeightAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getAllowWeightBelow() throws GLException {
        try {
            return (Weight)AutoApproveRuleDetailColumns.allowWeightBelow.convertFromDB(this.allowWeightBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAllowWeightBelow(Weight allowWeightBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.allowWeightBelow;
            AutoApproveRuleDetailData data = this.getData();
            this.allowWeightBelow = AutoApproveRuleDetailColumns.allowWeightBelow.convertToDB(allowWeightBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "allowWeightBelow", Weight.class, oldValue, this.allowWeightBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AutoApproveRuleDetailColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AutoApproveRuleDetailData data = this.getData();
            this.domainName = AutoApproveRuleDetailColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
