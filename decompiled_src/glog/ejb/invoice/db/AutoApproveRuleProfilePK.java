/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleProfileColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AutoApproveRuleProfilePK
extends Pk {
    public Object autoApproveRuleProfileGid;
    public transient Object autoApproveRuleProfileXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AutoApproveRuleProfilePK() {
    }

    public AutoApproveRuleProfilePK(String autoApproveRuleProfileGid) {
        this.autoApproveRuleProfileGid = this.notNull(AutoApproveRuleProfileColumns.autoApproveRuleProfileGid.convertToDB(autoApproveRuleProfileGid), "autoApproveRuleProfileGid");
    }

    public AutoApproveRuleProfilePK(String domainName, String autoApproveRuleProfileXid) {
        this.domainName = domainName;
        this.autoApproveRuleProfileXid = autoApproveRuleProfileXid;
        this.autoApproveRuleProfileGid = AutoApproveRuleProfilePK.concatForGid(domainName, autoApproveRuleProfileXid);
    }

    public AutoApproveRuleProfilePK(int dummy, Object autoApproveRuleProfileGid) {
        this(dummy, autoApproveRuleProfileGid, null);
    }

    public AutoApproveRuleProfilePK(int dummy, Object autoApproveRuleProfileGid, Object transaction) {
        this.autoApproveRuleProfileGid = autoApproveRuleProfileGid;
        this.transaction = transaction;
    }

    public AutoApproveRuleProfilePK(AutoApproveRuleProfilePK otherPk, Object transaction) {
        this.autoApproveRuleProfileGid = otherPk.autoApproveRuleProfileGid;
        this.autoApproveRuleProfileXid = otherPk.autoApproveRuleProfileXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.autoApproveRuleProfileGid != null ? String.valueOf(this.autoApproveRuleProfileGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.autoApproveRuleProfileGid != null ? String.valueOf(this.autoApproveRuleProfileGid) : "";
    }

    public static AutoApproveRuleProfilePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AutoApproveRuleProfilePK(gids[0]) : null;
    }

    public static AutoApproveRuleProfilePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AutoApproveRuleProfilePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.autoApproveRuleProfileGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AutoApproveRuleProfilePK)) {
            return false;
        }
        AutoApproveRuleProfilePK otherPk = (AutoApproveRuleProfilePK)other;
        return Functions.equals(otherPk.autoApproveRuleProfileGid, this.autoApproveRuleProfileGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.AutoApproveRuleProfileHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.autoApproveRuleProfileGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AutoApproveRuleProfileColumns.autoApproveRuleProfileGid.convertFromDB(this.autoApproveRuleProfileGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AutoApproveRuleProfilePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AutoApproveRuleProfilePK requires a non-null XID to generate a GID");
            }
            AutoApproveRuleProfilePK autoApproveRuleProfilePK = new AutoApproveRuleProfilePK(domain, xid);
            return autoApproveRuleProfilePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AutoApproveRuleProfilePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AutoApproveRuleProfilePK autoApproveRuleProfilePK = AutoApproveRuleProfilePK.newPK(domainName, xid, connection);
            return autoApproveRuleProfilePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AutoApproveRuleProfilePK.class;
        }

        @Override
        public final String getEntity() {
            return "AutoApproveRuleProfile";
        }

        @Override
        public final String getTableName() {
            return "auto_approve_rule_profile";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"auto_approve_rule_profile_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AutoApproveRuleProfilePK result = new AutoApproveRuleProfilePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
