/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceRouteColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn routeSeqNo = new SqlColumn(Integer.class, "route_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn transportModeIdentifier = new SqlColumn(String.class, "transport_mode_identifier", 101, 0, 0, null, 2, null);
    public static SqlColumn servprovAliasQualGid = new SqlColumn(String.class, "servprov_alias_qual_gid", 101, 0, 1, null, 3, null);
    public static SqlColumn servprovAliasValue = new SqlColumn(String.class, "servprov_alias_value", 101, 0, 1, null, 4, null);
    public static SqlColumn intermodalServiceCode = new SqlColumn(String.class, "intermodal_service_code", 2, 0, 0, null, 5, null);
    public static SqlColumn jctCityCode = new SqlColumn(String.class, "jct_city_code", 101, 0, 0, null, 6, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 7, null);
}
