/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AutoApproveRuleFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("maxAmtOverInvCostCurrGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getPkField() {
                return "maxAmtOverInvCostCurrGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("modeProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getPkField() {
                return "modeProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.modeprofile.db";
            }

            @Override
            public String getFkDataClass() {
                return "ModeProfileData";
            }

            @Override
            public String getFkDataField() {
                return "modeProfileGid";
            }
        });
        fks.put("defCostAboveCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getPkField() {
                return "defCostAboveCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("locationProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getPkField() {
                return "locationProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.location.db";
            }

            @Override
            public String getFkDataClass() {
                return "LocationProfileData";
            }

            @Override
            public String getFkDataField() {
                return "locationProfileGid";
            }
        });
        fks.put("defCostBelowCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getPkField() {
                return "defCostBelowCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
    }
}
