/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvSupplyProvinceVatRegFKList.2
implements Fk {
    InvSupplyProvinceVatRegFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvSupplyProvinceVatRegData";
    }

    @Override
    public String getPkField() {
        return "vatProvincialRegGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vat.db";
    }

    @Override
    public String getFkDataClass() {
        return "VatProvincialRegistrationData";
    }

    @Override
    public String getFkDataField() {
        return "vatProvincialRegGid";
    }
}
