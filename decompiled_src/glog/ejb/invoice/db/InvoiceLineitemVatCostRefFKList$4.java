/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemVatCostRefFKList.4
implements Fk {
    InvoiceLineitemVatCostRefFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemVatCostRefData";
    }

    @Override
    public String getPkField() {
        return "vatSeqno";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceLineitemVatData";
    }

    @Override
    public String getFkDataField() {
        return "vatSeqno";
    }
}
