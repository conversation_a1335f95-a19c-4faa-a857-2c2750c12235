/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRouteData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceRouteRemoteDB
extends EJBObject {
    public InvoiceRouteData getData() throws RemoteException, GLException;

    public void setData(InvoiceRouteData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getRouteSeqNo() throws RemoteException, GLException;

    public void setRouteSeqNo(Integer var1) throws RemoteException, GLException;

    public String getTransportModeIdentifier() throws RemoteException, GLException;

    public void setTransportModeIdentifier(String var1) throws RemoteException, GLException;

    public String getServprovAliasQualGid() throws RemoteException, GLException;

    public void setServprovAliasQualGid(String var1) throws RemoteException, GLException;

    public String getServprovAliasValue() throws RemoteException, GLException;

    public void setServprovAliasValue(String var1) throws RemoteException, GLException;

    public String getIntermodalServiceCode() throws RemoteException, GLException;

    public void setIntermodalServiceCode(String var1) throws RemoteException, GLException;

    public String getJctCityCode() throws RemoteException, GLException;

    public void setJctCityCode(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
