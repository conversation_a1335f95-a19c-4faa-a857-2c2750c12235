/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherRefnumPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class VoucherRefnumData
extends BeanData {
    public String voucherGid;
    public String voucherRefnumQualGid;
    public String voucherRefnumValue;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherRefnumData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field voucherGidField = beanDataFields[0];
    public static Field voucherRefnumQualGidField = beanDataFields[1];
    public static Field voucherRefnumValueField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public VoucherRefnumData() {
    }

    public VoucherRefnumData(VoucherRefnumData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getVoucherRefnumPK();
    }

    @Legacy
    public VoucherRefnumPK getVoucherRefnumPK() {
        if (this.voucherGid == null) {
            return null;
        }
        if (this.voucherRefnumQualGid == null) {
            return null;
        }
        if (this.voucherRefnumValue == null) {
            return null;
        }
        return new VoucherRefnumPK(this.voucherGid, this.voucherRefnumQualGid, this.voucherRefnumValue);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setVoucherRefnumPK((VoucherRefnumPK)pk);
    }

    @Legacy
    public void setVoucherRefnumPK(VoucherRefnumPK pk) {
        this.voucherGid = (String)pk.getAppValue(0);
        this.voucherRefnumQualGid = (String)pk.getAppValue(1);
        this.voucherRefnumValue = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.VoucherRefnumQueryGen";
    }

    public static VoucherRefnumData load(Connection conn, VoucherRefnumPK pk) throws GLException {
        return (VoucherRefnumData)VoucherRefnumData.load(conn, pk, VoucherRefnumData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return VoucherRefnumData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return VoucherRefnumData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherRefnumData.load(conn, whereClause, prepareArguments, fetchSize, VoucherRefnumData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return VoucherRefnumData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherRefnumData.load(conn, fromWhere, alias, prepareArguments, fetchSize, VoucherRefnumData.class);
    }
}
