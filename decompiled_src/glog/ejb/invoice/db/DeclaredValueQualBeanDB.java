/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.DeclaredValueQualColumns;
import glog.ejb.invoice.db.DeclaredValueQualData;
import glog.ejb.invoice.db.DeclaredValueQualPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class DeclaredValueQualBeanDB
extends BeanManagedEntityBean {
    public Object declaredValueQualGid;
    public Object declaredValueQualXid;
    public Object declaredValueDesc;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient DeclaredValueQualPK pk;
    protected transient DeclaredValueQualData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(DeclaredValueQualBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static DeclaredValueQualPK.Callback theCall = new DeclaredValueQualPK.Callback();

    public DeclaredValueQualBeanDB() {
        super(false);
    }

    public DeclaredValueQualPK ejbCreate(DeclaredValueQualData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected DeclaredValueQualPK newPK() throws GLException {
        return DeclaredValueQualPK.newPK(this.getDomainName(), this.getDeclaredValueQualXid(), this.getConnection());
    }

    public void ejbPostCreate(DeclaredValueQualData data) throws CreateException {
        this.ejbPostCreator();
    }

    public DeclaredValueQualPK ejbFindByPrimaryKey(DeclaredValueQualPK pk) throws FinderException {
        return (DeclaredValueQualPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(DeclaredValueQualPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<DeclaredValueQualPK> v = new Vector<DeclaredValueQualPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(DeclaredValueQualPK pk, DeclaredValueQualData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(DeclaredValueQualPK pk, DeclaredValueQualData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(DeclaredValueQualPK pk, DeclaredValueQualData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(DeclaredValueQualColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(DeclaredValueQualColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(DeclaredValueQualColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(DeclaredValueQualColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        DeclaredValueQualPK pk = (DeclaredValueQualPK)genericPk;
        this.declaredValueQualGid = pk.declaredValueQualGid;
        this.domainName = pk.domainName;
        this.declaredValueQualXid = pk.declaredValueQualXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        DeclaredValueQualPK pk = new DeclaredValueQualPK(0, this.declaredValueQualGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.DeclaredValueQual";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public DeclaredValueQualData getData() throws GLException {
        try {
            DeclaredValueQualData retval = new DeclaredValueQualData();
            retval.getFromBean(this, DeclaredValueQualColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(DeclaredValueQualData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(DeclaredValueQualData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            DeclaredValueQualData oldData = modified ? this.getData() : null;
            data.setToBean(this, DeclaredValueQualColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return DeclaredValueQualData.class;
    }

    @Override
    public Class getPkClass() {
        return DeclaredValueQualPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getDeclaredValueQualGid() throws GLException {
        try {
            return (String)DeclaredValueQualColumns.declaredValueQualGid.convertFromDB(this.declaredValueQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDeclaredValueQualGid(String declaredValueQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.declaredValueQualGid;
            DeclaredValueQualData data = this.getData();
            this.declaredValueQualGid = DeclaredValueQualColumns.declaredValueQualGid.convertToDB(declaredValueQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "declaredValueQualGid", String.class, oldValue, this.declaredValueQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDeclaredValueQualXid() throws GLException {
        try {
            return (String)DeclaredValueQualColumns.declaredValueQualXid.convertFromDB(this.declaredValueQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDeclaredValueQualXid(String declaredValueQualXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.declaredValueQualXid;
            DeclaredValueQualData data = this.getData();
            this.declaredValueQualXid = DeclaredValueQualColumns.declaredValueQualXid.convertToDB(declaredValueQualXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "declaredValueQualXid", String.class, oldValue, this.declaredValueQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDeclaredValueDesc() throws GLException {
        try {
            return (String)DeclaredValueQualColumns.declaredValueDesc.convertFromDB(this.declaredValueDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDeclaredValueDesc(String declaredValueDesc) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.declaredValueDesc;
            DeclaredValueQualData data = this.getData();
            this.declaredValueDesc = DeclaredValueQualColumns.declaredValueDesc.convertToDB(declaredValueDesc);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "declaredValueDesc", String.class, oldValue, this.declaredValueDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)DeclaredValueQualColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            DeclaredValueQualData data = this.getData();
            this.domainName = DeclaredValueQualColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
