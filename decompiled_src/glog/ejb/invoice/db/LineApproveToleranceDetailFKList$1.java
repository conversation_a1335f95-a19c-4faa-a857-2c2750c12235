/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineApproveToleranceDetailFKList.1
implements Fk {
    LineApproveToleranceDetailFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineApproveToleranceDetailData";
    }

    @Override
    public String getPkField() {
        return "allowCurrBelowCurrencyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "CurrencyData";
    }

    @Override
    public String getFkDataField() {
        return "currencyGid";
    }
}
