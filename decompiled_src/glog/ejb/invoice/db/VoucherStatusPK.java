/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherStatusColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class VoucherStatusPK
extends Pk {
    public Object statusTypeGid;
    public Object voucherGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public VoucherStatusPK() {
    }

    public VoucherStatusPK(String statusTypeGid, String voucherGid) {
        this.statusTypeGid = this.notNull(VoucherStatusColumns.statusTypeGid.convertToDB(statusTypeGid), "statusTypeGid");
        this.voucherGid = this.notNull(VoucherStatusColumns.voucherGid.convertToDB(voucherGid), "voucherGid");
    }

    public VoucherStatusPK(int dummy, Object statusTypeGid, Object voucherGid) {
        this(dummy, statusTypeGid, voucherGid, null);
    }

    public VoucherStatusPK(int dummy, Object statusTypeGid, Object voucherGid, Object transaction) {
        this.statusTypeGid = statusTypeGid;
        this.voucherGid = voucherGid;
        this.transaction = transaction;
    }

    public VoucherStatusPK(VoucherStatusPK otherPk, Object transaction) {
        this.statusTypeGid = otherPk.statusTypeGid;
        this.voucherGid = otherPk.voucherGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.statusTypeGid != null ? String.valueOf(this.statusTypeGid) : "") + " " + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.statusTypeGid != null ? String.valueOf(this.statusTypeGid) : "") + "|" + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    public static VoucherStatusPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new VoucherStatusPK(gids[0], gids[1]) : null;
    }

    public static VoucherStatusPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new VoucherStatusPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.statusTypeGid.hashCode() + this.voucherGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof VoucherStatusPK)) {
            return false;
        }
        VoucherStatusPK otherPk = (VoucherStatusPK)other;
        return Functions.equals(otherPk.statusTypeGid, this.statusTypeGid) && Functions.equals(otherPk.voucherGid, this.voucherGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.VoucherStatusHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.statusTypeGid;
            }
            case 1: {
                return this.voucherGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return VoucherStatusColumns.statusTypeGid.convertFromDB(this.statusTypeGid);
            }
            case 1: {
                return VoucherStatusColumns.voucherGid.convertFromDB(this.voucherGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return VoucherStatusPK.class;
        }

        @Override
        public final String getEntity() {
            return "VoucherStatus";
        }

        @Override
        public final String getTableName() {
            return "voucher_status";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"status_type_gid", "voucher_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            VoucherStatusPK result = new VoucherStatusPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
