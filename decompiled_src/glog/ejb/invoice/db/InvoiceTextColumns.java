/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceTextColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn textTemplateGid = new SqlColumn(String.class, "text_template_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn documentDefGid = new SqlColumn(String.class, "document_def_gid", 101, 0, 1, String.valueOf("ALL"), 2, null);
    public static SqlColumn textOverride = new SqlColumn(String.class, "text_override", 4000, 0, 0, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
