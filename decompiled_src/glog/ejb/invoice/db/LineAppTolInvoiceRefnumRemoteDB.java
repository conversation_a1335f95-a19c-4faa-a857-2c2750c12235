/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineAppTolInvoiceRefnumData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface LineAppTolInvoiceRefnumRemoteDB
extends EJBObject {
    public LineAppTolInvoiceRefnumData getData() throws RemoteException, GLException;

    public void setData(LineAppTolInvoiceRefnumData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getLineApproveToleranceGid() throws RemoteException, GLException;

    public void setLineApproveToleranceGid(String var1) throws RemoteException, GLException;

    public Integer getRefnumSequence() throws RemoteException, GLException;

    public void setRefnumSequence(Integer var1) throws RemoteException, GLException;

    public String getInvoiceRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceRefnumValue() throws RemoteException, GLException;

    public void setInvoiceRefnumValue(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
