/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.9
implements Fk {
    InvoiceFKList.9() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "destCountryCode3Gid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "CountryCodeData";
    }

    @Override
    public String getFkDataField() {
        return "countryCode3Gid";
    }
}
