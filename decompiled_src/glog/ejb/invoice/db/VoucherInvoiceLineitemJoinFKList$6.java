/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherInvoiceLineitemJoinFKList.6
implements Fk {
    VoucherInvoiceLineitemJoinFKList.6() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherInvoiceLineitemJoinData";
    }

    @Override
    public String getPkField() {
        return "adjustmentReasonGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "AdjustmentReasonData";
    }

    @Override
    public String getFkDataField() {
        return "adjustmentReasonGid";
    }
}
