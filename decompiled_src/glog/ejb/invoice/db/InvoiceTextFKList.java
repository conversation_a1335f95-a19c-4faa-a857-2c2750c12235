/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvoiceTextFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("invoiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceTextData";
            }

            @Override
            public String getPkField() {
                return "invoiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceGid";
            }
        });
        fks.put("documentDefGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceTextData";
            }

            @Override
            public String getPkField() {
                return "documentDefGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.document.db";
            }

            @Override
            public String getFkDataClass() {
                return "DocumentDefData";
            }

            @Override
            public String getFkDataField() {
                return "documentDefGid";
            }
        });
        fks.put("textTemplateGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceTextData";
            }

            @Override
            public String getPkField() {
                return "textTemplateGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.text.db";
            }

            @Override
            public String getFkDataClass() {
                return "TextTemplateData";
            }

            @Override
            public String getFkDataField() {
                return "textTemplateGid";
            }
        });
    }
}
