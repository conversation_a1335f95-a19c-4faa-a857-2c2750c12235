/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class AdjustmentReasonColumns {
    public static SqlColumn adjustmentReasonGid = new SqlColumn(String.class, "adjustment_reason_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn adjustmentReasonXid = new SqlColumn(String.class, "adjustment_reason_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 2, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 101, 0, 0, null, 3, null);
}
