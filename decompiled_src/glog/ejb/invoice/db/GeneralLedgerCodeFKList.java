/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class GeneralLedgerCodeFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("sellShipGlLookupKeyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getPkField() {
                return "sellShipGlLookupKeyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GlLookupKeyData";
            }

            @Override
            public String getFkDataField() {
                return "glLookupKeyGid";
            }
        });
        fks.put("buyOrderGlLookupKeyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getPkField() {
                return "buyOrderGlLookupKeyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GlLookupKeyData";
            }

            @Override
            public String getFkDataField() {
                return "glLookupKeyGid";
            }
        });
        fks.put("buyShipGlLookupKeyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getPkField() {
                return "buyShipGlLookupKeyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GlLookupKeyData";
            }

            @Override
            public String getFkDataField() {
                return "glLookupKeyGid";
            }
        });
        fks.put("sellOrderGlLookupKeyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getPkField() {
                return "sellOrderGlLookupKeyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GlLookupKeyData";
            }

            @Override
            public String getFkDataField() {
                return "glLookupKeyGid";
            }
        });
    }
}
