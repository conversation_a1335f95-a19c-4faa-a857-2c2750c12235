/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CostTypeColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class CostTypePK
extends Pk {
    public Object costTypeGid;
    public transient Object costTypeXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public CostTypePK() {
    }

    public CostTypePK(String costTypeGid) {
        this.costTypeGid = this.notNull(CostTypeColumns.costTypeGid.convertToDB(costTypeGid), "costTypeGid");
    }

    public CostTypePK(String domainName, String costTypeXid) {
        this.domainName = domainName;
        this.costTypeXid = costTypeXid;
        this.costTypeGid = CostTypePK.concatForGid(domainName, costTypeXid);
    }

    public CostTypePK(int dummy, Object costTypeGid) {
        this(dummy, costTypeGid, null);
    }

    public CostTypePK(int dummy, Object costTypeGid, Object transaction) {
        this.costTypeGid = costTypeGid;
        this.transaction = transaction;
    }

    public CostTypePK(CostTypePK otherPk, Object transaction) {
        this.costTypeGid = otherPk.costTypeGid;
        this.costTypeXid = otherPk.costTypeXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.costTypeGid != null ? String.valueOf(this.costTypeGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.costTypeGid != null ? String.valueOf(this.costTypeGid) : "";
    }

    public static CostTypePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new CostTypePK(gids[0]) : null;
    }

    public static CostTypePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new CostTypePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.costTypeGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof CostTypePK)) {
            return false;
        }
        CostTypePK otherPk = (CostTypePK)other;
        return Functions.equals(otherPk.costTypeGid, this.costTypeGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.CostTypeHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.costTypeGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return CostTypeColumns.costTypeGid.convertFromDB(this.costTypeGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static CostTypePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("CostTypePK requires a non-null XID to generate a GID");
            }
            CostTypePK costTypePK = new CostTypePK(domain, xid);
            return costTypePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static CostTypePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            CostTypePK costTypePK = CostTypePK.newPK(domainName, xid, connection);
            return costTypePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return CostTypePK.class;
        }

        @Override
        public final String getEntity() {
            return "CostType";
        }

        @Override
        public final String getTableName() {
            return "cost_type";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"cost_type_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            CostTypePK result = new CostTypePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
