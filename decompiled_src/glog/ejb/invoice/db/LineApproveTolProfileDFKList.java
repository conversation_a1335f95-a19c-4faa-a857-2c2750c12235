/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class LineApproveTolProfileDFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("lineApproveTolProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveTolProfileDData";
            }

            @Override
            public String getPkField() {
                return "lineApproveTolProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "LineApproveTolProfileData";
            }

            @Override
            public String getFkDataField() {
                return "lineApproveTolProfileGid";
            }
        });
        fks.put("lineApproveToleranceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveTolProfileDData";
            }

            @Override
            public String getPkField() {
                return "lineApproveToleranceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "LineApproveToleranceData";
            }

            @Override
            public String getFkDataField() {
                return "lineApproveToleranceGid";
            }
        });
    }
}
