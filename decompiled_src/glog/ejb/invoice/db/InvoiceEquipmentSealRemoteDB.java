/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceEquipmentSealData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceEquipmentSealRemoteDB
extends EJBObject {
    public InvoiceEquipmentSealData getData() throws RemoteException, GLException;

    public void setData(InvoiceEquipmentSealData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLEx<PERSON>;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Long getSeqNumber() throws RemoteException, GLException;

    public void setSeqNumber(Long var1) throws RemoteException, GLException;

    public Integer getSealSequence() throws RemoteException, GLException;

    public void setSealSequence(Integer var1) throws RemoteException, GLException;

    public String getSealNumber() throws RemoteException, GLException;

    public void setSealNumber(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
