/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceLineitemRemarkColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineitemSeqNo = new SqlColumn(Integer.class, "lineitem_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn remarkSeqNo = new SqlColumn(Integer.class, "remark_seq_no", 8, 0, 1, null, 2, null);
    public static SqlColumn remarkQualIdentifier = new SqlColumn(String.class, "remark_qual_identifier", 101, 0, 0, null, 3, null);
    public static SqlColumn remarkText = new SqlColumn(String.class, "remark_text", 4000, 0, 1, null, 4, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 5, null);
}
