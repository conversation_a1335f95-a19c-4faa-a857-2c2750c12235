/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemRefnumQualFKList.1
implements Fk {
    InvoiceLineitemRefnumQualFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemRefnumQualData";
    }

    @Override
    public String getPkField() {
        return "defaultRefnumBnTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.bngenerator.db";
    }

    @Override
    public String getFkDataClass() {
        return "BnTypeData";
    }

    @Override
    public String getFkDataField() {
        return "bnTypeGid";
    }
}
