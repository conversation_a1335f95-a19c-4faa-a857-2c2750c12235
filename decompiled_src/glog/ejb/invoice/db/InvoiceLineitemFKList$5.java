/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.5
implements Fk {
    InvoiceLineitemFKList.5() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "transportHandlingUnitGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.order.db";
    }

    @Override
    public String getFkDataClass() {
        return "ShipUnitSpecData";
    }

    @Override
    public String getFkDataField() {
        return "shipUnitSpecGid";
    }
}
