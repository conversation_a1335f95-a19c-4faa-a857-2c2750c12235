/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class AutoApproveRuleProfileColumns {
    public static SqlColumn autoApproveRuleProfileGid = new SqlColumn(String.class, "auto_approve_rule_profile_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn autoApproveRuleProfileXid = new SqlColumn(String.class, "auto_approve_rule_profile_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 101, 0, 0, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
