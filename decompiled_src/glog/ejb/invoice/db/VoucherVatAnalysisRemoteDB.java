/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherVatAnalysisData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface VoucherVatAnalysisRemoteDB
extends EJBObject {
    public VoucherVatAnalysisData getData() throws RemoteException, GLException;

    public void setData(VoucherVatAnalysisData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws <PERSON>moteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getVoucherGid() throws RemoteException, GLException;

    public void setVoucherGid(String var1) throws RemoteException, GLException;

    public String getVatCodeGid() throws RemoteException, GLException;

    public void setVatCodeGid(String var1) throws RemoteException, GLException;

    public Currency getTaxAmount() throws RemoteException, GLException;

    public void setTaxAmount(Currency var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public Double getVatRate() throws RemoteException, GLException;

    public void setVatRate(Double var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
