/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceData;
import glog.server.status.RemoteStatusable;
import glog.server.status.StatusFunction;
import glog.server.workflow.status.StatusError;
import glog.util.LocalDate;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceRemoteDB
extends EJBObject,
RemoteStatusable {
    public InvoiceData getData() throws RemoteException, GLException;

    public void setData(InvoiceData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws <PERSON>moteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public String getInvoiceXid() throws RemoteException, GLException;

    public void setInvoiceXid(String var1) throws RemoteException, GLException;

    public String getInvoiceType() throws RemoteException, GLException;

    public void setInvoiceType(String var1) throws RemoteException, GLException;

    public String getInvoiceSource() throws RemoteException, GLException;

    public void setInvoiceSource(String var1) throws RemoteException, GLException;

    public String getEnteredByGlUserGid() throws RemoteException, GLException;

    public void setEnteredByGlUserGid(String var1) throws RemoteException, GLException;

    public String getInvoiceNumber() throws RemoteException, GLException;

    public void setInvoiceNumber(String var1) throws RemoteException, GLException;

    public String getServprovAliasQualGid() throws RemoteException, GLException;

    public void setServprovAliasQualGid(String var1) throws RemoteException, GLException;

    public String getServprovAliasValue() throws RemoteException, GLException;

    public void setServprovAliasValue(String var1) throws RemoteException, GLException;

    public String getCorrectionCodeId() throws RemoteException, GLException;

    public void setCorrectionCodeId(String var1) throws RemoteException, GLException;

    public LocalTimestamp getInvoiceDate() throws RemoteException, GLException;

    public void setInvoiceDate(LocalTimestamp var1) throws RemoteException, GLException;

    public String getCurrencyGid() throws RemoteException, GLException;

    public void setCurrencyGid(String var1) throws RemoteException, GLException;

    public Currency getNetAmountDue() throws RemoteException, GLException;

    public void setNetAmountDue(Currency var1) throws RemoteException, GLException;

    public LocalTimestamp getNetDueDate() throws RemoteException, GLException;

    public void setNetDueDate(LocalTimestamp var1) throws RemoteException, GLException;

    public String getPaymentMethodCodeGid() throws RemoteException, GLException;

    public void setPaymentMethodCodeGid(String var1) throws RemoteException, GLException;

    public LocalTimestamp getStartDate() throws RemoteException, GLException;

    public void setStartDate(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getEndDate() throws RemoteException, GLException;

    public void setEndDate(LocalTimestamp var1) throws RemoteException, GLException;

    public String getInvoiceServiceCodeGid() throws RemoteException, GLException;

    public void setInvoiceServiceCodeGid(String var1) throws RemoteException, GLException;

    public String getIncoTermGid() throws RemoteException, GLException;

    public void setIncoTermGid(String var1) throws RemoteException, GLException;

    public Currency getDiscountAmount() throws RemoteException, GLException;

    public void setDiscountAmount(Currency var1) throws RemoteException, GLException;

    public Double getDiscountPercentage() throws RemoteException, GLException;

    public void setDiscountPercentage(Double var1) throws RemoteException, GLException;

    public Integer getDiscountDaysDue() throws RemoteException, GLException;

    public void setDiscountDaysDue(Integer var1) throws RemoteException, GLException;

    public LocalDate getDiscountDueDate() throws RemoteException, GLException;

    public void setDiscountDueDate(LocalDate var1) throws RemoteException, GLException;

    public String getOriginStationCity() throws RemoteException, GLException;

    public void setOriginStationCity(String var1) throws RemoteException, GLException;

    public String getOriginStationFsac() throws RemoteException, GLException;

    public void setOriginStationFsac(String var1) throws RemoteException, GLException;

    public String getOriginStationSplc() throws RemoteException, GLException;

    public void setOriginStationSplc(String var1) throws RemoteException, GLException;

    public String getOriginStationProvinceCode() throws RemoteException, GLException;

    public void setOriginStationProvinceCode(String var1) throws RemoteException, GLException;

    public String getOriginStationPostalCode() throws RemoteException, GLException;

    public void setOriginStationPostalCode(String var1) throws RemoteException, GLException;

    public String getOriginCountryCode3Gid() throws RemoteException, GLException;

    public void setOriginCountryCode3Gid(String var1) throws RemoteException, GLException;

    public String getDestStationCity() throws RemoteException, GLException;

    public void setDestStationCity(String var1) throws RemoteException, GLException;

    public String getDestStationFsac() throws RemoteException, GLException;

    public void setDestStationFsac(String var1) throws RemoteException, GLException;

    public String getDestStationSplc() throws RemoteException, GLException;

    public void setDestStationSplc(String var1) throws RemoteException, GLException;

    public String getDestStationProvinceCode() throws RemoteException, GLException;

    public void setDestStationProvinceCode(String var1) throws RemoteException, GLException;

    public String getDestStationPostalCode() throws RemoteException, GLException;

    public void setDestStationPostalCode(String var1) throws RemoteException, GLException;

    public String getDestCountryCode3Gid() throws RemoteException, GLException;

    public void setDestCountryCode3Gid(String var1) throws RemoteException, GLException;

    public LocalDate getLetterOfCreditExpDate() throws RemoteException, GLException;

    public void setLetterOfCreditExpDate(LocalDate var1) throws RemoteException, GLException;

    public LocalDate getLetterOfCreditIssueDate() throws RemoteException, GLException;

    public void setLetterOfCreditIssueDate(LocalDate var1) throws RemoteException, GLException;

    public String getLetterOfCreditNumber() throws RemoteException, GLException;

    public void setLetterOfCreditNumber(String var1) throws RemoteException, GLException;

    public String getVesselCodeQualifier() throws RemoteException, GLException;

    public void setVesselCodeQualifier(String var1) throws RemoteException, GLException;

    public String getVesselCode() throws RemoteException, GLException;

    public void setVesselCode(String var1) throws RemoteException, GLException;

    public String getVesselCountryCode3Gid() throws RemoteException, GLException;

    public void setVesselCountryCode3Gid(String var1) throws RemoteException, GLException;

    public String getVesselGid() throws RemoteException, GLException;

    public void setVesselGid(String var1) throws RemoteException, GLException;

    public String getVesOpServprovAliasQualGid() throws RemoteException, GLException;

    public void setVesOpServprovAliasQualGid(String var1) throws RemoteException, GLException;

    public String getVesOpServprovAliasValue() throws RemoteException, GLException;

    public void setVesOpServprovAliasValue(String var1) throws RemoteException, GLException;

    public String getVoyageNumber() throws RemoteException, GLException;

    public void setVoyageNumber(String var1) throws RemoteException, GLException;

    public String getParentInvoiceGid() throws RemoteException, GLException;

    public void setParentInvoiceGid(String var1) throws RemoteException, GLException;

    public LocalTimestamp getDateReceived() throws RemoteException, GLException;

    public void setDateReceived(LocalTimestamp var1) throws RemoteException, GLException;

    public String getSupplyCountryCode3Gid() throws RemoteException, GLException;

    public void setSupplyCountryCode3Gid(String var1) throws RemoteException, GLException;

    public String getServprovVatRegNoGid() throws RemoteException, GLException;

    public void setServprovVatRegNoGid(String var1) throws RemoteException, GLException;

    public String getCustomerVatRegNoGid() throws RemoteException, GLException;

    public void setCustomerVatRegNoGid(String var1) throws RemoteException, GLException;

    public String getVatExemptValue() throws RemoteException, GLException;

    public void setVatExemptValue(String var1) throws RemoteException, GLException;

    public Currency getNetAmtDueWithTax() throws RemoteException, GLException;

    public void setNetAmtDueWithTax(Currency var1) throws RemoteException, GLException;

    public Boolean getIsPassThrough() throws RemoteException, GLException;

    public void setIsPassThrough(Boolean var1) throws RemoteException, GLException;

    public String getConsolidationType() throws RemoteException, GLException;

    public void setConsolidationType(String var1) throws RemoteException, GLException;

    public Boolean getIsFixedCost() throws RemoteException, GLException;

    public void setIsFixedCost(Boolean var1) throws RemoteException, GLException;

    public Currency getBaseCharge() throws RemoteException, GLException;

    public void setBaseCharge(Currency var1) throws RemoteException, GLException;

    public Currency getOtherCharge() throws RemoteException, GLException;

    public void setOtherCharge(Currency var1) throws RemoteException, GLException;

    public Boolean getIsCreditNote() throws RemoteException, GLException;

    public void setIsCreditNote(Boolean var1) throws RemoteException, GLException;

    public String getUserDefined1IconGid() throws RemoteException, GLException;

    public void setUserDefined1IconGid(String var1) throws RemoteException, GLException;

    public String getUserDefined2IconGid() throws RemoteException, GLException;

    public void setUserDefined2IconGid(String var1) throws RemoteException, GLException;

    public String getUserDefined3IconGid() throws RemoteException, GLException;

    public void setUserDefined3IconGid(String var1) throws RemoteException, GLException;

    public String getUserDefined4IconGid() throws RemoteException, GLException;

    public void setUserDefined4IconGid(String var1) throws RemoteException, GLException;

    public String getUserDefined5IconGid() throws RemoteException, GLException;

    public void setUserDefined5IconGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getServprovGid() throws RemoteException, GLException;

    public void setServprovGid(String var1) throws RemoteException, GLException;

    public LocalTimestamp getSailDate() throws RemoteException, GLException;

    public void setSailDate(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getSailCutoffDate() throws RemoteException, GLException;

    public void setSailCutoffDate(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getRailDate() throws RemoteException, GLException;

    public void setRailDate(LocalTimestamp var1) throws RemoteException, GLException;

    public Boolean getIsHazardous() throws RemoteException, GLException;

    public void setIsHazardous(Boolean var1) throws RemoteException, GLException;

    public Boolean getIsTemperatureControl() throws RemoteException, GLException;

    public void setIsTemperatureControl(Boolean var1) throws RemoteException, GLException;

    public String getInvoicingProcess() throws RemoteException, GLException;

    public void setInvoicingProcess(String var1) throws RemoteException, GLException;

    public Boolean getIsVatAnalysisFixed() throws RemoteException, GLException;

    public void setIsVatAnalysisFixed(Boolean var1) throws RemoteException, GLException;

    public String getOriginalInvoiceGid() throws RemoteException, GLException;

    public void setOriginalInvoiceGid(String var1) throws RemoteException, GLException;

    public LocalDate getGlDate() throws RemoteException, GLException;

    public void setGlDate(LocalDate var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getAttribute1() throws RemoteException, GLException;

    public void setAttribute1(String var1) throws RemoteException, GLException;

    public String getAttribute2() throws RemoteException, GLException;

    public void setAttribute2(String var1) throws RemoteException, GLException;

    public String getAttribute3() throws RemoteException, GLException;

    public void setAttribute3(String var1) throws RemoteException, GLException;

    public String getAttribute4() throws RemoteException, GLException;

    public void setAttribute4(String var1) throws RemoteException, GLException;

    public String getAttribute5() throws RemoteException, GLException;

    public void setAttribute5(String var1) throws RemoteException, GLException;

    public String getAttribute6() throws RemoteException, GLException;

    public void setAttribute6(String var1) throws RemoteException, GLException;

    public String getAttribute7() throws RemoteException, GLException;

    public void setAttribute7(String var1) throws RemoteException, GLException;

    public String getAttribute8() throws RemoteException, GLException;

    public void setAttribute8(String var1) throws RemoteException, GLException;

    public String getAttribute9() throws RemoteException, GLException;

    public void setAttribute9(String var1) throws RemoteException, GLException;

    public String getAttribute10() throws RemoteException, GLException;

    public void setAttribute10(String var1) throws RemoteException, GLException;

    public String getAttribute11() throws RemoteException, GLException;

    public void setAttribute11(String var1) throws RemoteException, GLException;

    public String getAttribute12() throws RemoteException, GLException;

    public void setAttribute12(String var1) throws RemoteException, GLException;

    public String getAttribute13() throws RemoteException, GLException;

    public void setAttribute13(String var1) throws RemoteException, GLException;

    public String getAttribute14() throws RemoteException, GLException;

    public void setAttribute14(String var1) throws RemoteException, GLException;

    public String getAttribute15() throws RemoteException, GLException;

    public void setAttribute15(String var1) throws RemoteException, GLException;

    public String getAttribute16() throws RemoteException, GLException;

    public void setAttribute16(String var1) throws RemoteException, GLException;

    public String getAttribute17() throws RemoteException, GLException;

    public void setAttribute17(String var1) throws RemoteException, GLException;

    public String getAttribute18() throws RemoteException, GLException;

    public void setAttribute18(String var1) throws RemoteException, GLException;

    public String getAttribute19() throws RemoteException, GLException;

    public void setAttribute19(String var1) throws RemoteException, GLException;

    public String getAttribute20() throws RemoteException, GLException;

    public void setAttribute20(String var1) throws RemoteException, GLException;

    public Double getAttributeNumber1() throws RemoteException, GLException;

    public void setAttributeNumber1(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber2() throws RemoteException, GLException;

    public void setAttributeNumber2(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber3() throws RemoteException, GLException;

    public void setAttributeNumber3(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber4() throws RemoteException, GLException;

    public void setAttributeNumber4(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber5() throws RemoteException, GLException;

    public void setAttributeNumber5(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber6() throws RemoteException, GLException;

    public void setAttributeNumber6(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber7() throws RemoteException, GLException;

    public void setAttributeNumber7(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber8() throws RemoteException, GLException;

    public void setAttributeNumber8(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber9() throws RemoteException, GLException;

    public void setAttributeNumber9(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber10() throws RemoteException, GLException;

    public void setAttributeNumber10(Double var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate1() throws RemoteException, GLException;

    public void setAttributeDate1(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate2() throws RemoteException, GLException;

    public void setAttributeDate2(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate3() throws RemoteException, GLException;

    public void setAttributeDate3(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate4() throws RemoteException, GLException;

    public void setAttributeDate4(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate5() throws RemoteException, GLException;

    public void setAttributeDate5(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate6() throws RemoteException, GLException;

    public void setAttributeDate6(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate7() throws RemoteException, GLException;

    public void setAttributeDate7(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate8() throws RemoteException, GLException;

    public void setAttributeDate8(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate9() throws RemoteException, GLException;

    public void setAttributeDate9(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate10() throws RemoteException, GLException;

    public void setAttributeDate10(LocalTimestamp var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency1() throws RemoteException, GLException;

    public void setAttributeCurrency1(Currency var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency2() throws RemoteException, GLException;

    public void setAttributeCurrency2(Currency var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency3() throws RemoteException, GLException;

    public void setAttributeCurrency3(Currency var1) throws RemoteException, GLException;

    public StatusError canPerformWithCauses(StatusFunction var1, Object var2) throws RemoteException, GLException;

    public boolean canPerform(StatusFunction var1, Object var2) throws RemoteException, GLException;

    public void setAsPerformed(StatusFunction var1, Object var2) throws RemoteException, GLException;
}
