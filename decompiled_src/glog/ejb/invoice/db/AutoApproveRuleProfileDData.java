/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleProfileDPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AutoApproveRuleProfileDData
extends BeanData {
    public String autoApproveRuleProfileGid;
    public String autoApproveRuleGid;
    public Integer autoApproveRuleSeqNo;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleProfileDData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field autoApproveRuleProfileGidField = beanDataFields[0];
    public static Field autoApproveRuleGidField = beanDataFields[1];
    public static Field autoApproveRuleSeqNoField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AutoApproveRuleProfileDData() {
    }

    public AutoApproveRuleProfileDData(AutoApproveRuleProfileDData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAutoApproveRuleProfileDPK();
    }

    @Legacy
    public AutoApproveRuleProfileDPK getAutoApproveRuleProfileDPK() {
        if (this.autoApproveRuleGid == null) {
            return null;
        }
        if (this.autoApproveRuleProfileGid == null) {
            return null;
        }
        return new AutoApproveRuleProfileDPK(this.autoApproveRuleGid, this.autoApproveRuleProfileGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAutoApproveRuleProfileDPK((AutoApproveRuleProfileDPK)pk);
    }

    @Legacy
    public void setAutoApproveRuleProfileDPK(AutoApproveRuleProfileDPK pk) {
        this.autoApproveRuleGid = (String)pk.getAppValue(0);
        this.autoApproveRuleProfileGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.AutoApproveRuleProfileDQueryGen";
    }

    public static AutoApproveRuleProfileDData load(Connection conn, AutoApproveRuleProfileDPK pk) throws GLException {
        return (AutoApproveRuleProfileDData)AutoApproveRuleProfileDData.load(conn, pk, AutoApproveRuleProfileDData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AutoApproveRuleProfileDData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleProfileDData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleProfileDData.load(conn, whereClause, prepareArguments, fetchSize, AutoApproveRuleProfileDData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleProfileDData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleProfileDData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AutoApproveRuleProfileDData.class);
    }
}
