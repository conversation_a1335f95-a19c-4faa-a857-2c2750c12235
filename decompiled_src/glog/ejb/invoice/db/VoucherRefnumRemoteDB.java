/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherRefnumData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface VoucherRefnumRemoteDB
extends EJBObject {
    public VoucherRefnumData getData() throws RemoteException, GLException;

    public void setData(VoucherRefnumData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getVoucherGid() throws RemoteException, GLException;

    public void setVoucherGid(String var1) throws RemoteException, GLException;

    public String getVoucherRefnumQualGid() throws RemoteException, GLException;

    public void setVoucherRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getVoucherRefnumValue() throws RemoteException, GLException;

    public void setVoucherRefnumValue(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
