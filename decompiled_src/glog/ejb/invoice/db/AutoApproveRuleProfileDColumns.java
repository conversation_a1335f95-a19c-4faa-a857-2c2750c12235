/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class AutoApproveRuleProfileDColumns {
    public static SqlColumn autoApproveRuleProfileGid = new SqlColumn(String.class, "auto_approve_rule_profile_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn autoApproveRuleGid = new SqlColumn(String.class, "auto_approve_rule_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn autoApproveRuleSeqNo = new SqlColumn(Integer.class, "auto_approve_rule_seq_no", 8, 0, 1, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
