/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemVatData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceLineitemVatRemoteDB
extends EJBObject {
    public InvoiceLineitemVatData getData() throws RemoteException, GLException;

    public void setData(InvoiceLineitemVatData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws <PERSON>moteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Integer var1) throws RemoteException, GLException;

    public Integer getVatSeqno() throws RemoteException, GLException;

    public void setVatSeqno(Integer var1) throws RemoteException, GLException;

    public String getVatCodeGid() throws RemoteException, GLException;

    public void setVatCodeGid(String var1) throws RemoteException, GLException;

    public String getVatCountryCode3Gid() throws RemoteException, GLException;

    public void setVatCountryCode3Gid(String var1) throws RemoteException, GLException;

    public String getVatProvinceCode() throws RemoteException, GLException;

    public void setVatProvinceCode(String var1) throws RemoteException, GLException;

    public Double getVatRate() throws RemoteException, GLException;

    public void setVatRate(Double var1) throws RemoteException, GLException;

    public Currency getVatAmount() throws RemoteException, GLException;

    public void setVatAmount(Currency var1) throws RemoteException, GLException;

    public Currency getVatCalcAmt() throws RemoteException, GLException;

    public void setVatCalcAmt(Currency var1) throws RemoteException, GLException;

    public Currency getVatOverrideAmt() throws RemoteException, GLException;

    public void setVatOverrideAmt(Currency var1) throws RemoteException, GLException;

    public Boolean getIsCumulative() throws RemoteException, GLException;

    public void setIsCumulative(Boolean var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
