/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceRefnumQualColumns;
import glog.ejb.invoice.db.InvoiceRefnumQualData;
import glog.ejb.invoice.db.InvoiceRefnumQualPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceRefnumQualBeanDB
extends BeanManagedEntityBean {
    public Object invoiceRefnumQualGid;
    public Object invoiceRefnumQualXid;
    public Object invoiceRefnumDesc;
    public Object defaultRefnumBnTypeGid;
    public Object updateFlag;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceRefnumQualPK pk;
    protected transient InvoiceRefnumQualData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRefnumQualBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceRefnumQualPK.Callback theCall = new InvoiceRefnumQualPK.Callback();

    public InvoiceRefnumQualBeanDB() {
        super(false);
    }

    public InvoiceRefnumQualPK ejbCreate(InvoiceRefnumQualData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected InvoiceRefnumQualPK newPK() throws GLException {
        return InvoiceRefnumQualPK.newPK(this.getDomainName(), this.getInvoiceRefnumQualXid(), this.getConnection());
    }

    public void ejbPostCreate(InvoiceRefnumQualData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceRefnumQualPK ejbFindByPrimaryKey(InvoiceRefnumQualPK pk) throws FinderException {
        return (InvoiceRefnumQualPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceRefnumQualPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceRefnumQualPK> v = new Vector<InvoiceRefnumQualPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceRefnumQualPK pk, InvoiceRefnumQualData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceRefnumQualPK pk, InvoiceRefnumQualData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceRefnumQualPK pk, InvoiceRefnumQualData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceRefnumQualColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceRefnumQualPK pk = (InvoiceRefnumQualPK)genericPk;
        this.invoiceRefnumQualGid = pk.invoiceRefnumQualGid;
        this.domainName = pk.domainName;
        this.invoiceRefnumQualXid = pk.invoiceRefnumQualXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceRefnumQualPK pk = new InvoiceRefnumQualPK(0, this.invoiceRefnumQualGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceRefnumQual";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceRefnumQualData getData() throws GLException {
        try {
            InvoiceRefnumQualData retval = new InvoiceRefnumQualData();
            retval.getFromBean(this, InvoiceRefnumQualColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceRefnumQualData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceRefnumQualData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceRefnumQualData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceRefnumQualColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceRefnumQualData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceRefnumQualPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceRefnumQualGid() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.invoiceRefnumQualGid.convertFromDB(this.invoiceRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceRefnumQualGid(String invoiceRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceRefnumQualGid;
            InvoiceRefnumQualData data = this.getData();
            this.invoiceRefnumQualGid = InvoiceRefnumQualColumns.invoiceRefnumQualGid.convertToDB(invoiceRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceRefnumQualGid", String.class, oldValue, this.invoiceRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceRefnumQualXid() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.invoiceRefnumQualXid.convertFromDB(this.invoiceRefnumQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceRefnumQualXid(String invoiceRefnumQualXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceRefnumQualXid;
            InvoiceRefnumQualData data = this.getData();
            this.invoiceRefnumQualXid = InvoiceRefnumQualColumns.invoiceRefnumQualXid.convertToDB(invoiceRefnumQualXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceRefnumQualXid", String.class, oldValue, this.invoiceRefnumQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceRefnumDesc() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.invoiceRefnumDesc.convertFromDB(this.invoiceRefnumDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceRefnumDesc(String invoiceRefnumDesc) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceRefnumDesc;
            InvoiceRefnumQualData data = this.getData();
            this.invoiceRefnumDesc = InvoiceRefnumQualColumns.invoiceRefnumDesc.convertToDB(invoiceRefnumDesc);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceRefnumDesc", String.class, oldValue, this.invoiceRefnumDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDefaultRefnumBnTypeGid() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.defaultRefnumBnTypeGid.convertFromDB(this.defaultRefnumBnTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultRefnumBnTypeGid(String defaultRefnumBnTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultRefnumBnTypeGid;
            InvoiceRefnumQualData data = this.getData();
            this.defaultRefnumBnTypeGid = InvoiceRefnumQualColumns.defaultRefnumBnTypeGid.convertToDB(defaultRefnumBnTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultRefnumBnTypeGid", String.class, oldValue, this.defaultRefnumBnTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getUpdateFlag() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.updateFlag.convertFromDB(this.updateFlag);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUpdateFlag(String updateFlag) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.updateFlag;
            InvoiceRefnumQualData data = this.getData();
            this.updateFlag = InvoiceRefnumQualColumns.updateFlag.convertToDB(updateFlag);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "updateFlag", String.class, oldValue, this.updateFlag);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceRefnumQualColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceRefnumQualData data = this.getData();
            this.domainName = InvoiceRefnumQualColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
