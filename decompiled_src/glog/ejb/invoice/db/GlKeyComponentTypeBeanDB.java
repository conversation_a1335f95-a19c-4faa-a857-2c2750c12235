/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlKeyComponentTypeColumns;
import glog.ejb.invoice.db.GlKeyComponentTypeData;
import glog.ejb.invoice.db.GlKeyComponentTypePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlKeyComponentTypeBeanDB
extends BeanManagedEntityBean {
    public Object glKeyComponentTypeGid;
    public Object glKeyComponentTypeXid;
    public Object javaClass;
    public Object gidQueryClass;
    public Object glCodeAssignType;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlKeyComponentTypePK pk;
    protected transient GlKeyComponentTypeData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentTypeBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlKeyComponentTypePK.Callback theCall = new GlKeyComponentTypePK.Callback();

    public GlKeyComponentTypeBeanDB() {
        super(false);
    }

    public GlKeyComponentTypePK ejbCreate(GlKeyComponentTypeData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected GlKeyComponentTypePK newPK() throws GLException {
        return GlKeyComponentTypePK.newPK(this.getDomainName(), this.getGlKeyComponentTypeXid(), this.getConnection());
    }

    public void ejbPostCreate(GlKeyComponentTypeData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlKeyComponentTypePK ejbFindByPrimaryKey(GlKeyComponentTypePK pk) throws FinderException {
        return (GlKeyComponentTypePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlKeyComponentTypePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlKeyComponentTypePK> v = new Vector<GlKeyComponentTypePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlKeyComponentTypePK pk, GlKeyComponentTypeData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlKeyComponentTypePK pk, GlKeyComponentTypeData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlKeyComponentTypePK pk, GlKeyComponentTypeData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlKeyComponentTypeColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlKeyComponentTypeColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlKeyComponentTypeColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlKeyComponentTypeColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlKeyComponentTypePK pk = (GlKeyComponentTypePK)genericPk;
        this.glKeyComponentTypeGid = pk.glKeyComponentTypeGid;
        this.domainName = pk.domainName;
        this.glKeyComponentTypeXid = pk.glKeyComponentTypeXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlKeyComponentTypePK pk = new GlKeyComponentTypePK(0, this.glKeyComponentTypeGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlKeyComponentType";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlKeyComponentTypeData getData() throws GLException {
        try {
            GlKeyComponentTypeData retval = new GlKeyComponentTypeData();
            retval.getFromBean(this, GlKeyComponentTypeColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlKeyComponentTypeData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlKeyComponentTypeData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlKeyComponentTypeData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlKeyComponentTypeColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlKeyComponentTypeData.class;
    }

    @Override
    public Class getPkClass() {
        return GlKeyComponentTypePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlKeyComponentTypeGid() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentTypeGid(String glKeyComponentTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentTypeGid;
            GlKeyComponentTypeData data = this.getData();
            this.glKeyComponentTypeGid = GlKeyComponentTypeColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentTypeGid", String.class, oldValue, this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlKeyComponentTypeXid() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.glKeyComponentTypeXid.convertFromDB(this.glKeyComponentTypeXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentTypeXid(String glKeyComponentTypeXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentTypeXid;
            GlKeyComponentTypeData data = this.getData();
            this.glKeyComponentTypeXid = GlKeyComponentTypeColumns.glKeyComponentTypeXid.convertToDB(glKeyComponentTypeXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentTypeXid", String.class, oldValue, this.glKeyComponentTypeXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getJavaClass() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.javaClass.convertFromDB(this.javaClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setJavaClass(String javaClass) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.javaClass;
            GlKeyComponentTypeData data = this.getData();
            this.javaClass = GlKeyComponentTypeColumns.javaClass.convertToDB(javaClass);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "javaClass", String.class, oldValue, this.javaClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGidQueryClass() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.gidQueryClass.convertFromDB(this.gidQueryClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGidQueryClass(String gidQueryClass) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.gidQueryClass;
            GlKeyComponentTypeData data = this.getData();
            this.gidQueryClass = GlKeyComponentTypeColumns.gidQueryClass.convertToDB(gidQueryClass);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "gidQueryClass", String.class, oldValue, this.gidQueryClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlCodeAssignType() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.glCodeAssignType.convertFromDB(this.glCodeAssignType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlCodeAssignType(String glCodeAssignType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glCodeAssignType;
            GlKeyComponentTypeData data = this.getData();
            this.glCodeAssignType = GlKeyComponentTypeColumns.glCodeAssignType.convertToDB(glCodeAssignType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glCodeAssignType", String.class, oldValue, this.glCodeAssignType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlKeyComponentTypeColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlKeyComponentTypeData data = this.getData();
            this.domainName = GlKeyComponentTypeColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
