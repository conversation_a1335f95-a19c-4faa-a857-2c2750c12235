/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlKeyComponentTypeData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface GlKeyComponentTypeRemoteDB
extends EJBObject {
    public GlKeyComponentTypeData getData() throws RemoteException, GLException;

    public void setData(GlKeyComponentTypeData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getGlKeyComponentTypeGid() throws <PERSON>moteException, GLException;

    public void setGlKeyComponentTypeGid(String var1) throws RemoteException, GLException;

    public String getGlKeyComponentTypeXid() throws RemoteException, GLException;

    public void setGlKeyComponentTypeXid(String var1) throws RemoteException, GLException;

    public String getJavaClass() throws RemoteException, GLException;

    public void setJavaClass(String var1) throws RemoteException, GLException;

    public String getGidQueryClass() throws RemoteException, GLException;

    public void setGidQueryClass(String var1) throws RemoteException, GLException;

    public String getGlCodeAssignType() throws RemoteException, GLException;

    public void setGlCodeAssignType(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
