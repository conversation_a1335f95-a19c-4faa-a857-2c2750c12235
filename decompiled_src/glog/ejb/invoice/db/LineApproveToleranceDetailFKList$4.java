/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineApproveToleranceDetailFKList.4
implements Fk {
    LineApproveToleranceDetailFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineApproveToleranceDetailData";
    }

    @Override
    public String getPkField() {
        return "costType";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "CostTypeData";
    }

    @Override
    public String getFkDataField() {
        return "costTypeGid";
    }
}
