/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class InvoiceLineitemPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return InvoiceLineitemPK.class;
    }

    @Override
    public final String getEntity() {
        return "InvoiceLineitem";
    }

    @Override
    public final String getTableName() {
        return "invoice_lineitem";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"invoice_gid", "lineitem_seq_no"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        InvoiceLineitemPK result = new InvoiceLineitemPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
        return result;
    }
}
