/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRefnumData;
import glog.util.LocalDate;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceRefnumRemoteDB
extends EJBObject {
    public InvoiceRefnumData getData() throws RemoteException, GLException;

    public void setData(InvoiceRefnumData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteEx<PERSON>, <PERSON>LException;

    public String getInvoiceGid() throws RemoteException, <PERSON>LException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public String getInvoiceRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceRefnumValue() throws RemoteException, GLException;

    public void setInvoiceRefnumValue(String var1) throws RemoteException, GLException;

    public LocalDate getIssueDate() throws RemoteException, GLException;

    public void setIssueDate(LocalDate var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
