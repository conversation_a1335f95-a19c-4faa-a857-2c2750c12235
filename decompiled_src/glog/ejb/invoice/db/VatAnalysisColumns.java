/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class VatAnalysisColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn vatCodeGid = new SqlColumn(String.class, "vat_code_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn taxAmount = new SqlColumn(Currency.class, "tax_amount", 22, 2, 1, null, 2, "tax_amount_base");
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 3, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn vatBasisAmount = new SqlColumn(Currency.class, "vat_basis_amount", 22, 2, 0, null, 5, "vat_basis_amount_base");
    public static SqlColumn vatRate = new SqlColumn(Double.class, "vat_rate", 22, 3, 0, null, 6, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 7, null);
}
