/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRefnumColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceRefnumPK
extends Pk {
    public Object invoiceGid;
    public Object invoiceRefnumQualGid;
    public Object invoiceRefnumValue;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceRefnumPK() {
    }

    public InvoiceRefnumPK(String invoiceGid, String invoiceRefnumQualGid, String invoiceRefnumValue) {
        this.invoiceGid = this.notNull(InvoiceRefnumColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.invoiceRefnumQualGid = this.notNull(InvoiceRefnumColumns.invoiceRefnumQualGid.convertToDB(invoiceRefnumQualGid), "invoiceRefnumQualGid");
        this.invoiceRefnumValue = this.notNull(InvoiceRefnumColumns.invoiceRefnumValue.convertToDB(invoiceRefnumValue), "invoiceRefnumValue");
    }

    public InvoiceRefnumPK(int dummy, Object invoiceGid, Object invoiceRefnumQualGid, Object invoiceRefnumValue) {
        this(dummy, invoiceGid, invoiceRefnumQualGid, invoiceRefnumValue, null);
    }

    public InvoiceRefnumPK(int dummy, Object invoiceGid, Object invoiceRefnumQualGid, Object invoiceRefnumValue, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.invoiceRefnumQualGid = invoiceRefnumQualGid;
        this.invoiceRefnumValue = invoiceRefnumValue;
        this.transaction = transaction;
    }

    public InvoiceRefnumPK(InvoiceRefnumPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.invoiceRefnumQualGid = otherPk.invoiceRefnumQualGid;
        this.invoiceRefnumValue = otherPk.invoiceRefnumValue;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.invoiceRefnumQualGid != null ? String.valueOf(this.invoiceRefnumQualGid) : "") + " " + (this.invoiceRefnumValue != null ? String.valueOf(this.invoiceRefnumValue) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.invoiceRefnumQualGid != null ? String.valueOf(this.invoiceRefnumQualGid) : "") + "|" + (this.invoiceRefnumValue != null ? String.valueOf(this.invoiceRefnumValue) : "");
    }

    public static InvoiceRefnumPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceRefnumPK(gids[0], gids[1], gids[2]) : null;
    }

    public static InvoiceRefnumPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceRefnumPK(gids[0], gids[1], gids[2]) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.invoiceRefnumQualGid.hashCode() + this.invoiceRefnumValue.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceRefnumPK)) {
            return false;
        }
        InvoiceRefnumPK otherPk = (InvoiceRefnumPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.invoiceRefnumQualGid, this.invoiceRefnumQualGid) && Functions.equals(otherPk.invoiceRefnumValue, this.invoiceRefnumValue) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceRefnumHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.invoiceRefnumQualGid;
            }
            case 2: {
                return this.invoiceRefnumValue;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceRefnumColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceRefnumColumns.invoiceRefnumQualGid.convertFromDB(this.invoiceRefnumQualGid);
            }
            case 2: {
                return InvoiceRefnumColumns.invoiceRefnumValue.convertFromDB(this.invoiceRefnumValue);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceRefnumPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceRefnum";
        }

        @Override
        public final String getTableName() {
            return "invoice_refnum";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "invoice_refnum_qual_gid", "invoice_refnum_value"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceRefnumPK result = new InvoiceRefnumPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
