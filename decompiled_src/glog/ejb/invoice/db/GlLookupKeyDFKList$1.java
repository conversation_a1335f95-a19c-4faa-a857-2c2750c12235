/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class GlLookupKeyDFKList.1
implements Fk {
    GlLookupKeyDFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "GlLookupKeyDData";
    }

    @Override
    public String getPkField() {
        return "glKeyComponentGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "GlKeyComponentData";
    }

    @Override
    public String getFkDataField() {
        return "glKeyComponentGid";
    }
}
