/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CommercialInvoiceColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class CommercialInvoicePK
extends Pk {
    public Object commercialInvoiceGid;
    public transient Object commercialInvoiceXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public CommercialInvoicePK() {
    }

    public CommercialInvoicePK(String commercialInvoiceGid) {
        this.commercialInvoiceGid = this.notNull(CommercialInvoiceColumns.commercialInvoiceGid.convertToDB(commercialInvoiceGid), "commercialInvoiceGid");
    }

    public CommercialInvoicePK(String domainName, String commercialInvoiceXid) {
        this.domainName = domainName;
        this.commercialInvoiceXid = commercialInvoiceXid;
        this.commercialInvoiceGid = CommercialInvoicePK.concatForGid(domainName, commercialInvoiceXid);
    }

    public CommercialInvoicePK(int dummy, Object commercialInvoiceGid) {
        this(dummy, commercialInvoiceGid, null);
    }

    public CommercialInvoicePK(int dummy, Object commercialInvoiceGid, Object transaction) {
        this.commercialInvoiceGid = commercialInvoiceGid;
        this.transaction = transaction;
    }

    public CommercialInvoicePK(CommercialInvoicePK otherPk, Object transaction) {
        this.commercialInvoiceGid = otherPk.commercialInvoiceGid;
        this.commercialInvoiceXid = otherPk.commercialInvoiceXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.commercialInvoiceGid != null ? String.valueOf(this.commercialInvoiceGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.commercialInvoiceGid != null ? String.valueOf(this.commercialInvoiceGid) : "";
    }

    public static CommercialInvoicePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new CommercialInvoicePK(gids[0]) : null;
    }

    public static CommercialInvoicePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new CommercialInvoicePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.commercialInvoiceGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof CommercialInvoicePK)) {
            return false;
        }
        CommercialInvoicePK otherPk = (CommercialInvoicePK)other;
        return Functions.equals(otherPk.commercialInvoiceGid, this.commercialInvoiceGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.CommercialInvoiceHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.commercialInvoiceGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return CommercialInvoiceColumns.commercialInvoiceGid.convertFromDB(this.commercialInvoiceGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static CommercialInvoicePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("CommercialInvoicePK requires a non-null XID to generate a GID");
            }
            CommercialInvoicePK commercialInvoicePK = new CommercialInvoicePK(domain, xid);
            return commercialInvoicePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static CommercialInvoicePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            CommercialInvoicePK commercialInvoicePK = CommercialInvoicePK.newPK(domainName, xid, connection);
            return commercialInvoicePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return CommercialInvoicePK.class;
        }

        @Override
        public final String getEntity() {
            return "CommercialInvoice";
        }

        @Override
        public final String getTableName() {
            return "commercial_invoice";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"commercial_invoice_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            CommercialInvoicePK result = new CommercialInvoicePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
