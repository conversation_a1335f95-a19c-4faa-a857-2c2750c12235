/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceSummaryRemarkColumns;
import glog.ejb.invoice.db.InvoiceSummaryRemarkData;
import glog.ejb.invoice.db.InvoiceSummaryRemarkPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceSummaryRemarkBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object remarkSeqNo;
    public Object invoiceSummarySeqNo;
    public Object remarkQualIdentifier;
    public Object remarkText;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceSummaryRemarkPK pk;
    protected transient InvoiceSummaryRemarkData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceSummaryRemarkBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceSummaryRemarkPK.Callback theCall = new InvoiceSummaryRemarkPK.Callback();

    public InvoiceSummaryRemarkBeanDB() {
        super(false);
    }

    public InvoiceSummaryRemarkPK ejbCreate(InvoiceSummaryRemarkData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceSummaryRemarkData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceSummaryRemarkPK ejbFindByPrimaryKey(InvoiceSummaryRemarkPK pk) throws FinderException {
        return (InvoiceSummaryRemarkPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceSummaryRemarkPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceSummaryRemarkPK> v = new Vector<InvoiceSummaryRemarkPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceSummaryRemarkPK pk, InvoiceSummaryRemarkData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceSummaryRemarkPK pk, InvoiceSummaryRemarkData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceSummaryRemarkPK pk, InvoiceSummaryRemarkData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceSummaryRemarkPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceSummaryRemarkColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceSummaryRemarkColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceSummaryRemarkColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceSummaryRemarkColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceSummaryRemarkPK pk = (InvoiceSummaryRemarkPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.invoiceSummarySeqNo = pk.invoiceSummarySeqNo;
        this.remarkSeqNo = pk.remarkSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceSummaryRemarkPK pk = new InvoiceSummaryRemarkPK(0, this.invoiceGid, this.invoiceSummarySeqNo, this.remarkSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceSummaryRemark";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceSummaryRemarkData getData() throws GLException {
        try {
            InvoiceSummaryRemarkData retval = new InvoiceSummaryRemarkData();
            retval.getFromBean(this, InvoiceSummaryRemarkColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceSummaryRemarkData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceSummaryRemarkData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceSummaryRemarkData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceSummaryRemarkColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceSummaryRemarkData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceSummaryRemarkPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceSummaryRemarkColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceSummaryRemarkData data = this.getData();
            this.invoiceGid = InvoiceSummaryRemarkColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getRemarkSeqNo() throws GLException {
        try {
            return (Integer)InvoiceSummaryRemarkColumns.remarkSeqNo.convertFromDB(this.remarkSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkSeqNo(Integer remarkSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkSeqNo;
            InvoiceSummaryRemarkData data = this.getData();
            this.remarkSeqNo = InvoiceSummaryRemarkColumns.remarkSeqNo.convertToDB(remarkSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkSeqNo", Integer.class, oldValue, this.remarkSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getInvoiceSummarySeqNo() throws GLException {
        try {
            return (Integer)InvoiceSummaryRemarkColumns.invoiceSummarySeqNo.convertFromDB(this.invoiceSummarySeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceSummarySeqNo(Integer invoiceSummarySeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceSummarySeqNo;
            InvoiceSummaryRemarkData data = this.getData();
            this.invoiceSummarySeqNo = InvoiceSummaryRemarkColumns.invoiceSummarySeqNo.convertToDB(invoiceSummarySeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceSummarySeqNo", Integer.class, oldValue, this.invoiceSummarySeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkQualIdentifier() throws GLException {
        try {
            return (String)InvoiceSummaryRemarkColumns.remarkQualIdentifier.convertFromDB(this.remarkQualIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkQualIdentifier(String remarkQualIdentifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkQualIdentifier;
            InvoiceSummaryRemarkData data = this.getData();
            this.remarkQualIdentifier = InvoiceSummaryRemarkColumns.remarkQualIdentifier.convertToDB(remarkQualIdentifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkQualIdentifier", String.class, oldValue, this.remarkQualIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkText() throws GLException {
        try {
            return (String)InvoiceSummaryRemarkColumns.remarkText.convertFromDB(this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkText(String remarkText) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkText;
            InvoiceSummaryRemarkData data = this.getData();
            this.remarkText = InvoiceSummaryRemarkColumns.remarkText.convertToDB(remarkText);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkText", String.class, oldValue, this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceSummaryRemarkColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceSummaryRemarkData data = this.getData();
            this.domainName = InvoiceSummaryRemarkColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
