/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryRemarkPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceSummaryRemarkData
extends BeanData {
    public String invoiceGid;
    public Integer remarkSeqNo;
    public Integer invoiceSummarySeqNo;
    public String remarkQualIdentifier;
    public String remarkText;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceSummaryRemarkData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field remarkSeqNoField = beanDataFields[1];
    public static Field invoiceSummarySeqNoField = beanDataFields[2];
    public static Field remarkQualIdentifierField = beanDataFields[3];
    public static Field remarkTextField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceSummaryRemarkData() {
    }

    public InvoiceSummaryRemarkData(InvoiceSummaryRemarkData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceSummaryRemarkPK();
    }

    @Legacy
    public InvoiceSummaryRemarkPK getInvoiceSummaryRemarkPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.invoiceSummarySeqNo == null) {
            return null;
        }
        if (this.remarkSeqNo == null) {
            return null;
        }
        return new InvoiceSummaryRemarkPK(this.invoiceGid, this.invoiceSummarySeqNo, this.remarkSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceSummaryRemarkPK((InvoiceSummaryRemarkPK)pk);
    }

    @Legacy
    public void setInvoiceSummaryRemarkPK(InvoiceSummaryRemarkPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.invoiceSummarySeqNo = (Integer)pk.getAppValue(1);
        this.remarkSeqNo = (Integer)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceSummaryRemarkQueryGen";
    }

    public static InvoiceSummaryRemarkData load(Connection conn, InvoiceSummaryRemarkPK pk) throws GLException {
        return (InvoiceSummaryRemarkData)InvoiceSummaryRemarkData.load(conn, pk, InvoiceSummaryRemarkData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceSummaryRemarkData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceSummaryRemarkData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceSummaryRemarkData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceSummaryRemarkData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceSummaryRemarkData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceSummaryRemarkData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceSummaryRemarkData.class);
    }
}
