/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineAppTolInvoiceRefnumPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class LineAppTolInvoiceRefnumData
extends BeanData {
    public String lineApproveToleranceGid;
    public Integer refnumSequence;
    public String invoiceRefnumQualGid;
    public String invoiceRefnumValue;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineAppTolInvoiceRefnumData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field lineApproveToleranceGidField = beanDataFields[0];
    public static Field refnumSequenceField = beanDataFields[1];
    public static Field invoiceRefnumQualGidField = beanDataFields[2];
    public static Field invoiceRefnumValueField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public LineAppTolInvoiceRefnumData() {
    }

    public LineAppTolInvoiceRefnumData(LineAppTolInvoiceRefnumData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getLineAppTolInvoiceRefnumPK();
    }

    @Legacy
    public LineAppTolInvoiceRefnumPK getLineAppTolInvoiceRefnumPK() {
        if (this.invoiceRefnumQualGid == null) {
            return null;
        }
        if (this.lineApproveToleranceGid == null) {
            return null;
        }
        if (this.refnumSequence == null) {
            return null;
        }
        return new LineAppTolInvoiceRefnumPK(this.invoiceRefnumQualGid, this.lineApproveToleranceGid, this.refnumSequence);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setLineAppTolInvoiceRefnumPK((LineAppTolInvoiceRefnumPK)pk);
    }

    @Legacy
    public void setLineAppTolInvoiceRefnumPK(LineAppTolInvoiceRefnumPK pk) {
        this.invoiceRefnumQualGid = (String)pk.getAppValue(0);
        this.lineApproveToleranceGid = (String)pk.getAppValue(1);
        this.refnumSequence = (Integer)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.LineAppTolInvoiceRefnumQueryGen";
    }

    public static LineAppTolInvoiceRefnumData load(Connection conn, LineAppTolInvoiceRefnumPK pk) throws GLException {
        return (LineAppTolInvoiceRefnumData)LineAppTolInvoiceRefnumData.load(conn, pk, LineAppTolInvoiceRefnumData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return LineAppTolInvoiceRefnumData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return LineAppTolInvoiceRefnumData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineAppTolInvoiceRefnumData.load(conn, whereClause, prepareArguments, fetchSize, LineAppTolInvoiceRefnumData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return LineAppTolInvoiceRefnumData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineAppTolInvoiceRefnumData.load(conn, fromWhere, alias, prepareArguments, fetchSize, LineAppTolInvoiceRefnumData.class);
    }
}
