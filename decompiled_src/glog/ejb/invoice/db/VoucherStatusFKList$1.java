/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherStatusFKList.1
implements Fk {
    VoucherStatusFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherStatusData";
    }

    @Override
    public String getPkField() {
        return "statusTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "StatusValueData";
    }

    @Override
    public String getFkDataField() {
        return "statusTypeGid";
    }
}
