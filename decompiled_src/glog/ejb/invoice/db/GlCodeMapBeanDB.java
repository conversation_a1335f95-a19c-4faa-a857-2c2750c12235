/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlCodeMapColumns;
import glog.ejb.invoice.db.GlCodeMapData;
import glog.ejb.invoice.db.GlCodeMapPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlCodeMapBeanDB
extends BeanManagedEntityBean {
    public Object keyValue;
    public Object domainName;
    public Object glLookupKeyGid;
    public Object generalLedgerGid;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlCodeMapPK pk;
    protected transient GlCodeMapData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlCodeMapBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlCodeMapPK.Callback theCall = new GlCodeMapPK.Callback();

    public GlCodeMapBeanDB() {
        super(false);
    }

    public GlCodeMapPK ejbCreate(GlCodeMapData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(GlCodeMapData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlCodeMapPK ejbFindByPrimaryKey(GlCodeMapPK pk) throws FinderException {
        return (GlCodeMapPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlCodeMapPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlCodeMapPK> v = new Vector<GlCodeMapPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlCodeMapPK pk, GlCodeMapData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlCodeMapPK pk, GlCodeMapData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlCodeMapPK pk, GlCodeMapData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (GlCodeMapPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlCodeMapColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlCodeMapColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlCodeMapColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlCodeMapColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlCodeMapPK pk = (GlCodeMapPK)genericPk;
        this.domainName = pk.domainName;
        this.glLookupKeyGid = pk.glLookupKeyGid;
        this.keyValue = pk.keyValue;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlCodeMapPK pk = new GlCodeMapPK(0, this.domainName, this.glLookupKeyGid, this.keyValue, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlCodeMap";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlCodeMapData getData() throws GLException {
        try {
            GlCodeMapData retval = new GlCodeMapData();
            retval.getFromBean(this, GlCodeMapColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlCodeMapData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlCodeMapData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlCodeMapData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlCodeMapColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlCodeMapData.class;
    }

    @Override
    public Class getPkClass() {
        return GlCodeMapPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getKeyValue() throws GLException {
        try {
            return (String)GlCodeMapColumns.keyValue.convertFromDB(this.keyValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setKeyValue(String keyValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.keyValue;
            GlCodeMapData data = this.getData();
            this.keyValue = GlCodeMapColumns.keyValue.convertToDB(keyValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "keyValue", String.class, oldValue, this.keyValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlCodeMapColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlCodeMapData data = this.getData();
            this.domainName = GlCodeMapColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlLookupKeyGid() throws GLException {
        try {
            return (String)GlCodeMapColumns.glLookupKeyGid.convertFromDB(this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlLookupKeyGid(String glLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glLookupKeyGid;
            GlCodeMapData data = this.getData();
            this.glLookupKeyGid = GlCodeMapColumns.glLookupKeyGid.convertToDB(glLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glLookupKeyGid", String.class, oldValue, this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGeneralLedgerGid() throws GLException {
        try {
            return (String)GlCodeMapColumns.generalLedgerGid.convertFromDB(this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGeneralLedgerGid(String generalLedgerGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.generalLedgerGid;
            GlCodeMapData data = this.getData();
            this.generalLedgerGid = GlCodeMapColumns.generalLedgerGid.convertToDB(generalLedgerGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "generalLedgerGid", String.class, oldValue, this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
