/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceSummaryRemoteDB
extends EJBObject {
    public InvoiceSummaryData getData() throws RemoteException, GLException;

    public void setData(InvoiceSummaryData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws <PERSON>moteException, GLException;

    public Pk getPK() throws <PERSON>moteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getInvoiceSummarySeqNo() throws RemoteException, GLException;

    public void setInvoiceSummarySeqNo(Integer var1) throws RemoteException, GLException;

    public Currency getFreightCharge() throws RemoteException, GLException;

    public void setFreightCharge(Currency var1) throws RemoteException, GLException;

    public Currency getPrepaidAmount() throws RemoteException, GLException;

    public void setPrepaidAmount(Currency var1) throws RemoteException, GLException;

    public Currency getCommercialUnitPrice() throws RemoteException, GLException;

    public void setCommercialUnitPrice(Currency var1) throws RemoteException, GLException;

    public Long getUnitCount() throws RemoteException, GLException;

    public void setUnitCount(Long var1) throws RemoteException, GLException;

    public String getTransportHandlingUnitGid() throws RemoteException, GLException;

    public void setTransportHandlingUnitGid(String var1) throws RemoteException, GLException;

    public Weight getWeight() throws RemoteException, GLException;

    public void setWeight(Weight var1) throws RemoteException, GLException;

    public Volume getVolume() throws RemoteException, GLException;

    public void setVolume(Volume var1) throws RemoteException, GLException;

    public String getWeightQualifier() throws RemoteException, GLException;

    public void setWeightQualifier(String var1) throws RemoteException, GLException;

    public Integer getInvoiceTotal() throws RemoteException, GLException;

    public void setInvoiceTotal(Integer var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getInvoiceServiceCodeGid() throws RemoteException, GLException;

    public void setInvoiceServiceCodeGid(String var1) throws RemoteException, GLException;
}
