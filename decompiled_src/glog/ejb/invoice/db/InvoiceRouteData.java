/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRoutePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceRouteData
extends BeanData {
    public String invoiceGid;
    public Integer routeSeqNo;
    public String transportModeIdentifier;
    public String servprovAliasQualGid;
    public String servprovAliasValue;
    public String intermodalServiceCode;
    public String jctCityCode;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRouteData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field routeSeqNoField = beanDataFields[1];
    public static Field transportModeIdentifierField = beanDataFields[2];
    public static Field servprovAliasQualGidField = beanDataFields[3];
    public static Field servprovAliasValueField = beanDataFields[4];
    public static Field intermodalServiceCodeField = beanDataFields[5];
    public static Field jctCityCodeField = beanDataFields[6];
    public static Field domainNameField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceRouteData() {
    }

    public InvoiceRouteData(InvoiceRouteData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceRoutePK();
    }

    @Legacy
    public InvoiceRoutePK getInvoiceRoutePK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.routeSeqNo == null) {
            return null;
        }
        return new InvoiceRoutePK(this.invoiceGid, this.routeSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceRoutePK((InvoiceRoutePK)pk);
    }

    @Legacy
    public void setInvoiceRoutePK(InvoiceRoutePK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.routeSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceRouteQueryGen";
    }

    public static InvoiceRouteData load(Connection conn, InvoiceRoutePK pk) throws GLException {
        return (InvoiceRouteData)InvoiceRouteData.load(conn, pk, InvoiceRouteData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceRouteData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceRouteData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRouteData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceRouteData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceRouteData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRouteData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceRouteData.class);
    }
}
