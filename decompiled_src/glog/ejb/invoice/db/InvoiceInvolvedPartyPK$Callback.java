/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceInvolvedPartyPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class InvoiceInvolvedPartyPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return InvoiceInvolvedPartyPK.class;
    }

    @Override
    public final String getEntity() {
        return "InvoiceInvolvedParty";
    }

    @Override
    public final String getTableName() {
        return "invoice_involved_party";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"com_method_gid", "invoice_gid", "involved_party_contact_gid", "involved_party_qual_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        InvoiceInvolvedPartyPK result = new InvoiceInvolvedPartyPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), q.getObject(pkOffset + 3 + 1), transaction);
        return result;
    }
}
