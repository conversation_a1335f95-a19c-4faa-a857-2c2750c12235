/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class AutoApproveRuleProfileDFKList.2
implements Fk {
    AutoApproveRuleProfileDFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AutoApproveRuleProfileDData";
    }

    @Override
    public String getPkField() {
        return "autoApproveRuleProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "AutoApproveRuleProfileData";
    }

    @Override
    public String getFkDataField() {
        return "autoApproveRuleProfileGid";
    }
}
