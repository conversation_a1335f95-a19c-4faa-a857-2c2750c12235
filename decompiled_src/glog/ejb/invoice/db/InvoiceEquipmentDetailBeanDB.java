/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceEquipmentDetailColumns;
import glog.ejb.invoice.db.InvoiceEquipmentDetailData;
import glog.ejb.invoice.db.InvoiceEquipmentDetailPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import glog.util.uom.data.Length;
import glog.util.uom.data.Temperature;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceEquipmentDetailBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object seqNumber;
    public Object equipmentInitialNumber;
    public Object sEquipmentGid;
    public Object equipmentPrefix;
    public Object equipmentNumber;
    public Object isoEquipmentTypeIdentifier;
    public Object descriptionCode;
    public Object equipmentOwnerIdentifier;
    public Object ownershipCode;
    public Object length;
    public Object width;
    public Object height;
    public Object weight;
    public Object volume;
    public Object tareWeight;
    public Object dunnage;
    public Object unitCount;
    public Object weightQualifier;
    public Object minimumTemperature;
    public Object maximumTemperature;
    public Object percentHumidityAllowed;
    public Object ventSettingCode;
    public Object weightAllowance;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceEquipmentDetailPK pk;
    protected transient InvoiceEquipmentDetailData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceEquipmentDetailBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceEquipmentDetailPK.Callback theCall = new InvoiceEquipmentDetailPK.Callback();

    public InvoiceEquipmentDetailBeanDB() {
        super(false);
    }

    public InvoiceEquipmentDetailPK ejbCreate(InvoiceEquipmentDetailData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceEquipmentDetailData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceEquipmentDetailPK ejbFindByPrimaryKey(InvoiceEquipmentDetailPK pk) throws FinderException {
        return (InvoiceEquipmentDetailPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceEquipmentDetailPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceEquipmentDetailPK> v = new Vector<InvoiceEquipmentDetailPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceEquipmentDetailPK pk, InvoiceEquipmentDetailData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceEquipmentDetailPK pk, InvoiceEquipmentDetailData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceEquipmentDetailPK pk, InvoiceEquipmentDetailData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceEquipmentDetailPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceEquipmentDetailColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceEquipmentDetailColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceEquipmentDetailColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceEquipmentDetailColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceEquipmentDetailPK pk = (InvoiceEquipmentDetailPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.seqNumber = pk.seqNumber;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceEquipmentDetailPK pk = new InvoiceEquipmentDetailPK(0, this.invoiceGid, this.seqNumber, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceEquipmentDetail";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceEquipmentDetailData getData() throws GLException {
        try {
            InvoiceEquipmentDetailData retval = new InvoiceEquipmentDetailData();
            retval.getFromBean(this, InvoiceEquipmentDetailColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceEquipmentDetailData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceEquipmentDetailData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceEquipmentDetailData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceEquipmentDetailColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceEquipmentDetailData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceEquipmentDetailPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceEquipmentDetailData data = this.getData();
            this.invoiceGid = InvoiceEquipmentDetailColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getSeqNumber() throws GLException {
        try {
            return (Long)InvoiceEquipmentDetailColumns.seqNumber.convertFromDB(this.seqNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSeqNumber(Long seqNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.seqNumber;
            InvoiceEquipmentDetailData data = this.getData();
            this.seqNumber = InvoiceEquipmentDetailColumns.seqNumber.convertToDB(seqNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "seqNumber", Long.class, oldValue, this.seqNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getEquipmentInitialNumber() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.equipmentInitialNumber.convertFromDB(this.equipmentInitialNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEquipmentInitialNumber(String equipmentInitialNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.equipmentInitialNumber;
            InvoiceEquipmentDetailData data = this.getData();
            this.equipmentInitialNumber = InvoiceEquipmentDetailColumns.equipmentInitialNumber.convertToDB(equipmentInitialNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "equipmentInitialNumber", String.class, oldValue, this.equipmentInitialNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSEquipmentGid() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.sEquipmentGid.convertFromDB(this.sEquipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSEquipmentGid(String sEquipmentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sEquipmentGid;
            InvoiceEquipmentDetailData data = this.getData();
            this.sEquipmentGid = InvoiceEquipmentDetailColumns.sEquipmentGid.convertToDB(sEquipmentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sEquipmentGid", String.class, oldValue, this.sEquipmentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getEquipmentPrefix() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.equipmentPrefix.convertFromDB(this.equipmentPrefix);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEquipmentPrefix(String equipmentPrefix) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.equipmentPrefix;
            InvoiceEquipmentDetailData data = this.getData();
            this.equipmentPrefix = InvoiceEquipmentDetailColumns.equipmentPrefix.convertToDB(equipmentPrefix);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "equipmentPrefix", String.class, oldValue, this.equipmentPrefix);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getEquipmentNumber() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.equipmentNumber.convertFromDB(this.equipmentNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEquipmentNumber(String equipmentNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.equipmentNumber;
            InvoiceEquipmentDetailData data = this.getData();
            this.equipmentNumber = InvoiceEquipmentDetailColumns.equipmentNumber.convertToDB(equipmentNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "equipmentNumber", String.class, oldValue, this.equipmentNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getIsoEquipmentTypeIdentifier() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.isoEquipmentTypeIdentifier.convertFromDB(this.isoEquipmentTypeIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsoEquipmentTypeIdentifier(String isoEquipmentTypeIdentifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isoEquipmentTypeIdentifier;
            InvoiceEquipmentDetailData data = this.getData();
            this.isoEquipmentTypeIdentifier = InvoiceEquipmentDetailColumns.isoEquipmentTypeIdentifier.convertToDB(isoEquipmentTypeIdentifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isoEquipmentTypeIdentifier", String.class, oldValue, this.isoEquipmentTypeIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescriptionCode() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.descriptionCode.convertFromDB(this.descriptionCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescriptionCode(String descriptionCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.descriptionCode;
            InvoiceEquipmentDetailData data = this.getData();
            this.descriptionCode = InvoiceEquipmentDetailColumns.descriptionCode.convertToDB(descriptionCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "descriptionCode", String.class, oldValue, this.descriptionCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getEquipmentOwnerIdentifier() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.equipmentOwnerIdentifier.convertFromDB(this.equipmentOwnerIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEquipmentOwnerIdentifier(String equipmentOwnerIdentifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.equipmentOwnerIdentifier;
            InvoiceEquipmentDetailData data = this.getData();
            this.equipmentOwnerIdentifier = InvoiceEquipmentDetailColumns.equipmentOwnerIdentifier.convertToDB(equipmentOwnerIdentifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "equipmentOwnerIdentifier", String.class, oldValue, this.equipmentOwnerIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getOwnershipCode() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.ownershipCode.convertFromDB(this.ownershipCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOwnershipCode(String ownershipCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.ownershipCode;
            InvoiceEquipmentDetailData data = this.getData();
            this.ownershipCode = InvoiceEquipmentDetailColumns.ownershipCode.convertToDB(ownershipCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "ownershipCode", String.class, oldValue, this.ownershipCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Length getLength() throws GLException {
        try {
            return (Length)InvoiceEquipmentDetailColumns.length.convertFromDB(this.length);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLength(Length length) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.length;
            InvoiceEquipmentDetailData data = this.getData();
            this.length = InvoiceEquipmentDetailColumns.length.convertToDB(length);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "length", Length.class, oldValue, this.length);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Length getWidth() throws GLException {
        try {
            return (Length)InvoiceEquipmentDetailColumns.width.convertFromDB(this.width);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setWidth(Length width) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.width;
            InvoiceEquipmentDetailData data = this.getData();
            this.width = InvoiceEquipmentDetailColumns.width.convertToDB(width);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "width", Length.class, oldValue, this.width);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Length getHeight() throws GLException {
        try {
            return (Length)InvoiceEquipmentDetailColumns.height.convertFromDB(this.height);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setHeight(Length height) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.height;
            InvoiceEquipmentDetailData data = this.getData();
            this.height = InvoiceEquipmentDetailColumns.height.convertToDB(height);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "height", Length.class, oldValue, this.height);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getWeight() throws GLException {
        try {
            return (Weight)InvoiceEquipmentDetailColumns.weight.convertFromDB(this.weight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setWeight(Weight weight) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.weight;
            InvoiceEquipmentDetailData data = this.getData();
            this.weight = InvoiceEquipmentDetailColumns.weight.convertToDB(weight);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "weight", Weight.class, oldValue, this.weight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Volume getVolume() throws GLException {
        try {
            return (Volume)InvoiceEquipmentDetailColumns.volume.convertFromDB(this.volume);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVolume(Volume volume) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.volume;
            InvoiceEquipmentDetailData data = this.getData();
            this.volume = InvoiceEquipmentDetailColumns.volume.convertToDB(volume);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "volume", Volume.class, oldValue, this.volume);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getTareWeight() throws GLException {
        try {
            return (Weight)InvoiceEquipmentDetailColumns.tareWeight.convertFromDB(this.tareWeight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTareWeight(Weight tareWeight) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.tareWeight;
            InvoiceEquipmentDetailData data = this.getData();
            this.tareWeight = InvoiceEquipmentDetailColumns.tareWeight.convertToDB(tareWeight);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "tareWeight", Weight.class, oldValue, this.tareWeight);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getDunnage() throws GLException {
        try {
            return (Weight)InvoiceEquipmentDetailColumns.dunnage.convertFromDB(this.dunnage);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDunnage(Weight dunnage) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.dunnage;
            InvoiceEquipmentDetailData data = this.getData();
            this.dunnage = InvoiceEquipmentDetailColumns.dunnage.convertToDB(dunnage);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "dunnage", Weight.class, oldValue, this.dunnage);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getUnitCount() throws GLException {
        try {
            return (Long)InvoiceEquipmentDetailColumns.unitCount.convertFromDB(this.unitCount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUnitCount(Long unitCount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.unitCount;
            InvoiceEquipmentDetailData data = this.getData();
            this.unitCount = InvoiceEquipmentDetailColumns.unitCount.convertToDB(unitCount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "unitCount", Long.class, oldValue, this.unitCount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getWeightQualifier() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.weightQualifier.convertFromDB(this.weightQualifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setWeightQualifier(String weightQualifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.weightQualifier;
            InvoiceEquipmentDetailData data = this.getData();
            this.weightQualifier = InvoiceEquipmentDetailColumns.weightQualifier.convertToDB(weightQualifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "weightQualifier", String.class, oldValue, this.weightQualifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Temperature getMinimumTemperature() throws GLException {
        try {
            return (Temperature)InvoiceEquipmentDetailColumns.minimumTemperature.convertFromDB(this.minimumTemperature);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setMinimumTemperature(Temperature minimumTemperature) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.minimumTemperature;
            InvoiceEquipmentDetailData data = this.getData();
            this.minimumTemperature = InvoiceEquipmentDetailColumns.minimumTemperature.convertToDB(minimumTemperature);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "minimumTemperature", Temperature.class, oldValue, this.minimumTemperature);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Temperature getMaximumTemperature() throws GLException {
        try {
            return (Temperature)InvoiceEquipmentDetailColumns.maximumTemperature.convertFromDB(this.maximumTemperature);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setMaximumTemperature(Temperature maximumTemperature) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.maximumTemperature;
            InvoiceEquipmentDetailData data = this.getData();
            this.maximumTemperature = InvoiceEquipmentDetailColumns.maximumTemperature.convertToDB(maximumTemperature);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "maximumTemperature", Temperature.class, oldValue, this.maximumTemperature);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getPercentHumidityAllowed() throws GLException {
        try {
            return (Integer)InvoiceEquipmentDetailColumns.percentHumidityAllowed.convertFromDB(this.percentHumidityAllowed);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPercentHumidityAllowed(Integer percentHumidityAllowed) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.percentHumidityAllowed;
            InvoiceEquipmentDetailData data = this.getData();
            this.percentHumidityAllowed = InvoiceEquipmentDetailColumns.percentHumidityAllowed.convertToDB(percentHumidityAllowed);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "percentHumidityAllowed", Integer.class, oldValue, this.percentHumidityAllowed);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVentSettingCode() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.ventSettingCode.convertFromDB(this.ventSettingCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVentSettingCode(String ventSettingCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.ventSettingCode;
            InvoiceEquipmentDetailData data = this.getData();
            this.ventSettingCode = InvoiceEquipmentDetailColumns.ventSettingCode.convertToDB(ventSettingCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "ventSettingCode", String.class, oldValue, this.ventSettingCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getWeightAllowance() throws GLException {
        try {
            return (Weight)InvoiceEquipmentDetailColumns.weightAllowance.convertFromDB(this.weightAllowance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setWeightAllowance(Weight weightAllowance) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.weightAllowance;
            InvoiceEquipmentDetailData data = this.getData();
            this.weightAllowance = InvoiceEquipmentDetailColumns.weightAllowance.convertToDB(weightAllowance);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "weightAllowance", Weight.class, oldValue, this.weightAllowance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceEquipmentDetailColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceEquipmentDetailData data = this.getData();
            this.domainName = InvoiceEquipmentDetailColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
