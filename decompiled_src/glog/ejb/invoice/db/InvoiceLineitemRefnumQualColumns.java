/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceLineitemRefnumQualColumns {
    public static SqlColumn invoiceLiRefnumQualGid = new SqlColumn(String.class, "invoice_li_refnum_qual_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn invoiceLiRefnumQualXid = new SqlColumn(String.class, "invoice_li_refnum_qual_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn invoiceLiRefnumDesc = new SqlColumn(String.class, "invoice_li_refnum_desc", 120, 0, 0, null, 2, null);
    public static SqlColumn defaultRefnumBnTypeGid = new SqlColumn(String.class, "default_refnum_bn_type_gid", 101, 0, 0, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
