/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemRefnumColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceLineitemRefnumPK
extends Pk {
    public Object invoiceGid;
    public Object invoiceLiRefnumQualGid;
    public Object lineitemSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLineitemRefnumPK() {
    }

    public InvoiceLineitemRefnumPK(String invoiceGid, String invoiceLiRefnumQualGid, Integer lineitemSeqNo) {
        this.invoiceGid = this.notNull(InvoiceLineitemRefnumColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.invoiceLiRefnumQualGid = this.notNull(InvoiceLineitemRefnumColumns.invoiceLiRefnumQualGid.convertToDB(invoiceLiRefnumQualGid), "invoiceLiRefnumQualGid");
        this.lineitemSeqNo = this.notNull(InvoiceLineitemRefnumColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
    }

    public InvoiceLineitemRefnumPK(int dummy, Object invoiceGid, Object invoiceLiRefnumQualGid, Object lineitemSeqNo) {
        this(dummy, invoiceGid, invoiceLiRefnumQualGid, lineitemSeqNo, null);
    }

    public InvoiceLineitemRefnumPK(int dummy, Object invoiceGid, Object invoiceLiRefnumQualGid, Object lineitemSeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.invoiceLiRefnumQualGid = invoiceLiRefnumQualGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.transaction = transaction;
    }

    public InvoiceLineitemRefnumPK(InvoiceLineitemRefnumPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.invoiceLiRefnumQualGid = otherPk.invoiceLiRefnumQualGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.invoiceLiRefnumQualGid != null ? String.valueOf(this.invoiceLiRefnumQualGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.invoiceLiRefnumQualGid != null ? String.valueOf(this.invoiceLiRefnumQualGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    public static InvoiceLineitemRefnumPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLineitemRefnumPK(gids[0], gids[1], Integer.valueOf(gids[2])) : null;
    }

    public static InvoiceLineitemRefnumPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLineitemRefnumPK(gids[0], gids[1], Integer.valueOf(gids[2])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.invoiceLiRefnumQualGid.hashCode() + this.lineitemSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLineitemRefnumPK)) {
            return false;
        }
        InvoiceLineitemRefnumPK otherPk = (InvoiceLineitemRefnumPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.invoiceLiRefnumQualGid, this.invoiceLiRefnumQualGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLineitemRefnumHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.invoiceLiRefnumQualGid;
            }
            case 2: {
                return this.lineitemSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLineitemRefnumColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceLineitemRefnumColumns.invoiceLiRefnumQualGid.convertFromDB(this.invoiceLiRefnumQualGid);
            }
            case 2: {
                return InvoiceLineitemRefnumColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLineitemRefnumPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLineitemRefnum";
        }

        @Override
        public final String getTableName() {
            return "invoice_lineitem_refnum";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "invoice_li_refnum_qual_gid", "lineitem_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLineitemRefnumPK result = new InvoiceLineitemRefnumPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
