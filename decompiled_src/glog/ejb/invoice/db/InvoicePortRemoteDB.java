/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoicePortData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoicePortRemoteDB
extends EJBObject {
    public InvoicePortData getData() throws RemoteException, GLException;

    public void setData(InvoicePortData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getPortSeqNo() throws RemoteException, GLException;

    public void setPortSeqNo(Integer var1) throws RemoteException, GLException;

    public String getPortFunctionCode() throws RemoteException, GLException;

    public void setPortFunctionCode(String var1) throws RemoteException, GLException;

    public String getLocationIdentifier() throws RemoteException, GLException;

    public void setLocationIdentifier(String var1) throws RemoteException, GLException;

    public String getLocationIdentifierQualifier() throws RemoteException, GLException;

    public void setLocationIdentifierQualifier(String var1) throws RemoteException, GLException;

    public String getPortName() throws RemoteException, GLException;

    public void setPortName(String var1) throws RemoteException, GLException;

    public String getProvinceCode() throws RemoteException, GLException;

    public void setProvinceCode(String var1) throws RemoteException, GLException;

    public String getCountryCode3Gid() throws RemoteException, GLException;

    public void setCountryCode3Gid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
