/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvSupplyProvinceVatRegColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn countryCode3Gid = new SqlColumn(String.class, "country_code3_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn provinceCode = new SqlColumn(String.class, "province_code", 6, 0, 1, null, 2, null);
    public static SqlColumn vatProvincialRegGid = new SqlColumn(String.class, "vat_provincial_reg_gid", 101, 0, 1, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
