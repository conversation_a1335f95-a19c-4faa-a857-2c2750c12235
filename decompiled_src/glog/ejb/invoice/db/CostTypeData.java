/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CostTypePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class CostTypeData
extends BeanData {
    public String costTypeGid;
    public String costTypeXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(CostTypeData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field costTypeGidField = beanDataFields[0];
    public static Field costTypeXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public CostTypeData() {
    }

    public CostTypeData(CostTypeData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getCostTypePK();
    }

    @Legacy
    public CostTypePK getCostTypePK() {
        if (this.costTypeGid == null) {
            return null;
        }
        return new CostTypePK(this.costTypeGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setCostTypePK((CostTypePK)pk);
    }

    @Legacy
    public void setCostTypePK(CostTypePK pk) {
        this.costTypeGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.CostTypeQueryGen";
    }

    public static CostTypeData load(Connection conn, CostTypePK pk) throws GLException {
        return (CostTypeData)CostTypeData.load(conn, pk, CostTypeData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return CostTypeData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return CostTypeData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return CostTypeData.load(conn, whereClause, prepareArguments, fetchSize, CostTypeData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return CostTypeData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return CostTypeData.load(conn, fromWhere, alias, prepareArguments, fetchSize, CostTypeData.class);
    }
}
