/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class AutoApproveRuleProfileDFKList.1
implements Fk {
    AutoApproveRuleProfileDFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AutoApproveRuleProfileDData";
    }

    @Override
    public String getPkField() {
        return "autoApproveRuleGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "AutoApproveRuleData";
    }

    @Override
    public String getFkDataField() {
        return "autoApproveRuleGid";
    }
}
