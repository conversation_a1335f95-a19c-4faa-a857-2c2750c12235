/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStatusPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceStatusData
extends BeanData {
    public String invoiceGid;
    public String statusTypeGid;
    public String statusValueGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStatusData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field statusTypeGidField = beanDataFields[1];
    public static Field statusValueGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceStatusData() {
    }

    public InvoiceStatusData(InvoiceStatusData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceStatusPK();
    }

    @Legacy
    public InvoiceStatusPK getInvoiceStatusPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.statusTypeGid == null) {
            return null;
        }
        return new InvoiceStatusPK(this.invoiceGid, this.statusTypeGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceStatusPK((InvoiceStatusPK)pk);
    }

    @Legacy
    public void setInvoiceStatusPK(InvoiceStatusPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.statusTypeGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceStatusQueryGen";
    }

    public static InvoiceStatusData load(Connection conn, InvoiceStatusPK pk) throws GLException {
        return (InvoiceStatusData)InvoiceStatusData.load(conn, pk, InvoiceStatusData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceStatusData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceStatusData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStatusData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceStatusData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceStatusData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStatusData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceStatusData.class);
    }
}
