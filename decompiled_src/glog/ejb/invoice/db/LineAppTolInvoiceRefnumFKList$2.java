/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineAppTolInvoiceRefnumFKList.2
implements Fk {
    LineAppTolInvoiceRefnumFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineAppTolInvoiceRefnumData";
    }

    @Override
    public String getPkField() {
        return "lineApproveToleranceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "LineApproveToleranceData";
    }

    @Override
    public String getFkDataField() {
        return "lineApproveToleranceGid";
    }
}
