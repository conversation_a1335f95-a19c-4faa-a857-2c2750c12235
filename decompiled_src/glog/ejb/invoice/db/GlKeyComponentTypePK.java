/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlKeyComponentTypeColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class GlKeyComponentTypePK
extends Pk {
    public Object glKeyComponentTypeGid;
    public transient Object glKeyComponentTypeXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public GlKeyComponentTypePK() {
    }

    public GlKeyComponentTypePK(String glKeyComponentTypeGid) {
        this.glKeyComponentTypeGid = this.notNull(GlKeyComponentTypeColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid), "glKeyComponentTypeGid");
    }

    public GlKeyComponentTypePK(String domainName, String glKeyComponentTypeXid) {
        this.domainName = domainName;
        this.glKeyComponentTypeXid = glKeyComponentTypeXid;
        this.glKeyComponentTypeGid = GlKeyComponentTypePK.concatForGid(domainName, glKeyComponentTypeXid);
    }

    public GlKeyComponentTypePK(int dummy, Object glKeyComponentTypeGid) {
        this(dummy, glKeyComponentTypeGid, null);
    }

    public GlKeyComponentTypePK(int dummy, Object glKeyComponentTypeGid, Object transaction) {
        this.glKeyComponentTypeGid = glKeyComponentTypeGid;
        this.transaction = transaction;
    }

    public GlKeyComponentTypePK(GlKeyComponentTypePK otherPk, Object transaction) {
        this.glKeyComponentTypeGid = otherPk.glKeyComponentTypeGid;
        this.glKeyComponentTypeXid = otherPk.glKeyComponentTypeXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.glKeyComponentTypeGid != null ? String.valueOf(this.glKeyComponentTypeGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.glKeyComponentTypeGid != null ? String.valueOf(this.glKeyComponentTypeGid) : "";
    }

    public static GlKeyComponentTypePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new GlKeyComponentTypePK(gids[0]) : null;
    }

    public static GlKeyComponentTypePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new GlKeyComponentTypePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.glKeyComponentTypeGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof GlKeyComponentTypePK)) {
            return false;
        }
        GlKeyComponentTypePK otherPk = (GlKeyComponentTypePK)other;
        return Functions.equals(otherPk.glKeyComponentTypeGid, this.glKeyComponentTypeGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.GlKeyComponentTypeHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.glKeyComponentTypeGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return GlKeyComponentTypeColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GlKeyComponentTypePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("GlKeyComponentTypePK requires a non-null XID to generate a GID");
            }
            GlKeyComponentTypePK glKeyComponentTypePK = new GlKeyComponentTypePK(domain, xid);
            return glKeyComponentTypePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GlKeyComponentTypePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            GlKeyComponentTypePK glKeyComponentTypePK = GlKeyComponentTypePK.newPK(domainName, xid, connection);
            return glKeyComponentTypePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return GlKeyComponentTypePK.class;
        }

        @Override
        public final String getEntity() {
            return "GlKeyComponentType";
        }

        @Override
        public final String getTableName() {
            return "gl_key_component_type";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"gl_key_component_type_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            GlKeyComponentTypePK result = new GlKeyComponentTypePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
