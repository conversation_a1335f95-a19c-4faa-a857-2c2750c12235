/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlLookupKeyDPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class GlLookupKeyDData
extends BeanData {
    public String glLookupKeyGid;
    public Double rank;
    public String glKeyComponentGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlLookupKeyDData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field glLookupKeyGidField = beanDataFields[0];
    public static Field rankField = beanDataFields[1];
    public static Field glKeyComponentGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public GlLookupKeyDData() {
    }

    public GlLookupKeyDData(GlLookupKeyDData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getGlLookupKeyDPK();
    }

    @Legacy
    public GlLookupKeyDPK getGlLookupKeyDPK() {
        if (this.glLookupKeyGid == null) {
            return null;
        }
        if (this.rank == null) {
            return null;
        }
        return new GlLookupKeyDPK(this.glLookupKeyGid, this.rank);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setGlLookupKeyDPK((GlLookupKeyDPK)pk);
    }

    @Legacy
    public void setGlLookupKeyDPK(GlLookupKeyDPK pk) {
        this.glLookupKeyGid = (String)pk.getAppValue(0);
        this.rank = (Double)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.GlLookupKeyDQueryGen";
    }

    public static GlLookupKeyDData load(Connection conn, GlLookupKeyDPK pk) throws GLException {
        return (GlLookupKeyDData)GlLookupKeyDData.load(conn, pk, GlLookupKeyDData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return GlLookupKeyDData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return GlLookupKeyDData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlLookupKeyDData.load(conn, whereClause, prepareArguments, fetchSize, GlLookupKeyDData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return GlLookupKeyDData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlLookupKeyDData.load(conn, fromWhere, alias, prepareArguments, fetchSize, GlLookupKeyDData.class);
    }
}
