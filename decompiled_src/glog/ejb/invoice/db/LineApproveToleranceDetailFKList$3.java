/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineApproveToleranceDetailFKList.3
implements Fk {
    LineApproveToleranceDetailFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineApproveToleranceDetailData";
    }

    @Override
    public String getPkField() {
        return "accessorialCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.rates.db";
    }

    @Override
    public String getFkDataClass() {
        return "AccessorialCodeData";
    }

    @Override
    public String getFkDataField() {
        return "accessorialCodeGid";
    }
}
