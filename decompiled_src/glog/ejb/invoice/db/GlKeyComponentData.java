/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlKeyComponentPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class GlKeyComponentData
extends BeanData {
    public String glKeyComponentGid;
    public String glKeyComponentXid;
    public String glKeyComponentTypeGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field glKeyComponentGidField = beanDataFields[0];
    public static Field glKeyComponentXidField = beanDataFields[1];
    public static Field glKeyComponentTypeGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public GlKeyComponentData() {
    }

    public GlKeyComponentData(GlKeyComponentData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getGlKeyComponentPK();
    }

    @Legacy
    public GlKeyComponentPK getGlKeyComponentPK() {
        if (this.glKeyComponentGid == null) {
            return null;
        }
        return new GlKeyComponentPK(this.glKeyComponentGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setGlKeyComponentPK((GlKeyComponentPK)pk);
    }

    @Legacy
    public void setGlKeyComponentPK(GlKeyComponentPK pk) {
        this.glKeyComponentGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.GlKeyComponentQueryGen";
    }

    public static GlKeyComponentData load(Connection conn, GlKeyComponentPK pk) throws GLException {
        return (GlKeyComponentData)GlKeyComponentData.load(conn, pk, GlKeyComponentData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return GlKeyComponentData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return GlKeyComponentData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlKeyComponentData.load(conn, whereClause, prepareArguments, fetchSize, GlKeyComponentData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return GlKeyComponentData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlKeyComponentData.load(conn, fromWhere, alias, prepareArguments, fetchSize, GlKeyComponentData.class);
    }
}
