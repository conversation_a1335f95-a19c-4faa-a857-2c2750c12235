/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoicePortColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoicePortPK
extends Pk {
    public Object invoiceGid;
    public Object portSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoicePortPK() {
    }

    public InvoicePortPK(String invoiceGid, Integer portSeqNo) {
        this.invoiceGid = this.notNull(InvoicePortColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.portSeqNo = this.notNull(InvoicePortColumns.portSeqNo.convertToDB(portSeqNo), "portSeqNo");
    }

    public InvoicePortPK(int dummy, Object invoiceGid, Object portSeqNo) {
        this(dummy, invoiceGid, portSeqNo, null);
    }

    public InvoicePortPK(int dummy, Object invoiceGid, Object portSeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.portSeqNo = portSeqNo;
        this.transaction = transaction;
    }

    public InvoicePortPK(InvoicePortPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.portSeqNo = otherPk.portSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.portSeqNo != null ? String.valueOf(this.portSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.portSeqNo != null ? String.valueOf(this.portSeqNo) : "");
    }

    public static InvoicePortPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoicePortPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public static InvoicePortPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoicePortPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.portSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoicePortPK)) {
            return false;
        }
        InvoicePortPK otherPk = (InvoicePortPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.portSeqNo, this.portSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoicePortHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.portSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoicePortColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoicePortColumns.portSeqNo.convertFromDB(this.portSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoicePortPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoicePort";
        }

        @Override
        public final String getTableName() {
            return "invoice_port";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "port_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoicePortPK result = new InvoicePortPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
