/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.CommercialInvChargeCodeColumns;
import glog.ejb.invoice.db.CommercialInvChargeCodeData;
import glog.ejb.invoice.db.CommercialInvChargeCodePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class CommercialInvChargeCodeBeanDB
extends BeanManagedEntityBean {
    public Object commercialInvChargeCodeGid;
    public Object commercialInvChargeCodeXid;
    public Object description;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient CommercialInvChargeCodePK pk;
    protected transient CommercialInvChargeCodeData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(CommercialInvChargeCodeBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static CommercialInvChargeCodePK.Callback theCall = new CommercialInvChargeCodePK.Callback();

    public CommercialInvChargeCodeBeanDB() {
        super(false);
    }

    public CommercialInvChargeCodePK ejbCreate(CommercialInvChargeCodeData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected CommercialInvChargeCodePK newPK() throws GLException {
        return CommercialInvChargeCodePK.newPK(this.getDomainName(), this.getCommercialInvChargeCodeXid(), this.getConnection());
    }

    public void ejbPostCreate(CommercialInvChargeCodeData data) throws CreateException {
        this.ejbPostCreator();
    }

    public CommercialInvChargeCodePK ejbFindByPrimaryKey(CommercialInvChargeCodePK pk) throws FinderException {
        return (CommercialInvChargeCodePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(CommercialInvChargeCodePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<CommercialInvChargeCodePK> v = new Vector<CommercialInvChargeCodePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(CommercialInvChargeCodePK pk, CommercialInvChargeCodeData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(CommercialInvChargeCodePK pk, CommercialInvChargeCodeData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(CommercialInvChargeCodePK pk, CommercialInvChargeCodeData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(CommercialInvChargeCodeColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(CommercialInvChargeCodeColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(CommercialInvChargeCodeColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(CommercialInvChargeCodeColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        CommercialInvChargeCodePK pk = (CommercialInvChargeCodePK)genericPk;
        this.commercialInvChargeCodeGid = pk.commercialInvChargeCodeGid;
        this.domainName = pk.domainName;
        this.commercialInvChargeCodeXid = pk.commercialInvChargeCodeXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        CommercialInvChargeCodePK pk = new CommercialInvChargeCodePK(0, this.commercialInvChargeCodeGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.CommercialInvChargeCode";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public CommercialInvChargeCodeData getData() throws GLException {
        try {
            CommercialInvChargeCodeData retval = new CommercialInvChargeCodeData();
            retval.getFromBean(this, CommercialInvChargeCodeColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(CommercialInvChargeCodeData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(CommercialInvChargeCodeData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            CommercialInvChargeCodeData oldData = modified ? this.getData() : null;
            data.setToBean(this, CommercialInvChargeCodeColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return CommercialInvChargeCodeData.class;
    }

    @Override
    public Class getPkClass() {
        return CommercialInvChargeCodePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getCommercialInvChargeCodeGid() throws GLException {
        try {
            return (String)CommercialInvChargeCodeColumns.commercialInvChargeCodeGid.convertFromDB(this.commercialInvChargeCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvChargeCodeGid(String commercialInvChargeCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvChargeCodeGid;
            CommercialInvChargeCodeData data = this.getData();
            this.commercialInvChargeCodeGid = CommercialInvChargeCodeColumns.commercialInvChargeCodeGid.convertToDB(commercialInvChargeCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvChargeCodeGid", String.class, oldValue, this.commercialInvChargeCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialInvChargeCodeXid() throws GLException {
        try {
            return (String)CommercialInvChargeCodeColumns.commercialInvChargeCodeXid.convertFromDB(this.commercialInvChargeCodeXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvChargeCodeXid(String commercialInvChargeCodeXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvChargeCodeXid;
            CommercialInvChargeCodeData data = this.getData();
            this.commercialInvChargeCodeXid = CommercialInvChargeCodeColumns.commercialInvChargeCodeXid.convertToDB(commercialInvChargeCodeXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvChargeCodeXid", String.class, oldValue, this.commercialInvChargeCodeXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)CommercialInvChargeCodeColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            CommercialInvChargeCodeData data = this.getData();
            this.description = CommercialInvChargeCodeColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)CommercialInvChargeCodeColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            CommercialInvChargeCodeData data = this.getData();
            this.domainName = CommercialInvChargeCodeColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
