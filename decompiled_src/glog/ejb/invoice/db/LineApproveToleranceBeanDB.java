/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.LineApproveToleranceColumns;
import glog.ejb.invoice.db.LineApproveToleranceData;
import glog.ejb.invoice.db.LineApproveTolerancePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class LineApproveToleranceBeanDB
extends BeanManagedEntityBean {
    public Object lineApproveToleranceGid;
    public Object lineApproveToleranceXid;
    public Object isInvoiceRefnumMatchAll;
    public Object isShipmentRefnumMatchAll;
    public Object domainName;
    public Object effectiveDate;
    public Object expirationDate;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient LineApproveTolerancePK pk;
    protected transient LineApproveToleranceData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineApproveToleranceBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static LineApproveTolerancePK.Callback theCall = new LineApproveTolerancePK.Callback();

    public LineApproveToleranceBeanDB() {
        super(false);
    }

    public LineApproveTolerancePK ejbCreate(LineApproveToleranceData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected LineApproveTolerancePK newPK() throws GLException {
        return LineApproveTolerancePK.newPK(this.getDomainName(), this.getLineApproveToleranceXid(), this.getConnection());
    }

    public void ejbPostCreate(LineApproveToleranceData data) throws CreateException {
        this.ejbPostCreator();
    }

    public LineApproveTolerancePK ejbFindByPrimaryKey(LineApproveTolerancePK pk) throws FinderException {
        return (LineApproveTolerancePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(LineApproveTolerancePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<LineApproveTolerancePK> v = new Vector<LineApproveTolerancePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(LineApproveTolerancePK pk, LineApproveToleranceData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(LineApproveTolerancePK pk, LineApproveToleranceData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(LineApproveTolerancePK pk, LineApproveToleranceData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(LineApproveToleranceColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(LineApproveToleranceColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(LineApproveToleranceColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(LineApproveToleranceColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        LineApproveTolerancePK pk = (LineApproveTolerancePK)genericPk;
        this.lineApproveToleranceGid = pk.lineApproveToleranceGid;
        this.domainName = pk.domainName;
        this.lineApproveToleranceXid = pk.lineApproveToleranceXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        LineApproveTolerancePK pk = new LineApproveTolerancePK(0, this.lineApproveToleranceGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.LineApproveTolerance";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public LineApproveToleranceData getData() throws GLException {
        try {
            LineApproveToleranceData retval = new LineApproveToleranceData();
            retval.getFromBean(this, LineApproveToleranceColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(LineApproveToleranceData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(LineApproveToleranceData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            LineApproveToleranceData oldData = modified ? this.getData() : null;
            data.setToBean(this, LineApproveToleranceColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return LineApproveToleranceData.class;
    }

    @Override
    public Class getPkClass() {
        return LineApproveTolerancePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getLineApproveToleranceGid() throws GLException {
        try {
            return (String)LineApproveToleranceColumns.lineApproveToleranceGid.convertFromDB(this.lineApproveToleranceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineApproveToleranceGid(String lineApproveToleranceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineApproveToleranceGid;
            LineApproveToleranceData data = this.getData();
            this.lineApproveToleranceGid = LineApproveToleranceColumns.lineApproveToleranceGid.convertToDB(lineApproveToleranceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineApproveToleranceGid", String.class, oldValue, this.lineApproveToleranceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getLineApproveToleranceXid() throws GLException {
        try {
            return (String)LineApproveToleranceColumns.lineApproveToleranceXid.convertFromDB(this.lineApproveToleranceXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineApproveToleranceXid(String lineApproveToleranceXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineApproveToleranceXid;
            LineApproveToleranceData data = this.getData();
            this.lineApproveToleranceXid = LineApproveToleranceColumns.lineApproveToleranceXid.convertToDB(lineApproveToleranceXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineApproveToleranceXid", String.class, oldValue, this.lineApproveToleranceXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsInvoiceRefnumMatchAll() throws GLException {
        try {
            return (Boolean)LineApproveToleranceColumns.isInvoiceRefnumMatchAll.convertFromDB(this.isInvoiceRefnumMatchAll);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsInvoiceRefnumMatchAll(Boolean isInvoiceRefnumMatchAll) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isInvoiceRefnumMatchAll;
            LineApproveToleranceData data = this.getData();
            this.isInvoiceRefnumMatchAll = LineApproveToleranceColumns.isInvoiceRefnumMatchAll.convertToDB(isInvoiceRefnumMatchAll);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isInvoiceRefnumMatchAll", Boolean.class, oldValue, this.isInvoiceRefnumMatchAll);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsShipmentRefnumMatchAll() throws GLException {
        try {
            return (Boolean)LineApproveToleranceColumns.isShipmentRefnumMatchAll.convertFromDB(this.isShipmentRefnumMatchAll);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsShipmentRefnumMatchAll(Boolean isShipmentRefnumMatchAll) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isShipmentRefnumMatchAll;
            LineApproveToleranceData data = this.getData();
            this.isShipmentRefnumMatchAll = LineApproveToleranceColumns.isShipmentRefnumMatchAll.convertToDB(isShipmentRefnumMatchAll);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isShipmentRefnumMatchAll", Boolean.class, oldValue, this.isShipmentRefnumMatchAll);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)LineApproveToleranceColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            LineApproveToleranceData data = this.getData();
            this.domainName = LineApproveToleranceColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getEffectiveDate() throws GLException {
        try {
            return (LocalDate)LineApproveToleranceColumns.effectiveDate.convertFromDB(this.effectiveDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEffectiveDate(LocalDate effectiveDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.effectiveDate;
            LineApproveToleranceData data = this.getData();
            this.effectiveDate = LineApproveToleranceColumns.effectiveDate.convertToDB(effectiveDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "effectiveDate", LocalDate.class, oldValue, this.effectiveDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExpirationDate() throws GLException {
        try {
            return (LocalDate)LineApproveToleranceColumns.expirationDate.convertFromDB(this.expirationDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExpirationDate(LocalDate expirationDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.expirationDate;
            LineApproveToleranceData data = this.getData();
            this.expirationDate = LineApproveToleranceColumns.expirationDate.convertToDB(expirationDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "expirationDate", LocalDate.class, oldValue, this.expirationDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
