/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class CommercialInvoiceFKList.5
implements Fk {
    CommercialInvoiceFKList.5() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "CommercialInvoiceData";
    }

    @Override
    public String getPkField() {
        return "totalInvoiceAmtCurrencyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "CurrencyData";
    }

    @Override
    public String getFkDataField() {
        return "currencyGid";
    }
}
