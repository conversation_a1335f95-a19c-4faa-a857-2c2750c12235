/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryRemarkData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceSummaryRemarkRemoteDB
extends EJBObject {
    public InvoiceSummaryRemarkData getData() throws RemoteException, GLException;

    public void setData(InvoiceSummaryRemarkData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON><PERSON>Exception, GLException;

    public Map verify() throws <PERSON><PERSON><PERSON>x<PERSON>, <PERSON><PERSON>x<PERSON>;

    public String getInvoiceGid() throws <PERSON>moteException, <PERSON>LException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getRemarkSeqNo() throws RemoteException, GLException;

    public void setRemarkSeqNo(Integer var1) throws RemoteException, GLException;

    public Integer getInvoiceSummarySeqNo() throws RemoteException, GLException;

    public void setInvoiceSummarySeqNo(Integer var1) throws RemoteException, GLException;

    public String getRemarkQualIdentifier() throws RemoteException, GLException;

    public void setRemarkQualIdentifier(String var1) throws RemoteException, GLException;

    public String getRemarkText() throws RemoteException, GLException;

    public void setRemarkText(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
