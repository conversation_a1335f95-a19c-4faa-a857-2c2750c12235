/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.DeclaredValueQualPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class DeclaredValueQualData
extends BeanData {
    public String declaredValueQualGid;
    public String declaredValueQualXid;
    public String declaredValueDesc;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(DeclaredValueQualData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field declaredValueQualGidField = beanDataFields[0];
    public static Field declaredValueQualXidField = beanDataFields[1];
    public static Field declaredValueDescField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public DeclaredValueQualData() {
    }

    public DeclaredValueQualData(DeclaredValueQualData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getDeclaredValueQualPK();
    }

    @Legacy
    public DeclaredValueQualPK getDeclaredValueQualPK() {
        if (this.declaredValueQualGid == null) {
            return null;
        }
        return new DeclaredValueQualPK(this.declaredValueQualGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setDeclaredValueQualPK((DeclaredValueQualPK)pk);
    }

    @Legacy
    public void setDeclaredValueQualPK(DeclaredValueQualPK pk) {
        this.declaredValueQualGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.DeclaredValueQualQueryGen";
    }

    public static DeclaredValueQualData load(Connection conn, DeclaredValueQualPK pk) throws GLException {
        return (DeclaredValueQualData)DeclaredValueQualData.load(conn, pk, DeclaredValueQualData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return DeclaredValueQualData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return DeclaredValueQualData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return DeclaredValueQualData.load(conn, whereClause, prepareArguments, fetchSize, DeclaredValueQualData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return DeclaredValueQualData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return DeclaredValueQualData.load(conn, fromWhere, alias, prepareArguments, fetchSize, DeclaredValueQualData.class);
    }
}
