/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.24
implements Fk {
    InvoiceFKList.24() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "servprovVatRegNoGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vat.db";
    }

    @Override
    public String getFkDataClass() {
        return "VatRegistrationData";
    }

    @Override
    public String getFkDataField() {
        return "vatRegNoGid";
    }
}
