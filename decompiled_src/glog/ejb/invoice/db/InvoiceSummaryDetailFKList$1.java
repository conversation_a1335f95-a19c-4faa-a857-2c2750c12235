/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceSummaryDetailFKList.1
implements Fk {
    InvoiceSummaryDetailFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceSummaryDetailData";
    }

    @Override
    public String getPkField() {
        return "paymentMethodCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "PaymentMethodCodeData";
    }

    @Override
    public String getFkDataField() {
        return "paymentMethodCodeGid";
    }
}
