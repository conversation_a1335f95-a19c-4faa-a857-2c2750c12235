/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryDetailData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceSummaryDetailRemoteDB
extends EJBObject {
    public InvoiceSummaryDetailData getData() throws RemoteException, GLException;

    public void setData(InvoiceSummaryDetailData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Integer var1) throws RemoteException, GLException;

    public String getBilledAsQualifier() throws RemoteException, GLException;

    public void setBilledAsQualifier(String var1) throws RemoteException, GLException;

    public Currency getBilledAsQuantity() throws RemoteException, GLException;

    public void setBilledAsQuantity(Currency var1) throws RemoteException, GLException;

    public String getFreightRateQualifier() throws RemoteException, GLException;

    public void setFreightRateQualifier(String var1) throws RemoteException, GLException;

    public Currency getFreightRateQuantity() throws RemoteException, GLException;

    public void setFreightRateQuantity(Currency var1) throws RemoteException, GLException;

    public Long getUnitCount() throws RemoteException, GLException;

    public void setUnitCount(Long var1) throws RemoteException, GLException;

    public String getPackagingUnitGid() throws RemoteException, GLException;

    public void setPackagingUnitGid(String var1) throws RemoteException, GLException;

    public Weight getWeight() throws RemoteException, GLException;

    public void setWeight(Weight var1) throws RemoteException, GLException;

    public Volume getVolume() throws RemoteException, GLException;

    public void setVolume(Volume var1) throws RemoteException, GLException;

    public String getWeightQualifier() throws RemoteException, GLException;

    public void setWeightQualifier(String var1) throws RemoteException, GLException;

    public Currency getFreightCharge() throws RemoteException, GLException;

    public void setFreightCharge(Currency var1) throws RemoteException, GLException;

    public Currency getPrepaidAmount() throws RemoteException, GLException;

    public void setPrepaidAmount(Currency var1) throws RemoteException, GLException;

    public String getPaymentMethodCodeGid() throws RemoteException, GLException;

    public void setPaymentMethodCodeGid(String var1) throws RemoteException, GLException;

    public String getAccessorialCodeGid() throws RemoteException, GLException;

    public void setAccessorialCodeGid(String var1) throws RemoteException, GLException;

    public String getAccessorialDescription() throws RemoteException, GLException;

    public void setAccessorialDescription(String var1) throws RemoteException, GLException;

    public String getDeclaredValueQualGid() throws RemoteException, GLException;

    public void setDeclaredValueQualGid(String var1) throws RemoteException, GLException;

    public Currency getDeclaredValue() throws RemoteException, GLException;

    public void setDeclaredValue(Currency var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
