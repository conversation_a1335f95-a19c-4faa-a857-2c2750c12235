/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class GlKeyComponentFKList.1
implements Fk {
    GlKeyComponentFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "GlKeyComponentData";
    }

    @Override
    public String getPkField() {
        return "glKeyComponentTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "GlKeyComponentTypeData";
    }

    @Override
    public String getFkDataField() {
        return "glKeyComponentTypeGid";
    }
}
