/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class GlLookupKeyColumns {
    public static SqlColumn glLookupKeyGid = new SqlColumn(String.class, "gl_lookup_key_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn glLookupKeyXid = new SqlColumn(String.class, "gl_lookup_key_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn isActive = new SqlColumn(Boolean.class, "is_active", 1, 0, 1, Boolean.valueOf("false"), 2, null);
    public static SqlColumn perspective = new SqlColumn(String.class, "perspective", 1, 0, 1, String.valueOf("B"), 3, null);
    public static SqlColumn glCodeAssignType = new SqlColumn(String.class, "gl_code_assign_type", 1, 0, 1, String.valueOf("O"), 4, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 5, null);
}
