/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceEquipmentDetailColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceEquipmentDetailPK
extends Pk {
    public Object invoiceGid;
    public Object seqNumber;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceEquipmentDetailPK() {
    }

    public InvoiceEquipmentDetailPK(String invoiceGid, Long seqNumber) {
        this.invoiceGid = this.notNull(InvoiceEquipmentDetailColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.seqNumber = this.notNull(InvoiceEquipmentDetailColumns.seqNumber.convertToDB(seqNumber), "seqNumber");
    }

    public InvoiceEquipmentDetailPK(int dummy, Object invoiceGid, Object seqNumber) {
        this(dummy, invoiceGid, seqNumber, null);
    }

    public InvoiceEquipmentDetailPK(int dummy, Object invoiceGid, Object seqNumber, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.seqNumber = seqNumber;
        this.transaction = transaction;
    }

    public InvoiceEquipmentDetailPK(InvoiceEquipmentDetailPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.seqNumber = otherPk.seqNumber;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.seqNumber != null ? String.valueOf(this.seqNumber) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.seqNumber != null ? String.valueOf(this.seqNumber) : "");
    }

    public static InvoiceEquipmentDetailPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceEquipmentDetailPK(gids[0], Long.valueOf(gids[1])) : null;
    }

    public static InvoiceEquipmentDetailPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceEquipmentDetailPK(gids[0], Long.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.seqNumber.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceEquipmentDetailPK)) {
            return false;
        }
        InvoiceEquipmentDetailPK otherPk = (InvoiceEquipmentDetailPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.seqNumber, this.seqNumber) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceEquipmentDetailHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.seqNumber;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceEquipmentDetailColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceEquipmentDetailColumns.seqNumber.convertFromDB(this.seqNumber);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceEquipmentDetailPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceEquipmentDetail";
        }

        @Override
        public final String getTableName() {
            return "invoice_equipment_detail";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "seq_number"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceEquipmentDetailPK result = new InvoiceEquipmentDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
