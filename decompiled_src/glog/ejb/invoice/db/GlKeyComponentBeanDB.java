/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlKeyComponentColumns;
import glog.ejb.invoice.db.GlKeyComponentData;
import glog.ejb.invoice.db.GlKeyComponentPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlKeyComponentBeanDB
extends BeanManagedEntityBean {
    public Object glKeyComponentGid;
    public Object glKeyComponentXid;
    public Object glKeyComponentTypeGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlKeyComponentPK pk;
    protected transient GlKeyComponentData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlKeyComponentPK.Callback theCall = new GlKeyComponentPK.Callback();

    public GlKeyComponentBeanDB() {
        super(false);
    }

    public GlKeyComponentPK ejbCreate(GlKeyComponentData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected GlKeyComponentPK newPK() throws GLException {
        return GlKeyComponentPK.newPK(this.getDomainName(), this.getGlKeyComponentXid(), this.getConnection());
    }

    public void ejbPostCreate(GlKeyComponentData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlKeyComponentPK ejbFindByPrimaryKey(GlKeyComponentPK pk) throws FinderException {
        return (GlKeyComponentPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlKeyComponentPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlKeyComponentPK> v = new Vector<GlKeyComponentPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlKeyComponentPK pk, GlKeyComponentData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlKeyComponentPK pk, GlKeyComponentData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlKeyComponentPK pk, GlKeyComponentData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlKeyComponentColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlKeyComponentColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlKeyComponentColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlKeyComponentColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlKeyComponentPK pk = (GlKeyComponentPK)genericPk;
        this.glKeyComponentGid = pk.glKeyComponentGid;
        this.domainName = pk.domainName;
        this.glKeyComponentXid = pk.glKeyComponentXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlKeyComponentPK pk = new GlKeyComponentPK(0, this.glKeyComponentGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlKeyComponent";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlKeyComponentData getData() throws GLException {
        try {
            GlKeyComponentData retval = new GlKeyComponentData();
            retval.getFromBean(this, GlKeyComponentColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlKeyComponentData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlKeyComponentData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlKeyComponentData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlKeyComponentColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlKeyComponentData.class;
    }

    @Override
    public Class getPkClass() {
        return GlKeyComponentPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlKeyComponentGid() throws GLException {
        try {
            return (String)GlKeyComponentColumns.glKeyComponentGid.convertFromDB(this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentGid(String glKeyComponentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentGid;
            GlKeyComponentData data = this.getData();
            this.glKeyComponentGid = GlKeyComponentColumns.glKeyComponentGid.convertToDB(glKeyComponentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentGid", String.class, oldValue, this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlKeyComponentXid() throws GLException {
        try {
            return (String)GlKeyComponentColumns.glKeyComponentXid.convertFromDB(this.glKeyComponentXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentXid(String glKeyComponentXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentXid;
            GlKeyComponentData data = this.getData();
            this.glKeyComponentXid = GlKeyComponentColumns.glKeyComponentXid.convertToDB(glKeyComponentXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentXid", String.class, oldValue, this.glKeyComponentXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlKeyComponentTypeGid() throws GLException {
        try {
            return (String)GlKeyComponentColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentTypeGid(String glKeyComponentTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentTypeGid;
            GlKeyComponentData data = this.getData();
            this.glKeyComponentTypeGid = GlKeyComponentColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentTypeGid", String.class, oldValue, this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlKeyComponentColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlKeyComponentData data = this.getData();
            this.domainName = GlKeyComponentColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
