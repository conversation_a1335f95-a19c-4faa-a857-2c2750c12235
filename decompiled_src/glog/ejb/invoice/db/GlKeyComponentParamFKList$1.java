/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class GlKeyComponentParamFKList.1
implements Fk {
    GlKeyComponentParamFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "GlKeyComponentParamData";
    }

    @Override
    public String getPkField() {
        return "glKeyComponentTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "GlKeyComponentTypeParamData";
    }

    @Override
    public String getFkDataField() {
        return "glKeyComponentTypeGid";
    }
}
