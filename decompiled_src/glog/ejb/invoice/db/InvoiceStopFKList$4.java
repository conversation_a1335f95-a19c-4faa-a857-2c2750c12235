/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceStopFKList.4
implements Fk {
    InvoiceStopFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceStopData";
    }

    @Override
    public String getPkField() {
        return "locationGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "LocationData";
    }

    @Override
    public String getFkDataField() {
        return "locationGid";
    }
}
