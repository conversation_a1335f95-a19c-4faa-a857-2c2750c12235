/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.VoucherVatAnalysisColumns;
import glog.ejb.invoice.db.VoucherVatAnalysisData;
import glog.ejb.invoice.db.VoucherVatAnalysisPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class VoucherVatAnalysisBeanDB
extends BeanManagedEntityBean {
    public Object voucherGid;
    public Object vatCodeGid;
    public Object taxAmount;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object vatRate;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient VoucherVatAnalysisPK pk;
    protected transient VoucherVatAnalysisData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherVatAnalysisBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static VoucherVatAnalysisPK.Callback theCall = new VoucherVatAnalysisPK.Callback();

    public VoucherVatAnalysisBeanDB() {
        super(false);
    }

    public VoucherVatAnalysisPK ejbCreate(VoucherVatAnalysisData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(VoucherVatAnalysisData data) throws CreateException {
        this.ejbPostCreator();
    }

    public VoucherVatAnalysisPK ejbFindByPrimaryKey(VoucherVatAnalysisPK pk) throws FinderException {
        return (VoucherVatAnalysisPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(VoucherVatAnalysisPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<VoucherVatAnalysisPK> v = new Vector<VoucherVatAnalysisPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(VoucherVatAnalysisPK pk, VoucherVatAnalysisData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(VoucherVatAnalysisPK pk, VoucherVatAnalysisData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(VoucherVatAnalysisPK pk, VoucherVatAnalysisData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (VoucherVatAnalysisPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(VoucherVatAnalysisColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(VoucherVatAnalysisColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(VoucherVatAnalysisColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(VoucherVatAnalysisColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        VoucherVatAnalysisPK pk = (VoucherVatAnalysisPK)genericPk;
        this.vatCodeGid = pk.vatCodeGid;
        this.voucherGid = pk.voucherGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        VoucherVatAnalysisPK pk = new VoucherVatAnalysisPK(0, this.vatCodeGid, this.voucherGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.VoucherVatAnalysis";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public VoucherVatAnalysisData getData() throws GLException {
        try {
            VoucherVatAnalysisData retval = new VoucherVatAnalysisData();
            retval.getFromBean(this, VoucherVatAnalysisColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(VoucherVatAnalysisData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(VoucherVatAnalysisData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            VoucherVatAnalysisData oldData = modified ? this.getData() : null;
            data.setToBean(this, VoucherVatAnalysisColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return VoucherVatAnalysisData.class;
    }

    @Override
    public Class getPkClass() {
        return VoucherVatAnalysisPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getVoucherGid() throws GLException {
        try {
            return (String)VoucherVatAnalysisColumns.voucherGid.convertFromDB(this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherGid(String voucherGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherGid;
            VoucherVatAnalysisData data = this.getData();
            this.voucherGid = VoucherVatAnalysisColumns.voucherGid.convertToDB(voucherGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherGid", String.class, oldValue, this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVatCodeGid() throws GLException {
        try {
            return (String)VoucherVatAnalysisColumns.vatCodeGid.convertFromDB(this.vatCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVatCodeGid(String vatCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.vatCodeGid;
            VoucherVatAnalysisData data = this.getData();
            this.vatCodeGid = VoucherVatAnalysisColumns.vatCodeGid.convertToDB(vatCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "vatCodeGid", String.class, oldValue, this.vatCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTaxAmount() throws GLException {
        try {
            return (Currency)VoucherVatAnalysisColumns.taxAmount.convertFromDB(this.taxAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTaxAmount(Currency taxAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.taxAmount;
            VoucherVatAnalysisData data = this.getData();
            this.taxAmount = VoucherVatAnalysisColumns.taxAmount.convertToDB(taxAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "taxAmount", Currency.class, oldValue, this.taxAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)VoucherVatAnalysisColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            VoucherVatAnalysisData data = this.getData();
            this.exchangeRateDate = VoucherVatAnalysisColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)VoucherVatAnalysisColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            VoucherVatAnalysisData data = this.getData();
            this.exchangeRateGid = VoucherVatAnalysisColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getVatRate() throws GLException {
        try {
            return (Double)VoucherVatAnalysisColumns.vatRate.convertFromDB(this.vatRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVatRate(Double vatRate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.vatRate;
            VoucherVatAnalysisData data = this.getData();
            this.vatRate = VoucherVatAnalysisColumns.vatRate.convertToDB(vatRate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "vatRate", Double.class, oldValue, this.vatRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)VoucherVatAnalysisColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            VoucherVatAnalysisData data = this.getData();
            this.domainName = VoucherVatAnalysisColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
