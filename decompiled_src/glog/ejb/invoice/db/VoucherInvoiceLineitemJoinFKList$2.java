/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherInvoiceLineitemJoinFKList.2
implements Fk {
    VoucherInvoiceLineitemJoinFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherInvoiceLineitemJoinData";
    }

    @Override
    public String getPkField() {
        return "amountPaidFnCurrencyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "CurrencyData";
    }

    @Override
    public String getFkDataField() {
        return "currencyGid";
    }
}
