/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceStopRefnumRemoteDB
extends EJBObject {
    public InvoiceStopRefnumData getData() throws RemoteException, GLException;

    public void setData(InvoiceStopRefnumData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws <PERSON>moteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public String getStopSeqNo() throws RemoteException, GLException;

    public void setStopSeqNo(String var1) throws RemoteException, GLException;

    public String getInvoiceStopRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceStopRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getReferenceNumber() throws RemoteException, GLException;

    public void setReferenceNumber(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
