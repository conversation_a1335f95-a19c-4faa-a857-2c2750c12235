/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceLiCommercialDataColumns;
import glog.ejb.invoice.db.InvoiceLiCommercialDataData;
import glog.ejb.invoice.db.InvoiceLiCommercialDataPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceLiCommercialDataBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object commercialDataSeqNo;
    public Object unitPrice;
    public Object unitPriceQualifier;
    public Object liTotalCommercialValue;
    public Object unitCount;
    public Object packagingUnitGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceLiCommercialDataPK pk;
    protected transient InvoiceLiCommercialDataData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLiCommercialDataBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceLiCommercialDataPK.Callback theCall = new InvoiceLiCommercialDataPK.Callback();

    public InvoiceLiCommercialDataBeanDB() {
        super(false);
    }

    public InvoiceLiCommercialDataPK ejbCreate(InvoiceLiCommercialDataData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceLiCommercialDataData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceLiCommercialDataPK ejbFindByPrimaryKey(InvoiceLiCommercialDataPK pk) throws FinderException {
        return (InvoiceLiCommercialDataPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceLiCommercialDataPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceLiCommercialDataPK> v = new Vector<InvoiceLiCommercialDataPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceLiCommercialDataPK pk, InvoiceLiCommercialDataData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceLiCommercialDataPK pk, InvoiceLiCommercialDataData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceLiCommercialDataPK pk, InvoiceLiCommercialDataData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceLiCommercialDataPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceLiCommercialDataColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceLiCommercialDataColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceLiCommercialDataColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceLiCommercialDataColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceLiCommercialDataPK pk = (InvoiceLiCommercialDataPK)genericPk;
        this.commercialDataSeqNo = pk.commercialDataSeqNo;
        this.invoiceGid = pk.invoiceGid;
        this.lineitemSeqNo = pk.lineitemSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceLiCommercialDataPK pk = new InvoiceLiCommercialDataPK(0, this.commercialDataSeqNo, this.invoiceGid, this.lineitemSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceLiCommercialData";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceLiCommercialDataData getData() throws GLException {
        try {
            InvoiceLiCommercialDataData retval = new InvoiceLiCommercialDataData();
            retval.getFromBean(this, InvoiceLiCommercialDataColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceLiCommercialDataData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceLiCommercialDataData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceLiCommercialDataData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceLiCommercialDataColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceLiCommercialDataData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceLiCommercialDataPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceLiCommercialDataColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceLiCommercialDataData data = this.getData();
            this.invoiceGid = InvoiceLiCommercialDataColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getLineitemSeqNo() throws GLException {
        try {
            return (Integer)InvoiceLiCommercialDataColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineitemSeqNo(Integer lineitemSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineitemSeqNo;
            InvoiceLiCommercialDataData data = this.getData();
            this.lineitemSeqNo = InvoiceLiCommercialDataColumns.lineitemSeqNo.convertToDB(lineitemSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineitemSeqNo", Integer.class, oldValue, this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCommercialDataSeqNo() throws GLException {
        try {
            return (Integer)InvoiceLiCommercialDataColumns.commercialDataSeqNo.convertFromDB(this.commercialDataSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialDataSeqNo(Integer commercialDataSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialDataSeqNo;
            InvoiceLiCommercialDataData data = this.getData();
            this.commercialDataSeqNo = InvoiceLiCommercialDataColumns.commercialDataSeqNo.convertToDB(commercialDataSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialDataSeqNo", Integer.class, oldValue, this.commercialDataSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getUnitPrice() throws GLException {
        try {
            return (Currency)InvoiceLiCommercialDataColumns.unitPrice.convertFromDB(this.unitPrice);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUnitPrice(Currency unitPrice) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.unitPrice;
            InvoiceLiCommercialDataData data = this.getData();
            this.unitPrice = InvoiceLiCommercialDataColumns.unitPrice.convertToDB(unitPrice);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "unitPrice", Currency.class, oldValue, this.unitPrice);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getUnitPriceQualifier() throws GLException {
        try {
            return (String)InvoiceLiCommercialDataColumns.unitPriceQualifier.convertFromDB(this.unitPriceQualifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUnitPriceQualifier(String unitPriceQualifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.unitPriceQualifier;
            InvoiceLiCommercialDataData data = this.getData();
            this.unitPriceQualifier = InvoiceLiCommercialDataColumns.unitPriceQualifier.convertToDB(unitPriceQualifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "unitPriceQualifier", String.class, oldValue, this.unitPriceQualifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getLiTotalCommercialValue() throws GLException {
        try {
            return (Currency)InvoiceLiCommercialDataColumns.liTotalCommercialValue.convertFromDB(this.liTotalCommercialValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLiTotalCommercialValue(Currency liTotalCommercialValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.liTotalCommercialValue;
            InvoiceLiCommercialDataData data = this.getData();
            this.liTotalCommercialValue = InvoiceLiCommercialDataColumns.liTotalCommercialValue.convertToDB(liTotalCommercialValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "liTotalCommercialValue", Currency.class, oldValue, this.liTotalCommercialValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getUnitCount() throws GLException {
        try {
            return (Long)InvoiceLiCommercialDataColumns.unitCount.convertFromDB(this.unitCount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUnitCount(Long unitCount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.unitCount;
            InvoiceLiCommercialDataData data = this.getData();
            this.unitCount = InvoiceLiCommercialDataColumns.unitCount.convertToDB(unitCount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "unitCount", Long.class, oldValue, this.unitCount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getPackagingUnitGid() throws GLException {
        try {
            return (String)InvoiceLiCommercialDataColumns.packagingUnitGid.convertFromDB(this.packagingUnitGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPackagingUnitGid(String packagingUnitGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.packagingUnitGid;
            InvoiceLiCommercialDataData data = this.getData();
            this.packagingUnitGid = InvoiceLiCommercialDataColumns.packagingUnitGid.convertToDB(packagingUnitGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "packagingUnitGid", String.class, oldValue, this.packagingUnitGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)InvoiceLiCommercialDataColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            InvoiceLiCommercialDataData data = this.getData();
            this.exchangeRateDate = InvoiceLiCommercialDataColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)InvoiceLiCommercialDataColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            InvoiceLiCommercialDataData data = this.getData();
            this.exchangeRateGid = InvoiceLiCommercialDataColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceLiCommercialDataColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceLiCommercialDataData data = this.getData();
            this.domainName = InvoiceLiCommercialDataColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
