/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceStopReasonColumns;
import glog.ejb.invoice.db.InvoiceStopReasonData;
import glog.ejb.invoice.db.InvoiceStopReasonPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceStopReasonBeanDB
extends BeanManagedEntityBean {
    public Object invoiceStopReasonGid;
    public Object invoiceStopReasonXid;
    public Object invoiceStopReasonDesc;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceStopReasonPK pk;
    protected transient InvoiceStopReasonData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStopReasonBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceStopReasonPK.Callback theCall = new InvoiceStopReasonPK.Callback();

    public InvoiceStopReasonBeanDB() {
        super(false);
    }

    public InvoiceStopReasonPK ejbCreate(InvoiceStopReasonData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected InvoiceStopReasonPK newPK() throws GLException {
        return InvoiceStopReasonPK.newPK(this.getDomainName(), this.getInvoiceStopReasonXid(), this.getConnection());
    }

    public void ejbPostCreate(InvoiceStopReasonData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceStopReasonPK ejbFindByPrimaryKey(InvoiceStopReasonPK pk) throws FinderException {
        return (InvoiceStopReasonPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceStopReasonPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceStopReasonPK> v = new Vector<InvoiceStopReasonPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceStopReasonPK pk, InvoiceStopReasonData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceStopReasonPK pk, InvoiceStopReasonData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceStopReasonPK pk, InvoiceStopReasonData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceStopReasonColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceStopReasonColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceStopReasonColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceStopReasonColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceStopReasonPK pk = (InvoiceStopReasonPK)genericPk;
        this.invoiceStopReasonGid = pk.invoiceStopReasonGid;
        this.domainName = pk.domainName;
        this.invoiceStopReasonXid = pk.invoiceStopReasonXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceStopReasonPK pk = new InvoiceStopReasonPK(0, this.invoiceStopReasonGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceStopReason";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceStopReasonData getData() throws GLException {
        try {
            InvoiceStopReasonData retval = new InvoiceStopReasonData();
            retval.getFromBean(this, InvoiceStopReasonColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceStopReasonData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceStopReasonData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceStopReasonData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceStopReasonColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceStopReasonData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceStopReasonPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceStopReasonGid() throws GLException {
        try {
            return (String)InvoiceStopReasonColumns.invoiceStopReasonGid.convertFromDB(this.invoiceStopReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceStopReasonGid(String invoiceStopReasonGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceStopReasonGid;
            InvoiceStopReasonData data = this.getData();
            this.invoiceStopReasonGid = InvoiceStopReasonColumns.invoiceStopReasonGid.convertToDB(invoiceStopReasonGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceStopReasonGid", String.class, oldValue, this.invoiceStopReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceStopReasonXid() throws GLException {
        try {
            return (String)InvoiceStopReasonColumns.invoiceStopReasonXid.convertFromDB(this.invoiceStopReasonXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceStopReasonXid(String invoiceStopReasonXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceStopReasonXid;
            InvoiceStopReasonData data = this.getData();
            this.invoiceStopReasonXid = InvoiceStopReasonColumns.invoiceStopReasonXid.convertToDB(invoiceStopReasonXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceStopReasonXid", String.class, oldValue, this.invoiceStopReasonXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceStopReasonDesc() throws GLException {
        try {
            return (String)InvoiceStopReasonColumns.invoiceStopReasonDesc.convertFromDB(this.invoiceStopReasonDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceStopReasonDesc(String invoiceStopReasonDesc) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceStopReasonDesc;
            InvoiceStopReasonData data = this.getData();
            this.invoiceStopReasonDesc = InvoiceStopReasonColumns.invoiceStopReasonDesc.convertToDB(invoiceStopReasonDesc);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceStopReasonDesc", String.class, oldValue, this.invoiceStopReasonDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceStopReasonColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceStopReasonData data = this.getData();
            this.domainName = InvoiceStopReasonColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
