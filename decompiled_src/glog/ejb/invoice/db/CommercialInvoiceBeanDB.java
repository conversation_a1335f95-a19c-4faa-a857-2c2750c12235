/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.CommercialInvoiceColumns;
import glog.ejb.invoice.db.CommercialInvoiceData;
import glog.ejb.invoice.db.CommercialInvoicePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class CommercialInvoiceBeanDB
extends BeanManagedEntityBean {
    public Object commercialInvoiceGid;
    public Object commercialInvoiceXid;
    public Object commercialInvoiceType;
    public Object invoiceDate;
    public Object exchangeRate;
    public Object exchangeToCurrencyGid;
    public Object productAmount;
    public Object otherChargeAmount;
    public Object isCalculateProductAmount;
    public Object isCalculateTotalInvoiceAmt;
    public Object totalInvoiceAmount;
    public Object incoTermGid;
    public Object termLocationText;
    public Object finalIncoTermGid;
    public Object finalTextLocation;
    public Object commercialPaymentCodeGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object exchangeFromCurrencyGid;
    public Object isOverrideExchangeRate;
    public Object commercialInvoiceNumber;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient CommercialInvoicePK pk;
    protected transient CommercialInvoiceData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(CommercialInvoiceBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static CommercialInvoicePK.Callback theCall = new CommercialInvoicePK.Callback();

    public CommercialInvoiceBeanDB() {
        super(false);
    }

    public CommercialInvoicePK ejbCreate(CommercialInvoiceData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected CommercialInvoicePK newPK() throws GLException {
        return CommercialInvoicePK.newPK(this.getDomainName(), this.getCommercialInvoiceXid(), this.getConnection());
    }

    public void ejbPostCreate(CommercialInvoiceData data) throws CreateException {
        this.ejbPostCreator();
    }

    public CommercialInvoicePK ejbFindByPrimaryKey(CommercialInvoicePK pk) throws FinderException {
        return (CommercialInvoicePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(CommercialInvoicePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<CommercialInvoicePK> v = new Vector<CommercialInvoicePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(CommercialInvoicePK pk, CommercialInvoiceData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(CommercialInvoicePK pk, CommercialInvoiceData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(CommercialInvoicePK pk, CommercialInvoiceData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(CommercialInvoiceColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(CommercialInvoiceColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(CommercialInvoiceColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(CommercialInvoiceColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        CommercialInvoicePK pk = (CommercialInvoicePK)genericPk;
        this.commercialInvoiceGid = pk.commercialInvoiceGid;
        this.domainName = pk.domainName;
        this.commercialInvoiceXid = pk.commercialInvoiceXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        CommercialInvoicePK pk = new CommercialInvoicePK(0, this.commercialInvoiceGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.CommercialInvoice";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public CommercialInvoiceData getData() throws GLException {
        try {
            CommercialInvoiceData retval = new CommercialInvoiceData();
            retval.getFromBean(this, CommercialInvoiceColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(CommercialInvoiceData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(CommercialInvoiceData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            CommercialInvoiceData oldData = modified ? this.getData() : null;
            data.setToBean(this, CommercialInvoiceColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return CommercialInvoiceData.class;
    }

    @Override
    public Class getPkClass() {
        return CommercialInvoicePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getCommercialInvoiceGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.commercialInvoiceGid.convertFromDB(this.commercialInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvoiceGid(String commercialInvoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvoiceGid;
            CommercialInvoiceData data = this.getData();
            this.commercialInvoiceGid = CommercialInvoiceColumns.commercialInvoiceGid.convertToDB(commercialInvoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvoiceGid", String.class, oldValue, this.commercialInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialInvoiceXid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.commercialInvoiceXid.convertFromDB(this.commercialInvoiceXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvoiceXid(String commercialInvoiceXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvoiceXid;
            CommercialInvoiceData data = this.getData();
            this.commercialInvoiceXid = CommercialInvoiceColumns.commercialInvoiceXid.convertToDB(commercialInvoiceXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvoiceXid", String.class, oldValue, this.commercialInvoiceXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialInvoiceType() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.commercialInvoiceType.convertFromDB(this.commercialInvoiceType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvoiceType(String commercialInvoiceType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvoiceType;
            CommercialInvoiceData data = this.getData();
            this.commercialInvoiceType = CommercialInvoiceColumns.commercialInvoiceType.convertToDB(commercialInvoiceType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvoiceType", String.class, oldValue, this.commercialInvoiceType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getInvoiceDate() throws GLException {
        try {
            return (LocalDate)CommercialInvoiceColumns.invoiceDate.convertFromDB(this.invoiceDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceDate(LocalDate invoiceDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceDate;
            CommercialInvoiceData data = this.getData();
            this.invoiceDate = CommercialInvoiceColumns.invoiceDate.convertToDB(invoiceDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceDate", LocalDate.class, oldValue, this.invoiceDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getExchangeRate() throws GLException {
        try {
            return (Double)CommercialInvoiceColumns.exchangeRate.convertFromDB(this.exchangeRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRate(Double exchangeRate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRate;
            CommercialInvoiceData data = this.getData();
            this.exchangeRate = CommercialInvoiceColumns.exchangeRate.convertToDB(exchangeRate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRate", Double.class, oldValue, this.exchangeRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeToCurrencyGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.exchangeToCurrencyGid.convertFromDB(this.exchangeToCurrencyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeToCurrencyGid(String exchangeToCurrencyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeToCurrencyGid;
            CommercialInvoiceData data = this.getData();
            this.exchangeToCurrencyGid = CommercialInvoiceColumns.exchangeToCurrencyGid.convertToDB(exchangeToCurrencyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeToCurrencyGid", String.class, oldValue, this.exchangeToCurrencyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getProductAmount() throws GLException {
        try {
            return (Currency)CommercialInvoiceColumns.productAmount.convertFromDB(this.productAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setProductAmount(Currency productAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.productAmount;
            CommercialInvoiceData data = this.getData();
            this.productAmount = CommercialInvoiceColumns.productAmount.convertToDB(productAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "productAmount", Currency.class, oldValue, this.productAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getOtherChargeAmount() throws GLException {
        try {
            return (Currency)CommercialInvoiceColumns.otherChargeAmount.convertFromDB(this.otherChargeAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setOtherChargeAmount(Currency otherChargeAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.otherChargeAmount;
            CommercialInvoiceData data = this.getData();
            this.otherChargeAmount = CommercialInvoiceColumns.otherChargeAmount.convertToDB(otherChargeAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "otherChargeAmount", Currency.class, oldValue, this.otherChargeAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsCalculateProductAmount() throws GLException {
        try {
            return (Boolean)CommercialInvoiceColumns.isCalculateProductAmount.convertFromDB(this.isCalculateProductAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsCalculateProductAmount(Boolean isCalculateProductAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isCalculateProductAmount;
            CommercialInvoiceData data = this.getData();
            this.isCalculateProductAmount = CommercialInvoiceColumns.isCalculateProductAmount.convertToDB(isCalculateProductAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isCalculateProductAmount", Boolean.class, oldValue, this.isCalculateProductAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsCalculateTotalInvoiceAmt() throws GLException {
        try {
            return (Boolean)CommercialInvoiceColumns.isCalculateTotalInvoiceAmt.convertFromDB(this.isCalculateTotalInvoiceAmt);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsCalculateTotalInvoiceAmt(Boolean isCalculateTotalInvoiceAmt) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isCalculateTotalInvoiceAmt;
            CommercialInvoiceData data = this.getData();
            this.isCalculateTotalInvoiceAmt = CommercialInvoiceColumns.isCalculateTotalInvoiceAmt.convertToDB(isCalculateTotalInvoiceAmt);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isCalculateTotalInvoiceAmt", Boolean.class, oldValue, this.isCalculateTotalInvoiceAmt);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTotalInvoiceAmount() throws GLException {
        try {
            return (Currency)CommercialInvoiceColumns.totalInvoiceAmount.convertFromDB(this.totalInvoiceAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalInvoiceAmount(Currency totalInvoiceAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalInvoiceAmount;
            CommercialInvoiceData data = this.getData();
            this.totalInvoiceAmount = CommercialInvoiceColumns.totalInvoiceAmount.convertToDB(totalInvoiceAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalInvoiceAmount", Currency.class, oldValue, this.totalInvoiceAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getIncoTermGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.incoTermGid.convertFromDB(this.incoTermGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIncoTermGid(String incoTermGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.incoTermGid;
            CommercialInvoiceData data = this.getData();
            this.incoTermGid = CommercialInvoiceColumns.incoTermGid.convertToDB(incoTermGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "incoTermGid", String.class, oldValue, this.incoTermGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getTermLocationText() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.termLocationText.convertFromDB(this.termLocationText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTermLocationText(String termLocationText) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.termLocationText;
            CommercialInvoiceData data = this.getData();
            this.termLocationText = CommercialInvoiceColumns.termLocationText.convertToDB(termLocationText);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "termLocationText", String.class, oldValue, this.termLocationText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getFinalIncoTermGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.finalIncoTermGid.convertFromDB(this.finalIncoTermGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setFinalIncoTermGid(String finalIncoTermGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.finalIncoTermGid;
            CommercialInvoiceData data = this.getData();
            this.finalIncoTermGid = CommercialInvoiceColumns.finalIncoTermGid.convertToDB(finalIncoTermGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "finalIncoTermGid", String.class, oldValue, this.finalIncoTermGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getFinalTextLocation() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.finalTextLocation.convertFromDB(this.finalTextLocation);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setFinalTextLocation(String finalTextLocation) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.finalTextLocation;
            CommercialInvoiceData data = this.getData();
            this.finalTextLocation = CommercialInvoiceColumns.finalTextLocation.convertToDB(finalTextLocation);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "finalTextLocation", String.class, oldValue, this.finalTextLocation);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialPaymentCodeGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.commercialPaymentCodeGid.convertFromDB(this.commercialPaymentCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialPaymentCodeGid(String commercialPaymentCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialPaymentCodeGid;
            CommercialInvoiceData data = this.getData();
            this.commercialPaymentCodeGid = CommercialInvoiceColumns.commercialPaymentCodeGid.convertToDB(commercialPaymentCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialPaymentCodeGid", String.class, oldValue, this.commercialPaymentCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)CommercialInvoiceColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            CommercialInvoiceData data = this.getData();
            this.exchangeRateDate = CommercialInvoiceColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            CommercialInvoiceData data = this.getData();
            this.exchangeRateGid = CommercialInvoiceColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeFromCurrencyGid() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.exchangeFromCurrencyGid.convertFromDB(this.exchangeFromCurrencyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeFromCurrencyGid(String exchangeFromCurrencyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeFromCurrencyGid;
            CommercialInvoiceData data = this.getData();
            this.exchangeFromCurrencyGid = CommercialInvoiceColumns.exchangeFromCurrencyGid.convertToDB(exchangeFromCurrencyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeFromCurrencyGid", String.class, oldValue, this.exchangeFromCurrencyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsOverrideExchangeRate() throws GLException {
        try {
            return (Boolean)CommercialInvoiceColumns.isOverrideExchangeRate.convertFromDB(this.isOverrideExchangeRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsOverrideExchangeRate(Boolean isOverrideExchangeRate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isOverrideExchangeRate;
            CommercialInvoiceData data = this.getData();
            this.isOverrideExchangeRate = CommercialInvoiceColumns.isOverrideExchangeRate.convertToDB(isOverrideExchangeRate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isOverrideExchangeRate", Boolean.class, oldValue, this.isOverrideExchangeRate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialInvoiceNumber() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.commercialInvoiceNumber.convertFromDB(this.commercialInvoiceNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvoiceNumber(String commercialInvoiceNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvoiceNumber;
            CommercialInvoiceData data = this.getData();
            this.commercialInvoiceNumber = CommercialInvoiceColumns.commercialInvoiceNumber.convertToDB(commercialInvoiceNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvoiceNumber", String.class, oldValue, this.commercialInvoiceNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)CommercialInvoiceColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            CommercialInvoiceData data = this.getData();
            this.domainName = CommercialInvoiceColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
