/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceRemarkColumns;
import glog.ejb.invoice.db.InvoiceRemarkData;
import glog.ejb.invoice.db.InvoiceRemarkPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceRemarkBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object remarkSeqNo;
    public Object remarkQualIdentifier;
    public Object remarkText;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceRemarkPK pk;
    protected transient InvoiceRemarkData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRemarkBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceRemarkPK.Callback theCall = new InvoiceRemarkPK.Callback();

    public InvoiceRemarkBeanDB() {
        super(false);
    }

    public InvoiceRemarkPK ejbCreate(InvoiceRemarkData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceRemarkData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceRemarkPK ejbFindByPrimaryKey(InvoiceRemarkPK pk) throws FinderException {
        return (InvoiceRemarkPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceRemarkPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceRemarkPK> v = new Vector<InvoiceRemarkPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceRemarkPK pk, InvoiceRemarkData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceRemarkPK pk, InvoiceRemarkData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceRemarkPK pk, InvoiceRemarkData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceRemarkPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceRemarkColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceRemarkColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceRemarkColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceRemarkColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceRemarkPK pk = (InvoiceRemarkPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.remarkSeqNo = pk.remarkSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceRemarkPK pk = new InvoiceRemarkPK(0, this.invoiceGid, this.remarkSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceRemark";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceRemarkData getData() throws GLException {
        try {
            InvoiceRemarkData retval = new InvoiceRemarkData();
            retval.getFromBean(this, InvoiceRemarkColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceRemarkData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceRemarkData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceRemarkData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceRemarkColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceRemarkData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceRemarkPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceRemarkColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceRemarkData data = this.getData();
            this.invoiceGid = InvoiceRemarkColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getRemarkSeqNo() throws GLException {
        try {
            return (Integer)InvoiceRemarkColumns.remarkSeqNo.convertFromDB(this.remarkSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkSeqNo(Integer remarkSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkSeqNo;
            InvoiceRemarkData data = this.getData();
            this.remarkSeqNo = InvoiceRemarkColumns.remarkSeqNo.convertToDB(remarkSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkSeqNo", Integer.class, oldValue, this.remarkSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkQualIdentifier() throws GLException {
        try {
            return (String)InvoiceRemarkColumns.remarkQualIdentifier.convertFromDB(this.remarkQualIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkQualIdentifier(String remarkQualIdentifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkQualIdentifier;
            InvoiceRemarkData data = this.getData();
            this.remarkQualIdentifier = InvoiceRemarkColumns.remarkQualIdentifier.convertToDB(remarkQualIdentifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkQualIdentifier", String.class, oldValue, this.remarkQualIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkText() throws GLException {
        try {
            return (String)InvoiceRemarkColumns.remarkText.convertFromDB(this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkText(String remarkText) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkText;
            InvoiceRemarkData data = this.getData();
            this.remarkText = InvoiceRemarkColumns.remarkText.convertToDB(remarkText);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkText", String.class, oldValue, this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceRemarkColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceRemarkData data = this.getData();
            this.domainName = InvoiceRemarkColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
