/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlKeyComponentParamColumns;
import glog.ejb.invoice.db.GlKeyComponentParamData;
import glog.ejb.invoice.db.GlKeyComponentParamPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlKeyComponentParamBeanDB
extends BeanManagedEntityBean {
    public Object glKeyComponentGid;
    public Object sequence;
    public Object paramValue;
    public Object paramValueUomCode;
    public Object glKeyComponentTypeGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlKeyComponentParamPK pk;
    protected transient GlKeyComponentParamData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentParamBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlKeyComponentParamPK.Callback theCall = new GlKeyComponentParamPK.Callback();

    public GlKeyComponentParamBeanDB() {
        super(false);
    }

    public GlKeyComponentParamPK ejbCreate(GlKeyComponentParamData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(GlKeyComponentParamData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlKeyComponentParamPK ejbFindByPrimaryKey(GlKeyComponentParamPK pk) throws FinderException {
        return (GlKeyComponentParamPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlKeyComponentParamPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlKeyComponentParamPK> v = new Vector<GlKeyComponentParamPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlKeyComponentParamPK pk, GlKeyComponentParamData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlKeyComponentParamPK pk, GlKeyComponentParamData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlKeyComponentParamPK pk, GlKeyComponentParamData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (GlKeyComponentParamPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlKeyComponentParamColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlKeyComponentParamColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlKeyComponentParamColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlKeyComponentParamColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlKeyComponentParamPK pk = (GlKeyComponentParamPK)genericPk;
        this.glKeyComponentGid = pk.glKeyComponentGid;
        this.sequence = pk.sequence;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlKeyComponentParamPK pk = new GlKeyComponentParamPK(0, this.glKeyComponentGid, this.sequence, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlKeyComponentParam";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlKeyComponentParamData getData() throws GLException {
        try {
            GlKeyComponentParamData retval = new GlKeyComponentParamData();
            retval.getFromBean(this, GlKeyComponentParamColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlKeyComponentParamData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlKeyComponentParamData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlKeyComponentParamData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlKeyComponentParamColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlKeyComponentParamData.class;
    }

    @Override
    public Class getPkClass() {
        return GlKeyComponentParamPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlKeyComponentGid() throws GLException {
        try {
            return (String)GlKeyComponentParamColumns.glKeyComponentGid.convertFromDB(this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentGid(String glKeyComponentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentGid;
            GlKeyComponentParamData data = this.getData();
            this.glKeyComponentGid = GlKeyComponentParamColumns.glKeyComponentGid.convertToDB(glKeyComponentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentGid", String.class, oldValue, this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getSequence() throws GLException {
        try {
            return (Double)GlKeyComponentParamColumns.sequence.convertFromDB(this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSequence(Double sequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sequence;
            GlKeyComponentParamData data = this.getData();
            this.sequence = GlKeyComponentParamColumns.sequence.convertToDB(sequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sequence", Double.class, oldValue, this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getParamValue() throws GLException {
        try {
            return (String)GlKeyComponentParamColumns.paramValue.convertFromDB(this.paramValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setParamValue(String paramValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.paramValue;
            GlKeyComponentParamData data = this.getData();
            this.paramValue = GlKeyComponentParamColumns.paramValue.convertToDB(paramValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "paramValue", String.class, oldValue, this.paramValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getParamValueUomCode() throws GLException {
        try {
            return (String)GlKeyComponentParamColumns.paramValueUomCode.convertFromDB(this.paramValueUomCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setParamValueUomCode(String paramValueUomCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.paramValueUomCode;
            GlKeyComponentParamData data = this.getData();
            this.paramValueUomCode = GlKeyComponentParamColumns.paramValueUomCode.convertToDB(paramValueUomCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "paramValueUomCode", String.class, oldValue, this.paramValueUomCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlKeyComponentTypeGid() throws GLException {
        try {
            return (String)GlKeyComponentParamColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentTypeGid(String glKeyComponentTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentTypeGid;
            GlKeyComponentParamData data = this.getData();
            this.glKeyComponentTypeGid = GlKeyComponentParamColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentTypeGid", String.class, oldValue, this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlKeyComponentParamColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlKeyComponentParamData data = this.getData();
            this.domainName = GlKeyComponentParamColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
