/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GeneralLedgerCodeColumns;
import glog.ejb.invoice.db.GeneralLedgerCodeData;
import glog.ejb.invoice.db.GeneralLedgerCodePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GeneralLedgerCodeBeanDB
extends BeanManagedEntityBean {
    public Object generalLedgerGid;
    public Object generalLedgerXid;
    public Object description;
    public Object isActive;
    public Object buyOrderGlLookupKeyGid;
    public Object sellOrderGlLookupKeyGid;
    public Object buyShipGlLookupKeyGid;
    public Object sellShipGlLookupKeyGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GeneralLedgerCodePK pk;
    protected transient GeneralLedgerCodeData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GeneralLedgerCodeBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GeneralLedgerCodePK.Callback theCall = new GeneralLedgerCodePK.Callback();

    public GeneralLedgerCodeBeanDB() {
        super(false);
    }

    public GeneralLedgerCodePK ejbCreate(GeneralLedgerCodeData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected GeneralLedgerCodePK newPK() throws GLException {
        return GeneralLedgerCodePK.newPK(this.getDomainName(), this.getGeneralLedgerXid(), this.getConnection());
    }

    public void ejbPostCreate(GeneralLedgerCodeData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GeneralLedgerCodePK ejbFindByPrimaryKey(GeneralLedgerCodePK pk) throws FinderException {
        return (GeneralLedgerCodePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GeneralLedgerCodePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GeneralLedgerCodePK> v = new Vector<GeneralLedgerCodePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GeneralLedgerCodePK pk, GeneralLedgerCodeData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GeneralLedgerCodePK pk, GeneralLedgerCodeData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GeneralLedgerCodePK pk, GeneralLedgerCodeData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GeneralLedgerCodeColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GeneralLedgerCodeColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GeneralLedgerCodeColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GeneralLedgerCodeColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GeneralLedgerCodePK pk = (GeneralLedgerCodePK)genericPk;
        this.generalLedgerGid = pk.generalLedgerGid;
        this.domainName = pk.domainName;
        this.generalLedgerXid = pk.generalLedgerXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GeneralLedgerCodePK pk = new GeneralLedgerCodePK(0, this.generalLedgerGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GeneralLedgerCode";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GeneralLedgerCodeData getData() throws GLException {
        try {
            GeneralLedgerCodeData retval = new GeneralLedgerCodeData();
            retval.getFromBean(this, GeneralLedgerCodeColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GeneralLedgerCodeData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GeneralLedgerCodeData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GeneralLedgerCodeData oldData = modified ? this.getData() : null;
            data.setToBean(this, GeneralLedgerCodeColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GeneralLedgerCodeData.class;
    }

    @Override
    public Class getPkClass() {
        return GeneralLedgerCodePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGeneralLedgerGid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.generalLedgerGid.convertFromDB(this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGeneralLedgerGid(String generalLedgerGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.generalLedgerGid;
            GeneralLedgerCodeData data = this.getData();
            this.generalLedgerGid = GeneralLedgerCodeColumns.generalLedgerGid.convertToDB(generalLedgerGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "generalLedgerGid", String.class, oldValue, this.generalLedgerGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGeneralLedgerXid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.generalLedgerXid.convertFromDB(this.generalLedgerXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGeneralLedgerXid(String generalLedgerXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.generalLedgerXid;
            GeneralLedgerCodeData data = this.getData();
            this.generalLedgerXid = GeneralLedgerCodeColumns.generalLedgerXid.convertToDB(generalLedgerXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "generalLedgerXid", String.class, oldValue, this.generalLedgerXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            GeneralLedgerCodeData data = this.getData();
            this.description = GeneralLedgerCodeColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsActive() throws GLException {
        try {
            return (Boolean)GeneralLedgerCodeColumns.isActive.convertFromDB(this.isActive);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsActive(Boolean isActive) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isActive;
            GeneralLedgerCodeData data = this.getData();
            this.isActive = GeneralLedgerCodeColumns.isActive.convertToDB(isActive);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isActive", Boolean.class, oldValue, this.isActive);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getBuyOrderGlLookupKeyGid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.buyOrderGlLookupKeyGid.convertFromDB(this.buyOrderGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBuyOrderGlLookupKeyGid(String buyOrderGlLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.buyOrderGlLookupKeyGid;
            GeneralLedgerCodeData data = this.getData();
            this.buyOrderGlLookupKeyGid = GeneralLedgerCodeColumns.buyOrderGlLookupKeyGid.convertToDB(buyOrderGlLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "buyOrderGlLookupKeyGid", String.class, oldValue, this.buyOrderGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSellOrderGlLookupKeyGid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.sellOrderGlLookupKeyGid.convertFromDB(this.sellOrderGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSellOrderGlLookupKeyGid(String sellOrderGlLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sellOrderGlLookupKeyGid;
            GeneralLedgerCodeData data = this.getData();
            this.sellOrderGlLookupKeyGid = GeneralLedgerCodeColumns.sellOrderGlLookupKeyGid.convertToDB(sellOrderGlLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sellOrderGlLookupKeyGid", String.class, oldValue, this.sellOrderGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getBuyShipGlLookupKeyGid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.buyShipGlLookupKeyGid.convertFromDB(this.buyShipGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBuyShipGlLookupKeyGid(String buyShipGlLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.buyShipGlLookupKeyGid;
            GeneralLedgerCodeData data = this.getData();
            this.buyShipGlLookupKeyGid = GeneralLedgerCodeColumns.buyShipGlLookupKeyGid.convertToDB(buyShipGlLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "buyShipGlLookupKeyGid", String.class, oldValue, this.buyShipGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSellShipGlLookupKeyGid() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.sellShipGlLookupKeyGid.convertFromDB(this.sellShipGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSellShipGlLookupKeyGid(String sellShipGlLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sellShipGlLookupKeyGid;
            GeneralLedgerCodeData data = this.getData();
            this.sellShipGlLookupKeyGid = GeneralLedgerCodeColumns.sellShipGlLookupKeyGid.convertToDB(sellShipGlLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sellShipGlLookupKeyGid", String.class, oldValue, this.sellShipGlLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GeneralLedgerCodeColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GeneralLedgerCodeData data = this.getData();
            this.domainName = GeneralLedgerCodeColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
