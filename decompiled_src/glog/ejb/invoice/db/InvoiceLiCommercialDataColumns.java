/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class InvoiceLiCommercialDataColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineitemSeqNo = new SqlColumn(Integer.class, "lineitem_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn commercialDataSeqNo = new SqlColumn(Integer.class, "commercial_data_seq_no", 8, 0, 1, null, 2, null);
    public static SqlColumn unitPrice = new SqlColumn(Currency.class, "unit_price", 22, 2, 0, null, 3, "unit_price_base");
    public static SqlColumn unitPriceQualifier = new SqlColumn(String.class, "unit_price_qualifier", 2, 0, 0, null, 4, null);
    public static SqlColumn liTotalCommercialValue = new SqlColumn(Currency.class, "li_total_commercial_value", 22, 2, 0, null, 5, "li_total_base");
    public static SqlColumn unitCount = new SqlColumn(Long.class, "unit_count", 10, 0, 0, null, 6, null);
    public static SqlColumn packagingUnitGid = new SqlColumn(String.class, "packaging_unit_gid", 101, 0, 0, null, 7, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 8, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 9, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
}
