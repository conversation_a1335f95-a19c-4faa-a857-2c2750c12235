/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlKeyComponentTypeParamColumns;
import glog.ejb.invoice.db.GlKeyComponentTypeParamData;
import glog.ejb.invoice.db.GlKeyComponentTypeParamPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlKeyComponentTypeParamBeanDB
extends BeanManagedEntityBean {
    public Object glKeyComponentTypeGid;
    public Object sequence;
    public Object fieldLabel;
    public Object dataType;
    public Object uomType;
    public Object gidQueryClass;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlKeyComponentTypeParamPK pk;
    protected transient GlKeyComponentTypeParamData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentTypeParamBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlKeyComponentTypeParamPK.Callback theCall = new GlKeyComponentTypeParamPK.Callback();

    public GlKeyComponentTypeParamBeanDB() {
        super(false);
    }

    public GlKeyComponentTypeParamPK ejbCreate(GlKeyComponentTypeParamData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(GlKeyComponentTypeParamData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlKeyComponentTypeParamPK ejbFindByPrimaryKey(GlKeyComponentTypeParamPK pk) throws FinderException {
        return (GlKeyComponentTypeParamPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlKeyComponentTypeParamPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlKeyComponentTypeParamPK> v = new Vector<GlKeyComponentTypeParamPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlKeyComponentTypeParamPK pk, GlKeyComponentTypeParamData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlKeyComponentTypeParamPK pk, GlKeyComponentTypeParamData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlKeyComponentTypeParamPK pk, GlKeyComponentTypeParamData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (GlKeyComponentTypeParamPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlKeyComponentTypeParamColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlKeyComponentTypeParamColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlKeyComponentTypeParamColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlKeyComponentTypeParamColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlKeyComponentTypeParamPK pk = (GlKeyComponentTypeParamPK)genericPk;
        this.glKeyComponentTypeGid = pk.glKeyComponentTypeGid;
        this.sequence = pk.sequence;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlKeyComponentTypeParamPK pk = new GlKeyComponentTypeParamPK(0, this.glKeyComponentTypeGid, this.sequence, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlKeyComponentTypeParam";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlKeyComponentTypeParamData getData() throws GLException {
        try {
            GlKeyComponentTypeParamData retval = new GlKeyComponentTypeParamData();
            retval.getFromBean(this, GlKeyComponentTypeParamColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlKeyComponentTypeParamData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlKeyComponentTypeParamData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlKeyComponentTypeParamData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlKeyComponentTypeParamColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlKeyComponentTypeParamData.class;
    }

    @Override
    public Class getPkClass() {
        return GlKeyComponentTypeParamPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlKeyComponentTypeGid() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentTypeGid(String glKeyComponentTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentTypeGid;
            GlKeyComponentTypeParamData data = this.getData();
            this.glKeyComponentTypeGid = GlKeyComponentTypeParamColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentTypeGid", String.class, oldValue, this.glKeyComponentTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getSequence() throws GLException {
        try {
            return (Double)GlKeyComponentTypeParamColumns.sequence.convertFromDB(this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSequence(Double sequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sequence;
            GlKeyComponentTypeParamData data = this.getData();
            this.sequence = GlKeyComponentTypeParamColumns.sequence.convertToDB(sequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sequence", Double.class, oldValue, this.sequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getFieldLabel() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.fieldLabel.convertFromDB(this.fieldLabel);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setFieldLabel(String fieldLabel) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.fieldLabel;
            GlKeyComponentTypeParamData data = this.getData();
            this.fieldLabel = GlKeyComponentTypeParamColumns.fieldLabel.convertToDB(fieldLabel);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "fieldLabel", String.class, oldValue, this.fieldLabel);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDataType() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.dataType.convertFromDB(this.dataType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDataType(String dataType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.dataType;
            GlKeyComponentTypeParamData data = this.getData();
            this.dataType = GlKeyComponentTypeParamColumns.dataType.convertToDB(dataType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "dataType", String.class, oldValue, this.dataType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getUomType() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.uomType.convertFromDB(this.uomType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setUomType(String uomType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.uomType;
            GlKeyComponentTypeParamData data = this.getData();
            this.uomType = GlKeyComponentTypeParamColumns.uomType.convertToDB(uomType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "uomType", String.class, oldValue, this.uomType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGidQueryClass() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.gidQueryClass.convertFromDB(this.gidQueryClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGidQueryClass(String gidQueryClass) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.gidQueryClass;
            GlKeyComponentTypeParamData data = this.getData();
            this.gidQueryClass = GlKeyComponentTypeParamColumns.gidQueryClass.convertToDB(gidQueryClass);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "gidQueryClass", String.class, oldValue, this.gidQueryClass);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlKeyComponentTypeParamColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlKeyComponentTypeParamData data = this.getData();
            this.domainName = GlKeyComponentTypeParamColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
