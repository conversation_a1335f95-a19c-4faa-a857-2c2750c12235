/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GeneralLedgerCodeColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class GeneralLedgerCodePK
extends Pk {
    public Object generalLedgerGid;
    public transient Object generalLedgerXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public GeneralLedgerCodePK() {
    }

    public GeneralLedgerCodePK(String generalLedgerGid) {
        this.generalLedgerGid = this.notNull(GeneralLedgerCodeColumns.generalLedgerGid.convertToDB(generalLedgerGid), "generalLedgerGid");
    }

    public GeneralLedgerCodePK(String domainName, String generalLedgerXid) {
        this.domainName = domainName;
        this.generalLedgerXid = generalLedgerXid;
        this.generalLedgerGid = GeneralLedgerCodePK.concatForGid(domainName, generalLedgerXid);
    }

    public GeneralLedgerCodePK(int dummy, Object generalLedgerGid) {
        this(dummy, generalLedgerGid, null);
    }

    public GeneralLedgerCodePK(int dummy, Object generalLedgerGid, Object transaction) {
        this.generalLedgerGid = generalLedgerGid;
        this.transaction = transaction;
    }

    public GeneralLedgerCodePK(GeneralLedgerCodePK otherPk, Object transaction) {
        this.generalLedgerGid = otherPk.generalLedgerGid;
        this.generalLedgerXid = otherPk.generalLedgerXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.generalLedgerGid != null ? String.valueOf(this.generalLedgerGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.generalLedgerGid != null ? String.valueOf(this.generalLedgerGid) : "";
    }

    public static GeneralLedgerCodePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new GeneralLedgerCodePK(gids[0]) : null;
    }

    public static GeneralLedgerCodePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new GeneralLedgerCodePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.generalLedgerGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof GeneralLedgerCodePK)) {
            return false;
        }
        GeneralLedgerCodePK otherPk = (GeneralLedgerCodePK)other;
        return Functions.equals(otherPk.generalLedgerGid, this.generalLedgerGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.GeneralLedgerCodeHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.generalLedgerGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return GeneralLedgerCodeColumns.generalLedgerGid.convertFromDB(this.generalLedgerGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GeneralLedgerCodePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("GeneralLedgerCodePK requires a non-null XID to generate a GID");
            }
            GeneralLedgerCodePK generalLedgerCodePK = new GeneralLedgerCodePK(domain, xid);
            return generalLedgerCodePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GeneralLedgerCodePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            GeneralLedgerCodePK generalLedgerCodePK = GeneralLedgerCodePK.newPK(domainName, xid, connection);
            return generalLedgerCodePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return GeneralLedgerCodePK.class;
        }

        @Override
        public final String getEntity() {
            return "GeneralLedgerCode";
        }

        @Override
        public final String getTableName() {
            return "general_ledger_code";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"general_ledger_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            GeneralLedgerCodePK result = new GeneralLedgerCodePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
