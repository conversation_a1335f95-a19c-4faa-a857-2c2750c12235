/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineApproveToleranceDetailFKList.7
implements Fk {
    LineApproveToleranceDetailFKList.7() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineApproveToleranceDetailData";
    }

    @Override
    public String getPkField() {
        return "lineApproveToleranceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "LineApproveToleranceData";
    }

    @Override
    public String getFkDataField() {
        return "lineApproveToleranceGid";
    }
}
