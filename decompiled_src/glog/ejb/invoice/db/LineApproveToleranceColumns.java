/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalDate;
import glog.util.jdbc.SqlColumn;

public class LineApproveToleranceColumns {
    public static SqlColumn lineApproveToleranceGid = new SqlColumn(String.class, "line_approve_tolerance_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineApproveToleranceXid = new SqlColumn(String.class, "line_approve_tolerance_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn isInvoiceRefnumMatchAll = new SqlColumn(Boolean.class, "is_invoice_refnum_match_all", 1, 0, 1, Boolean.valueOf("true"), 2, null);
    public static SqlColumn isShipmentRefnumMatchAll = new SqlColumn(Boolean.class, "is_shipment_refnum_match_all", 1, 0, 1, Boolean.valueOf("true"), 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
    public static SqlColumn effectiveDate = new SqlColumn(LocalDate.class, "effective_date", 7, 0, 0, null, 5, null);
    public static SqlColumn expirationDate = new SqlColumn(LocalDate.class, "expiration_date", 7, 0, 0, null, 6, null);
}
