/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherStatusPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class VoucherStatusData
extends BeanData {
    public String voucherGid;
    public String statusTypeGid;
    public String statusValueGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherStatusData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field voucherGidField = beanDataFields[0];
    public static Field statusTypeGidField = beanDataFields[1];
    public static Field statusValueGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public VoucherStatusData() {
    }

    public VoucherStatusData(VoucherStatusData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getVoucherStatusPK();
    }

    @Legacy
    public VoucherStatusPK getVoucherStatusPK() {
        if (this.statusTypeGid == null) {
            return null;
        }
        if (this.voucherGid == null) {
            return null;
        }
        return new VoucherStatusPK(this.statusTypeGid, this.voucherGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setVoucherStatusPK((VoucherStatusPK)pk);
    }

    @Legacy
    public void setVoucherStatusPK(VoucherStatusPK pk) {
        this.statusTypeGid = (String)pk.getAppValue(0);
        this.voucherGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.VoucherStatusQueryGen";
    }

    public static VoucherStatusData load(Connection conn, VoucherStatusPK pk) throws GLException {
        return (VoucherStatusData)VoucherStatusData.load(conn, pk, VoucherStatusData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return VoucherStatusData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return VoucherStatusData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherStatusData.load(conn, whereClause, prepareArguments, fetchSize, VoucherStatusData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return VoucherStatusData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherStatusData.load(conn, fromWhere, alias, prepareArguments, fetchSize, VoucherStatusData.class);
    }
}
