/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CommercialInvoiceChargeData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface CommercialInvoiceChargeRemoteDB
extends EJBObject {
    public CommercialInvoiceChargeData getData() throws RemoteException, GLException;

    public void setData(CommercialInvoiceChargeData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getCommercialInvoiceGid() throws RemoteException, GLException;

    public void setCommercialInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getSequenceNo() throws RemoteException, GLException;

    public void setSequenceNo(Integer var1) throws RemoteException, GLException;

    public Currency getChargeAmount() throws RemoteException, GLException;

    public void setChargeAmount(Currency var1) throws RemoteException, GLException;

    public String getChargeActivity() throws RemoteException, GLException;

    public void setChargeActivity(String var1) throws RemoteException, GLException;

    public String getCommercialInvChargeCodeGid() throws RemoteException, GLException;

    public void setCommercialInvChargeCodeGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
