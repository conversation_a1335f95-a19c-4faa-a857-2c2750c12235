/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceInvolvedPartyColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceInvolvedPartyPK
extends Pk {
    public Object comMethodGid;
    public Object invoiceGid;
    public Object involvedPartyContactGid;
    public Object involvedPartyQualGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceInvolvedPartyPK() {
    }

    public InvoiceInvolvedPartyPK(String comMethodGid, String invoiceGid, String involvedPartyContactGid, String involvedPartyQualGid) {
        this.comMethodGid = this.notNull(InvoiceInvolvedPartyColumns.comMethodGid.convertToDB(comMethodGid), "comMethodGid");
        this.invoiceGid = this.notNull(InvoiceInvolvedPartyColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.involvedPartyContactGid = this.notNull(InvoiceInvolvedPartyColumns.involvedPartyContactGid.convertToDB(involvedPartyContactGid), "involvedPartyContactGid");
        this.involvedPartyQualGid = this.notNull(InvoiceInvolvedPartyColumns.involvedPartyQualGid.convertToDB(involvedPartyQualGid), "involvedPartyQualGid");
    }

    public InvoiceInvolvedPartyPK(int dummy, Object comMethodGid, Object invoiceGid, Object involvedPartyContactGid, Object involvedPartyQualGid) {
        this(dummy, comMethodGid, invoiceGid, involvedPartyContactGid, involvedPartyQualGid, null);
    }

    public InvoiceInvolvedPartyPK(int dummy, Object comMethodGid, Object invoiceGid, Object involvedPartyContactGid, Object involvedPartyQualGid, Object transaction) {
        this.comMethodGid = comMethodGid;
        this.invoiceGid = invoiceGid;
        this.involvedPartyContactGid = involvedPartyContactGid;
        this.involvedPartyQualGid = involvedPartyQualGid;
        this.transaction = transaction;
    }

    public InvoiceInvolvedPartyPK(InvoiceInvolvedPartyPK otherPk, Object transaction) {
        this.comMethodGid = otherPk.comMethodGid;
        this.invoiceGid = otherPk.invoiceGid;
        this.involvedPartyContactGid = otherPk.involvedPartyContactGid;
        this.involvedPartyQualGid = otherPk.involvedPartyQualGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.comMethodGid != null ? String.valueOf(this.comMethodGid) : "") + " " + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.involvedPartyContactGid != null ? String.valueOf(this.involvedPartyContactGid) : "") + " " + (this.involvedPartyQualGid != null ? String.valueOf(this.involvedPartyQualGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.comMethodGid != null ? String.valueOf(this.comMethodGid) : "") + "|" + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.involvedPartyContactGid != null ? String.valueOf(this.involvedPartyContactGid) : "") + "|" + (this.involvedPartyQualGid != null ? String.valueOf(this.involvedPartyQualGid) : "");
    }

    public static InvoiceInvolvedPartyPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceInvolvedPartyPK(gids[0], gids[1], gids[2], gids[3]) : null;
    }

    public static InvoiceInvolvedPartyPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceInvolvedPartyPK(gids[0], gids[1], gids[2], gids[3]) : null;
    }

    public int hashCode() {
        return this.comMethodGid.hashCode() + this.invoiceGid.hashCode() + this.involvedPartyContactGid.hashCode() + this.involvedPartyQualGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceInvolvedPartyPK)) {
            return false;
        }
        InvoiceInvolvedPartyPK otherPk = (InvoiceInvolvedPartyPK)other;
        return Functions.equals(otherPk.comMethodGid, this.comMethodGid) && Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.involvedPartyContactGid, this.involvedPartyContactGid) && Functions.equals(otherPk.involvedPartyQualGid, this.involvedPartyQualGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceInvolvedPartyHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.comMethodGid;
            }
            case 1: {
                return this.invoiceGid;
            }
            case 2: {
                return this.involvedPartyContactGid;
            }
            case 3: {
                return this.involvedPartyQualGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceInvolvedPartyColumns.comMethodGid.convertFromDB(this.comMethodGid);
            }
            case 1: {
                return InvoiceInvolvedPartyColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 2: {
                return InvoiceInvolvedPartyColumns.involvedPartyContactGid.convertFromDB(this.involvedPartyContactGid);
            }
            case 3: {
                return InvoiceInvolvedPartyColumns.involvedPartyQualGid.convertFromDB(this.involvedPartyQualGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceInvolvedPartyPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceInvolvedParty";
        }

        @Override
        public final String getTableName() {
            return "invoice_involved_party";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"com_method_gid", "invoice_gid", "involved_party_contact_gid", "involved_party_qual_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceInvolvedPartyPK result = new InvoiceInvolvedPartyPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), q.getObject(pkOffset + 3 + 1), transaction);
            return result;
        }
    }
}
