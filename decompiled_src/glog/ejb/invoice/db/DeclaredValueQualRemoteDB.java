/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.DeclaredValueQualData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface DeclaredValueQualRemoteDB
extends EJBObject {
    public DeclaredValueQualData getData() throws RemoteException, GLException;

    public void setData(DeclaredValueQualData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON><PERSON>Ex<PERSON>, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getDeclaredValueQualGid() throws RemoteException, GLException;

    public void setDeclaredValueQualGid(String var1) throws RemoteException, GLException;

    public String getDeclaredValueQualXid() throws RemoteException, GLException;

    public void setDeclaredValueQualXid(String var1) throws RemoteException, GLException;

    public String getDeclaredValueDesc() throws RemoteException, GLException;

    public void setDeclaredValueDesc(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
