/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceSummaryData
extends BeanData {
    public String invoiceGid;
    public Integer invoiceSummarySeqNo;
    public Currency freightCharge;
    public Currency prepaidAmount;
    public Currency commercialUnitPrice;
    public Long unitCount;
    public String transportHandlingUnitGid;
    public Weight weight;
    public Volume volume;
    public String weightQualifier;
    public Integer invoiceTotal;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public String invoiceServiceCodeGid;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceSummaryData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field invoiceSummarySeqNoField = beanDataFields[1];
    public static Field freightChargeField = beanDataFields[2];
    public static Field prepaidAmountField = beanDataFields[3];
    public static Field commercialUnitPriceField = beanDataFields[4];
    public static Field unitCountField = beanDataFields[5];
    public static Field transportHandlingUnitGidField = beanDataFields[6];
    public static Field weightField = beanDataFields[7];
    public static Field volumeField = beanDataFields[8];
    public static Field weightQualifierField = beanDataFields[9];
    public static Field invoiceTotalField = beanDataFields[10];
    public static Field exchangeRateDateField = beanDataFields[11];
    public static Field exchangeRateGidField = beanDataFields[12];
    public static Field domainNameField = beanDataFields[13];
    public static Field invoiceServiceCodeGidField = beanDataFields[14];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceSummaryData() {
    }

    public InvoiceSummaryData(InvoiceSummaryData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceSummaryPK();
    }

    @Legacy
    public InvoiceSummaryPK getInvoiceSummaryPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.invoiceSummarySeqNo == null) {
            return null;
        }
        return new InvoiceSummaryPK(this.invoiceGid, this.invoiceSummarySeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceSummaryPK((InvoiceSummaryPK)pk);
    }

    @Legacy
    public void setInvoiceSummaryPK(InvoiceSummaryPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.invoiceSummarySeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceSummaryQueryGen";
    }

    public static InvoiceSummaryData load(Connection conn, InvoiceSummaryPK pk) throws GLException {
        return (InvoiceSummaryData)InvoiceSummaryData.load(conn, pk, InvoiceSummaryData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceSummaryData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceSummaryData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceSummaryData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceSummaryData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceSummaryData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceSummaryData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceSummaryData.class);
    }
}
