/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleProfileDData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface AutoApproveRuleProfileDRemoteDB
extends EJBObject {
    public AutoApproveRuleProfileDData getData() throws RemoteException, GLException;

    public void setData(AutoApproveRuleProfileDData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getAutoApproveRuleProfileGid() throws RemoteException, GLException;

    public void setAutoApproveRuleProfileGid(String var1) throws RemoteException, GLException;

    public String getAutoApproveRuleGid() throws RemoteException, GLException;

    public void setAutoApproveRuleGid(String var1) throws RemoteException, GLException;

    public Integer getAutoApproveRuleSeqNo() throws RemoteException, GLException;

    public void setAutoApproveRuleSeqNo(Integer var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
