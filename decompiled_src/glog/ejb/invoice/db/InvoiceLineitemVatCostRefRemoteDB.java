/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemVatCostRefData;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceLineitemVatCostRefRemoteDB
extends EJBObject {
    public InvoiceLineitemVatCostRefData getData() throws RemoteException, GLException;

    public void setData(InvoiceLineitemVatCostRefData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLEx<PERSON>;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Integer var1) throws RemoteException, GLException;

    public Integer getVatSeqno() throws RemoteException, GLException;

    public void setVatSeqno(Integer var1) throws RemoteException, GLException;

    public String getCostReferenceGid() throws RemoteException, GLException;

    public void setCostReferenceGid(String var1) throws RemoteException, GLException;

    public String getShipmentCostQualGid() throws RemoteException, GLException;

    public void setShipmentCostQualGid(String var1) throws RemoteException, GLException;

    public Currency getVatBasisAmount() throws RemoteException, GLException;

    public void setVatBasisAmount(Currency var1) throws RemoteException, GLException;

    public Currency getVatAmount() throws RemoteException, GLException;

    public void setVatAmount(Currency var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
