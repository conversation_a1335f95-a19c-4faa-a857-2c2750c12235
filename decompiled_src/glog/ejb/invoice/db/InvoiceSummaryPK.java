/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceSummaryPK
extends Pk {
    public Object invoiceGid;
    public Object invoiceSummarySeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceSummaryPK() {
    }

    public InvoiceSummaryPK(String invoiceGid, Integer invoiceSummarySeqNo) {
        this.invoiceGid = this.notNull(InvoiceSummaryColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.invoiceSummarySeqNo = this.notNull(InvoiceSummaryColumns.invoiceSummarySeqNo.convertToDB(invoiceSummarySeqNo), "invoiceSummarySeqNo");
    }

    public InvoiceSummaryPK(int dummy, Object invoiceGid, Object invoiceSummarySeqNo) {
        this(dummy, invoiceGid, invoiceSummarySeqNo, null);
    }

    public InvoiceSummaryPK(int dummy, Object invoiceGid, Object invoiceSummarySeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.invoiceSummarySeqNo = invoiceSummarySeqNo;
        this.transaction = transaction;
    }

    public InvoiceSummaryPK(InvoiceSummaryPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.invoiceSummarySeqNo = otherPk.invoiceSummarySeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.invoiceSummarySeqNo != null ? String.valueOf(this.invoiceSummarySeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.invoiceSummarySeqNo != null ? String.valueOf(this.invoiceSummarySeqNo) : "");
    }

    public static InvoiceSummaryPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceSummaryPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public static InvoiceSummaryPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceSummaryPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.invoiceSummarySeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceSummaryPK)) {
            return false;
        }
        InvoiceSummaryPK otherPk = (InvoiceSummaryPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.invoiceSummarySeqNo, this.invoiceSummarySeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceSummaryHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.invoiceSummarySeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceSummaryColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceSummaryColumns.invoiceSummarySeqNo.convertFromDB(this.invoiceSummarySeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceSummaryPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceSummary";
        }

        @Override
        public final String getTableName() {
            return "invoice_summary";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "invoice_summary_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceSummaryPK result = new InvoiceSummaryPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
