/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherRemarkFKList.1
implements Fk {
    VoucherRemarkFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherRemarkData";
    }

    @Override
    public String getPkField() {
        return "voucherGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "VoucherData";
    }

    @Override
    public String getFkDataField() {
        return "voucherGid";
    }
}
