/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceNoteColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceNotePK
extends Pk {
    public Object invoiceGid;
    public Object invoiceNoteSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceNotePK() {
    }

    public InvoiceNotePK(String invoiceGid, Long invoiceNoteSeqNo) {
        this.invoiceGid = this.notNull(InvoiceNoteColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.invoiceNoteSeqNo = this.notNull(InvoiceNoteColumns.invoiceNoteSeqNo.convertToDB(invoiceNoteSeqNo), "invoiceNoteSeqNo");
    }

    public InvoiceNotePK(int dummy, Object invoiceGid, Object invoiceNoteSeqNo) {
        this(dummy, invoiceGid, invoiceNoteSeqNo, null);
    }

    public InvoiceNotePK(int dummy, Object invoiceGid, Object invoiceNoteSeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.invoiceNoteSeqNo = invoiceNoteSeqNo;
        this.transaction = transaction;
    }

    public InvoiceNotePK(InvoiceNotePK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.invoiceNoteSeqNo = otherPk.invoiceNoteSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.invoiceNoteSeqNo != null ? String.valueOf(this.invoiceNoteSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.invoiceNoteSeqNo != null ? String.valueOf(this.invoiceNoteSeqNo) : "");
    }

    public static InvoiceNotePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceNotePK(gids[0], Long.valueOf(gids[1])) : null;
    }

    public static InvoiceNotePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceNotePK(gids[0], Long.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.invoiceNoteSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceNotePK)) {
            return false;
        }
        InvoiceNotePK otherPk = (InvoiceNotePK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.invoiceNoteSeqNo, this.invoiceNoteSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceNoteHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.invoiceNoteSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceNoteColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceNoteColumns.invoiceNoteSeqNo.convertFromDB(this.invoiceNoteSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceNotePK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceNote";
        }

        @Override
        public final String getTableName() {
            return "invoice_note";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "invoice_note_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceNotePK result = new InvoiceNotePK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
