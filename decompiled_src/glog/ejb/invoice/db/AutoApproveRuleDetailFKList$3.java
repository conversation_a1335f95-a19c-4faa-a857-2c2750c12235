/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class AutoApproveRuleDetailFKList.3
implements Fk {
    AutoApproveRuleDetailFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AutoApproveRuleDetailData";
    }

    @Override
    public String getPkField() {
        return "autoApproveRuleGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "AutoApproveRuleData";
    }

    @Override
    public String getFkDataField() {
        return "autoApproveRuleGid";
    }
}
