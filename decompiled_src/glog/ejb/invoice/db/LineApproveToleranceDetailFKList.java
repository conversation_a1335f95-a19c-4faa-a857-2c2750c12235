/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class LineApproveToleranceDetailFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("allowCurrBelowCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "allowCurrBelowCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("generalLedgerGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "generalLedgerGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getFkDataField() {
                return "generalLedgerGid";
            }
        });
        fks.put("accessorialCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "accessorialCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.rates.db";
            }

            @Override
            public String getFkDataClass() {
                return "AccessorialCodeData";
            }

            @Override
            public String getFkDataField() {
                return "accessorialCodeGid";
            }
        });
        fks.put("costType", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "costType";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "CostTypeData";
            }

            @Override
            public String getFkDataField() {
                return "costTypeGid";
            }
        });
        fks.put("allowCurrAboveCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "allowCurrAboveCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("paymentMethodCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "paymentMethodCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "PaymentMethodCodeData";
            }

            @Override
            public String getFkDataField() {
                return "paymentMethodCodeGid";
            }
        });
        fks.put("lineApproveToleranceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineApproveToleranceDetailData";
            }

            @Override
            public String getPkField() {
                return "lineApproveToleranceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "LineApproveToleranceData";
            }

            @Override
            public String getFkDataField() {
                return "lineApproveToleranceGid";
            }
        });
    }
}
