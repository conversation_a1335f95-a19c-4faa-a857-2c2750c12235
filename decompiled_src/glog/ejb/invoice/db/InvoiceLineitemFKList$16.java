/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.16
implements Fk {
    InvoiceLineitemFKList.16() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "outOfTolReasonCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.diagprocessconfig.db";
    }

    @Override
    public String getFkDataClass() {
        return "DiagProcessFailureReasonData";
    }

    @Override
    public String getFkDataField() {
        return "reasonCodeGid";
    }
}
