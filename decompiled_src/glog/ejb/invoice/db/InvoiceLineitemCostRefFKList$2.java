/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemCostRefFKList.2
implements Fk {
    InvoiceLineitemCostRefFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemCostRefData";
    }

    @Override
    public String getPkField() {
        return "lineitemSeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getFkDataField() {
        return "lineitemSeqNo";
    }
}
