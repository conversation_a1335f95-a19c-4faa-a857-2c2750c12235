/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvoiceLineitemFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("prepaidAmountFnCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "prepaidAmountFnCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("generalLedgerGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "generalLedgerGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "GeneralLedgerCodeData";
            }

            @Override
            public String getFkDataField() {
                return "generalLedgerGid";
            }
        });
        fks.put("freightChargeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "freightChargeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("outOfTolAmtGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "outOfTolAmtGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("transportHandlingUnitGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "transportHandlingUnitGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.order.db";
            }

            @Override
            public String getFkDataClass() {
                return "ShipUnitSpecData";
            }

            @Override
            public String getFkDataField() {
                return "shipUnitSpecGid";
            }
        });
        fks.put("vatCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "vatCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.vat.db";
            }

            @Override
            public String getFkDataClass() {
                return "VatCodeData";
            }

            @Override
            public String getFkDataField() {
                return "vatCodeGid";
            }
        });
        fks.put("deviationAmountGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "deviationAmountGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("declaredValueFnCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "declaredValueFnCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("adjustmentReasonCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "adjustmentReasonCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "AdjustmentReasonData";
            }

            @Override
            public String getFkDataField() {
                return "adjustmentReasonGid";
            }
        });
        fks.put("freightChargeFnCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "freightChargeFnCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("freightRateVFnCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "freightRateVFnCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("prepaidAmountGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "prepaidAmountGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("accessorialCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "accessorialCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.rates.db";
            }

            @Override
            public String getFkDataClass() {
                return "AccessorialCodeData";
            }

            @Override
            public String getFkDataField() {
                return "accessorialCodeGid";
            }
        });
        fks.put("costTypeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "costTypeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "CostTypeData";
            }

            @Override
            public String getFkDataField() {
                return "costTypeGid";
            }
        });
        fks.put("declaredValueQualGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "declaredValueQualGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "DeclaredValueQualData";
            }

            @Override
            public String getFkDataField() {
                return "declaredValueQualGid";
            }
        });
        fks.put("outOfTolReasonCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "outOfTolReasonCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.diagprocessconfig.db";
            }

            @Override
            public String getFkDataClass() {
                return "DiagProcessFailureReasonData";
            }

            @Override
            public String getFkDataField() {
                return "reasonCodeGid";
            }
        });
        fks.put("invoiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "invoiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceGid";
            }
        });
        fks.put("exchangeRateGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "exchangeRateGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "ExchangeRateData";
            }

            @Override
            public String getFkDataField() {
                return "exchangeRateGid";
            }
        });
        fks.put("billableIndicatorGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "billableIndicatorGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "BillableIndicatorData";
            }

            @Override
            public String getFkDataField() {
                return "billableIndicatorGid";
            }
        });
        fks.put("paymentMethodCodeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "paymentMethodCodeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "PaymentMethodCodeData";
            }

            @Override
            public String getFkDataField() {
                return "paymentMethodCodeGid";
            }
        });
        fks.put("flexCommodityQualGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "flexCommodityQualGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.flexcommodityprofile.db";
            }

            @Override
            public String getFkDataClass() {
                return "FlexCommodityQualData";
            }

            @Override
            public String getFkDataField() {
                return "flexCommodityQualGid";
            }
        });
        fks.put("exportLicCountryCode3Gid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getPkField() {
                return "exportLicCountryCode3Gid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.location.db";
            }

            @Override
            public String getFkDataClass() {
                return "CountryCodeData";
            }

            @Override
            public String getFkDataField() {
                return "countryCode3Gid";
            }
        });
    }
}
