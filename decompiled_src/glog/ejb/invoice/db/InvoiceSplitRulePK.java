/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSplitRuleColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoiceSplitRulePK
extends Pk {
    public Object invoiceSplitRuleGid;
    public transient Object invoiceSplitRuleXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceSplitRulePK() {
    }

    public InvoiceSplitRulePK(String invoiceSplitRuleGid) {
        this.invoiceSplitRuleGid = this.notNull(InvoiceSplitRuleColumns.invoiceSplitRuleGid.convertToDB(invoiceSplitRuleGid), "invoiceSplitRuleGid");
    }

    public InvoiceSplitRulePK(String domainName, String invoiceSplitRuleXid) {
        this.domainName = domainName;
        this.invoiceSplitRuleXid = invoiceSplitRuleXid;
        this.invoiceSplitRuleGid = InvoiceSplitRulePK.concatForGid(domainName, invoiceSplitRuleXid);
    }

    public InvoiceSplitRulePK(int dummy, Object invoiceSplitRuleGid) {
        this(dummy, invoiceSplitRuleGid, null);
    }

    public InvoiceSplitRulePK(int dummy, Object invoiceSplitRuleGid, Object transaction) {
        this.invoiceSplitRuleGid = invoiceSplitRuleGid;
        this.transaction = transaction;
    }

    public InvoiceSplitRulePK(InvoiceSplitRulePK otherPk, Object transaction) {
        this.invoiceSplitRuleGid = otherPk.invoiceSplitRuleGid;
        this.invoiceSplitRuleXid = otherPk.invoiceSplitRuleXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceSplitRuleGid != null ? String.valueOf(this.invoiceSplitRuleGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceSplitRuleGid != null ? String.valueOf(this.invoiceSplitRuleGid) : "";
    }

    public static InvoiceSplitRulePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceSplitRulePK(gids[0]) : null;
    }

    public static InvoiceSplitRulePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceSplitRulePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceSplitRuleGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceSplitRulePK)) {
            return false;
        }
        InvoiceSplitRulePK otherPk = (InvoiceSplitRulePK)other;
        return Functions.equals(otherPk.invoiceSplitRuleGid, this.invoiceSplitRuleGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceSplitRuleHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceSplitRuleGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceSplitRuleColumns.invoiceSplitRuleGid.convertFromDB(this.invoiceSplitRuleGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceSplitRulePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoiceSplitRulePK requires a non-null XID to generate a GID");
            }
            InvoiceSplitRulePK invoiceSplitRulePK = new InvoiceSplitRulePK(domain, xid);
            return invoiceSplitRulePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceSplitRulePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoiceSplitRulePK invoiceSplitRulePK = InvoiceSplitRulePK.newPK(domainName, xid, connection);
            return invoiceSplitRulePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceSplitRulePK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceSplitRule";
        }

        @Override
        public final String getTableName() {
            return "invoice_split_rule";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_split_rule_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceSplitRulePK result = new InvoiceSplitRulePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
