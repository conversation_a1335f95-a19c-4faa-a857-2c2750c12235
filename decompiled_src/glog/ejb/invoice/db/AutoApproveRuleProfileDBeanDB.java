/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.AutoApproveRuleProfileDColumns;
import glog.ejb.invoice.db.AutoApproveRuleProfileDData;
import glog.ejb.invoice.db.AutoApproveRuleProfileDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AutoApproveRuleProfileDBeanDB
extends BeanManagedEntityBean {
    public Object autoApproveRuleProfileGid;
    public Object autoApproveRuleGid;
    public Object autoApproveRuleSeqNo;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AutoApproveRuleProfileDPK pk;
    protected transient AutoApproveRuleProfileDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleProfileDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AutoApproveRuleProfileDPK.Callback theCall = new AutoApproveRuleProfileDPK.Callback();

    public AutoApproveRuleProfileDBeanDB() {
        super(false);
    }

    public AutoApproveRuleProfileDPK ejbCreate(AutoApproveRuleProfileDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AutoApproveRuleProfileDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AutoApproveRuleProfileDPK ejbFindByPrimaryKey(AutoApproveRuleProfileDPK pk) throws FinderException {
        return (AutoApproveRuleProfileDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AutoApproveRuleProfileDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AutoApproveRuleProfileDPK> v = new Vector<AutoApproveRuleProfileDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AutoApproveRuleProfileDPK pk, AutoApproveRuleProfileDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AutoApproveRuleProfileDPK pk, AutoApproveRuleProfileDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AutoApproveRuleProfileDPK pk, AutoApproveRuleProfileDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AutoApproveRuleProfileDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AutoApproveRuleProfileDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AutoApproveRuleProfileDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AutoApproveRuleProfileDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AutoApproveRuleProfileDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AutoApproveRuleProfileDPK pk = (AutoApproveRuleProfileDPK)genericPk;
        this.autoApproveRuleGid = pk.autoApproveRuleGid;
        this.autoApproveRuleProfileGid = pk.autoApproveRuleProfileGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AutoApproveRuleProfileDPK pk = new AutoApproveRuleProfileDPK(0, this.autoApproveRuleGid, this.autoApproveRuleProfileGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AutoApproveRuleProfileD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AutoApproveRuleProfileDData getData() throws GLException {
        try {
            AutoApproveRuleProfileDData retval = new AutoApproveRuleProfileDData();
            retval.getFromBean(this, AutoApproveRuleProfileDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AutoApproveRuleProfileDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AutoApproveRuleProfileDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AutoApproveRuleProfileDData oldData = modified ? this.getData() : null;
            data.setToBean(this, AutoApproveRuleProfileDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AutoApproveRuleProfileDData.class;
    }

    @Override
    public Class getPkClass() {
        return AutoApproveRuleProfileDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAutoApproveRuleProfileGid() throws GLException {
        try {
            return (String)AutoApproveRuleProfileDColumns.autoApproveRuleProfileGid.convertFromDB(this.autoApproveRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleProfileGid(String autoApproveRuleProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleProfileGid;
            AutoApproveRuleProfileDData data = this.getData();
            this.autoApproveRuleProfileGid = AutoApproveRuleProfileDColumns.autoApproveRuleProfileGid.convertToDB(autoApproveRuleProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleProfileGid", String.class, oldValue, this.autoApproveRuleProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAutoApproveRuleGid() throws GLException {
        try {
            return (String)AutoApproveRuleProfileDColumns.autoApproveRuleGid.convertFromDB(this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleGid(String autoApproveRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleGid;
            AutoApproveRuleProfileDData data = this.getData();
            this.autoApproveRuleGid = AutoApproveRuleProfileDColumns.autoApproveRuleGid.convertToDB(autoApproveRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleGid", String.class, oldValue, this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getAutoApproveRuleSeqNo() throws GLException {
        try {
            return (Integer)AutoApproveRuleProfileDColumns.autoApproveRuleSeqNo.convertFromDB(this.autoApproveRuleSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleSeqNo(Integer autoApproveRuleSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleSeqNo;
            AutoApproveRuleProfileDData data = this.getData();
            this.autoApproveRuleSeqNo = AutoApproveRuleProfileDColumns.autoApproveRuleSeqNo.convertToDB(autoApproveRuleSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleSeqNo", Integer.class, oldValue, this.autoApproveRuleSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AutoApproveRuleProfileDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AutoApproveRuleProfileDData data = this.getData();
            this.domainName = AutoApproveRuleProfileDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
