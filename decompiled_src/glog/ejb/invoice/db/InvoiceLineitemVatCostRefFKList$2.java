/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemVatCostRefFKList.2
implements Fk {
    InvoiceLineitemVatCostRefFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemVatCostRefData";
    }

    @Override
    public String getPkField() {
        return "shipmentCostQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.shipment.db";
    }

    @Override
    public String getFkDataClass() {
        return "ShipmentCostQualData";
    }

    @Override
    public String getFkDataField() {
        return "shipmentCostQualGid";
    }
}
