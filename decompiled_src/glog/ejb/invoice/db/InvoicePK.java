/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoicePK
extends Pk {
    public Object invoiceGid;
    public transient Object invoiceXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoicePK() {
    }

    public InvoicePK(String invoiceGid) {
        this.invoiceGid = this.notNull(InvoiceColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
    }

    public InvoicePK(String domainName, String invoiceXid) {
        this.domainName = domainName;
        this.invoiceXid = invoiceXid;
        this.invoiceGid = InvoicePK.concatForGid(domainName, invoiceXid);
    }

    public InvoicePK(int dummy, Object invoiceGid) {
        this(dummy, invoiceGid, null);
    }

    public InvoicePK(int dummy, Object invoiceGid, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.transaction = transaction;
    }

    public InvoicePK(InvoicePK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.invoiceXid = otherPk.invoiceXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "";
    }

    public static InvoicePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoicePK(gids[0]) : null;
    }

    public static InvoicePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoicePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoicePK)) {
            return false;
        }
        InvoicePK otherPk = (InvoicePK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoicePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoicePK requires a non-null XID to generate a GID");
            }
            InvoicePK invoicePK = new InvoicePK(domain, xid);
            return invoicePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoicePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoicePK invoicePK = InvoicePK.newPK(domainName, xid, connection);
            return invoicePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoicePK.class;
        }

        @Override
        public final String getEntity() {
            return "Invoice";
        }

        @Override
        public final String getTableName() {
            return "invoice";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoicePK result = new InvoicePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
