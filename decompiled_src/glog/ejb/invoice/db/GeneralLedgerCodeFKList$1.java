/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class GeneralLedgerCodeFKList.1
implements Fk {
    GeneralLedgerCodeFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "GeneralLedgerCodeData";
    }

    @Override
    public String getPkField() {
        return "sellShipGlLookupKeyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "GlLookupKeyData";
    }

    @Override
    public String getFkDataField() {
        return "glLookupKeyGid";
    }
}
