/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlCodeMapData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface GlCodeMapRemoteDB
extends EJBObject {
    public GlCodeMapData getData() throws RemoteException, GLException;

    public void setData(GlCodeMapData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getKeyValue() throws RemoteException, GLException;

    public void setKeyValue(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getGlLookupKeyGid() throws RemoteException, GLException;

    public void setGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public String getGeneralLedgerGid() throws RemoteException, GLException;

    public void setGeneralLedgerGid(String var1) throws RemoteException, GLException;
}
