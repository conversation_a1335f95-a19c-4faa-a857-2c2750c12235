/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherRemarkColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class VoucherRemarkPK
extends Pk {
    public Object remarkSequence;
    public Object voucherGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public VoucherRemarkPK() {
    }

    public VoucherRemarkPK(Long remarkSequence, String voucherGid) {
        this.remarkSequence = this.notNull(VoucherRemarkColumns.remarkSequence.convertToDB(remarkSequence), "remarkSequence");
        this.voucherGid = this.notNull(VoucherRemarkColumns.voucherGid.convertToDB(voucherGid), "voucherGid");
    }

    public VoucherRemarkPK(int dummy, Object remarkSequence, Object voucherGid) {
        this(dummy, remarkSequence, voucherGid, null);
    }

    public VoucherRemarkPK(int dummy, Object remarkSequence, Object voucherGid, Object transaction) {
        this.remarkSequence = remarkSequence;
        this.voucherGid = voucherGid;
        this.transaction = transaction;
    }

    public VoucherRemarkPK(VoucherRemarkPK otherPk, Object transaction) {
        this.remarkSequence = otherPk.remarkSequence;
        this.voucherGid = otherPk.voucherGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.remarkSequence != null ? String.valueOf(this.remarkSequence) : "") + " " + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.remarkSequence != null ? String.valueOf(this.remarkSequence) : "") + "|" + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    public static VoucherRemarkPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new VoucherRemarkPK(Long.valueOf(gids[0]), gids[1]) : null;
    }

    public static VoucherRemarkPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new VoucherRemarkPK(Long.valueOf(gids[0]), gids[1]) : null;
    }

    public int hashCode() {
        return this.remarkSequence.hashCode() + this.voucherGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof VoucherRemarkPK)) {
            return false;
        }
        VoucherRemarkPK otherPk = (VoucherRemarkPK)other;
        return Functions.equals(otherPk.remarkSequence, this.remarkSequence) && Functions.equals(otherPk.voucherGid, this.voucherGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.VoucherRemarkHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.remarkSequence;
            }
            case 1: {
                return this.voucherGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return VoucherRemarkColumns.remarkSequence.convertFromDB(this.remarkSequence);
            }
            case 1: {
                return VoucherRemarkColumns.voucherGid.convertFromDB(this.voucherGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return VoucherRemarkPK.class;
        }

        @Override
        public final String getEntity() {
            return "VoucherRemark";
        }

        @Override
        public final String getTableName() {
            return "voucher_remark";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"remark_sequence", "voucher_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            VoucherRemarkPK result = new VoucherRemarkPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
