/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.21
implements Fk {
    InvoiceLineitemFKList.21() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "flexCommodityQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.flexcommodityprofile.db";
    }

    @Override
    public String getFkDataClass() {
        return "FlexCommodityQualData";
    }

    @Override
    public String getFkDataField() {
        return "flexCommodityQualGid";
    }
}
