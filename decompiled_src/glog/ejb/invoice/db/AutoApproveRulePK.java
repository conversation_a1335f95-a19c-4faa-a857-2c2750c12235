/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class AutoApproveRulePK
extends Pk {
    public Object autoApproveRuleGid;
    public transient Object autoApproveRuleXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AutoApproveRulePK() {
    }

    public AutoApproveRulePK(String autoApproveRuleGid) {
        this.autoApproveRuleGid = this.notNull(AutoApproveRuleColumns.autoApproveRuleGid.convertToDB(autoApproveRuleGid), "autoApproveRuleGid");
    }

    public AutoApproveRulePK(String domainName, String autoApproveRuleXid) {
        this.domainName = domainName;
        this.autoApproveRuleXid = autoApproveRuleXid;
        this.autoApproveRuleGid = AutoApproveRulePK.concatForGid(domainName, autoApproveRuleXid);
    }

    public AutoApproveRulePK(int dummy, Object autoApproveRuleGid) {
        this(dummy, autoApproveRuleGid, null);
    }

    public AutoApproveRulePK(int dummy, Object autoApproveRuleGid, Object transaction) {
        this.autoApproveRuleGid = autoApproveRuleGid;
        this.transaction = transaction;
    }

    public AutoApproveRulePK(AutoApproveRulePK otherPk, Object transaction) {
        this.autoApproveRuleGid = otherPk.autoApproveRuleGid;
        this.autoApproveRuleXid = otherPk.autoApproveRuleXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.autoApproveRuleGid != null ? String.valueOf(this.autoApproveRuleGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.autoApproveRuleGid != null ? String.valueOf(this.autoApproveRuleGid) : "";
    }

    public static AutoApproveRulePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AutoApproveRulePK(gids[0]) : null;
    }

    public static AutoApproveRulePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AutoApproveRulePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.autoApproveRuleGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AutoApproveRulePK)) {
            return false;
        }
        AutoApproveRulePK otherPk = (AutoApproveRulePK)other;
        return Functions.equals(otherPk.autoApproveRuleGid, this.autoApproveRuleGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.AutoApproveRuleHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.autoApproveRuleGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AutoApproveRuleColumns.autoApproveRuleGid.convertFromDB(this.autoApproveRuleGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AutoApproveRulePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("AutoApproveRulePK requires a non-null XID to generate a GID");
            }
            AutoApproveRulePK autoApproveRulePK = new AutoApproveRulePK(domain, xid);
            return autoApproveRulePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static AutoApproveRulePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            AutoApproveRulePK autoApproveRulePK = AutoApproveRulePK.newPK(domainName, xid, connection);
            return autoApproveRulePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AutoApproveRulePK.class;
        }

        @Override
        public final String getEntity() {
            return "AutoApproveRule";
        }

        @Override
        public final String getTableName() {
            return "auto_approve_rule";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"auto_approve_rule_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AutoApproveRulePK result = new AutoApproveRulePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
