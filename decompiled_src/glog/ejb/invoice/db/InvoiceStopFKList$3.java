/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceStopFKList.3
implements Fk {
    InvoiceStopFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceStopData";
    }

    @Override
    public String getPkField() {
        return "invoiceStopReasonGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceStopReasonData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceStopReasonGid";
    }
}
