/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceProtectiveServicePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Temperature;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceProtectiveServiceData
extends BeanData {
    public String invoiceGid;
    public Integer protSrvSeqNo;
    public String protSrvCode;
    public String protSrvRuleCode;
    public Temperature protSrvTemp;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceProtectiveServiceData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field protSrvSeqNoField = beanDataFields[1];
    public static Field protSrvCodeField = beanDataFields[2];
    public static Field protSrvRuleCodeField = beanDataFields[3];
    public static Field protSrvTempField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceProtectiveServiceData() {
    }

    public InvoiceProtectiveServiceData(InvoiceProtectiveServiceData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceProtectiveServicePK();
    }

    @Legacy
    public InvoiceProtectiveServicePK getInvoiceProtectiveServicePK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.protSrvSeqNo == null) {
            return null;
        }
        return new InvoiceProtectiveServicePK(this.invoiceGid, this.protSrvSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceProtectiveServicePK((InvoiceProtectiveServicePK)pk);
    }

    @Legacy
    public void setInvoiceProtectiveServicePK(InvoiceProtectiveServicePK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.protSrvSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceProtectiveServiceQueryGen";
    }

    public static InvoiceProtectiveServiceData load(Connection conn, InvoiceProtectiveServicePK pk) throws GLException {
        return (InvoiceProtectiveServiceData)InvoiceProtectiveServiceData.load(conn, pk, InvoiceProtectiveServiceData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceProtectiveServiceData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceProtectiveServiceData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceProtectiveServiceData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceProtectiveServiceData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceProtectiveServiceData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceProtectiveServiceData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceProtectiveServiceData.class);
    }
}
