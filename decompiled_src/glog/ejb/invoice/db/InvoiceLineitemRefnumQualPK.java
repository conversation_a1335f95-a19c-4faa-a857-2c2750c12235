/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemRefnumQualColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoiceLineitemRefnumQualPK
extends Pk {
    public Object invoiceLiRefnumQualGid;
    public transient Object invoiceLiRefnumQualXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLineitemRefnumQualPK() {
    }

    public InvoiceLineitemRefnumQualPK(String invoiceLiRefnumQualGid) {
        this.invoiceLiRefnumQualGid = this.notNull(InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualGid.convertToDB(invoiceLiRefnumQualGid), "invoiceLiRefnumQualGid");
    }

    public InvoiceLineitemRefnumQualPK(String domainName, String invoiceLiRefnumQualXid) {
        this.domainName = domainName;
        this.invoiceLiRefnumQualXid = invoiceLiRefnumQualXid;
        this.invoiceLiRefnumQualGid = InvoiceLineitemRefnumQualPK.concatForGid(domainName, invoiceLiRefnumQualXid);
    }

    public InvoiceLineitemRefnumQualPK(int dummy, Object invoiceLiRefnumQualGid) {
        this(dummy, invoiceLiRefnumQualGid, null);
    }

    public InvoiceLineitemRefnumQualPK(int dummy, Object invoiceLiRefnumQualGid, Object transaction) {
        this.invoiceLiRefnumQualGid = invoiceLiRefnumQualGid;
        this.transaction = transaction;
    }

    public InvoiceLineitemRefnumQualPK(InvoiceLineitemRefnumQualPK otherPk, Object transaction) {
        this.invoiceLiRefnumQualGid = otherPk.invoiceLiRefnumQualGid;
        this.invoiceLiRefnumQualXid = otherPk.invoiceLiRefnumQualXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceLiRefnumQualGid != null ? String.valueOf(this.invoiceLiRefnumQualGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceLiRefnumQualGid != null ? String.valueOf(this.invoiceLiRefnumQualGid) : "";
    }

    public static InvoiceLineitemRefnumQualPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLineitemRefnumQualPK(gids[0]) : null;
    }

    public static InvoiceLineitemRefnumQualPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLineitemRefnumQualPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceLiRefnumQualGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLineitemRefnumQualPK)) {
            return false;
        }
        InvoiceLineitemRefnumQualPK otherPk = (InvoiceLineitemRefnumQualPK)other;
        return Functions.equals(otherPk.invoiceLiRefnumQualGid, this.invoiceLiRefnumQualGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLineitemRefnumQualHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceLiRefnumQualGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualGid.convertFromDB(this.invoiceLiRefnumQualGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceLineitemRefnumQualPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoiceLineitemRefnumQualPK requires a non-null XID to generate a GID");
            }
            InvoiceLineitemRefnumQualPK invoiceLineitemRefnumQualPK = new InvoiceLineitemRefnumQualPK(domain, xid);
            return invoiceLineitemRefnumQualPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceLineitemRefnumQualPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoiceLineitemRefnumQualPK invoiceLineitemRefnumQualPK = InvoiceLineitemRefnumQualPK.newPK(domainName, xid, connection);
            return invoiceLineitemRefnumQualPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLineitemRefnumQualPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLineitemRefnumQual";
        }

        @Override
        public final String getTableName() {
            return "invoice_lineitem_refnum_qual";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_li_refnum_qual_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLineitemRefnumQualPK result = new InvoiceLineitemRefnumQualPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
