/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.14
implements Fk {
    InvoiceLineitemFKList.14() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "costTypeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "CostTypeData";
    }

    @Override
    public String getFkDataField() {
        return "costTypeGid";
    }
}
