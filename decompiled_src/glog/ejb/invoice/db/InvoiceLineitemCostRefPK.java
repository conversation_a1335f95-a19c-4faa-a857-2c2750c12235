/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemCostRefColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceLineitemCostRefPK
extends Pk {
    public Object costReferenceGid;
    public Object invoiceGid;
    public Object lineitemSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLineitemCostRefPK() {
    }

    public InvoiceLineitemCostRefPK(String costReferenceGid, String invoiceGid, Integer lineitemSeqNo) {
        this.costReferenceGid = this.notNull(InvoiceLineitemCostRefColumns.costReferenceGid.convertToDB(costReferenceGid), "costReferenceGid");
        this.invoiceGid = this.notNull(InvoiceLineitemCostRefColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.lineitemSeqNo = this.notNull(InvoiceLineitemCostRefColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
    }

    public InvoiceLineitemCostRefPK(int dummy, Object costReferenceGid, Object invoiceGid, Object lineitemSeqNo) {
        this(dummy, costReferenceGid, invoiceGid, lineitemSeqNo, null);
    }

    public InvoiceLineitemCostRefPK(int dummy, Object costReferenceGid, Object invoiceGid, Object lineitemSeqNo, Object transaction) {
        this.costReferenceGid = costReferenceGid;
        this.invoiceGid = invoiceGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.transaction = transaction;
    }

    public InvoiceLineitemCostRefPK(InvoiceLineitemCostRefPK otherPk, Object transaction) {
        this.costReferenceGid = otherPk.costReferenceGid;
        this.invoiceGid = otherPk.invoiceGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.costReferenceGid != null ? String.valueOf(this.costReferenceGid) : "") + " " + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.costReferenceGid != null ? String.valueOf(this.costReferenceGid) : "") + "|" + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    public static InvoiceLineitemCostRefPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLineitemCostRefPK(gids[0], gids[1], Integer.valueOf(gids[2])) : null;
    }

    public static InvoiceLineitemCostRefPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLineitemCostRefPK(gids[0], gids[1], Integer.valueOf(gids[2])) : null;
    }

    public int hashCode() {
        return this.costReferenceGid.hashCode() + this.invoiceGid.hashCode() + this.lineitemSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLineitemCostRefPK)) {
            return false;
        }
        InvoiceLineitemCostRefPK otherPk = (InvoiceLineitemCostRefPK)other;
        return Functions.equals(otherPk.costReferenceGid, this.costReferenceGid) && Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLineitemCostRefHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.costReferenceGid;
            }
            case 1: {
                return this.invoiceGid;
            }
            case 2: {
                return this.lineitemSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLineitemCostRefColumns.costReferenceGid.convertFromDB(this.costReferenceGid);
            }
            case 1: {
                return InvoiceLineitemCostRefColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 2: {
                return InvoiceLineitemCostRefColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLineitemCostRefPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLineitemCostRef";
        }

        @Override
        public final String getTableName() {
            return "invoice_lineitem_cost_ref";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"cost_reference_gid", "invoice_gid", "lineitem_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLineitemCostRefPK result = new InvoiceLineitemCostRefPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
