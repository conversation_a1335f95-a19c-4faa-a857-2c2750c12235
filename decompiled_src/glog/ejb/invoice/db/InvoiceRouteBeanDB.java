/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceRouteColumns;
import glog.ejb.invoice.db.InvoiceRouteData;
import glog.ejb.invoice.db.InvoiceRoutePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceRouteBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object routeSeqNo;
    public Object transportModeIdentifier;
    public Object servprovAliasQualGid;
    public Object servprovAliasValue;
    public Object intermodalServiceCode;
    public Object jctCityCode;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceRoutePK pk;
    protected transient InvoiceRouteData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRouteBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceRoutePK.Callback theCall = new InvoiceRoutePK.Callback();

    public InvoiceRouteBeanDB() {
        super(false);
    }

    public InvoiceRoutePK ejbCreate(InvoiceRouteData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceRouteData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceRoutePK ejbFindByPrimaryKey(InvoiceRoutePK pk) throws FinderException {
        return (InvoiceRoutePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceRoutePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceRoutePK> v = new Vector<InvoiceRoutePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceRoutePK pk, InvoiceRouteData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceRoutePK pk, InvoiceRouteData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceRoutePK pk, InvoiceRouteData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceRoutePK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceRouteColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceRouteColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceRouteColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceRouteColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceRoutePK pk = (InvoiceRoutePK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.routeSeqNo = pk.routeSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceRoutePK pk = new InvoiceRoutePK(0, this.invoiceGid, this.routeSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceRoute";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceRouteData getData() throws GLException {
        try {
            InvoiceRouteData retval = new InvoiceRouteData();
            retval.getFromBean(this, InvoiceRouteColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceRouteData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceRouteData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceRouteData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceRouteColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceRouteData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceRoutePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceRouteColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceRouteData data = this.getData();
            this.invoiceGid = InvoiceRouteColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getRouteSeqNo() throws GLException {
        try {
            return (Integer)InvoiceRouteColumns.routeSeqNo.convertFromDB(this.routeSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRouteSeqNo(Integer routeSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.routeSeqNo;
            InvoiceRouteData data = this.getData();
            this.routeSeqNo = InvoiceRouteColumns.routeSeqNo.convertToDB(routeSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "routeSeqNo", Integer.class, oldValue, this.routeSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getTransportModeIdentifier() throws GLException {
        try {
            return (String)InvoiceRouteColumns.transportModeIdentifier.convertFromDB(this.transportModeIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTransportModeIdentifier(String transportModeIdentifier) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.transportModeIdentifier;
            InvoiceRouteData data = this.getData();
            this.transportModeIdentifier = InvoiceRouteColumns.transportModeIdentifier.convertToDB(transportModeIdentifier);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "transportModeIdentifier", String.class, oldValue, this.transportModeIdentifier);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getServprovAliasQualGid() throws GLException {
        try {
            return (String)InvoiceRouteColumns.servprovAliasQualGid.convertFromDB(this.servprovAliasQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setServprovAliasQualGid(String servprovAliasQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.servprovAliasQualGid;
            InvoiceRouteData data = this.getData();
            this.servprovAliasQualGid = InvoiceRouteColumns.servprovAliasQualGid.convertToDB(servprovAliasQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "servprovAliasQualGid", String.class, oldValue, this.servprovAliasQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getServprovAliasValue() throws GLException {
        try {
            return (String)InvoiceRouteColumns.servprovAliasValue.convertFromDB(this.servprovAliasValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setServprovAliasValue(String servprovAliasValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.servprovAliasValue;
            InvoiceRouteData data = this.getData();
            this.servprovAliasValue = InvoiceRouteColumns.servprovAliasValue.convertToDB(servprovAliasValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "servprovAliasValue", String.class, oldValue, this.servprovAliasValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getIntermodalServiceCode() throws GLException {
        try {
            return (String)InvoiceRouteColumns.intermodalServiceCode.convertFromDB(this.intermodalServiceCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIntermodalServiceCode(String intermodalServiceCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.intermodalServiceCode;
            InvoiceRouteData data = this.getData();
            this.intermodalServiceCode = InvoiceRouteColumns.intermodalServiceCode.convertToDB(intermodalServiceCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "intermodalServiceCode", String.class, oldValue, this.intermodalServiceCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getJctCityCode() throws GLException {
        try {
            return (String)InvoiceRouteColumns.jctCityCode.convertFromDB(this.jctCityCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setJctCityCode(String jctCityCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.jctCityCode;
            InvoiceRouteData data = this.getData();
            this.jctCityCode = InvoiceRouteColumns.jctCityCode.convertToDB(jctCityCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "jctCityCode", String.class, oldValue, this.jctCityCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceRouteColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceRouteData data = this.getData();
            this.domainName = InvoiceRouteColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
