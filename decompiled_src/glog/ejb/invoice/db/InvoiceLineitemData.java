/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceLineitemData
extends BeanData {
    public String invoiceGid;
    public Integer lineitemSeqNo;
    public String description;
    public Long unitCount;
    public String transportHandlingUnitGid;
    public String billableIndicatorGid;
    public String flexCommodityQualGid;
    public String flexCommodityCode;
    public Weight weight;
    public Volume volume;
    public String weightQualifier;
    public String marks;
    public String marksQualifier;
    public String billedAsQualifier;
    public Double billedAsQuantity;
    public String freightRateQualifier;
    public Currency freightRateValue;
    public Currency freightCharge;
    public Currency prepaidAmount;
    public String paymentMethodCodeGid;
    public String accessorialCodeGid;
    public String accessorialDescription;
    public String compartmentIdCode;
    public String exportLicControlCode;
    public String exportLicCountryCode3Gid;
    public LocalDate exportLicExpDate;
    public String exportLicNumber;
    public LocalDate importLicExpDate;
    public LocalDate importLicIssueDate;
    public String importLicNumber;
    public String tariffAgencyCode;
    public String tariffRefnum;
    public String tariffRefnumQualifier;
    public String tariffRefnumSuffix;
    public String tariffSection;
    public String tariffItemNumber;
    public String tariffItemNumberSuffix;
    public String tariffItemPart;
    public String tariffSupplementId;
    public String tariffRegAgencyCode;
    public String tariffPubAuthority;
    public String tariffIssuingCarrierId;
    public String tariffFreightClassCode;
    public LocalDate tariffEffectiveDate;
    public String declaredValueQualGid;
    public Currency declaredValue;
    public String adjustmentReasonCodeGid;
    public Boolean processAsFlowThru = Boolean.valueOf("false");
    public String generalLedgerGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String vatCodeGid;
    public String costTypeGid;
    public String domainName;
    public String indicator;
    public Currency outOfTolAmt;
    public String outOfTolReasonCodeGid;
    public Currency deviationAmount;
    public String attribute1;
    public String attribute2;
    public String attribute3;
    public String attribute4;
    public String attribute5;
    public String attribute6;
    public String attribute7;
    public String attribute8;
    public String attribute9;
    public String attribute10;
    public String attribute11;
    public String attribute12;
    public String attribute13;
    public String attribute14;
    public String attribute15;
    public String attribute16;
    public String attribute17;
    public String attribute18;
    public String attribute19;
    public String attribute20;
    public Double attributeNumber1;
    public Double attributeNumber2;
    public Double attributeNumber3;
    public Double attributeNumber4;
    public Double attributeNumber5;
    public Double attributeNumber6;
    public Double attributeNumber7;
    public Double attributeNumber8;
    public Double attributeNumber9;
    public Double attributeNumber10;
    public LocalTimestamp attributeDate1;
    public LocalTimestamp attributeDate2;
    public LocalTimestamp attributeDate3;
    public LocalTimestamp attributeDate4;
    public LocalTimestamp attributeDate5;
    public LocalTimestamp attributeDate6;
    public LocalTimestamp attributeDate7;
    public LocalTimestamp attributeDate8;
    public LocalTimestamp attributeDate9;
    public LocalTimestamp attributeDate10;
    public Currency attributeCurrency1;
    public Currency attributeCurrency2;
    public Currency attributeCurrency3;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLineitemData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field lineitemSeqNoField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field unitCountField = beanDataFields[3];
    public static Field transportHandlingUnitGidField = beanDataFields[4];
    public static Field billableIndicatorGidField = beanDataFields[5];
    public static Field flexCommodityQualGidField = beanDataFields[6];
    public static Field flexCommodityCodeField = beanDataFields[7];
    public static Field weightField = beanDataFields[8];
    public static Field volumeField = beanDataFields[9];
    public static Field weightQualifierField = beanDataFields[10];
    public static Field marksField = beanDataFields[11];
    public static Field marksQualifierField = beanDataFields[12];
    public static Field billedAsQualifierField = beanDataFields[13];
    public static Field billedAsQuantityField = beanDataFields[14];
    public static Field freightRateQualifierField = beanDataFields[15];
    public static Field freightRateValueField = beanDataFields[16];
    public static Field freightChargeField = beanDataFields[17];
    public static Field prepaidAmountField = beanDataFields[18];
    public static Field paymentMethodCodeGidField = beanDataFields[19];
    public static Field accessorialCodeGidField = beanDataFields[20];
    public static Field accessorialDescriptionField = beanDataFields[21];
    public static Field compartmentIdCodeField = beanDataFields[22];
    public static Field exportLicControlCodeField = beanDataFields[23];
    public static Field exportLicCountryCode3GidField = beanDataFields[24];
    public static Field exportLicExpDateField = beanDataFields[25];
    public static Field exportLicNumberField = beanDataFields[26];
    public static Field importLicExpDateField = beanDataFields[27];
    public static Field importLicIssueDateField = beanDataFields[28];
    public static Field importLicNumberField = beanDataFields[29];
    public static Field tariffAgencyCodeField = beanDataFields[30];
    public static Field tariffRefnumField = beanDataFields[31];
    public static Field tariffRefnumQualifierField = beanDataFields[32];
    public static Field tariffRefnumSuffixField = beanDataFields[33];
    public static Field tariffSectionField = beanDataFields[34];
    public static Field tariffItemNumberField = beanDataFields[35];
    public static Field tariffItemNumberSuffixField = beanDataFields[36];
    public static Field tariffItemPartField = beanDataFields[37];
    public static Field tariffSupplementIdField = beanDataFields[38];
    public static Field tariffRegAgencyCodeField = beanDataFields[39];
    public static Field tariffPubAuthorityField = beanDataFields[40];
    public static Field tariffIssuingCarrierIdField = beanDataFields[41];
    public static Field tariffFreightClassCodeField = beanDataFields[42];
    public static Field tariffEffectiveDateField = beanDataFields[43];
    public static Field declaredValueQualGidField = beanDataFields[44];
    public static Field declaredValueField = beanDataFields[45];
    public static Field adjustmentReasonCodeGidField = beanDataFields[46];
    public static Field processAsFlowThruField = beanDataFields[47];
    public static Field generalLedgerGidField = beanDataFields[48];
    public static Field exchangeRateDateField = beanDataFields[49];
    public static Field exchangeRateGidField = beanDataFields[50];
    public static Field vatCodeGidField = beanDataFields[51];
    public static Field costTypeGidField = beanDataFields[52];
    public static Field domainNameField = beanDataFields[53];
    public static Field indicatorField = beanDataFields[54];
    public static Field outOfTolAmtField = beanDataFields[55];
    public static Field outOfTolReasonCodeGidField = beanDataFields[56];
    public static Field deviationAmountField = beanDataFields[57];
    public static Field attribute1Field = beanDataFields[58];
    public static Field attribute2Field = beanDataFields[59];
    public static Field attribute3Field = beanDataFields[60];
    public static Field attribute4Field = beanDataFields[61];
    public static Field attribute5Field = beanDataFields[62];
    public static Field attribute6Field = beanDataFields[63];
    public static Field attribute7Field = beanDataFields[64];
    public static Field attribute8Field = beanDataFields[65];
    public static Field attribute9Field = beanDataFields[66];
    public static Field attribute10Field = beanDataFields[67];
    public static Field attribute11Field = beanDataFields[68];
    public static Field attribute12Field = beanDataFields[69];
    public static Field attribute13Field = beanDataFields[70];
    public static Field attribute14Field = beanDataFields[71];
    public static Field attribute15Field = beanDataFields[72];
    public static Field attribute16Field = beanDataFields[73];
    public static Field attribute17Field = beanDataFields[74];
    public static Field attribute18Field = beanDataFields[75];
    public static Field attribute19Field = beanDataFields[76];
    public static Field attribute20Field = beanDataFields[77];
    public static Field attributeNumber1Field = beanDataFields[78];
    public static Field attributeNumber2Field = beanDataFields[79];
    public static Field attributeNumber3Field = beanDataFields[80];
    public static Field attributeNumber4Field = beanDataFields[81];
    public static Field attributeNumber5Field = beanDataFields[82];
    public static Field attributeNumber6Field = beanDataFields[83];
    public static Field attributeNumber7Field = beanDataFields[84];
    public static Field attributeNumber8Field = beanDataFields[85];
    public static Field attributeNumber9Field = beanDataFields[86];
    public static Field attributeNumber10Field = beanDataFields[87];
    public static Field attributeDate1Field = beanDataFields[88];
    public static Field attributeDate2Field = beanDataFields[89];
    public static Field attributeDate3Field = beanDataFields[90];
    public static Field attributeDate4Field = beanDataFields[91];
    public static Field attributeDate5Field = beanDataFields[92];
    public static Field attributeDate6Field = beanDataFields[93];
    public static Field attributeDate7Field = beanDataFields[94];
    public static Field attributeDate8Field = beanDataFields[95];
    public static Field attributeDate9Field = beanDataFields[96];
    public static Field attributeDate10Field = beanDataFields[97];
    public static Field attributeCurrency1Field = beanDataFields[98];
    public static Field attributeCurrency2Field = beanDataFields[99];
    public static Field attributeCurrency3Field = beanDataFields[100];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceLineitemData() {
    }

    public InvoiceLineitemData(InvoiceLineitemData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceLineitemPK();
    }

    @Legacy
    public InvoiceLineitemPK getInvoiceLineitemPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.lineitemSeqNo == null) {
            return null;
        }
        return new InvoiceLineitemPK(this.invoiceGid, this.lineitemSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceLineitemPK((InvoiceLineitemPK)pk);
    }

    @Legacy
    public void setInvoiceLineitemPK(InvoiceLineitemPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.lineitemSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceLineitemQueryGen";
    }

    public static InvoiceLineitemData load(Connection conn, InvoiceLineitemPK pk) throws GLException {
        return (InvoiceLineitemData)InvoiceLineitemData.load(conn, pk, InvoiceLineitemData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceLineitemData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceLineitemData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceLineitemData.class);
    }
}
