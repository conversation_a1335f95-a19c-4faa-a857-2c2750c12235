/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class LineAppTolInvoiceRefnumFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("invoiceRefnumQualGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineAppTolInvoiceRefnumData";
            }

            @Override
            public String getPkField() {
                return "invoiceRefnumQualGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceRefnumQualData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceRefnumQualGid";
            }
        });
        fks.put("lineApproveToleranceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "LineAppTolInvoiceRefnumData";
            }

            @Override
            public String getPkField() {
                return "lineApproveToleranceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "LineApproveToleranceData";
            }

            @Override
            public String getFkDataField() {
                return "lineApproveToleranceGid";
            }
        });
    }
}
