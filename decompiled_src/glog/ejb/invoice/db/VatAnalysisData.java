/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VatAnalysisPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class VatAnalysisData
extends BeanData {
    public String invoiceGid;
    public String vatCodeGid;
    public Currency taxAmount;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public Currency vatBasisAmount;
    public Double vatRate;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VatAnalysisData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field vatCodeGidField = beanDataFields[1];
    public static Field taxAmountField = beanDataFields[2];
    public static Field exchangeRateDateField = beanDataFields[3];
    public static Field exchangeRateGidField = beanDataFields[4];
    public static Field vatBasisAmountField = beanDataFields[5];
    public static Field vatRateField = beanDataFields[6];
    public static Field domainNameField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public VatAnalysisData() {
    }

    public VatAnalysisData(VatAnalysisData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getVatAnalysisPK();
    }

    @Legacy
    public VatAnalysisPK getVatAnalysisPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.vatCodeGid == null) {
            return null;
        }
        return new VatAnalysisPK(this.invoiceGid, this.vatCodeGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setVatAnalysisPK((VatAnalysisPK)pk);
    }

    @Legacy
    public void setVatAnalysisPK(VatAnalysisPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.vatCodeGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.vat.gen.VatAnalysisQueryGen";
    }

    public static VatAnalysisData load(Connection conn, VatAnalysisPK pk) throws GLException {
        return (VatAnalysisData)VatAnalysisData.load(conn, pk, VatAnalysisData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return VatAnalysisData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return VatAnalysisData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return VatAnalysisData.load(conn, whereClause, prepareArguments, fetchSize, VatAnalysisData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return VatAnalysisData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return VatAnalysisData.load(conn, fromWhere, alias, prepareArguments, fetchSize, VatAnalysisData.class);
    }
}
