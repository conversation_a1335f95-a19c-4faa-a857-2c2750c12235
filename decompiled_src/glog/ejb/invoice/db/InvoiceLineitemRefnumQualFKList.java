/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvoiceLineitemRefnumQualFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("defaultRefnumBnTypeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemRefnumQualData";
            }

            @Override
            public String getPkField() {
                return "defaultRefnumBnTypeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.bngenerator.db";
            }

            @Override
            public String getFkDataClass() {
                return "BnTypeData";
            }

            @Override
            public String getFkDataField() {
                return "bnTypeGid";
            }
        });
    }
}
