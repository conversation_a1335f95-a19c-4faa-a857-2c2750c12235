/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class InvoiceLineitemVatCostRefColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineitemSeqNo = new SqlColumn(Integer.class, "lineitem_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn vatSeqno = new SqlColumn(Integer.class, "vat_seqno", 8, 0, 1, null, 2, null);
    public static SqlColumn costReferenceGid = new SqlColumn(String.class, "cost_reference_gid", 101, 0, 1, null, 3, null);
    public static SqlColumn shipmentCostQualGid = new SqlColumn(String.class, "shipment_cost_qual_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn vatBasisAmount = new SqlColumn(Currency.class, "vat_basis_amount", 22, 2, 0, null, 5, "vat_basis_amount_base");
    public static SqlColumn vatAmount = new SqlColumn(Currency.class, "vat_amount", 22, 2, 0, null, 6, "vat_amount_base");
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 7, null);
}
