/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class LineApproveToleranceDetailColumns {
    public static SqlColumn lineApproveToleranceGid = new SqlColumn(String.class, "line_approve_tolerance_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineApproveToleranceSeq = new SqlColumn(Integer.class, "line_approve_tolerance_seq", 9, 0, 1, null, 1, null);
    public static SqlColumn costType = new SqlColumn(String.class, "cost_type", 1, 0, 0, null, 2, null);
    public static SqlColumn accessorialCodeGid = new SqlColumn(String.class, "accessorial_code_gid", 101, 0, 0, null, 3, null);
    public static SqlColumn generalLedgerGid = new SqlColumn(String.class, "general_ledger_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn paymentMethodCodeGid = new SqlColumn(String.class, "payment_method_code_gid", 101, 0, 0, null, 5, null);
    public static SqlColumn allowablePercentAbove = new SqlColumn(Double.class, "allowable_percent_above", 22, 3, 0, null, 6, null);
    public static SqlColumn allowCurrAbove = new SqlColumn(Currency.class, "allow_curr_above", 22, 2, 0, null, 7, "allow_curr_above_base");
    public static SqlColumn allowablePercentBelow = new SqlColumn(Double.class, "allowable_percent_below", 22, 3, 0, null, 8, null);
    public static SqlColumn allowCurrBelow = new SqlColumn(Currency.class, "allow_curr_below", 22, 2, 0, null, 9, "allow_curr_below_base");
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
}
