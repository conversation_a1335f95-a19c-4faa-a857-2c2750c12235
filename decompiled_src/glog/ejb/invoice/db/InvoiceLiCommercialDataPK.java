/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLiCommercialDataColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceLiCommercialDataPK
extends Pk {
    public Object commercialDataSeqNo;
    public Object invoiceGid;
    public Object lineitemSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLiCommercialDataPK() {
    }

    public InvoiceLiCommercialDataPK(Integer commercialDataSeqNo, String invoiceGid, Integer lineitemSeqNo) {
        this.commercialDataSeqNo = this.notNull(InvoiceLiCommercialDataColumns.commercialDataSeqNo.convertToDB(commercialDataSeqNo), "commercialDataSeqNo");
        this.invoiceGid = this.notNull(InvoiceLiCommercialDataColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.lineitemSeqNo = this.notNull(InvoiceLiCommercialDataColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
    }

    public InvoiceLiCommercialDataPK(int dummy, Object commercialDataSeqNo, Object invoiceGid, Object lineitemSeqNo) {
        this(dummy, commercialDataSeqNo, invoiceGid, lineitemSeqNo, null);
    }

    public InvoiceLiCommercialDataPK(int dummy, Object commercialDataSeqNo, Object invoiceGid, Object lineitemSeqNo, Object transaction) {
        this.commercialDataSeqNo = commercialDataSeqNo;
        this.invoiceGid = invoiceGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.transaction = transaction;
    }

    public InvoiceLiCommercialDataPK(InvoiceLiCommercialDataPK otherPk, Object transaction) {
        this.commercialDataSeqNo = otherPk.commercialDataSeqNo;
        this.invoiceGid = otherPk.invoiceGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.commercialDataSeqNo != null ? String.valueOf(this.commercialDataSeqNo) : "") + " " + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.commercialDataSeqNo != null ? String.valueOf(this.commercialDataSeqNo) : "") + "|" + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    public static InvoiceLiCommercialDataPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLiCommercialDataPK(Integer.valueOf(gids[0]), gids[1], Integer.valueOf(gids[2])) : null;
    }

    public static InvoiceLiCommercialDataPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLiCommercialDataPK(Integer.valueOf(gids[0]), gids[1], Integer.valueOf(gids[2])) : null;
    }

    public int hashCode() {
        return this.commercialDataSeqNo.hashCode() + this.invoiceGid.hashCode() + this.lineitemSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLiCommercialDataPK)) {
            return false;
        }
        InvoiceLiCommercialDataPK otherPk = (InvoiceLiCommercialDataPK)other;
        return Functions.equals(otherPk.commercialDataSeqNo, this.commercialDataSeqNo) && Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLiCommercialDataHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.commercialDataSeqNo;
            }
            case 1: {
                return this.invoiceGid;
            }
            case 2: {
                return this.lineitemSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLiCommercialDataColumns.commercialDataSeqNo.convertFromDB(this.commercialDataSeqNo);
            }
            case 1: {
                return InvoiceLiCommercialDataColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 2: {
                return InvoiceLiCommercialDataColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLiCommercialDataPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLiCommercialData";
        }

        @Override
        public final String getTableName() {
            return "invoice_li_commercial_data";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"commercial_data_seq_no", "invoice_gid", "lineitem_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLiCommercialDataPK result = new InvoiceLiCommercialDataPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
