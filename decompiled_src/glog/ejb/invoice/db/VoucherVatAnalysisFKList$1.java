/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherVatAnalysisFKList.1
implements Fk {
    VoucherVatAnalysisFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherVatAnalysisData";
    }

    @Override
    public String getPkField() {
        return "vatCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vat.db";
    }

    @Override
    public String getFkDataClass() {
        return "VatCodeData";
    }

    @Override
    public String getFkDataField() {
        return "vatCodeGid";
    }
}
