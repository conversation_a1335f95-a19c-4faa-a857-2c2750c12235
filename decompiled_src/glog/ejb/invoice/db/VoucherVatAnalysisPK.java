/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherVatAnalysisColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class VoucherVatAnalysisPK
extends Pk {
    public Object vatCodeGid;
    public Object voucherGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public VoucherVatAnalysisPK() {
    }

    public VoucherVatAnalysisPK(String vatCodeGid, String voucherGid) {
        this.vatCodeGid = this.notNull(VoucherVatAnalysisColumns.vatCodeGid.convertToDB(vatCodeGid), "vatCodeGid");
        this.voucherGid = this.notNull(VoucherVatAnalysisColumns.voucherGid.convertToDB(voucherGid), "voucherGid");
    }

    public VoucherVatAnalysisPK(int dummy, Object vatCodeGid, Object voucherGid) {
        this(dummy, vatCodeGid, voucherGid, null);
    }

    public VoucherVatAnalysisPK(int dummy, Object vatCodeGid, Object voucherGid, Object transaction) {
        this.vatCodeGid = vatCodeGid;
        this.voucherGid = voucherGid;
        this.transaction = transaction;
    }

    public VoucherVatAnalysisPK(VoucherVatAnalysisPK otherPk, Object transaction) {
        this.vatCodeGid = otherPk.vatCodeGid;
        this.voucherGid = otherPk.voucherGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.vatCodeGid != null ? String.valueOf(this.vatCodeGid) : "") + " " + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.vatCodeGid != null ? String.valueOf(this.vatCodeGid) : "") + "|" + (this.voucherGid != null ? String.valueOf(this.voucherGid) : "");
    }

    public static VoucherVatAnalysisPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new VoucherVatAnalysisPK(gids[0], gids[1]) : null;
    }

    public static VoucherVatAnalysisPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new VoucherVatAnalysisPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.vatCodeGid.hashCode() + this.voucherGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof VoucherVatAnalysisPK)) {
            return false;
        }
        VoucherVatAnalysisPK otherPk = (VoucherVatAnalysisPK)other;
        return Functions.equals(otherPk.vatCodeGid, this.vatCodeGid) && Functions.equals(otherPk.voucherGid, this.voucherGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.VoucherVatAnalysisHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.vatCodeGid;
            }
            case 1: {
                return this.voucherGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return VoucherVatAnalysisColumns.vatCodeGid.convertFromDB(this.vatCodeGid);
            }
            case 1: {
                return VoucherVatAnalysisColumns.voucherGid.convertFromDB(this.voucherGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return VoucherVatAnalysisPK.class;
        }

        @Override
        public final String getEntity() {
            return "VoucherVatAnalysis";
        }

        @Override
        public final String getTableName() {
            return "voucher_vat_analysis";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"vat_code_gid", "voucher_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            VoucherVatAnalysisPK result = new VoucherVatAnalysisPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
