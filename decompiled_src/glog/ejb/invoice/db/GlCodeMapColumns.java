/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class GlCodeMapColumns {
    public static SqlColumn keyValue = new SqlColumn(String.class, "key_value", 500, 0, 1, null, 0, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 1, null);
    public static SqlColumn glLookupKeyGid = new SqlColumn(String.class, "gl_lookup_key_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn generalLedgerGid = new SqlColumn(String.class, "general_ledger_gid", 101, 0, 1, null, 3, null);
}
