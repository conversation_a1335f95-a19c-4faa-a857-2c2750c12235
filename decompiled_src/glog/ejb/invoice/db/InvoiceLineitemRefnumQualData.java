/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemRefnumQualPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceLineitemRefnumQualData
extends BeanData {
    public String invoiceLiRefnumQualGid;
    public String invoiceLiRefnumQualXid;
    public String invoiceLiRefnumDesc;
    public String defaultRefnumBnTypeGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLineitemRefnumQualData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceLiRefnumQualGidField = beanDataFields[0];
    public static Field invoiceLiRefnumQualXidField = beanDataFields[1];
    public static Field invoiceLiRefnumDescField = beanDataFields[2];
    public static Field defaultRefnumBnTypeGidField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceLineitemRefnumQualData() {
    }

    public InvoiceLineitemRefnumQualData(InvoiceLineitemRefnumQualData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceLineitemRefnumQualPK();
    }

    @Legacy
    public InvoiceLineitemRefnumQualPK getInvoiceLineitemRefnumQualPK() {
        if (this.invoiceLiRefnumQualGid == null) {
            return null;
        }
        return new InvoiceLineitemRefnumQualPK(this.invoiceLiRefnumQualGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceLineitemRefnumQualPK((InvoiceLineitemRefnumQualPK)pk);
    }

    @Legacy
    public void setInvoiceLineitemRefnumQualPK(InvoiceLineitemRefnumQualPK pk) {
        this.invoiceLiRefnumQualGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceLineitemRefnumQualQueryGen";
    }

    public static InvoiceLineitemRefnumQualData load(Connection conn, InvoiceLineitemRefnumQualPK pk) throws GLException {
        return (InvoiceLineitemRefnumQualData)InvoiceLineitemRefnumQualData.load(conn, pk, InvoiceLineitemRefnumQualData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceLineitemRefnumQualData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemRefnumQualData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemRefnumQualData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceLineitemRefnumQualData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemRefnumQualData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemRefnumQualData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceLineitemRefnumQualData.class);
    }
}
