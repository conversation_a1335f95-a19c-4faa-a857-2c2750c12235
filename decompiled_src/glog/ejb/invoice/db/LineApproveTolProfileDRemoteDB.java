/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveTolProfileDData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface LineApproveTolProfileDRemoteDB
extends EJBObject {
    public LineApproveTolProfileDData getData() throws RemoteException, GLException;

    public void setData(LineApproveTolProfileDData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getLineApproveTolProfileGid() throws RemoteException, GLException;

    public void setLineApproveTolProfileGid(String var1) throws RemoteException, GLException;

    public String getLineApproveToleranceGid() throws RemoteException, GLException;

    public void setLineApproveToleranceGid(String var1) throws RemoteException, GLException;

    public Integer getRuleSequence() throws RemoteException, GLException;

    public void setRuleSequence(Integer var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
