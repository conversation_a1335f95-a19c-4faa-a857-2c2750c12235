/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvSupplyProvinceVatRegPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvSupplyProvinceVatRegData
extends BeanData {
    public String invoiceGid;
    public String countryCode3Gid;
    public String provinceCode;
    public String vatProvincialRegGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvSupplyProvinceVatRegData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field countryCode3GidField = beanDataFields[1];
    public static Field provinceCodeField = beanDataFields[2];
    public static Field vatProvincialRegGidField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvSupplyProvinceVatRegData() {
    }

    public InvSupplyProvinceVatRegData(InvSupplyProvinceVatRegData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvSupplyProvinceVatRegPK();
    }

    @Legacy
    public InvSupplyProvinceVatRegPK getInvSupplyProvinceVatRegPK() {
        if (this.countryCode3Gid == null) {
            return null;
        }
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.provinceCode == null) {
            return null;
        }
        return new InvSupplyProvinceVatRegPK(this.countryCode3Gid, this.invoiceGid, this.provinceCode);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvSupplyProvinceVatRegPK((InvSupplyProvinceVatRegPK)pk);
    }

    @Legacy
    public void setInvSupplyProvinceVatRegPK(InvSupplyProvinceVatRegPK pk) {
        this.countryCode3Gid = (String)pk.getAppValue(0);
        this.invoiceGid = (String)pk.getAppValue(1);
        this.provinceCode = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvSupplyProvinceVatRegQueryGen";
    }

    public static InvSupplyProvinceVatRegData load(Connection conn, InvSupplyProvinceVatRegPK pk) throws GLException {
        return (InvSupplyProvinceVatRegData)InvSupplyProvinceVatRegData.load(conn, pk, InvSupplyProvinceVatRegData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvSupplyProvinceVatRegData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvSupplyProvinceVatRegData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvSupplyProvinceVatRegData.load(conn, whereClause, prepareArguments, fetchSize, InvSupplyProvinceVatRegData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvSupplyProvinceVatRegData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvSupplyProvinceVatRegData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvSupplyProvinceVatRegData.class);
    }
}
