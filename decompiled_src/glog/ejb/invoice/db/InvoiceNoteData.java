/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceNotePK;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceNoteData
extends BeanData {
    public String invoiceGid;
    public Long invoiceNoteSeqNo;
    public LocalTimestamp timestamp;
    public String summary;
    public String note;
    public Boolean isSystemGenerated = Boolean.valueOf("false");
    public String enteredBy;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceNoteData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field invoiceNoteSeqNoField = beanDataFields[1];
    public static Field timestampField = beanDataFields[2];
    public static Field summaryField = beanDataFields[3];
    public static Field noteField = beanDataFields[4];
    public static Field isSystemGeneratedField = beanDataFields[5];
    public static Field enteredByField = beanDataFields[6];
    public static Field domainNameField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceNoteData() {
    }

    public InvoiceNoteData(InvoiceNoteData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceNotePK();
    }

    @Legacy
    public InvoiceNotePK getInvoiceNotePK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.invoiceNoteSeqNo == null) {
            return null;
        }
        return new InvoiceNotePK(this.invoiceGid, this.invoiceNoteSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceNotePK((InvoiceNotePK)pk);
    }

    @Legacy
    public void setInvoiceNotePK(InvoiceNotePK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.invoiceNoteSeqNo = (Long)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceNoteQueryGen";
    }

    public static InvoiceNoteData load(Connection conn, InvoiceNotePK pk) throws GLException {
        return (InvoiceNoteData)InvoiceNoteData.load(conn, pk, InvoiceNoteData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceNoteData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceNoteData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceNoteData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceNoteData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceNoteData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceNoteData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceNoteData.class);
    }
}
