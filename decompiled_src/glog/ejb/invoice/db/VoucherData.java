/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.Voucher;
import glog.ejb.invoice.db.VoucherPK;
import glog.server.status.ObjectStatus;
import glog.server.status.StatusFunction;
import glog.server.workflow.status.StatusError;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class VoucherData
extends BeanData {
    public String voucherGid;
    public String voucherXid;
    public String voucherNumber;
    public String invoiceGid;
    public Boolean wasAutoApproved = Boolean.valueOf("false");
    public String approvedBy;
    public Currency amountToPay;
    public LocalDate timestamp;
    public String adjustmentReasonGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public Currency amtToPayWithTax;
    public String domainName;
    public String attribute1;
    public String attribute2;
    public String attribute3;
    public String attribute4;
    public String attribute5;
    public String attribute6;
    public String attribute7;
    public String attribute8;
    public String attribute9;
    public String attribute10;
    public String attribute11;
    public String attribute12;
    public String attribute13;
    public String attribute14;
    public String attribute15;
    public String attribute16;
    public String attribute17;
    public String attribute18;
    public String attribute19;
    public String attribute20;
    public Double attributeNumber1;
    public Double attributeNumber2;
    public Double attributeNumber3;
    public Double attributeNumber4;
    public Double attributeNumber5;
    public Double attributeNumber6;
    public Double attributeNumber7;
    public Double attributeNumber8;
    public Double attributeNumber9;
    public Double attributeNumber10;
    public LocalTimestamp attributeDate1;
    public LocalTimestamp attributeDate2;
    public LocalTimestamp attributeDate3;
    public LocalTimestamp attributeDate4;
    public LocalTimestamp attributeDate5;
    public LocalTimestamp attributeDate6;
    public LocalTimestamp attributeDate7;
    public LocalTimestamp attributeDate8;
    public LocalTimestamp attributeDate9;
    public LocalTimestamp attributeDate10;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field voucherGidField = beanDataFields[0];
    public static Field voucherXidField = beanDataFields[1];
    public static Field voucherNumberField = beanDataFields[2];
    public static Field invoiceGidField = beanDataFields[3];
    public static Field wasAutoApprovedField = beanDataFields[4];
    public static Field approvedByField = beanDataFields[5];
    public static Field amountToPayField = beanDataFields[6];
    public static Field timestampField = beanDataFields[7];
    public static Field adjustmentReasonGidField = beanDataFields[8];
    public static Field exchangeRateDateField = beanDataFields[9];
    public static Field exchangeRateGidField = beanDataFields[10];
    public static Field amtToPayWithTaxField = beanDataFields[11];
    public static Field domainNameField = beanDataFields[12];
    public static Field attribute1Field = beanDataFields[13];
    public static Field attribute2Field = beanDataFields[14];
    public static Field attribute3Field = beanDataFields[15];
    public static Field attribute4Field = beanDataFields[16];
    public static Field attribute5Field = beanDataFields[17];
    public static Field attribute6Field = beanDataFields[18];
    public static Field attribute7Field = beanDataFields[19];
    public static Field attribute8Field = beanDataFields[20];
    public static Field attribute9Field = beanDataFields[21];
    public static Field attribute10Field = beanDataFields[22];
    public static Field attribute11Field = beanDataFields[23];
    public static Field attribute12Field = beanDataFields[24];
    public static Field attribute13Field = beanDataFields[25];
    public static Field attribute14Field = beanDataFields[26];
    public static Field attribute15Field = beanDataFields[27];
    public static Field attribute16Field = beanDataFields[28];
    public static Field attribute17Field = beanDataFields[29];
    public static Field attribute18Field = beanDataFields[30];
    public static Field attribute19Field = beanDataFields[31];
    public static Field attribute20Field = beanDataFields[32];
    public static Field attributeNumber1Field = beanDataFields[33];
    public static Field attributeNumber2Field = beanDataFields[34];
    public static Field attributeNumber3Field = beanDataFields[35];
    public static Field attributeNumber4Field = beanDataFields[36];
    public static Field attributeNumber5Field = beanDataFields[37];
    public static Field attributeNumber6Field = beanDataFields[38];
    public static Field attributeNumber7Field = beanDataFields[39];
    public static Field attributeNumber8Field = beanDataFields[40];
    public static Field attributeNumber9Field = beanDataFields[41];
    public static Field attributeNumber10Field = beanDataFields[42];
    public static Field attributeDate1Field = beanDataFields[43];
    public static Field attributeDate2Field = beanDataFields[44];
    public static Field attributeDate3Field = beanDataFields[45];
    public static Field attributeDate4Field = beanDataFields[46];
    public static Field attributeDate5Field = beanDataFields[47];
    public static Field attributeDate6Field = beanDataFields[48];
    public static Field attributeDate7Field = beanDataFields[49];
    public static Field attributeDate8Field = beanDataFields[50];
    public static Field attributeDate9Field = beanDataFields[51];
    public static Field attributeDate10Field = beanDataFields[52];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public VoucherData() {
    }

    public VoucherData(VoucherData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getVoucherPK();
    }

    @Legacy
    public VoucherPK getVoucherPK() {
        if (this.voucherGid == null) {
            return null;
        }
        return new VoucherPK(this.voucherGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setVoucherPK((VoucherPK)pk);
    }

    @Legacy
    public void setVoucherPK(VoucherPK pk) {
        this.voucherGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.VoucherQueryGen";
    }

    public static VoucherData load(Connection conn, VoucherPK pk) throws GLException {
        return (VoucherData)VoucherData.load(conn, pk, VoucherData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return VoucherData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return VoucherData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherData.load(conn, whereClause, prepareArguments, fetchSize, VoucherData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return VoucherData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherData.load(conn, fromWhere, alias, prepareArguments, fetchSize, VoucherData.class);
    }

    @Override
    public ObjectStatus newObjectStatus() throws GLException {
        String domain = this.domainName != null ? this.domainName : SecurityUtil.getCurrentDomain();
        ObjectStatus status = new ObjectStatus("voucher_status", new VoucherPK.Callback(), true);
        status.init(Voucher.class, domain);
        return status;
    }

    public boolean canPerform(StatusFunction statusFunction, Object context) throws GLException {
        return this.canPerform(statusFunction, context, "ejb.Voucher");
    }

    public StatusError canPerformWithCauses(StatusFunction statusFunction, Object context) throws GLException {
        return this.canPerformWithCauses(statusFunction, context, "ejb.Voucher");
    }
}
