/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveTolProfilePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class LineApproveTolProfileData
extends BeanData {
    public String lineApproveTolProfileGid;
    public String lineApproveTolProfileXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineApproveTolProfileData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field lineApproveTolProfileGidField = beanDataFields[0];
    public static Field lineApproveTolProfileXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public LineApproveTolProfileData() {
    }

    public LineApproveTolProfileData(LineApproveTolProfileData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getLineApproveTolProfilePK();
    }

    @Legacy
    public LineApproveTolProfilePK getLineApproveTolProfilePK() {
        if (this.lineApproveTolProfileGid == null) {
            return null;
        }
        return new LineApproveTolProfilePK(this.lineApproveTolProfileGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setLineApproveTolProfilePK((LineApproveTolProfilePK)pk);
    }

    @Legacy
    public void setLineApproveTolProfilePK(LineApproveTolProfilePK pk) {
        this.lineApproveTolProfileGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.LineApproveTolProfileQueryGen";
    }

    public static LineApproveTolProfileData load(Connection conn, LineApproveTolProfilePK pk) throws GLException {
        return (LineApproveTolProfileData)LineApproveTolProfileData.load(conn, pk, LineApproveTolProfileData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return LineApproveTolProfileData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return LineApproveTolProfileData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveTolProfileData.load(conn, whereClause, prepareArguments, fetchSize, LineApproveTolProfileData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return LineApproveTolProfileData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveTolProfileData.load(conn, fromWhere, alias, prepareArguments, fetchSize, LineApproveTolProfileData.class);
    }
}
