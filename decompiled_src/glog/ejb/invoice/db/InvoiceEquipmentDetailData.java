/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceEquipmentDetailPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Length;
import glog.util.uom.data.Temperature;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceEquipmentDetailData
extends BeanData {
    public String invoiceGid;
    public Long seqNumber;
    public String equipmentInitialNumber;
    public String sEquipmentGid;
    public String equipmentPrefix;
    public String equipmentNumber;
    public String isoEquipmentTypeIdentifier;
    public String descriptionCode;
    public String equipmentOwnerIdentifier;
    public String ownershipCode;
    public Length length;
    public Length width;
    public Length height;
    public Weight weight;
    public Volume volume;
    public Weight tareWeight;
    public Weight dunnage;
    public Long unitCount;
    public String weightQualifier;
    public Temperature minimumTemperature;
    public Temperature maximumTemperature;
    public Integer percentHumidityAllowed;
    public String ventSettingCode;
    public Weight weightAllowance;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceEquipmentDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field seqNumberField = beanDataFields[1];
    public static Field equipmentInitialNumberField = beanDataFields[2];
    public static Field sEquipmentGidField = beanDataFields[3];
    public static Field equipmentPrefixField = beanDataFields[4];
    public static Field equipmentNumberField = beanDataFields[5];
    public static Field isoEquipmentTypeIdentifierField = beanDataFields[6];
    public static Field descriptionCodeField = beanDataFields[7];
    public static Field equipmentOwnerIdentifierField = beanDataFields[8];
    public static Field ownershipCodeField = beanDataFields[9];
    public static Field lengthField = beanDataFields[10];
    public static Field widthField = beanDataFields[11];
    public static Field heightField = beanDataFields[12];
    public static Field weightField = beanDataFields[13];
    public static Field volumeField = beanDataFields[14];
    public static Field tareWeightField = beanDataFields[15];
    public static Field dunnageField = beanDataFields[16];
    public static Field unitCountField = beanDataFields[17];
    public static Field weightQualifierField = beanDataFields[18];
    public static Field minimumTemperatureField = beanDataFields[19];
    public static Field maximumTemperatureField = beanDataFields[20];
    public static Field percentHumidityAllowedField = beanDataFields[21];
    public static Field ventSettingCodeField = beanDataFields[22];
    public static Field weightAllowanceField = beanDataFields[23];
    public static Field domainNameField = beanDataFields[24];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceEquipmentDetailData() {
    }

    public InvoiceEquipmentDetailData(InvoiceEquipmentDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceEquipmentDetailPK();
    }

    @Legacy
    public InvoiceEquipmentDetailPK getInvoiceEquipmentDetailPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.seqNumber == null) {
            return null;
        }
        return new InvoiceEquipmentDetailPK(this.invoiceGid, this.seqNumber);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceEquipmentDetailPK((InvoiceEquipmentDetailPK)pk);
    }

    @Legacy
    public void setInvoiceEquipmentDetailPK(InvoiceEquipmentDetailPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.seqNumber = (Long)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceEquipmentDetailQueryGen";
    }

    public static InvoiceEquipmentDetailData load(Connection conn, InvoiceEquipmentDetailPK pk) throws GLException {
        return (InvoiceEquipmentDetailData)InvoiceEquipmentDetailData.load(conn, pk, InvoiceEquipmentDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceEquipmentDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceEquipmentDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceEquipmentDetailData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceEquipmentDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceEquipmentDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceEquipmentDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceEquipmentDetailData.class);
    }
}
