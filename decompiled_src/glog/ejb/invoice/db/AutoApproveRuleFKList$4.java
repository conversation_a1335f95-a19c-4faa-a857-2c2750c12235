/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class AutoApproveRuleFKList.4
implements Fk {
    AutoApproveRuleFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AutoApproveRuleData";
    }

    @Override
    public String getPkField() {
        return "locationProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "LocationProfileData";
    }

    @Override
    public String getFkDataField() {
        return "locationProfileGid";
    }
}
