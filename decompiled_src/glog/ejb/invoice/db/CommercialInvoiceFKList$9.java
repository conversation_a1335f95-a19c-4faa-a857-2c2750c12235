/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class CommercialInvoiceFKList.9
implements Fk {
    CommercialInvoiceFKList.9() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "CommercialInvoiceData";
    }

    @Override
    public String getPkField() {
        return "finalIncoTermGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "IncoTermData";
    }

    @Override
    public String getFkDataField() {
        return "incoTermGid";
    }
}
