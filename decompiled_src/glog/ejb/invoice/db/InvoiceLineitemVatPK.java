/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemVatColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceLineitemVatPK
extends Pk {
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object vatSeqno;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLineitemVatPK() {
    }

    public InvoiceLineitemVatPK(String invoiceGid, Integer lineitemSeqNo, Integer vatSeqno) {
        this.invoiceGid = this.notNull(InvoiceLineitemVatColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.lineitemSeqNo = this.notNull(InvoiceLineitemVatColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
        this.vatSeqno = this.notNull(InvoiceLineitemVatColumns.vatSeqno.convertToDB(vatSeqno), "vatSeqno");
    }

    public InvoiceLineitemVatPK(int dummy, Object invoiceGid, Object lineitemSeqNo, Object vatSeqno) {
        this(dummy, invoiceGid, lineitemSeqNo, vatSeqno, null);
    }

    public InvoiceLineitemVatPK(int dummy, Object invoiceGid, Object lineitemSeqNo, Object vatSeqno, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.vatSeqno = vatSeqno;
        this.transaction = transaction;
    }

    public InvoiceLineitemVatPK(InvoiceLineitemVatPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.vatSeqno = otherPk.vatSeqno;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "") + " " + (this.vatSeqno != null ? String.valueOf(this.vatSeqno) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "") + "|" + (this.vatSeqno != null ? String.valueOf(this.vatSeqno) : "");
    }

    public static InvoiceLineitemVatPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLineitemVatPK(gids[0], Integer.valueOf(gids[1]), Integer.valueOf(gids[2])) : null;
    }

    public static InvoiceLineitemVatPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLineitemVatPK(gids[0], Integer.valueOf(gids[1]), Integer.valueOf(gids[2])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.lineitemSeqNo.hashCode() + this.vatSeqno.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLineitemVatPK)) {
            return false;
        }
        InvoiceLineitemVatPK otherPk = (InvoiceLineitemVatPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.vatSeqno, this.vatSeqno) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLineitemVatHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.lineitemSeqNo;
            }
            case 2: {
                return this.vatSeqno;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLineitemVatColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceLineitemVatColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
            case 2: {
                return InvoiceLineitemVatColumns.vatSeqno.convertFromDB(this.vatSeqno);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLineitemVatPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLineitemVat";
        }

        @Override
        public final String getTableName() {
            return "invoice_lineitem_vat";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "lineitem_seq_no", "vat_seqno"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLineitemVatPK result = new InvoiceLineitemVatPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
