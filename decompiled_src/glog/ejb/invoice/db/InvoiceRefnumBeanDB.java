/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceRefnumColumns;
import glog.ejb.invoice.db.InvoiceRefnumData;
import glog.ejb.invoice.db.InvoiceRefnumPK;
import glog.server.bngenerator.BNEngine;
import glog.server.bngenerator.BNNumber;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceRefnumBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object invoiceRefnumQualGid;
    public Object invoiceRefnumValue;
    public Object issueDate;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceRefnumPK pk;
    protected transient InvoiceRefnumData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRefnumBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceRefnumPK.Callback theCall = new InvoiceRefnumPK.Callback();

    public InvoiceRefnumBeanDB() {
        super(false);
    }

    public InvoiceRefnumPK ejbCreate(InvoiceRefnumData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceRefnumData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceRefnumPK ejbFindByPrimaryKey(InvoiceRefnumPK pk) throws FinderException {
        return (InvoiceRefnumPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceRefnumPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceRefnumPK> v = new Vector<InvoiceRefnumPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceRefnumPK pk, InvoiceRefnumData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceRefnumPK pk, InvoiceRefnumData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceRefnumPK pk, InvoiceRefnumData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        if (this.invoiceRefnumValue == null) {
            BNNumber bnNumber = BNEngine.generateRefnum("invoice_refnum_qual_gid", (String)this.invoiceRefnumQualGid, null, this.data, this.getConnection());
            this.invoiceRefnumValue = bnNumber.getValue();
        }
        this.pk = (InvoiceRefnumPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceRefnumColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceRefnumColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceRefnumColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceRefnumColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceRefnumPK pk = (InvoiceRefnumPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.invoiceRefnumQualGid = pk.invoiceRefnumQualGid;
        this.invoiceRefnumValue = pk.invoiceRefnumValue;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceRefnumPK pk = new InvoiceRefnumPK(0, this.invoiceGid, this.invoiceRefnumQualGid, this.invoiceRefnumValue, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceRefnum";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceRefnumData getData() throws GLException {
        try {
            InvoiceRefnumData retval = new InvoiceRefnumData();
            retval.getFromBean(this, InvoiceRefnumColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceRefnumData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceRefnumData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceRefnumData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceRefnumColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceRefnumData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceRefnumPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceRefnumColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceRefnumData data = this.getData();
            this.invoiceGid = InvoiceRefnumColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceRefnumQualGid() throws GLException {
        try {
            return (String)InvoiceRefnumColumns.invoiceRefnumQualGid.convertFromDB(this.invoiceRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceRefnumQualGid(String invoiceRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceRefnumQualGid;
            InvoiceRefnumData data = this.getData();
            this.invoiceRefnumQualGid = InvoiceRefnumColumns.invoiceRefnumQualGid.convertToDB(invoiceRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceRefnumQualGid", String.class, oldValue, this.invoiceRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceRefnumValue() throws GLException {
        try {
            return (String)InvoiceRefnumColumns.invoiceRefnumValue.convertFromDB(this.invoiceRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceRefnumValue(String invoiceRefnumValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceRefnumValue;
            InvoiceRefnumData data = this.getData();
            this.invoiceRefnumValue = InvoiceRefnumColumns.invoiceRefnumValue.convertToDB(invoiceRefnumValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceRefnumValue", String.class, oldValue, this.invoiceRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getIssueDate() throws GLException {
        try {
            return (LocalDate)InvoiceRefnumColumns.issueDate.convertFromDB(this.issueDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIssueDate(LocalDate issueDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.issueDate;
            InvoiceRefnumData data = this.getData();
            this.issueDate = InvoiceRefnumColumns.issueDate.convertToDB(issueDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "issueDate", LocalDate.class, oldValue, this.issueDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceRefnumColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceRefnumData data = this.getData();
            this.domainName = InvoiceRefnumColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
