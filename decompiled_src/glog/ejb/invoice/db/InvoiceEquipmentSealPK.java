/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceEquipmentSealColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceEquipmentSealPK
extends Pk {
    public Object invoiceGid;
    public Object sealSequence;
    public Object seqNumber;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceEquipmentSealPK() {
    }

    public InvoiceEquipmentSealPK(String invoiceGid, Integer sealSequence, Long seqNumber) {
        this.invoiceGid = this.notNull(InvoiceEquipmentSealColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.sealSequence = this.notNull(InvoiceEquipmentSealColumns.sealSequence.convertToDB(sealSequence), "sealSequence");
        this.seqNumber = this.notNull(InvoiceEquipmentSealColumns.seqNumber.convertToDB(seqNumber), "seqNumber");
    }

    public InvoiceEquipmentSealPK(int dummy, Object invoiceGid, Object sealSequence, Object seqNumber) {
        this(dummy, invoiceGid, sealSequence, seqNumber, null);
    }

    public InvoiceEquipmentSealPK(int dummy, Object invoiceGid, Object sealSequence, Object seqNumber, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.sealSequence = sealSequence;
        this.seqNumber = seqNumber;
        this.transaction = transaction;
    }

    public InvoiceEquipmentSealPK(InvoiceEquipmentSealPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.sealSequence = otherPk.sealSequence;
        this.seqNumber = otherPk.seqNumber;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.sealSequence != null ? String.valueOf(this.sealSequence) : "") + " " + (this.seqNumber != null ? String.valueOf(this.seqNumber) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.sealSequence != null ? String.valueOf(this.sealSequence) : "") + "|" + (this.seqNumber != null ? String.valueOf(this.seqNumber) : "");
    }

    public static InvoiceEquipmentSealPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceEquipmentSealPK(gids[0], Integer.valueOf(gids[1]), Long.valueOf(gids[2])) : null;
    }

    public static InvoiceEquipmentSealPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceEquipmentSealPK(gids[0], Integer.valueOf(gids[1]), Long.valueOf(gids[2])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.sealSequence.hashCode() + this.seqNumber.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceEquipmentSealPK)) {
            return false;
        }
        InvoiceEquipmentSealPK otherPk = (InvoiceEquipmentSealPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.sealSequence, this.sealSequence) && Functions.equals(otherPk.seqNumber, this.seqNumber) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceEquipmentSealHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.sealSequence;
            }
            case 2: {
                return this.seqNumber;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceEquipmentSealColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceEquipmentSealColumns.sealSequence.convertFromDB(this.sealSequence);
            }
            case 2: {
                return InvoiceEquipmentSealColumns.seqNumber.convertFromDB(this.seqNumber);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceEquipmentSealPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceEquipmentSeal";
        }

        @Override
        public final String getTableName() {
            return "invoice_equipment_seal";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "seal_sequence", "seq_number"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceEquipmentSealPK result = new InvoiceEquipmentSealPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
