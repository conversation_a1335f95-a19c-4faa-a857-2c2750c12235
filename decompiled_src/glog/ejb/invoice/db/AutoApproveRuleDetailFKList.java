/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AutoApproveRuleDetailFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("approveToCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleDetailData";
            }

            @Override
            public String getPkField() {
                return "approveToCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("allowCurrBelowCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleDetailData";
            }

            @Override
            public String getPkField() {
                return "allowCurrBelowCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("autoApproveRuleGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleDetailData";
            }

            @Override
            public String getPkField() {
                return "autoApproveRuleGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getFkDataField() {
                return "autoApproveRuleGid";
            }
        });
        fks.put("allowCurrAboveCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleDetailData";
            }

            @Override
            public String getPkField() {
                return "allowCurrAboveCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
    }
}
