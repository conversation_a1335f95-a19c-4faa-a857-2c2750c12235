/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class CommercialInvoiceFKList.2
implements Fk {
    CommercialInvoiceFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "CommercialInvoiceData";
    }

    @Override
    public String getPkField() {
        return "commercialPaymentCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "PaymentMethodCodeData";
    }

    @Override
    public String getFkDataField() {
        return "paymentMethodCodeGid";
    }
}
