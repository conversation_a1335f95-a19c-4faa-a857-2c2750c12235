/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CommercialInvChargeCodePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class CommercialInvChargeCodeData
extends BeanData {
    public String commercialInvChargeCodeGid;
    public String commercialInvChargeCodeXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(CommercialInvChargeCodeData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field commercialInvChargeCodeGidField = beanDataFields[0];
    public static Field commercialInvChargeCodeXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public CommercialInvChargeCodeData() {
    }

    public CommercialInvChargeCodeData(CommercialInvChargeCodeData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getCommercialInvChargeCodePK();
    }

    @Legacy
    public CommercialInvChargeCodePK getCommercialInvChargeCodePK() {
        if (this.commercialInvChargeCodeGid == null) {
            return null;
        }
        return new CommercialInvChargeCodePK(this.commercialInvChargeCodeGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setCommercialInvChargeCodePK((CommercialInvChargeCodePK)pk);
    }

    @Legacy
    public void setCommercialInvChargeCodePK(CommercialInvChargeCodePK pk) {
        this.commercialInvChargeCodeGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.CommercialInvChargeCodeQueryGen";
    }

    public static CommercialInvChargeCodeData load(Connection conn, CommercialInvChargeCodePK pk) throws GLException {
        return (CommercialInvChargeCodeData)CommercialInvChargeCodeData.load(conn, pk, CommercialInvChargeCodeData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return CommercialInvChargeCodeData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return CommercialInvChargeCodeData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return CommercialInvChargeCodeData.load(conn, whereClause, prepareArguments, fetchSize, CommercialInvChargeCodeData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return CommercialInvChargeCodeData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return CommercialInvChargeCodeData.load(conn, fromWhere, alias, prepareArguments, fetchSize, CommercialInvChargeCodeData.class);
    }
}
