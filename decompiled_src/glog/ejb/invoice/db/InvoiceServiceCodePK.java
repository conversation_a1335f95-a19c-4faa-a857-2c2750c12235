/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceServiceCodeColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoiceServiceCodePK
extends Pk {
    public Object invoiceServiceCodeGid;
    public transient Object invoiceServiceCodeXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceServiceCodePK() {
    }

    public InvoiceServiceCodePK(String invoiceServiceCodeGid) {
        this.invoiceServiceCodeGid = this.notNull(InvoiceServiceCodeColumns.invoiceServiceCodeGid.convertToDB(invoiceServiceCodeGid), "invoiceServiceCodeGid");
    }

    public InvoiceServiceCodePK(String domainName, String invoiceServiceCodeXid) {
        this.domainName = domainName;
        this.invoiceServiceCodeXid = invoiceServiceCodeXid;
        this.invoiceServiceCodeGid = InvoiceServiceCodePK.concatForGid(domainName, invoiceServiceCodeXid);
    }

    public InvoiceServiceCodePK(int dummy, Object invoiceServiceCodeGid) {
        this(dummy, invoiceServiceCodeGid, null);
    }

    public InvoiceServiceCodePK(int dummy, Object invoiceServiceCodeGid, Object transaction) {
        this.invoiceServiceCodeGid = invoiceServiceCodeGid;
        this.transaction = transaction;
    }

    public InvoiceServiceCodePK(InvoiceServiceCodePK otherPk, Object transaction) {
        this.invoiceServiceCodeGid = otherPk.invoiceServiceCodeGid;
        this.invoiceServiceCodeXid = otherPk.invoiceServiceCodeXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceServiceCodeGid != null ? String.valueOf(this.invoiceServiceCodeGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceServiceCodeGid != null ? String.valueOf(this.invoiceServiceCodeGid) : "";
    }

    public static InvoiceServiceCodePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceServiceCodePK(gids[0]) : null;
    }

    public static InvoiceServiceCodePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceServiceCodePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceServiceCodeGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceServiceCodePK)) {
            return false;
        }
        InvoiceServiceCodePK otherPk = (InvoiceServiceCodePK)other;
        return Functions.equals(otherPk.invoiceServiceCodeGid, this.invoiceServiceCodeGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceServiceCodeHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceServiceCodeGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceServiceCodeColumns.invoiceServiceCodeGid.convertFromDB(this.invoiceServiceCodeGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceServiceCodePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoiceServiceCodePK requires a non-null XID to generate a GID");
            }
            InvoiceServiceCodePK invoiceServiceCodePK = new InvoiceServiceCodePK(domain, xid);
            return invoiceServiceCodePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceServiceCodePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoiceServiceCodePK invoiceServiceCodePK = InvoiceServiceCodePK.newPK(domainName, xid, connection);
            return invoiceServiceCodePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceServiceCodePK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceServiceCode";
        }

        @Override
        public final String getTableName() {
            return "invoice_service_code";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_service_code_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceServiceCodePK result = new InvoiceServiceCodePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
