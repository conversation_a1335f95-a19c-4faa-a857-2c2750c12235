/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AdjustmentReasonPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AdjustmentReasonData
extends BeanData {
    public String adjustmentReasonGid;
    public String adjustmentReasonXid;
    public String domainName;
    public String description;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AdjustmentReasonData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field adjustmentReasonGidField = beanDataFields[0];
    public static Field adjustmentReasonXidField = beanDataFields[1];
    public static Field domainNameField = beanDataFields[2];
    public static Field descriptionField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AdjustmentReasonData() {
    }

    public AdjustmentReasonData(AdjustmentReasonData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAdjustmentReasonPK();
    }

    @Legacy
    public AdjustmentReasonPK getAdjustmentReasonPK() {
        if (this.adjustmentReasonGid == null) {
            return null;
        }
        return new AdjustmentReasonPK(this.adjustmentReasonGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAdjustmentReasonPK((AdjustmentReasonPK)pk);
    }

    @Legacy
    public void setAdjustmentReasonPK(AdjustmentReasonPK pk) {
        this.adjustmentReasonGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.AdjustmentReasonQueryGen";
    }

    public static AdjustmentReasonData load(Connection conn, AdjustmentReasonPK pk) throws GLException {
        return (AdjustmentReasonData)AdjustmentReasonData.load(conn, pk, AdjustmentReasonData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AdjustmentReasonData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AdjustmentReasonData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AdjustmentReasonData.load(conn, whereClause, prepareArguments, fetchSize, AdjustmentReasonData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AdjustmentReasonData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AdjustmentReasonData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AdjustmentReasonData.class);
    }
}
