/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumQualPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceStopRefnumQualData
extends BeanData {
    public String invoiceStopRefnumQualGid;
    public String invoiceStopRefnumQualXid;
    public String invoiceStopRefnumDesc;
    public String defaultRefnumBnTypeGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStopRefnumQualData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceStopRefnumQualGidField = beanDataFields[0];
    public static Field invoiceStopRefnumQualXidField = beanDataFields[1];
    public static Field invoiceStopRefnumDescField = beanDataFields[2];
    public static Field defaultRefnumBnTypeGidField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceStopRefnumQualData() {
    }

    public InvoiceStopRefnumQualData(InvoiceStopRefnumQualData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceStopRefnumQualPK();
    }

    @Legacy
    public InvoiceStopRefnumQualPK getInvoiceStopRefnumQualPK() {
        if (this.invoiceStopRefnumQualGid == null) {
            return null;
        }
        return new InvoiceStopRefnumQualPK(this.invoiceStopRefnumQualGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceStopRefnumQualPK((InvoiceStopRefnumQualPK)pk);
    }

    @Legacy
    public void setInvoiceStopRefnumQualPK(InvoiceStopRefnumQualPK pk) {
        this.invoiceStopRefnumQualGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceStopRefnumQualQueryGen";
    }

    public static InvoiceStopRefnumQualData load(Connection conn, InvoiceStopRefnumQualPK pk) throws GLException {
        return (InvoiceStopRefnumQualData)InvoiceStopRefnumQualData.load(conn, pk, InvoiceStopRefnumQualData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceStopRefnumQualData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceStopRefnumQualData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopRefnumQualData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceStopRefnumQualData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceStopRefnumQualData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopRefnumQualData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceStopRefnumQualData.class);
    }
}
