/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlCodeMapPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class GlCodeMapData
extends BeanData {
    public String keyValue;
    public String domainName;
    public String glLookupKeyGid;
    public String generalLedgerGid;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlCodeMapData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field keyValueField = beanDataFields[0];
    public static Field domainNameField = beanDataFields[1];
    public static Field glLookupKeyGidField = beanDataFields[2];
    public static Field generalLedgerGidField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public GlCodeMapData() {
    }

    public GlCodeMapData(GlCodeMapData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getGlCodeMapPK();
    }

    @Legacy
    public GlCodeMapPK getGlCodeMapPK() {
        if (this.domainName == null) {
            return null;
        }
        if (this.glLookupKeyGid == null) {
            return null;
        }
        if (this.keyValue == null) {
            return null;
        }
        return new GlCodeMapPK(this.domainName, this.glLookupKeyGid, this.keyValue);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setGlCodeMapPK((GlCodeMapPK)pk);
    }

    @Legacy
    public void setGlCodeMapPK(GlCodeMapPK pk) {
        this.domainName = (String)pk.getAppValue(0);
        this.glLookupKeyGid = (String)pk.getAppValue(1);
        this.keyValue = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.GlCodeMapQueryGen";
    }

    public static GlCodeMapData load(Connection conn, GlCodeMapPK pk) throws GLException {
        return (GlCodeMapData)GlCodeMapData.load(conn, pk, GlCodeMapData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return GlCodeMapData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return GlCodeMapData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlCodeMapData.load(conn, whereClause, prepareArguments, fetchSize, GlCodeMapData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return GlCodeMapData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlCodeMapData.load(conn, fromWhere, alias, prepareArguments, fetchSize, GlCodeMapData.class);
    }
}
