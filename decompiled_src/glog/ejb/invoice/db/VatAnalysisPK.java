/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VatAnalysisColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class VatAnalysisPK
extends Pk {
    public Object invoiceGid;
    public Object vatCodeGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public VatAnalysisPK() {
    }

    public VatAnalysisPK(String invoiceGid, String vatCodeGid) {
        this.invoiceGid = this.notNull(VatAnalysisColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.vatCodeGid = this.notNull(VatAnalysisColumns.vatCodeGid.convertToDB(vatCodeGid), "vatCodeGid");
    }

    public VatAnalysisPK(int dummy, Object invoiceGid, Object vatCodeGid) {
        this(dummy, invoiceGid, vatCodeGid, null);
    }

    public VatAnalysisPK(int dummy, Object invoiceGid, Object vatCodeGid, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.vatCodeGid = vatCodeGid;
        this.transaction = transaction;
    }

    public VatAnalysisPK(VatAnalysisPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.vatCodeGid = otherPk.vatCodeGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.vatCodeGid != null ? String.valueOf(this.vatCodeGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.vatCodeGid != null ? String.valueOf(this.vatCodeGid) : "");
    }

    public static VatAnalysisPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new VatAnalysisPK(gids[0], gids[1]) : null;
    }

    public static VatAnalysisPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new VatAnalysisPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.vatCodeGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof VatAnalysisPK)) {
            return false;
        }
        VatAnalysisPK otherPk = (VatAnalysisPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.vatCodeGid, this.vatCodeGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.VatAnalysisHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.vatCodeGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return VatAnalysisColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return VatAnalysisColumns.vatCodeGid.convertFromDB(this.vatCodeGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return VatAnalysisPK.class;
        }

        @Override
        public final String getEntity() {
            return "VatAnalysis";
        }

        @Override
        public final String getTableName() {
            return "vat_analysis";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "vat_code_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            VatAnalysisPK result = new VatAnalysisPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
