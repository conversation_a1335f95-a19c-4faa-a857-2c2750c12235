/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveTolerancePK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class LineApproveToleranceData
extends BeanData {
    public String lineApproveToleranceGid;
    public String lineApproveToleranceXid;
    public Boolean isInvoiceRefnumMatchAll = Boolean.valueOf("true");
    public Boolean isShipmentRefnumMatchAll = Boolean.valueOf("true");
    public String domainName;
    public LocalDate effectiveDate;
    public LocalDate expirationDate;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineApproveToleranceData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field lineApproveToleranceGidField = beanDataFields[0];
    public static Field lineApproveToleranceXidField = beanDataFields[1];
    public static Field isInvoiceRefnumMatchAllField = beanDataFields[2];
    public static Field isShipmentRefnumMatchAllField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];
    public static Field effectiveDateField = beanDataFields[5];
    public static Field expirationDateField = beanDataFields[6];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public LineApproveToleranceData() {
    }

    public LineApproveToleranceData(LineApproveToleranceData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getLineApproveTolerancePK();
    }

    @Legacy
    public LineApproveTolerancePK getLineApproveTolerancePK() {
        if (this.lineApproveToleranceGid == null) {
            return null;
        }
        return new LineApproveTolerancePK(this.lineApproveToleranceGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setLineApproveTolerancePK((LineApproveTolerancePK)pk);
    }

    @Legacy
    public void setLineApproveTolerancePK(LineApproveTolerancePK pk) {
        this.lineApproveToleranceGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.LineApproveToleranceQueryGen";
    }

    public static LineApproveToleranceData load(Connection conn, LineApproveTolerancePK pk) throws GLException {
        return (LineApproveToleranceData)LineApproveToleranceData.load(conn, pk, LineApproveToleranceData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return LineApproveToleranceData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return LineApproveToleranceData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveToleranceData.load(conn, whereClause, prepareArguments, fetchSize, LineApproveToleranceData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return LineApproveToleranceData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveToleranceData.load(conn, fromWhere, alias, prepareArguments, fetchSize, LineApproveToleranceData.class);
    }
}
