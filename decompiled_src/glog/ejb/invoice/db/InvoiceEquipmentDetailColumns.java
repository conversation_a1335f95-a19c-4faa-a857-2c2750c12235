/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;
import glog.util.uom.data.Length;
import glog.util.uom.data.Temperature;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;

public class InvoiceEquipmentDetailColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn seqNumber = new SqlColumn(Long.class, "seq_number", 10, 0, 1, null, 1, null);
    public static SqlColumn equipmentInitialNumber = new SqlColumn(String.class, "equipment_initial_number", 50, 0, 0, null, 2, null);
    public static SqlColumn sEquipmentGid = new SqlColumn(String.class, "s_equipment_gid", 101, 0, 0, null, 3, null);
    public static SqlColumn equipmentPrefix = new SqlColumn(String.class, "equipment_prefix", 20, 0, 0, null, 4, null);
    public static SqlColumn equipmentNumber = new SqlColumn(String.class, "equipment_number", 30, 0, 0, null, 5, null);
    public static SqlColumn isoEquipmentTypeIdentifier = new SqlColumn(String.class, "iso_equipment_type_identifier", 101, 0, 0, null, 6, null);
    public static SqlColumn descriptionCode = new SqlColumn(String.class, "description_code", 60, 0, 0, null, 7, null);
    public static SqlColumn equipmentOwnerIdentifier = new SqlColumn(String.class, "equipment_owner_identifier", 101, 0, 0, null, 8, null);
    public static SqlColumn ownershipCode = new SqlColumn(String.class, "ownership_code", 20, 0, 0, null, 9, null);
    public static SqlColumn length = new SqlColumn(Length.class, "length", 22, 1, 0, null, 10, "length_base");
    public static SqlColumn width = new SqlColumn(Length.class, "width", 22, 1, 0, null, 11, "width_base");
    public static SqlColumn height = new SqlColumn(Length.class, "height", 22, 1, 0, null, 12, "height_base");
    public static SqlColumn weight = new SqlColumn(Weight.class, "weight", 22, 2, 0, null, 13, "weight_base");
    public static SqlColumn volume = new SqlColumn(Volume.class, "volume", 22, 2, 0, null, 14, "volume_base");
    public static SqlColumn tareWeight = new SqlColumn(Weight.class, "tare_weight", 22, 2, 0, null, 15, "tare_weight_base");
    public static SqlColumn dunnage = new SqlColumn(Weight.class, "dunnage", 22, 2, 0, null, 16, "dunnage_base");
    public static SqlColumn unitCount = new SqlColumn(Long.class, "unit_count", 10, 0, 0, null, 17, null);
    public static SqlColumn weightQualifier = new SqlColumn(String.class, "weight_qualifier", 2, 0, 0, null, 18, null);
    public static SqlColumn minimumTemperature = new SqlColumn(Temperature.class, "minimum_temperature", 22, 3, 0, null, 19, "minimum_temperature_base");
    public static SqlColumn maximumTemperature = new SqlColumn(Temperature.class, "maximum_temperature", 22, 3, 0, null, 20, "maximum_temperature_base");
    public static SqlColumn percentHumidityAllowed = new SqlColumn(Integer.class, "percent_humidity_allowed", 3, 0, 0, null, 21, null);
    public static SqlColumn ventSettingCode = new SqlColumn(String.class, "vent_setting_code", 1, 0, 0, null, 22, null);
    public static SqlColumn weightAllowance = new SqlColumn(Weight.class, "weight_allowance", 22, 2, 0, null, 23, "weight_allowance_base");
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 24, null);
}
