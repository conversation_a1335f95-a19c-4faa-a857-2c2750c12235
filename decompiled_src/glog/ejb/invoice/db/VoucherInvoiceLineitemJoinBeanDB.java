/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinColumns;
import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinData;
import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class VoucherInvoiceLineitemJoinBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object voucherGid;
    public Object amountPaid;
    public Object adjustmentReasonGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient VoucherInvoiceLineitemJoinPK pk;
    protected transient VoucherInvoiceLineitemJoinData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherInvoiceLineitemJoinBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static VoucherInvoiceLineitemJoinPK.Callback theCall = new VoucherInvoiceLineitemJoinPK.Callback();

    public VoucherInvoiceLineitemJoinBeanDB() {
        super(false);
    }

    public VoucherInvoiceLineitemJoinPK ejbCreate(VoucherInvoiceLineitemJoinData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(VoucherInvoiceLineitemJoinData data) throws CreateException {
        this.ejbPostCreator();
    }

    public VoucherInvoiceLineitemJoinPK ejbFindByPrimaryKey(VoucherInvoiceLineitemJoinPK pk) throws FinderException {
        return (VoucherInvoiceLineitemJoinPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(VoucherInvoiceLineitemJoinPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<VoucherInvoiceLineitemJoinPK> v = new Vector<VoucherInvoiceLineitemJoinPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(VoucherInvoiceLineitemJoinPK pk, VoucherInvoiceLineitemJoinData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(VoucherInvoiceLineitemJoinPK pk, VoucherInvoiceLineitemJoinData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(VoucherInvoiceLineitemJoinPK pk, VoucherInvoiceLineitemJoinData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (VoucherInvoiceLineitemJoinPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(VoucherInvoiceLineitemJoinColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(VoucherInvoiceLineitemJoinColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(VoucherInvoiceLineitemJoinColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(VoucherInvoiceLineitemJoinColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        VoucherInvoiceLineitemJoinPK pk = (VoucherInvoiceLineitemJoinPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.lineitemSeqNo = pk.lineitemSeqNo;
        this.voucherGid = pk.voucherGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        VoucherInvoiceLineitemJoinPK pk = new VoucherInvoiceLineitemJoinPK(0, this.invoiceGid, this.lineitemSeqNo, this.voucherGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.VoucherInvoiceLineitemJoin";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public VoucherInvoiceLineitemJoinData getData() throws GLException {
        try {
            VoucherInvoiceLineitemJoinData retval = new VoucherInvoiceLineitemJoinData();
            retval.getFromBean(this, VoucherInvoiceLineitemJoinColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(VoucherInvoiceLineitemJoinData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(VoucherInvoiceLineitemJoinData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            VoucherInvoiceLineitemJoinData oldData = modified ? this.getData() : null;
            data.setToBean(this, VoucherInvoiceLineitemJoinColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return VoucherInvoiceLineitemJoinData.class;
    }

    @Override
    public Class getPkClass() {
        return VoucherInvoiceLineitemJoinPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)VoucherInvoiceLineitemJoinColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.invoiceGid = VoucherInvoiceLineitemJoinColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getLineitemSeqNo() throws GLException {
        try {
            return (Integer)VoucherInvoiceLineitemJoinColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineitemSeqNo(Integer lineitemSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineitemSeqNo;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.lineitemSeqNo = VoucherInvoiceLineitemJoinColumns.lineitemSeqNo.convertToDB(lineitemSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineitemSeqNo", Integer.class, oldValue, this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVoucherGid() throws GLException {
        try {
            return (String)VoucherInvoiceLineitemJoinColumns.voucherGid.convertFromDB(this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherGid(String voucherGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherGid;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.voucherGid = VoucherInvoiceLineitemJoinColumns.voucherGid.convertToDB(voucherGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherGid", String.class, oldValue, this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getAmountPaid() throws GLException {
        try {
            return (Currency)VoucherInvoiceLineitemJoinColumns.amountPaid.convertFromDB(this.amountPaid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAmountPaid(Currency amountPaid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.amountPaid;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.amountPaid = VoucherInvoiceLineitemJoinColumns.amountPaid.convertToDB(amountPaid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "amountPaid", Currency.class, oldValue, this.amountPaid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAdjustmentReasonGid() throws GLException {
        try {
            return (String)VoucherInvoiceLineitemJoinColumns.adjustmentReasonGid.convertFromDB(this.adjustmentReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAdjustmentReasonGid(String adjustmentReasonGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.adjustmentReasonGid;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.adjustmentReasonGid = VoucherInvoiceLineitemJoinColumns.adjustmentReasonGid.convertToDB(adjustmentReasonGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "adjustmentReasonGid", String.class, oldValue, this.adjustmentReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)VoucherInvoiceLineitemJoinColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.exchangeRateDate = VoucherInvoiceLineitemJoinColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)VoucherInvoiceLineitemJoinColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.exchangeRateGid = VoucherInvoiceLineitemJoinColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)VoucherInvoiceLineitemJoinColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            VoucherInvoiceLineitemJoinData data = this.getData();
            this.domainName = VoucherInvoiceLineitemJoinColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
