/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlCodeMapColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class GlCodeMapPK
extends Pk {
    public Object domainName;
    public Object glLookupKeyGid;
    public Object keyValue;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public GlCodeMapPK() {
    }

    public GlCodeMapPK(String domainName, String glLookupKeyGid, String keyValue) {
        this.domainName = this.notNull(GlCodeMapColumns.domainName.convertToDB(domainName), "domainName");
        this.glLookupKeyGid = this.notNull(GlCodeMapColumns.glLookupKeyGid.convertToDB(glLookupKeyGid), "glLookupKeyGid");
        this.keyValue = this.notNull(GlCodeMapColumns.keyValue.convertToDB(keyValue), "keyValue");
    }

    public GlCodeMapPK(int dummy, Object domainName, Object glLookupKeyGid, Object keyValue) {
        this(dummy, domainName, glLookupKeyGid, keyValue, null);
    }

    public GlCodeMapPK(int dummy, Object domainName, Object glLookupKeyGid, Object keyValue, Object transaction) {
        this.domainName = domainName;
        this.glLookupKeyGid = glLookupKeyGid;
        this.keyValue = keyValue;
        this.transaction = transaction;
    }

    public GlCodeMapPK(GlCodeMapPK otherPk, Object transaction) {
        this.domainName = otherPk.domainName;
        this.glLookupKeyGid = otherPk.glLookupKeyGid;
        this.keyValue = otherPk.keyValue;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.domainName != null ? String.valueOf(this.domainName) : "") + " " + (this.glLookupKeyGid != null ? String.valueOf(this.glLookupKeyGid) : "") + " " + (this.keyValue != null ? String.valueOf(this.keyValue) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.domainName != null ? String.valueOf(this.domainName) : "") + "|" + (this.glLookupKeyGid != null ? String.valueOf(this.glLookupKeyGid) : "") + "|" + (this.keyValue != null ? String.valueOf(this.keyValue) : "");
    }

    public static GlCodeMapPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new GlCodeMapPK(gids[0], gids[1], gids[2]) : null;
    }

    public static GlCodeMapPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new GlCodeMapPK(gids[0], gids[1], gids[2]) : null;
    }

    public int hashCode() {
        return this.domainName.hashCode() + this.glLookupKeyGid.hashCode() + this.keyValue.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof GlCodeMapPK)) {
            return false;
        }
        GlCodeMapPK otherPk = (GlCodeMapPK)other;
        return Functions.equals(otherPk.domainName, this.domainName) && Functions.equals(otherPk.glLookupKeyGid, this.glLookupKeyGid) && Functions.equals(otherPk.keyValue, this.keyValue) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.GlCodeMapHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.domainName;
            }
            case 1: {
                return this.glLookupKeyGid;
            }
            case 2: {
                return this.keyValue;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return GlCodeMapColumns.domainName.convertFromDB(this.domainName);
            }
            case 1: {
                return GlCodeMapColumns.glLookupKeyGid.convertFromDB(this.glLookupKeyGid);
            }
            case 2: {
                return GlCodeMapColumns.keyValue.convertFromDB(this.keyValue);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return GlCodeMapPK.class;
        }

        @Override
        public final String getEntity() {
            return "GlCodeMap";
        }

        @Override
        public final String getTableName() {
            return "gl_code_map";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"domain_name", "gl_lookup_key_gid", "key_value"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            GlCodeMapPK result = new GlCodeMapPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
