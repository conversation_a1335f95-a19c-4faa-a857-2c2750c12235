/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceTextPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceTextData
extends BeanData {
    public String invoiceGid;
    public String textTemplateGid;
    public String documentDefGid = String.valueOf("ALL");
    public String textOverride;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceTextData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field textTemplateGidField = beanDataFields[1];
    public static Field documentDefGidField = beanDataFields[2];
    public static Field textOverrideField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceTextData() {
    }

    public InvoiceTextData(InvoiceTextData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceTextPK();
    }

    @Legacy
    public InvoiceTextPK getInvoiceTextPK() {
        if (this.documentDefGid == null) {
            return null;
        }
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.textTemplateGid == null) {
            return null;
        }
        return new InvoiceTextPK(this.documentDefGid, this.invoiceGid, this.textTemplateGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceTextPK((InvoiceTextPK)pk);
    }

    @Legacy
    public void setInvoiceTextPK(InvoiceTextPK pk) {
        this.documentDefGid = (String)pk.getAppValue(0);
        this.invoiceGid = (String)pk.getAppValue(1);
        this.textTemplateGid = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceTextQueryGen";
    }

    public static InvoiceTextData load(Connection conn, InvoiceTextPK pk) throws GLException {
        return (InvoiceTextData)InvoiceTextData.load(conn, pk, InvoiceTextData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceTextData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceTextData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceTextData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceTextData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceTextData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceTextData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceTextData.class);
    }
}
