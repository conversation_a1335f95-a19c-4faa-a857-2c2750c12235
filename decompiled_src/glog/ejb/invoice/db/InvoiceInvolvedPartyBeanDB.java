/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceInvolvedPartyColumns;
import glog.ejb.invoice.db.InvoiceInvolvedPartyData;
import glog.ejb.invoice.db.InvoiceInvolvedPartyPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceInvolvedPartyBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object involvedPartyQualGid;
    public Object involvedPartyContactGid;
    public Object comMethodGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceInvolvedPartyPK pk;
    protected transient InvoiceInvolvedPartyData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceInvolvedPartyBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceInvolvedPartyPK.Callback theCall = new InvoiceInvolvedPartyPK.Callback();

    public InvoiceInvolvedPartyBeanDB() {
        super(false);
    }

    public InvoiceInvolvedPartyPK ejbCreate(InvoiceInvolvedPartyData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceInvolvedPartyData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceInvolvedPartyPK ejbFindByPrimaryKey(InvoiceInvolvedPartyPK pk) throws FinderException {
        return (InvoiceInvolvedPartyPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceInvolvedPartyPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceInvolvedPartyPK> v = new Vector<InvoiceInvolvedPartyPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceInvolvedPartyPK pk, InvoiceInvolvedPartyData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceInvolvedPartyPK pk, InvoiceInvolvedPartyData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceInvolvedPartyPK pk, InvoiceInvolvedPartyData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceInvolvedPartyPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceInvolvedPartyColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceInvolvedPartyColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceInvolvedPartyColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceInvolvedPartyColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceInvolvedPartyPK pk = (InvoiceInvolvedPartyPK)genericPk;
        this.comMethodGid = pk.comMethodGid;
        this.invoiceGid = pk.invoiceGid;
        this.involvedPartyContactGid = pk.involvedPartyContactGid;
        this.involvedPartyQualGid = pk.involvedPartyQualGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceInvolvedPartyPK pk = new InvoiceInvolvedPartyPK(0, this.comMethodGid, this.invoiceGid, this.involvedPartyContactGid, this.involvedPartyQualGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceInvolvedParty";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceInvolvedPartyData getData() throws GLException {
        try {
            InvoiceInvolvedPartyData retval = new InvoiceInvolvedPartyData();
            retval.getFromBean(this, InvoiceInvolvedPartyColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceInvolvedPartyData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceInvolvedPartyData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceInvolvedPartyData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceInvolvedPartyColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceInvolvedPartyData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceInvolvedPartyPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceInvolvedPartyColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceInvolvedPartyData data = this.getData();
            this.invoiceGid = InvoiceInvolvedPartyColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvolvedPartyQualGid() throws GLException {
        try {
            return (String)InvoiceInvolvedPartyColumns.involvedPartyQualGid.convertFromDB(this.involvedPartyQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvolvedPartyQualGid(String involvedPartyQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.involvedPartyQualGid;
            InvoiceInvolvedPartyData data = this.getData();
            this.involvedPartyQualGid = InvoiceInvolvedPartyColumns.involvedPartyQualGid.convertToDB(involvedPartyQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "involvedPartyQualGid", String.class, oldValue, this.involvedPartyQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvolvedPartyContactGid() throws GLException {
        try {
            return (String)InvoiceInvolvedPartyColumns.involvedPartyContactGid.convertFromDB(this.involvedPartyContactGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvolvedPartyContactGid(String involvedPartyContactGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.involvedPartyContactGid;
            InvoiceInvolvedPartyData data = this.getData();
            this.involvedPartyContactGid = InvoiceInvolvedPartyColumns.involvedPartyContactGid.convertToDB(involvedPartyContactGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "involvedPartyContactGid", String.class, oldValue, this.involvedPartyContactGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getComMethodGid() throws GLException {
        try {
            return (String)InvoiceInvolvedPartyColumns.comMethodGid.convertFromDB(this.comMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setComMethodGid(String comMethodGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.comMethodGid;
            InvoiceInvolvedPartyData data = this.getData();
            this.comMethodGid = InvoiceInvolvedPartyColumns.comMethodGid.convertToDB(comMethodGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "comMethodGid", String.class, oldValue, this.comMethodGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceInvolvedPartyColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceInvolvedPartyData data = this.getData();
            this.domainName = InvoiceInvolvedPartyColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
