/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceTextColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceTextPK
extends Pk {
    public Object documentDefGid;
    public Object invoiceGid;
    public Object textTemplateGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceTextPK() {
    }

    public InvoiceTextPK(String documentDefGid, String invoiceGid, String textTemplateGid) {
        this.documentDefGid = this.notNull(InvoiceTextColumns.documentDefGid.convertToDB(documentDefGid), "documentDefGid");
        this.invoiceGid = this.notNull(InvoiceTextColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.textTemplateGid = this.notNull(InvoiceTextColumns.textTemplateGid.convertToDB(textTemplateGid), "textTemplateGid");
    }

    public InvoiceTextPK(int dummy, Object documentDefGid, Object invoiceGid, Object textTemplateGid) {
        this(dummy, documentDefGid, invoiceGid, textTemplateGid, null);
    }

    public InvoiceTextPK(int dummy, Object documentDefGid, Object invoiceGid, Object textTemplateGid, Object transaction) {
        this.documentDefGid = documentDefGid;
        this.invoiceGid = invoiceGid;
        this.textTemplateGid = textTemplateGid;
        this.transaction = transaction;
    }

    public InvoiceTextPK(InvoiceTextPK otherPk, Object transaction) {
        this.documentDefGid = otherPk.documentDefGid;
        this.invoiceGid = otherPk.invoiceGid;
        this.textTemplateGid = otherPk.textTemplateGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.documentDefGid != null ? String.valueOf(this.documentDefGid) : "") + " " + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.textTemplateGid != null ? String.valueOf(this.textTemplateGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.documentDefGid != null ? String.valueOf(this.documentDefGid) : "") + "|" + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.textTemplateGid != null ? String.valueOf(this.textTemplateGid) : "");
    }

    public static InvoiceTextPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceTextPK(gids[0], gids[1], gids[2]) : null;
    }

    public static InvoiceTextPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceTextPK(gids[0], gids[1], gids[2]) : null;
    }

    public int hashCode() {
        return this.documentDefGid.hashCode() + this.invoiceGid.hashCode() + this.textTemplateGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceTextPK)) {
            return false;
        }
        InvoiceTextPK otherPk = (InvoiceTextPK)other;
        return Functions.equals(otherPk.documentDefGid, this.documentDefGid) && Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.textTemplateGid, this.textTemplateGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceTextHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.documentDefGid;
            }
            case 1: {
                return this.invoiceGid;
            }
            case 2: {
                return this.textTemplateGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceTextColumns.documentDefGid.convertFromDB(this.documentDefGid);
            }
            case 1: {
                return InvoiceTextColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 2: {
                return InvoiceTextColumns.textTemplateGid.convertFromDB(this.textTemplateGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceTextPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceText";
        }

        @Override
        public final String getTableName() {
            return "invoice_text";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"document_def_gid", "invoice_gid", "text_template_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceTextPK result = new InvoiceTextPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
            return result;
        }
    }
}
