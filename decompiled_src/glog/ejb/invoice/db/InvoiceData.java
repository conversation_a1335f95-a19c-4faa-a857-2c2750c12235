/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.Invoice;
import glog.ejb.invoice.db.InvoicePK;
import glog.server.status.ObjectStatus;
import glog.server.status.StatusFunction;
import glog.server.workflow.status.StatusError;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceData
extends BeanData {
    public String invoiceGid;
    public String invoiceXid;
    public String invoiceType;
    public String invoiceSource;
    public String enteredByGlUserGid;
    public String invoiceNumber;
    public String servprovAliasQualGid;
    public String servprovAliasValue;
    public String correctionCodeId;
    public LocalTimestamp invoiceDate;
    public String currencyGid;
    public Currency netAmountDue;
    public LocalTimestamp netDueDate;
    public String paymentMethodCodeGid;
    public LocalTimestamp startDate;
    public LocalTimestamp endDate;
    public String invoiceServiceCodeGid;
    public String incoTermGid;
    public Currency discountAmount;
    public Double discountPercentage;
    public Integer discountDaysDue;
    public LocalDate discountDueDate;
    public String originStationCity;
    public String originStationFsac;
    public String originStationSplc;
    public String originStationProvinceCode;
    public String originStationPostalCode;
    public String originCountryCode3Gid;
    public String destStationCity;
    public String destStationFsac;
    public String destStationSplc;
    public String destStationProvinceCode;
    public String destStationPostalCode;
    public String destCountryCode3Gid;
    public LocalDate letterOfCreditExpDate;
    public LocalDate letterOfCreditIssueDate;
    public String letterOfCreditNumber;
    public String vesselCodeQualifier;
    public String vesselCode;
    public String vesselCountryCode3Gid;
    public String vesselGid;
    public String vesOpServprovAliasQualGid;
    public String vesOpServprovAliasValue;
    public String voyageNumber;
    public String parentInvoiceGid;
    public LocalTimestamp dateReceived;
    public String supplyCountryCode3Gid;
    public String servprovVatRegNoGid;
    public String customerVatRegNoGid;
    public String vatExemptValue = String.valueOf("NOT_EXEMPT");
    public Currency netAmtDueWithTax;
    public Boolean isPassThrough = Boolean.valueOf("false");
    public String consolidationType = String.valueOf("STANDARD");
    public Boolean isFixedCost = Boolean.valueOf("false");
    public Currency baseCharge;
    public Currency otherCharge;
    public Boolean isCreditNote = Boolean.valueOf("false");
    public String userDefined1IconGid;
    public String userDefined2IconGid;
    public String userDefined3IconGid;
    public String userDefined4IconGid;
    public String userDefined5IconGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String servprovGid;
    public LocalTimestamp sailDate;
    public LocalTimestamp sailCutoffDate;
    public LocalTimestamp railDate;
    public Boolean isHazardous = Boolean.valueOf("false");
    public Boolean isTemperatureControl = Boolean.valueOf("false");
    public String invoicingProcess = String.valueOf("S");
    public Boolean isVatAnalysisFixed = Boolean.valueOf("false");
    public String originalInvoiceGid;
    public LocalDate glDate;
    public String domainName;
    public String attribute1;
    public String attribute2;
    public String attribute3;
    public String attribute4;
    public String attribute5;
    public String attribute6;
    public String attribute7;
    public String attribute8;
    public String attribute9;
    public String attribute10;
    public String attribute11;
    public String attribute12;
    public String attribute13;
    public String attribute14;
    public String attribute15;
    public String attribute16;
    public String attribute17;
    public String attribute18;
    public String attribute19;
    public String attribute20;
    public Double attributeNumber1;
    public Double attributeNumber2;
    public Double attributeNumber3;
    public Double attributeNumber4;
    public Double attributeNumber5;
    public Double attributeNumber6;
    public Double attributeNumber7;
    public Double attributeNumber8;
    public Double attributeNumber9;
    public Double attributeNumber10;
    public LocalTimestamp attributeDate1;
    public LocalTimestamp attributeDate2;
    public LocalTimestamp attributeDate3;
    public LocalTimestamp attributeDate4;
    public LocalTimestamp attributeDate5;
    public LocalTimestamp attributeDate6;
    public LocalTimestamp attributeDate7;
    public LocalTimestamp attributeDate8;
    public LocalTimestamp attributeDate9;
    public LocalTimestamp attributeDate10;
    public Currency attributeCurrency1;
    public Currency attributeCurrency2;
    public Currency attributeCurrency3;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field invoiceXidField = beanDataFields[1];
    public static Field invoiceTypeField = beanDataFields[2];
    public static Field invoiceSourceField = beanDataFields[3];
    public static Field enteredByGlUserGidField = beanDataFields[4];
    public static Field invoiceNumberField = beanDataFields[5];
    public static Field servprovAliasQualGidField = beanDataFields[6];
    public static Field servprovAliasValueField = beanDataFields[7];
    public static Field correctionCodeIdField = beanDataFields[8];
    public static Field invoiceDateField = beanDataFields[9];
    public static Field currencyGidField = beanDataFields[10];
    public static Field netAmountDueField = beanDataFields[11];
    public static Field netDueDateField = beanDataFields[12];
    public static Field paymentMethodCodeGidField = beanDataFields[13];
    public static Field startDateField = beanDataFields[14];
    public static Field endDateField = beanDataFields[15];
    public static Field invoiceServiceCodeGidField = beanDataFields[16];
    public static Field incoTermGidField = beanDataFields[17];
    public static Field discountAmountField = beanDataFields[18];
    public static Field discountPercentageField = beanDataFields[19];
    public static Field discountDaysDueField = beanDataFields[20];
    public static Field discountDueDateField = beanDataFields[21];
    public static Field originStationCityField = beanDataFields[22];
    public static Field originStationFsacField = beanDataFields[23];
    public static Field originStationSplcField = beanDataFields[24];
    public static Field originStationProvinceCodeField = beanDataFields[25];
    public static Field originStationPostalCodeField = beanDataFields[26];
    public static Field originCountryCode3GidField = beanDataFields[27];
    public static Field destStationCityField = beanDataFields[28];
    public static Field destStationFsacField = beanDataFields[29];
    public static Field destStationSplcField = beanDataFields[30];
    public static Field destStationProvinceCodeField = beanDataFields[31];
    public static Field destStationPostalCodeField = beanDataFields[32];
    public static Field destCountryCode3GidField = beanDataFields[33];
    public static Field letterOfCreditExpDateField = beanDataFields[34];
    public static Field letterOfCreditIssueDateField = beanDataFields[35];
    public static Field letterOfCreditNumberField = beanDataFields[36];
    public static Field vesselCodeQualifierField = beanDataFields[37];
    public static Field vesselCodeField = beanDataFields[38];
    public static Field vesselCountryCode3GidField = beanDataFields[39];
    public static Field vesselGidField = beanDataFields[40];
    public static Field vesOpServprovAliasQualGidField = beanDataFields[41];
    public static Field vesOpServprovAliasValueField = beanDataFields[42];
    public static Field voyageNumberField = beanDataFields[43];
    public static Field parentInvoiceGidField = beanDataFields[44];
    public static Field dateReceivedField = beanDataFields[45];
    public static Field supplyCountryCode3GidField = beanDataFields[46];
    public static Field servprovVatRegNoGidField = beanDataFields[47];
    public static Field customerVatRegNoGidField = beanDataFields[48];
    public static Field vatExemptValueField = beanDataFields[49];
    public static Field netAmtDueWithTaxField = beanDataFields[50];
    public static Field isPassThroughField = beanDataFields[51];
    public static Field consolidationTypeField = beanDataFields[52];
    public static Field isFixedCostField = beanDataFields[53];
    public static Field baseChargeField = beanDataFields[54];
    public static Field otherChargeField = beanDataFields[55];
    public static Field isCreditNoteField = beanDataFields[56];
    public static Field userDefined1IconGidField = beanDataFields[57];
    public static Field userDefined2IconGidField = beanDataFields[58];
    public static Field userDefined3IconGidField = beanDataFields[59];
    public static Field userDefined4IconGidField = beanDataFields[60];
    public static Field userDefined5IconGidField = beanDataFields[61];
    public static Field exchangeRateDateField = beanDataFields[62];
    public static Field exchangeRateGidField = beanDataFields[63];
    public static Field servprovGidField = beanDataFields[64];
    public static Field sailDateField = beanDataFields[65];
    public static Field sailCutoffDateField = beanDataFields[66];
    public static Field railDateField = beanDataFields[67];
    public static Field isHazardousField = beanDataFields[68];
    public static Field isTemperatureControlField = beanDataFields[69];
    public static Field invoicingProcessField = beanDataFields[70];
    public static Field isVatAnalysisFixedField = beanDataFields[71];
    public static Field originalInvoiceGidField = beanDataFields[72];
    public static Field glDateField = beanDataFields[73];
    public static Field domainNameField = beanDataFields[74];
    public static Field attribute1Field = beanDataFields[75];
    public static Field attribute2Field = beanDataFields[76];
    public static Field attribute3Field = beanDataFields[77];
    public static Field attribute4Field = beanDataFields[78];
    public static Field attribute5Field = beanDataFields[79];
    public static Field attribute6Field = beanDataFields[80];
    public static Field attribute7Field = beanDataFields[81];
    public static Field attribute8Field = beanDataFields[82];
    public static Field attribute9Field = beanDataFields[83];
    public static Field attribute10Field = beanDataFields[84];
    public static Field attribute11Field = beanDataFields[85];
    public static Field attribute12Field = beanDataFields[86];
    public static Field attribute13Field = beanDataFields[87];
    public static Field attribute14Field = beanDataFields[88];
    public static Field attribute15Field = beanDataFields[89];
    public static Field attribute16Field = beanDataFields[90];
    public static Field attribute17Field = beanDataFields[91];
    public static Field attribute18Field = beanDataFields[92];
    public static Field attribute19Field = beanDataFields[93];
    public static Field attribute20Field = beanDataFields[94];
    public static Field attributeNumber1Field = beanDataFields[95];
    public static Field attributeNumber2Field = beanDataFields[96];
    public static Field attributeNumber3Field = beanDataFields[97];
    public static Field attributeNumber4Field = beanDataFields[98];
    public static Field attributeNumber5Field = beanDataFields[99];
    public static Field attributeNumber6Field = beanDataFields[100];
    public static Field attributeNumber7Field = beanDataFields[101];
    public static Field attributeNumber8Field = beanDataFields[102];
    public static Field attributeNumber9Field = beanDataFields[103];
    public static Field attributeNumber10Field = beanDataFields[104];
    public static Field attributeDate1Field = beanDataFields[105];
    public static Field attributeDate2Field = beanDataFields[106];
    public static Field attributeDate3Field = beanDataFields[107];
    public static Field attributeDate4Field = beanDataFields[108];
    public static Field attributeDate5Field = beanDataFields[109];
    public static Field attributeDate6Field = beanDataFields[110];
    public static Field attributeDate7Field = beanDataFields[111];
    public static Field attributeDate8Field = beanDataFields[112];
    public static Field attributeDate9Field = beanDataFields[113];
    public static Field attributeDate10Field = beanDataFields[114];
    public static Field attributeCurrency1Field = beanDataFields[115];
    public static Field attributeCurrency2Field = beanDataFields[116];
    public static Field attributeCurrency3Field = beanDataFields[117];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceData() {
    }

    public InvoiceData(InvoiceData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoicePK();
    }

    @Legacy
    public InvoicePK getInvoicePK() {
        if (this.invoiceGid == null) {
            return null;
        }
        return new InvoicePK(this.invoiceGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoicePK((InvoicePK)pk);
    }

    @Legacy
    public void setInvoicePK(InvoicePK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceQueryGen";
    }

    public static InvoiceData load(Connection conn, InvoicePK pk) throws GLException {
        return (InvoiceData)InvoiceData.load(conn, pk, InvoiceData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceData.class);
    }

    @Override
    public ObjectStatus newObjectStatus() throws GLException {
        String domain = this.domainName != null ? this.domainName : SecurityUtil.getCurrentDomain();
        ObjectStatus status = new ObjectStatus("invoice_status", new InvoicePK.Callback(), true);
        status.init(Invoice.class, domain);
        return status;
    }

    public boolean canPerform(StatusFunction statusFunction, Object context) throws GLException {
        return this.canPerform(statusFunction, context, "ejb.Invoice");
    }

    public StatusError canPerformWithCauses(StatusFunction statusFunction, Object context) throws GLException {
        return this.canPerformWithCauses(statusFunction, context, "ejb.Invoice");
    }
}
