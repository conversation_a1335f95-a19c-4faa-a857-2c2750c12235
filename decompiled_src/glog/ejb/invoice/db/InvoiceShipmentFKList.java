/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvoiceShipmentFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("sShipUnitGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceShipmentData";
            }

            @Override
            public String getPkField() {
                return "sShipUnitGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.shipment.db";
            }

            @Override
            public String getFkDataClass() {
                return "SShipUnitData";
            }

            @Override
            public String getFkDataField() {
                return "sShipUnitGid";
            }
        });
        fks.put("shipmentGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceShipmentData";
            }

            @Override
            public String getPkField() {
                return "shipmentGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.shipment.db";
            }

            @Override
            public String getFkDataClass() {
                return "ShipmentData";
            }

            @Override
            public String getFkDataField() {
                return "shipmentGid";
            }
        });
        fks.put("invoiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceShipmentData";
            }

            @Override
            public String getPkField() {
                return "invoiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceGid";
            }
        });
    }
}
