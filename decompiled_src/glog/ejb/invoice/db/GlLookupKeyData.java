/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlLookupKeyPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class GlLookupKeyData
extends BeanData {
    public String glLookupKeyGid;
    public String glLookupKeyXid;
    public Boolean isActive = Boolean.valueOf("false");
    public String perspective = String.valueOf("B");
    public String glCodeAssignType = String.valueOf("O");
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlLookupKeyData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field glLookupKeyGidField = beanDataFields[0];
    public static Field glLookupKeyXidField = beanDataFields[1];
    public static Field isActiveField = beanDataFields[2];
    public static Field perspectiveField = beanDataFields[3];
    public static Field glCodeAssignTypeField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public GlLookupKeyData() {
    }

    public GlLookupKeyData(GlLookupKeyData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getGlLookupKeyPK();
    }

    @Legacy
    public GlLookupKeyPK getGlLookupKeyPK() {
        if (this.glLookupKeyGid == null) {
            return null;
        }
        return new GlLookupKeyPK(this.glLookupKeyGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setGlLookupKeyPK((GlLookupKeyPK)pk);
    }

    @Legacy
    public void setGlLookupKeyPK(GlLookupKeyPK pk) {
        this.glLookupKeyGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.GlLookupKeyQueryGen";
    }

    public static GlLookupKeyData load(Connection conn, GlLookupKeyPK pk) throws GLException {
        return (GlLookupKeyData)GlLookupKeyData.load(conn, pk, GlLookupKeyData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return GlLookupKeyData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return GlLookupKeyData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlLookupKeyData.load(conn, whereClause, prepareArguments, fetchSize, GlLookupKeyData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return GlLookupKeyData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlLookupKeyData.load(conn, fromWhere, alias, prepareArguments, fetchSize, GlLookupKeyData.class);
    }
}
