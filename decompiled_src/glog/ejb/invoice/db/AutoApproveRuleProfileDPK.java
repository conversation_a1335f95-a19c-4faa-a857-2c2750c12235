/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleProfileDColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AutoApproveRuleProfileDPK
extends Pk {
    public Object autoApproveRuleGid;
    public Object autoApproveRuleProfileGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AutoApproveRuleProfileDPK() {
    }

    public AutoApproveRuleProfileDPK(String autoApproveRuleGid, String autoApproveRuleProfileGid) {
        this.autoApproveRuleGid = this.notNull(AutoApproveRuleProfileDColumns.autoApproveRuleGid.convertToDB(autoApproveRuleGid), "autoApproveRuleGid");
        this.autoApproveRuleProfileGid = this.notNull(AutoApproveRuleProfileDColumns.autoApproveRuleProfileGid.convertToDB(autoApproveRuleProfileGid), "autoApproveRuleProfileGid");
    }

    public AutoApproveRuleProfileDPK(int dummy, Object autoApproveRuleGid, Object autoApproveRuleProfileGid) {
        this(dummy, autoApproveRuleGid, autoApproveRuleProfileGid, null);
    }

    public AutoApproveRuleProfileDPK(int dummy, Object autoApproveRuleGid, Object autoApproveRuleProfileGid, Object transaction) {
        this.autoApproveRuleGid = autoApproveRuleGid;
        this.autoApproveRuleProfileGid = autoApproveRuleProfileGid;
        this.transaction = transaction;
    }

    public AutoApproveRuleProfileDPK(AutoApproveRuleProfileDPK otherPk, Object transaction) {
        this.autoApproveRuleGid = otherPk.autoApproveRuleGid;
        this.autoApproveRuleProfileGid = otherPk.autoApproveRuleProfileGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.autoApproveRuleGid != null ? String.valueOf(this.autoApproveRuleGid) : "") + " " + (this.autoApproveRuleProfileGid != null ? String.valueOf(this.autoApproveRuleProfileGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.autoApproveRuleGid != null ? String.valueOf(this.autoApproveRuleGid) : "") + "|" + (this.autoApproveRuleProfileGid != null ? String.valueOf(this.autoApproveRuleProfileGid) : "");
    }

    public static AutoApproveRuleProfileDPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AutoApproveRuleProfileDPK(gids[0], gids[1]) : null;
    }

    public static AutoApproveRuleProfileDPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AutoApproveRuleProfileDPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.autoApproveRuleGid.hashCode() + this.autoApproveRuleProfileGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AutoApproveRuleProfileDPK)) {
            return false;
        }
        AutoApproveRuleProfileDPK otherPk = (AutoApproveRuleProfileDPK)other;
        return Functions.equals(otherPk.autoApproveRuleGid, this.autoApproveRuleGid) && Functions.equals(otherPk.autoApproveRuleProfileGid, this.autoApproveRuleProfileGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.AutoApproveRuleProfileDHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.autoApproveRuleGid;
            }
            case 1: {
                return this.autoApproveRuleProfileGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AutoApproveRuleProfileDColumns.autoApproveRuleGid.convertFromDB(this.autoApproveRuleGid);
            }
            case 1: {
                return AutoApproveRuleProfileDColumns.autoApproveRuleProfileGid.convertFromDB(this.autoApproveRuleProfileGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AutoApproveRuleProfileDPK.class;
        }

        @Override
        public final String getEntity() {
            return "AutoApproveRuleProfileD";
        }

        @Override
        public final String getTableName() {
            return "auto_approve_rule_profile_d";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"auto_approve_rule_gid", "auto_approve_rule_profile_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AutoApproveRuleProfileDPK result = new AutoApproveRuleProfileDPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
