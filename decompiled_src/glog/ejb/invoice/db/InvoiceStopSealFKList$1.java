/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceStopSealFKList.1
implements Fk {
    InvoiceStopSealFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceStopSealData";
    }

    @Override
    public String getPkField() {
        return "stopSeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceStopData";
    }

    @Override
    public String getFkDataField() {
        return "stopSeqNo";
    }
}
