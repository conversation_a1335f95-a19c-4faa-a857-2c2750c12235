/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.23
implements Fk {
    InvoiceFKList.23() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "customerVatRegNoGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vat.db";
    }

    @Override
    public String getFkDataClass() {
        return "VatRegistrationData";
    }

    @Override
    public String getFkDataField() {
        return "vatRegNoGid";
    }
}
