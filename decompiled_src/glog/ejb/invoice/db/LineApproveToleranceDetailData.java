/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveToleranceDetailPK;
import glog.util.Functions;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class LineApproveToleranceDetailData
extends BeanData {
    public String lineApproveToleranceGid;
    public Integer lineApproveToleranceSeq;
    public String costType;
    public String accessorialCodeGid;
    public String generalLedgerGid;
    public String paymentMethodCodeGid;
    public Double allowablePercentAbove;
    public Currency allowCurrAbove;
    public Double allowablePercentBelow;
    public Currency allowCurrBelow;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineApproveToleranceDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field lineApproveToleranceGidField = beanDataFields[0];
    public static Field lineApproveToleranceSeqField = beanDataFields[1];
    public static Field costTypeField = beanDataFields[2];
    public static Field accessorialCodeGidField = beanDataFields[3];
    public static Field generalLedgerGidField = beanDataFields[4];
    public static Field paymentMethodCodeGidField = beanDataFields[5];
    public static Field allowablePercentAboveField = beanDataFields[6];
    public static Field allowCurrAboveField = beanDataFields[7];
    public static Field allowablePercentBelowField = beanDataFields[8];
    public static Field allowCurrBelowField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public LineApproveToleranceDetailData() {
    }

    public LineApproveToleranceDetailData(LineApproveToleranceDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getLineApproveToleranceDetailPK();
    }

    @Legacy
    public LineApproveToleranceDetailPK getLineApproveToleranceDetailPK() {
        if (this.lineApproveToleranceGid == null) {
            return null;
        }
        if (this.lineApproveToleranceSeq == null) {
            return null;
        }
        return new LineApproveToleranceDetailPK(this.lineApproveToleranceGid, this.lineApproveToleranceSeq);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setLineApproveToleranceDetailPK((LineApproveToleranceDetailPK)pk);
    }

    @Legacy
    public void setLineApproveToleranceDetailPK(LineApproveToleranceDetailPK pk) {
        this.lineApproveToleranceGid = (String)pk.getAppValue(0);
        this.lineApproveToleranceSeq = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.LineApproveToleranceDetailQueryGen";
    }

    public static LineApproveToleranceDetailData load(Connection conn, LineApproveToleranceDetailPK pk) throws GLException {
        return (LineApproveToleranceDetailData)LineApproveToleranceDetailData.load(conn, pk, LineApproveToleranceDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return LineApproveToleranceDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return LineApproveToleranceDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveToleranceDetailData.load(conn, whereClause, prepareArguments, fetchSize, LineApproveToleranceDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return LineApproveToleranceDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineApproveToleranceDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, LineApproveToleranceDetailData.class);
    }
}
