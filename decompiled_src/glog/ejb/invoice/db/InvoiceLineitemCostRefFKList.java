/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvoiceLineitemCostRefFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("shipmentCostQualGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemCostRefData";
            }

            @Override
            public String getPkField() {
                return "shipmentCostQualGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.shipment.db";
            }

            @Override
            public String getFkDataClass() {
                return "ShipmentCostQualData";
            }

            @Override
            public String getFkDataField() {
                return "shipmentCostQualGid";
            }
        });
        fks.put("lineitemSeqNo", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemCostRefData";
            }

            @Override
            public String getPkField() {
                return "lineitemSeqNo";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getFkDataField() {
                return "lineitemSeqNo";
            }
        });
        fks.put("invoiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvoiceLineitemCostRefData";
            }

            @Override
            public String getPkField() {
                return "invoiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceLineitemData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceGid";
            }
        });
    }
}
