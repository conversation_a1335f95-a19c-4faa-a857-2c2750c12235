/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class VoucherInvoiceLineitemJoinColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn lineitemSeqNo = new SqlColumn(Integer.class, "lineitem_seq_no", 8, 0, 1, null, 1, null);
    public static SqlColumn voucherGid = new SqlColumn(String.class, "voucher_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn amountPaid = new SqlColumn(Currency.class, "amount_paid", 22, 2, 1, null, 3, "amount_paid_base");
    public static SqlColumn adjustmentReasonGid = new SqlColumn(String.class, "adjustment_reason_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 5, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 6, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 7, null);
}
