/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class LineAppTolInvoiceRefnumColumns {
    public static SqlColumn lineApproveToleranceGid = new SqlColumn(String.class, "line_approve_tolerance_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn refnumSequence = new SqlColumn(Integer.class, "refnum_sequence", 8, 0, 1, null, 1, null);
    public static SqlColumn invoiceRefnumQualGid = new SqlColumn(String.class, "invoice_refnum_qual_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn invoiceRefnumValue = new SqlColumn(String.class, "invoice_refnum_value", 240, 0, 0, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
