/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherRemarkData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface VoucherRemarkRemoteDB
extends EJBObject {
    public VoucherRemarkData getData() throws RemoteException, GLException;

    public void setData(VoucherRemarkData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getVoucherGid() throws RemoteException, GLException;

    public void setVoucherGid(String var1) throws RemoteException, GLException;

    public Long getRemarkSequence() throws RemoteException, GLException;

    public void setRemarkSequence(Long var1) throws RemoteException, GLException;

    public String getRemarkQualGid() throws RemoteException, GLException;

    public void setRemarkQualGid(String var1) throws RemoteException, GLException;

    public String getRemarkText() throws RemoteException, GLException;

    public void setRemarkText(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
