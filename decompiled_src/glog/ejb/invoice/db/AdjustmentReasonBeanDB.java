/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.AdjustmentReasonColumns;
import glog.ejb.invoice.db.AdjustmentReasonData;
import glog.ejb.invoice.db.AdjustmentReasonPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AdjustmentReasonBeanDB
extends BeanManagedEntityBean {
    public Object adjustmentReasonGid;
    public Object adjustmentReasonXid;
    public Object domainName;
    public Object description;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AdjustmentReasonPK pk;
    protected transient AdjustmentReasonData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AdjustmentReasonBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AdjustmentReasonPK.Callback theCall = new AdjustmentReasonPK.Callback();

    public AdjustmentReasonBeanDB() {
        super(false);
    }

    public AdjustmentReasonPK ejbCreate(AdjustmentReasonData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AdjustmentReasonPK newPK() throws GLException {
        return AdjustmentReasonPK.newPK(this.getDomainName(), this.getAdjustmentReasonXid(), this.getConnection());
    }

    public void ejbPostCreate(AdjustmentReasonData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AdjustmentReasonPK ejbFindByPrimaryKey(AdjustmentReasonPK pk) throws FinderException {
        return (AdjustmentReasonPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AdjustmentReasonPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AdjustmentReasonPK> v = new Vector<AdjustmentReasonPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AdjustmentReasonPK pk, AdjustmentReasonData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AdjustmentReasonPK pk, AdjustmentReasonData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AdjustmentReasonPK pk, AdjustmentReasonData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AdjustmentReasonColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AdjustmentReasonColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AdjustmentReasonColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AdjustmentReasonColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AdjustmentReasonPK pk = (AdjustmentReasonPK)genericPk;
        this.adjustmentReasonGid = pk.adjustmentReasonGid;
        this.domainName = pk.domainName;
        this.adjustmentReasonXid = pk.adjustmentReasonXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AdjustmentReasonPK pk = new AdjustmentReasonPK(0, this.adjustmentReasonGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AdjustmentReason";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AdjustmentReasonData getData() throws GLException {
        try {
            AdjustmentReasonData retval = new AdjustmentReasonData();
            retval.getFromBean(this, AdjustmentReasonColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AdjustmentReasonData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AdjustmentReasonData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AdjustmentReasonData oldData = modified ? this.getData() : null;
            data.setToBean(this, AdjustmentReasonColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AdjustmentReasonData.class;
    }

    @Override
    public Class getPkClass() {
        return AdjustmentReasonPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAdjustmentReasonGid() throws GLException {
        try {
            return (String)AdjustmentReasonColumns.adjustmentReasonGid.convertFromDB(this.adjustmentReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAdjustmentReasonGid(String adjustmentReasonGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.adjustmentReasonGid;
            AdjustmentReasonData data = this.getData();
            this.adjustmentReasonGid = AdjustmentReasonColumns.adjustmentReasonGid.convertToDB(adjustmentReasonGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "adjustmentReasonGid", String.class, oldValue, this.adjustmentReasonGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAdjustmentReasonXid() throws GLException {
        try {
            return (String)AdjustmentReasonColumns.adjustmentReasonXid.convertFromDB(this.adjustmentReasonXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAdjustmentReasonXid(String adjustmentReasonXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.adjustmentReasonXid;
            AdjustmentReasonData data = this.getData();
            this.adjustmentReasonXid = AdjustmentReasonColumns.adjustmentReasonXid.convertToDB(adjustmentReasonXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "adjustmentReasonXid", String.class, oldValue, this.adjustmentReasonXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AdjustmentReasonColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AdjustmentReasonData data = this.getData();
            this.domainName = AdjustmentReasonColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)AdjustmentReasonColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            AdjustmentReasonData data = this.getData();
            this.description = AdjustmentReasonColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
