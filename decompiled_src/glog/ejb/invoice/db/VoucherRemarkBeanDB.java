/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.VoucherRemarkColumns;
import glog.ejb.invoice.db.VoucherRemarkData;
import glog.ejb.invoice.db.VoucherRemarkPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class VoucherRemarkBeanDB
extends BeanManagedEntityBean {
    public Object voucherGid;
    public Object remarkSequence;
    public Object remarkQualGid;
    public Object remarkText;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient VoucherRemarkPK pk;
    protected transient VoucherRemarkData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherRemarkBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static VoucherRemarkPK.Callback theCall = new VoucherRemarkPK.Callback();

    public VoucherRemarkBeanDB() {
        super(false);
    }

    public VoucherRemarkPK ejbCreate(VoucherRemarkData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(VoucherRemarkData data) throws CreateException {
        this.ejbPostCreator();
    }

    public VoucherRemarkPK ejbFindByPrimaryKey(VoucherRemarkPK pk) throws FinderException {
        return (VoucherRemarkPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(VoucherRemarkPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<VoucherRemarkPK> v = new Vector<VoucherRemarkPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(VoucherRemarkPK pk, VoucherRemarkData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(VoucherRemarkPK pk, VoucherRemarkData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(VoucherRemarkPK pk, VoucherRemarkData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (VoucherRemarkPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(VoucherRemarkColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(VoucherRemarkColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(VoucherRemarkColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(VoucherRemarkColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        VoucherRemarkPK pk = (VoucherRemarkPK)genericPk;
        this.remarkSequence = pk.remarkSequence;
        this.voucherGid = pk.voucherGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        VoucherRemarkPK pk = new VoucherRemarkPK(0, this.remarkSequence, this.voucherGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.VoucherRemark";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public VoucherRemarkData getData() throws GLException {
        try {
            VoucherRemarkData retval = new VoucherRemarkData();
            retval.getFromBean(this, VoucherRemarkColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(VoucherRemarkData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(VoucherRemarkData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            VoucherRemarkData oldData = modified ? this.getData() : null;
            data.setToBean(this, VoucherRemarkColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return VoucherRemarkData.class;
    }

    @Override
    public Class getPkClass() {
        return VoucherRemarkPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getVoucherGid() throws GLException {
        try {
            return (String)VoucherRemarkColumns.voucherGid.convertFromDB(this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherGid(String voucherGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherGid;
            VoucherRemarkData data = this.getData();
            this.voucherGid = VoucherRemarkColumns.voucherGid.convertToDB(voucherGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherGid", String.class, oldValue, this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getRemarkSequence() throws GLException {
        try {
            return (Long)VoucherRemarkColumns.remarkSequence.convertFromDB(this.remarkSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkSequence(Long remarkSequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkSequence;
            VoucherRemarkData data = this.getData();
            this.remarkSequence = VoucherRemarkColumns.remarkSequence.convertToDB(remarkSequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkSequence", Long.class, oldValue, this.remarkSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkQualGid() throws GLException {
        try {
            return (String)VoucherRemarkColumns.remarkQualGid.convertFromDB(this.remarkQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkQualGid(String remarkQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkQualGid;
            VoucherRemarkData data = this.getData();
            this.remarkQualGid = VoucherRemarkColumns.remarkQualGid.convertToDB(remarkQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkQualGid", String.class, oldValue, this.remarkQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRemarkText() throws GLException {
        try {
            return (String)VoucherRemarkColumns.remarkText.convertFromDB(this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRemarkText(String remarkText) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.remarkText;
            VoucherRemarkData data = this.getData();
            this.remarkText = VoucherRemarkColumns.remarkText.convertToDB(remarkText);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "remarkText", String.class, oldValue, this.remarkText);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)VoucherRemarkColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            VoucherRemarkData data = this.getData();
            this.domainName = VoucherRemarkColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
