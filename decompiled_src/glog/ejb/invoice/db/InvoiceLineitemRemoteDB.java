/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemData;
import glog.util.LocalDate;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.uom.data.Volume;
import glog.util.uom.data.Weight;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceLineitemRemoteDB
extends EJBObject {
    public InvoiceLineitemData getData() throws RemoteException, GLException;

    public void setData(InvoiceLineitemData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Integer var1) throws RemoteException, GLException;

    public String getDescription() throws RemoteException, GLException;

    public void setDescription(String var1) throws RemoteException, GLException;

    public Long getUnitCount() throws RemoteException, GLException;

    public void setUnitCount(Long var1) throws RemoteException, GLException;

    public String getTransportHandlingUnitGid() throws RemoteException, GLException;

    public void setTransportHandlingUnitGid(String var1) throws RemoteException, GLException;

    public String getBillableIndicatorGid() throws RemoteException, GLException;

    public void setBillableIndicatorGid(String var1) throws RemoteException, GLException;

    public String getFlexCommodityQualGid() throws RemoteException, GLException;

    public void setFlexCommodityQualGid(String var1) throws RemoteException, GLException;

    public String getFlexCommodityCode() throws RemoteException, GLException;

    public void setFlexCommodityCode(String var1) throws RemoteException, GLException;

    public Weight getWeight() throws RemoteException, GLException;

    public void setWeight(Weight var1) throws RemoteException, GLException;

    public Volume getVolume() throws RemoteException, GLException;

    public void setVolume(Volume var1) throws RemoteException, GLException;

    public String getWeightQualifier() throws RemoteException, GLException;

    public void setWeightQualifier(String var1) throws RemoteException, GLException;

    public String getMarks() throws RemoteException, GLException;

    public void setMarks(String var1) throws RemoteException, GLException;

    public String getMarksQualifier() throws RemoteException, GLException;

    public void setMarksQualifier(String var1) throws RemoteException, GLException;

    public String getBilledAsQualifier() throws RemoteException, GLException;

    public void setBilledAsQualifier(String var1) throws RemoteException, GLException;

    public Double getBilledAsQuantity() throws RemoteException, GLException;

    public void setBilledAsQuantity(Double var1) throws RemoteException, GLException;

    public String getFreightRateQualifier() throws RemoteException, GLException;

    public void setFreightRateQualifier(String var1) throws RemoteException, GLException;

    public Currency getFreightRateValue() throws RemoteException, GLException;

    public void setFreightRateValue(Currency var1) throws RemoteException, GLException;

    public Currency getFreightCharge() throws RemoteException, GLException;

    public void setFreightCharge(Currency var1) throws RemoteException, GLException;

    public Currency getPrepaidAmount() throws RemoteException, GLException;

    public void setPrepaidAmount(Currency var1) throws RemoteException, GLException;

    public String getPaymentMethodCodeGid() throws RemoteException, GLException;

    public void setPaymentMethodCodeGid(String var1) throws RemoteException, GLException;

    public String getAccessorialCodeGid() throws RemoteException, GLException;

    public void setAccessorialCodeGid(String var1) throws RemoteException, GLException;

    public String getAccessorialDescription() throws RemoteException, GLException;

    public void setAccessorialDescription(String var1) throws RemoteException, GLException;

    public String getCompartmentIdCode() throws RemoteException, GLException;

    public void setCompartmentIdCode(String var1) throws RemoteException, GLException;

    public String getExportLicControlCode() throws RemoteException, GLException;

    public void setExportLicControlCode(String var1) throws RemoteException, GLException;

    public String getExportLicCountryCode3Gid() throws RemoteException, GLException;

    public void setExportLicCountryCode3Gid(String var1) throws RemoteException, GLException;

    public LocalDate getExportLicExpDate() throws RemoteException, GLException;

    public void setExportLicExpDate(LocalDate var1) throws RemoteException, GLException;

    public String getExportLicNumber() throws RemoteException, GLException;

    public void setExportLicNumber(String var1) throws RemoteException, GLException;

    public LocalDate getImportLicExpDate() throws RemoteException, GLException;

    public void setImportLicExpDate(LocalDate var1) throws RemoteException, GLException;

    public LocalDate getImportLicIssueDate() throws RemoteException, GLException;

    public void setImportLicIssueDate(LocalDate var1) throws RemoteException, GLException;

    public String getImportLicNumber() throws RemoteException, GLException;

    public void setImportLicNumber(String var1) throws RemoteException, GLException;

    public String getTariffAgencyCode() throws RemoteException, GLException;

    public void setTariffAgencyCode(String var1) throws RemoteException, GLException;

    public String getTariffRefnum() throws RemoteException, GLException;

    public void setTariffRefnum(String var1) throws RemoteException, GLException;

    public String getTariffRefnumQualifier() throws RemoteException, GLException;

    public void setTariffRefnumQualifier(String var1) throws RemoteException, GLException;

    public String getTariffRefnumSuffix() throws RemoteException, GLException;

    public void setTariffRefnumSuffix(String var1) throws RemoteException, GLException;

    public String getTariffSection() throws RemoteException, GLException;

    public void setTariffSection(String var1) throws RemoteException, GLException;

    public String getTariffItemNumber() throws RemoteException, GLException;

    public void setTariffItemNumber(String var1) throws RemoteException, GLException;

    public String getTariffItemNumberSuffix() throws RemoteException, GLException;

    public void setTariffItemNumberSuffix(String var1) throws RemoteException, GLException;

    public String getTariffItemPart() throws RemoteException, GLException;

    public void setTariffItemPart(String var1) throws RemoteException, GLException;

    public String getTariffSupplementId() throws RemoteException, GLException;

    public void setTariffSupplementId(String var1) throws RemoteException, GLException;

    public String getTariffRegAgencyCode() throws RemoteException, GLException;

    public void setTariffRegAgencyCode(String var1) throws RemoteException, GLException;

    public String getTariffPubAuthority() throws RemoteException, GLException;

    public void setTariffPubAuthority(String var1) throws RemoteException, GLException;

    public String getTariffIssuingCarrierId() throws RemoteException, GLException;

    public void setTariffIssuingCarrierId(String var1) throws RemoteException, GLException;

    public String getTariffFreightClassCode() throws RemoteException, GLException;

    public void setTariffFreightClassCode(String var1) throws RemoteException, GLException;

    public LocalDate getTariffEffectiveDate() throws RemoteException, GLException;

    public void setTariffEffectiveDate(LocalDate var1) throws RemoteException, GLException;

    public String getDeclaredValueQualGid() throws RemoteException, GLException;

    public void setDeclaredValueQualGid(String var1) throws RemoteException, GLException;

    public Currency getDeclaredValue() throws RemoteException, GLException;

    public void setDeclaredValue(Currency var1) throws RemoteException, GLException;

    public String getAdjustmentReasonCodeGid() throws RemoteException, GLException;

    public void setAdjustmentReasonCodeGid(String var1) throws RemoteException, GLException;

    public Boolean getProcessAsFlowThru() throws RemoteException, GLException;

    public void setProcessAsFlowThru(Boolean var1) throws RemoteException, GLException;

    public String getGeneralLedgerGid() throws RemoteException, GLException;

    public void setGeneralLedgerGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getVatCodeGid() throws RemoteException, GLException;

    public void setVatCodeGid(String var1) throws RemoteException, GLException;

    public String getCostTypeGid() throws RemoteException, GLException;

    public void setCostTypeGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getIndicator() throws RemoteException, GLException;

    public void setIndicator(String var1) throws RemoteException, GLException;

    public Currency getOutOfTolAmt() throws RemoteException, GLException;

    public void setOutOfTolAmt(Currency var1) throws RemoteException, GLException;

    public String getOutOfTolReasonCodeGid() throws RemoteException, GLException;

    public void setOutOfTolReasonCodeGid(String var1) throws RemoteException, GLException;

    public Currency getDeviationAmount() throws RemoteException, GLException;

    public void setDeviationAmount(Currency var1) throws RemoteException, GLException;

    public String getAttribute1() throws RemoteException, GLException;

    public void setAttribute1(String var1) throws RemoteException, GLException;

    public String getAttribute2() throws RemoteException, GLException;

    public void setAttribute2(String var1) throws RemoteException, GLException;

    public String getAttribute3() throws RemoteException, GLException;

    public void setAttribute3(String var1) throws RemoteException, GLException;

    public String getAttribute4() throws RemoteException, GLException;

    public void setAttribute4(String var1) throws RemoteException, GLException;

    public String getAttribute5() throws RemoteException, GLException;

    public void setAttribute5(String var1) throws RemoteException, GLException;

    public String getAttribute6() throws RemoteException, GLException;

    public void setAttribute6(String var1) throws RemoteException, GLException;

    public String getAttribute7() throws RemoteException, GLException;

    public void setAttribute7(String var1) throws RemoteException, GLException;

    public String getAttribute8() throws RemoteException, GLException;

    public void setAttribute8(String var1) throws RemoteException, GLException;

    public String getAttribute9() throws RemoteException, GLException;

    public void setAttribute9(String var1) throws RemoteException, GLException;

    public String getAttribute10() throws RemoteException, GLException;

    public void setAttribute10(String var1) throws RemoteException, GLException;

    public String getAttribute11() throws RemoteException, GLException;

    public void setAttribute11(String var1) throws RemoteException, GLException;

    public String getAttribute12() throws RemoteException, GLException;

    public void setAttribute12(String var1) throws RemoteException, GLException;

    public String getAttribute13() throws RemoteException, GLException;

    public void setAttribute13(String var1) throws RemoteException, GLException;

    public String getAttribute14() throws RemoteException, GLException;

    public void setAttribute14(String var1) throws RemoteException, GLException;

    public String getAttribute15() throws RemoteException, GLException;

    public void setAttribute15(String var1) throws RemoteException, GLException;

    public String getAttribute16() throws RemoteException, GLException;

    public void setAttribute16(String var1) throws RemoteException, GLException;

    public String getAttribute17() throws RemoteException, GLException;

    public void setAttribute17(String var1) throws RemoteException, GLException;

    public String getAttribute18() throws RemoteException, GLException;

    public void setAttribute18(String var1) throws RemoteException, GLException;

    public String getAttribute19() throws RemoteException, GLException;

    public void setAttribute19(String var1) throws RemoteException, GLException;

    public String getAttribute20() throws RemoteException, GLException;

    public void setAttribute20(String var1) throws RemoteException, GLException;

    public Double getAttributeNumber1() throws RemoteException, GLException;

    public void setAttributeNumber1(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber2() throws RemoteException, GLException;

    public void setAttributeNumber2(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber3() throws RemoteException, GLException;

    public void setAttributeNumber3(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber4() throws RemoteException, GLException;

    public void setAttributeNumber4(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber5() throws RemoteException, GLException;

    public void setAttributeNumber5(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber6() throws RemoteException, GLException;

    public void setAttributeNumber6(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber7() throws RemoteException, GLException;

    public void setAttributeNumber7(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber8() throws RemoteException, GLException;

    public void setAttributeNumber8(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber9() throws RemoteException, GLException;

    public void setAttributeNumber9(Double var1) throws RemoteException, GLException;

    public Double getAttributeNumber10() throws RemoteException, GLException;

    public void setAttributeNumber10(Double var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate1() throws RemoteException, GLException;

    public void setAttributeDate1(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate2() throws RemoteException, GLException;

    public void setAttributeDate2(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate3() throws RemoteException, GLException;

    public void setAttributeDate3(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate4() throws RemoteException, GLException;

    public void setAttributeDate4(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate5() throws RemoteException, GLException;

    public void setAttributeDate5(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate6() throws RemoteException, GLException;

    public void setAttributeDate6(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate7() throws RemoteException, GLException;

    public void setAttributeDate7(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate8() throws RemoteException, GLException;

    public void setAttributeDate8(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate9() throws RemoteException, GLException;

    public void setAttributeDate9(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getAttributeDate10() throws RemoteException, GLException;

    public void setAttributeDate10(LocalTimestamp var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency1() throws RemoteException, GLException;

    public void setAttributeCurrency1(Currency var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency2() throws RemoteException, GLException;

    public void setAttributeCurrency2(Currency var1) throws RemoteException, GLException;

    public Currency getAttributeCurrency3() throws RemoteException, GLException;

    public void setAttributeCurrency3(Currency var1) throws RemoteException, GLException;
}
