/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.VoucherStatus;
import glog.ejb.invoice.db.VoucherStatusData;
import glog.ejb.invoice.db.VoucherStatusPK;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.FinderException;

public interface VoucherStatusHomeDB
extends EJBHome {
    public static final String NAME = "ejb.VoucherStatus";
    public static final String dataClass = "glog.ejb.invoice.db.VoucherStatusData";
    public static final String primaryKeyClass = "glog.ejb.invoice.db.VoucherStatusPK";

    public VoucherStatus create(VoucherStatusData var1) throws CreateException, RemoteException;

    public VoucherStatus findByPrimaryKey(VoucherStatusPK var1) throws FinderException, RemoteException;

    public Enumeration findAll() throws FinderException, RemoteException;

    public Enumeration findAll(Object var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1, Object var2) throws FinderException, RemoteException;

    public Enumeration findInCache(VoucherStatusPK var1) throws FinderException, RemoteException;

    public void onCreate(VoucherStatusPK var1, VoucherStatusData var2) throws GLException, RemoteException;

    public void onUpdate(VoucherStatusPK var1, VoucherStatusData var2) throws GLException, RemoteException;

    public void onRemove(VoucherStatusPK var1, VoucherStatusData var2) throws GLException, RemoteException;

    public Collection listBeansInCache() throws GLException, RemoteException;

    public Collection listLocks() throws GLException, RemoteException;

    public void unlock(Pk var1) throws GLException, RemoteException;

    public BeanData getDataNoLock(Pk var1) throws GLException, RemoteException;

    public LockData getLockData(Pk var1) throws GLException, RemoteException;

    public int getMaxCacheSize() throws GLException, RemoteException;

    public void updateMaxCacheSize(int var1) throws GLException, RemoteException;
}
