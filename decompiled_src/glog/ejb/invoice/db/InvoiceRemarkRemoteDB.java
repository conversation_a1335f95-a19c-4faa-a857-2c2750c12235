/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRemarkData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceRemarkRemoteDB
extends EJBObject {
    public InvoiceRemarkData getData() throws RemoteException, GLException;

    public void setData(InvoiceRemarkData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Integer getRemarkSeqNo() throws RemoteException, GLException;

    public void setRemarkSeqNo(Integer var1) throws RemoteException, GLException;

    public String getRemarkQualIdentifier() throws RemoteException, GLException;

    public void setRemarkQualIdentifier(String var1) throws RemoteException, GLException;

    public String getRemarkText() throws RemoteException, GLException;

    public void setRemarkText(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
