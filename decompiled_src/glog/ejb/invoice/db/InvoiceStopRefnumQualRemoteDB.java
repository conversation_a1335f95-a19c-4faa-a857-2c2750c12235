/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumQualData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceStopRefnumQualRemoteDB
extends EJBObject {
    public InvoiceStopRefnumQualData getData() throws RemoteException, GLException;

    public void setData(InvoiceStopRefnumQualData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceStopRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceStopRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceStopRefnumQualXid() throws RemoteException, GLException;

    public void setInvoiceStopRefnumQualXid(String var1) throws RemoteException, GLException;

    public String getInvoiceStopRefnumDesc() throws RemoteException, GLException;

    public void setInvoiceStopRefnumDesc(String var1) throws RemoteException, GLException;

    public String getDefaultRefnumBnTypeGid() throws RemoteException, GLException;

    public void setDefaultRefnumBnTypeGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
