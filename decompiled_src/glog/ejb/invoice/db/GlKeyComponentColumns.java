/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class GlKeyComponentColumns {
    public static SqlColumn glKeyComponentGid = new SqlColumn(String.class, "gl_key_component_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn glKeyComponentXid = new SqlColumn(String.class, "gl_key_component_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn glKeyComponentTypeGid = new SqlColumn(String.class, "gl_key_component_type_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
