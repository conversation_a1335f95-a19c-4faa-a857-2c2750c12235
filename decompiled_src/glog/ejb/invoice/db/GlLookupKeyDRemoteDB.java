/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlLookupKeyDData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface GlLookupKeyDRemoteDB
extends EJBObject {
    public GlLookupKeyDData getData() throws RemoteException, GLException;

    public void setData(GlLookupKeyDData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getGlLookupKeyGid() throws RemoteException, GLException;

    public void setGlLookupKeyGid(String var1) throws RemoteException, GLException;

    public Double getRank() throws RemoteException, GLException;

    public void setRank(Double var1) throws RemoteException, GLException;

    public String getGlKeyComponentGid() throws RemoteException, GLException;

    public void setGlKeyComponentGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
