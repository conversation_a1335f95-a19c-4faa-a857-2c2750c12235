/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceRefnumFKList.1
implements Fk {
    InvoiceRefnumFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceRefnumData";
    }

    @Override
    public String getPkField() {
        return "invoiceRefnumQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceRefnumQualData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceRefnumQualGid";
    }
}
