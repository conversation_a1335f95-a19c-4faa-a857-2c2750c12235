/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceStopRefnumData
extends BeanData {
    public String invoiceGid;
    public String stopSeqNo;
    public String invoiceStopRefnumQualGid;
    public String referenceNumber;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStopRefnumData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field stopSeqNoField = beanDataFields[1];
    public static Field invoiceStopRefnumQualGidField = beanDataFields[2];
    public static Field referenceNumberField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceStopRefnumData() {
    }

    public InvoiceStopRefnumData(InvoiceStopRefnumData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceStopRefnumPK();
    }

    @Legacy
    public InvoiceStopRefnumPK getInvoiceStopRefnumPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.invoiceStopRefnumQualGid == null) {
            return null;
        }
        if (this.stopSeqNo == null) {
            return null;
        }
        return new InvoiceStopRefnumPK(this.invoiceGid, this.invoiceStopRefnumQualGid, this.stopSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceStopRefnumPK((InvoiceStopRefnumPK)pk);
    }

    @Legacy
    public void setInvoiceStopRefnumPK(InvoiceStopRefnumPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.invoiceStopRefnumQualGid = (String)pk.getAppValue(1);
        this.stopSeqNo = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceStopRefnumQueryGen";
    }

    public static InvoiceStopRefnumData load(Connection conn, InvoiceStopRefnumPK pk) throws GLException {
        return (InvoiceStopRefnumData)InvoiceStopRefnumData.load(conn, pk, InvoiceStopRefnumData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceStopRefnumData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceStopRefnumData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopRefnumData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceStopRefnumData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceStopRefnumData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopRefnumData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceStopRefnumData.class);
    }
}
