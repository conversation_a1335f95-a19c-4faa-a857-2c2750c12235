/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.1
implements Fk {
    InvoiceFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "vesselGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vessel.db";
    }

    @Override
    public String getFkDataClass() {
        return "VesselData";
    }

    @Override
    public String getFkDataField() {
        return "vesselGid";
    }
}
