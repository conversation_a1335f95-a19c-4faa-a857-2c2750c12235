/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceProtectiveServiceColumns;
import glog.ejb.invoice.db.InvoiceProtectiveServiceData;
import glog.ejb.invoice.db.InvoiceProtectiveServicePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import glog.util.uom.data.Temperature;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceProtectiveServiceBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object protSrvSeqNo;
    public Object protSrvCode;
    public Object protSrvRuleCode;
    public Object protSrvTemp;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceProtectiveServicePK pk;
    protected transient InvoiceProtectiveServiceData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceProtectiveServiceBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceProtectiveServicePK.Callback theCall = new InvoiceProtectiveServicePK.Callback();

    public InvoiceProtectiveServiceBeanDB() {
        super(false);
    }

    public InvoiceProtectiveServicePK ejbCreate(InvoiceProtectiveServiceData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceProtectiveServiceData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceProtectiveServicePK ejbFindByPrimaryKey(InvoiceProtectiveServicePK pk) throws FinderException {
        return (InvoiceProtectiveServicePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceProtectiveServicePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceProtectiveServicePK> v = new Vector<InvoiceProtectiveServicePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceProtectiveServicePK pk, InvoiceProtectiveServiceData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceProtectiveServicePK pk, InvoiceProtectiveServiceData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceProtectiveServicePK pk, InvoiceProtectiveServiceData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceProtectiveServicePK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceProtectiveServiceColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceProtectiveServiceColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceProtectiveServiceColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceProtectiveServiceColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceProtectiveServicePK pk = (InvoiceProtectiveServicePK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.protSrvSeqNo = pk.protSrvSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceProtectiveServicePK pk = new InvoiceProtectiveServicePK(0, this.invoiceGid, this.protSrvSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceProtectiveService";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceProtectiveServiceData getData() throws GLException {
        try {
            InvoiceProtectiveServiceData retval = new InvoiceProtectiveServiceData();
            retval.getFromBean(this, InvoiceProtectiveServiceColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceProtectiveServiceData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceProtectiveServiceData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceProtectiveServiceData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceProtectiveServiceColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceProtectiveServiceData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceProtectiveServicePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceProtectiveServiceColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceProtectiveServiceData data = this.getData();
            this.invoiceGid = InvoiceProtectiveServiceColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getProtSrvSeqNo() throws GLException {
        try {
            return (Integer)InvoiceProtectiveServiceColumns.protSrvSeqNo.convertFromDB(this.protSrvSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setProtSrvSeqNo(Integer protSrvSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.protSrvSeqNo;
            InvoiceProtectiveServiceData data = this.getData();
            this.protSrvSeqNo = InvoiceProtectiveServiceColumns.protSrvSeqNo.convertToDB(protSrvSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "protSrvSeqNo", Integer.class, oldValue, this.protSrvSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getProtSrvCode() throws GLException {
        try {
            return (String)InvoiceProtectiveServiceColumns.protSrvCode.convertFromDB(this.protSrvCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setProtSrvCode(String protSrvCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.protSrvCode;
            InvoiceProtectiveServiceData data = this.getData();
            this.protSrvCode = InvoiceProtectiveServiceColumns.protSrvCode.convertToDB(protSrvCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "protSrvCode", String.class, oldValue, this.protSrvCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getProtSrvRuleCode() throws GLException {
        try {
            return (String)InvoiceProtectiveServiceColumns.protSrvRuleCode.convertFromDB(this.protSrvRuleCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setProtSrvRuleCode(String protSrvRuleCode) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.protSrvRuleCode;
            InvoiceProtectiveServiceData data = this.getData();
            this.protSrvRuleCode = InvoiceProtectiveServiceColumns.protSrvRuleCode.convertToDB(protSrvRuleCode);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "protSrvRuleCode", String.class, oldValue, this.protSrvRuleCode);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Temperature getProtSrvTemp() throws GLException {
        try {
            return (Temperature)InvoiceProtectiveServiceColumns.protSrvTemp.convertFromDB(this.protSrvTemp);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setProtSrvTemp(Temperature protSrvTemp) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.protSrvTemp;
            InvoiceProtectiveServiceData data = this.getData();
            this.protSrvTemp = InvoiceProtectiveServiceColumns.protSrvTemp.convertToDB(protSrvTemp);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "protSrvTemp", Temperature.class, oldValue, this.protSrvTemp);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceProtectiveServiceColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceProtectiveServiceData data = this.getData();
            this.domainName = InvoiceProtectiveServiceColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
