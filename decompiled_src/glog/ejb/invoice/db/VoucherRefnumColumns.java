/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class VoucherRefnumColumns {
    public static SqlColumn voucherGid = new SqlColumn(String.class, "voucher_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn voucherRefnumQualGid = new SqlColumn(String.class, "voucher_refnum_qual_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn voucherRefnumValue = new SqlColumn(String.class, "voucher_refnum_value", 240, 0, 1, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
