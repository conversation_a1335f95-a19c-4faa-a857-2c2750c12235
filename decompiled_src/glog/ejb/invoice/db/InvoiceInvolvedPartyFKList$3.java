/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceInvolvedPartyFKList.3
implements Fk {
    InvoiceInvolvedPartyFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceInvolvedPartyData";
    }

    @Override
    public String getPkField() {
        return "involvedPartyQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvolvedPartyQualData";
    }

    @Override
    public String getFkDataField() {
        return "involvedPartyQualGid";
    }
}
