/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceEquipmentSealColumns;
import glog.ejb.invoice.db.InvoiceEquipmentSealData;
import glog.ejb.invoice.db.InvoiceEquipmentSealPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceEquipmentSealBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object seqNumber;
    public Object sealSequence;
    public Object sealNumber;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceEquipmentSealPK pk;
    protected transient InvoiceEquipmentSealData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceEquipmentSealBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceEquipmentSealPK.Callback theCall = new InvoiceEquipmentSealPK.Callback();

    public InvoiceEquipmentSealBeanDB() {
        super(false);
    }

    public InvoiceEquipmentSealPK ejbCreate(InvoiceEquipmentSealData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceEquipmentSealData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceEquipmentSealPK ejbFindByPrimaryKey(InvoiceEquipmentSealPK pk) throws FinderException {
        return (InvoiceEquipmentSealPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceEquipmentSealPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceEquipmentSealPK> v = new Vector<InvoiceEquipmentSealPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceEquipmentSealPK pk, InvoiceEquipmentSealData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceEquipmentSealPK pk, InvoiceEquipmentSealData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceEquipmentSealPK pk, InvoiceEquipmentSealData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (InvoiceEquipmentSealPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceEquipmentSealColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceEquipmentSealColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceEquipmentSealColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceEquipmentSealColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceEquipmentSealPK pk = (InvoiceEquipmentSealPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.sealSequence = pk.sealSequence;
        this.seqNumber = pk.seqNumber;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceEquipmentSealPK pk = new InvoiceEquipmentSealPK(0, this.invoiceGid, this.sealSequence, this.seqNumber, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceEquipmentSeal";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceEquipmentSealData getData() throws GLException {
        try {
            InvoiceEquipmentSealData retval = new InvoiceEquipmentSealData();
            retval.getFromBean(this, InvoiceEquipmentSealColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceEquipmentSealData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceEquipmentSealData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceEquipmentSealData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceEquipmentSealColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceEquipmentSealData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceEquipmentSealPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceEquipmentSealColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceEquipmentSealData data = this.getData();
            this.invoiceGid = InvoiceEquipmentSealColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getSeqNumber() throws GLException {
        try {
            return (Long)InvoiceEquipmentSealColumns.seqNumber.convertFromDB(this.seqNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSeqNumber(Long seqNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.seqNumber;
            InvoiceEquipmentSealData data = this.getData();
            this.seqNumber = InvoiceEquipmentSealColumns.seqNumber.convertToDB(seqNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "seqNumber", Long.class, oldValue, this.seqNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getSealSequence() throws GLException {
        try {
            return (Integer)InvoiceEquipmentSealColumns.sealSequence.convertFromDB(this.sealSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSealSequence(Integer sealSequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sealSequence;
            InvoiceEquipmentSealData data = this.getData();
            this.sealSequence = InvoiceEquipmentSealColumns.sealSequence.convertToDB(sealSequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sealSequence", Integer.class, oldValue, this.sealSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSealNumber() throws GLException {
        try {
            return (String)InvoiceEquipmentSealColumns.sealNumber.convertFromDB(this.sealNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSealNumber(String sealNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sealNumber;
            InvoiceEquipmentSealData data = this.getData();
            this.sealNumber = InvoiceEquipmentSealColumns.sealNumber.convertToDB(sealNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sealNumber", String.class, oldValue, this.sealNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceEquipmentSealColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceEquipmentSealData data = this.getData();
            this.domainName = InvoiceEquipmentSealColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
