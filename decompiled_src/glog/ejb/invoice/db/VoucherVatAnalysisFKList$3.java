/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherVatAnalysisFKList.3
implements Fk {
    VoucherVatAnalysisFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherVatAnalysisData";
    }

    @Override
    public String getPkField() {
        return "exchangeRateGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "ExchangeRateData";
    }

    @Override
    public String getFkDataField() {
        return "exchangeRateGid";
    }
}
