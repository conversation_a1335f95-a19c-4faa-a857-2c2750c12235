/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.DeclaredValueQualColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class DeclaredValueQualPK
extends Pk {
    public Object declaredValueQualGid;
    public transient Object declaredValueQualXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public DeclaredValueQualPK() {
    }

    public DeclaredValueQualPK(String declaredValueQualGid) {
        this.declaredValueQualGid = this.notNull(DeclaredValueQualColumns.declaredValueQualGid.convertToDB(declaredValueQualGid), "declaredValueQualGid");
    }

    public DeclaredValueQualPK(String domainName, String declaredValueQualXid) {
        this.domainName = domainName;
        this.declaredValueQualXid = declaredValueQualXid;
        this.declaredValueQualGid = DeclaredValueQualPK.concatForGid(domainName, declaredValueQualXid);
    }

    public DeclaredValueQualPK(int dummy, Object declaredValueQualGid) {
        this(dummy, declaredValueQualGid, null);
    }

    public DeclaredValueQualPK(int dummy, Object declaredValueQualGid, Object transaction) {
        this.declaredValueQualGid = declaredValueQualGid;
        this.transaction = transaction;
    }

    public DeclaredValueQualPK(DeclaredValueQualPK otherPk, Object transaction) {
        this.declaredValueQualGid = otherPk.declaredValueQualGid;
        this.declaredValueQualXid = otherPk.declaredValueQualXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.declaredValueQualGid != null ? String.valueOf(this.declaredValueQualGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.declaredValueQualGid != null ? String.valueOf(this.declaredValueQualGid) : "";
    }

    public static DeclaredValueQualPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new DeclaredValueQualPK(gids[0]) : null;
    }

    public static DeclaredValueQualPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new DeclaredValueQualPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.declaredValueQualGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof DeclaredValueQualPK)) {
            return false;
        }
        DeclaredValueQualPK otherPk = (DeclaredValueQualPK)other;
        return Functions.equals(otherPk.declaredValueQualGid, this.declaredValueQualGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.DeclaredValueQualHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.declaredValueQualGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return DeclaredValueQualColumns.declaredValueQualGid.convertFromDB(this.declaredValueQualGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static DeclaredValueQualPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("DeclaredValueQualPK requires a non-null XID to generate a GID");
            }
            DeclaredValueQualPK declaredValueQualPK = new DeclaredValueQualPK(domain, xid);
            return declaredValueQualPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static DeclaredValueQualPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            DeclaredValueQualPK declaredValueQualPK = DeclaredValueQualPK.newPK(domainName, xid, connection);
            return declaredValueQualPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return DeclaredValueQualPK.class;
        }

        @Override
        public final String getEntity() {
            return "DeclaredValueQual";
        }

        @Override
        public final String getTableName() {
            return "declared_value_qual";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"declared_value_qual_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            DeclaredValueQualPK result = new DeclaredValueQualPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
