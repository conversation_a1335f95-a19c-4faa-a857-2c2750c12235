/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveToleranceDetailData;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface LineApproveToleranceDetailRemoteDB
extends EJBObject {
    public LineApproveToleranceDetailData getData() throws RemoteException, GLException;

    public void setData(LineApproveToleranceDetailData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getLineApproveToleranceGid() throws RemoteException, GLException;

    public void setLineApproveToleranceGid(String var1) throws RemoteException, GLException;

    public Integer getLineApproveToleranceSeq() throws RemoteException, GLException;

    public void setLineApproveToleranceSeq(Integer var1) throws RemoteException, GLException;

    public String getCostType() throws RemoteException, GLException;

    public void setCostType(String var1) throws RemoteException, GLException;

    public String getAccessorialCodeGid() throws RemoteException, GLException;

    public void setAccessorialCodeGid(String var1) throws RemoteException, GLException;

    public String getGeneralLedgerGid() throws RemoteException, GLException;

    public void setGeneralLedgerGid(String var1) throws RemoteException, GLException;

    public String getPaymentMethodCodeGid() throws RemoteException, GLException;

    public void setPaymentMethodCodeGid(String var1) throws RemoteException, GLException;

    public Double getAllowablePercentAbove() throws RemoteException, GLException;

    public void setAllowablePercentAbove(Double var1) throws RemoteException, GLException;

    public Currency getAllowCurrAbove() throws RemoteException, GLException;

    public void setAllowCurrAbove(Currency var1) throws RemoteException, GLException;

    public Double getAllowablePercentBelow() throws RemoteException, GLException;

    public void setAllowablePercentBelow(Double var1) throws RemoteException, GLException;

    public Currency getAllowCurrBelow() throws RemoteException, GLException;

    public void setAllowCurrBelow(Currency var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
