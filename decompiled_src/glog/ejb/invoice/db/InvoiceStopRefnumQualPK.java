/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopRefnumQualColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoiceStopRefnumQualPK
extends Pk {
    public Object invoiceStopRefnumQualGid;
    public transient Object invoiceStopRefnumQualXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceStopRefnumQualPK() {
    }

    public InvoiceStopRefnumQualPK(String invoiceStopRefnumQualGid) {
        this.invoiceStopRefnumQualGid = this.notNull(InvoiceStopRefnumQualColumns.invoiceStopRefnumQualGid.convertToDB(invoiceStopRefnumQualGid), "invoiceStopRefnumQualGid");
    }

    public InvoiceStopRefnumQualPK(String domainName, String invoiceStopRefnumQualXid) {
        this.domainName = domainName;
        this.invoiceStopRefnumQualXid = invoiceStopRefnumQualXid;
        this.invoiceStopRefnumQualGid = InvoiceStopRefnumQualPK.concatForGid(domainName, invoiceStopRefnumQualXid);
    }

    public InvoiceStopRefnumQualPK(int dummy, Object invoiceStopRefnumQualGid) {
        this(dummy, invoiceStopRefnumQualGid, null);
    }

    public InvoiceStopRefnumQualPK(int dummy, Object invoiceStopRefnumQualGid, Object transaction) {
        this.invoiceStopRefnumQualGid = invoiceStopRefnumQualGid;
        this.transaction = transaction;
    }

    public InvoiceStopRefnumQualPK(InvoiceStopRefnumQualPK otherPk, Object transaction) {
        this.invoiceStopRefnumQualGid = otherPk.invoiceStopRefnumQualGid;
        this.invoiceStopRefnumQualXid = otherPk.invoiceStopRefnumQualXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceStopRefnumQualGid != null ? String.valueOf(this.invoiceStopRefnumQualGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceStopRefnumQualGid != null ? String.valueOf(this.invoiceStopRefnumQualGid) : "";
    }

    public static InvoiceStopRefnumQualPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceStopRefnumQualPK(gids[0]) : null;
    }

    public static InvoiceStopRefnumQualPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceStopRefnumQualPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceStopRefnumQualGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceStopRefnumQualPK)) {
            return false;
        }
        InvoiceStopRefnumQualPK otherPk = (InvoiceStopRefnumQualPK)other;
        return Functions.equals(otherPk.invoiceStopRefnumQualGid, this.invoiceStopRefnumQualGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceStopRefnumQualHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceStopRefnumQualGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceStopRefnumQualColumns.invoiceStopRefnumQualGid.convertFromDB(this.invoiceStopRefnumQualGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceStopRefnumQualPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoiceStopRefnumQualPK requires a non-null XID to generate a GID");
            }
            InvoiceStopRefnumQualPK invoiceStopRefnumQualPK = new InvoiceStopRefnumQualPK(domain, xid);
            return invoiceStopRefnumQualPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceStopRefnumQualPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoiceStopRefnumQualPK invoiceStopRefnumQualPK = InvoiceStopRefnumQualPK.newPK(domainName, xid, connection);
            return invoiceStopRefnumQualPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceStopRefnumQualPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceStopRefnumQual";
        }

        @Override
        public final String getTableName() {
            return "invoice_stop_refnum_qual";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_stop_refnum_qual_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceStopRefnumQualPK result = new InvoiceStopRefnumQualPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
