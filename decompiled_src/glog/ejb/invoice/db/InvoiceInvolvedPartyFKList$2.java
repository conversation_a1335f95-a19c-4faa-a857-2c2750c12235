/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceInvolvedPartyFKList.2
implements Fk {
    InvoiceInvolvedPartyFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceInvolvedPartyData";
    }

    @Override
    public String getPkField() {
        return "involvedPartyContactGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "ContactData";
    }

    @Override
    public String getFkDataField() {
        return "contactGid";
    }
}
