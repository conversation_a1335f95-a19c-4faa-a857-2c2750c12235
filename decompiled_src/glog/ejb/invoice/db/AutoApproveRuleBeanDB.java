/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.AutoApproveRuleColumns;
import glog.ejb.invoice.db.AutoApproveRuleData;
import glog.ejb.invoice.db.AutoApproveRulePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AutoApproveRuleBeanDB
extends BeanManagedEntityBean {
    public Object autoApproveRuleGid;
    public Object autoApproveRuleXid;
    public Object description;
    public Object defaultCostPercentAbove;
    public Object defaultCostPercentBelow;
    public Object defaultWeightPercentAbove;
    public Object defaultWeightPercentBelow;
    public Object defCostAbove;
    public Object defCostBelow;
    public Object defWeightAbove;
    public Object defWeightBelow;
    public Object locationProfileGid;
    public Object applyCostRule;
    public Object applyWeightRule;
    public Object ruleType;
    public Object approvePassThroughInv;
    public Object domainName;
    public Object modeProfileGid;
    public Object maxAmtOverInvoiceCost;
    public Object applyDeviationRule;
    public Object defDeviationCostBelow;
    public Object defDeviationCostAbove;
    public Object defDeviationAggType;
    public Object defDeviationPercentAbove;
    public Object defDeviationPercentBelow;
    public Object effectiveDate;
    public Object expirationDate;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AutoApproveRulePK pk;
    protected transient AutoApproveRuleData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AutoApproveRulePK.Callback theCall = new AutoApproveRulePK.Callback();

    public AutoApproveRuleBeanDB() {
        super(false);
    }

    public AutoApproveRulePK ejbCreate(AutoApproveRuleData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected AutoApproveRulePK newPK() throws GLException {
        return AutoApproveRulePK.newPK(this.getDomainName(), this.getAutoApproveRuleXid(), this.getConnection());
    }

    public void ejbPostCreate(AutoApproveRuleData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AutoApproveRulePK ejbFindByPrimaryKey(AutoApproveRulePK pk) throws FinderException {
        return (AutoApproveRulePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AutoApproveRulePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AutoApproveRulePK> v = new Vector<AutoApproveRulePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AutoApproveRulePK pk, AutoApproveRuleData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AutoApproveRulePK pk, AutoApproveRuleData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AutoApproveRulePK pk, AutoApproveRuleData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AutoApproveRuleColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AutoApproveRuleColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AutoApproveRuleColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AutoApproveRuleColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AutoApproveRulePK pk = (AutoApproveRulePK)genericPk;
        this.autoApproveRuleGid = pk.autoApproveRuleGid;
        this.domainName = pk.domainName;
        this.autoApproveRuleXid = pk.autoApproveRuleXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AutoApproveRulePK pk = new AutoApproveRulePK(0, this.autoApproveRuleGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AutoApproveRule";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AutoApproveRuleData getData() throws GLException {
        try {
            AutoApproveRuleData retval = new AutoApproveRuleData();
            retval.getFromBean(this, AutoApproveRuleColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AutoApproveRuleData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AutoApproveRuleData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AutoApproveRuleData oldData = modified ? this.getData() : null;
            data.setToBean(this, AutoApproveRuleColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AutoApproveRuleData.class;
    }

    @Override
    public Class getPkClass() {
        return AutoApproveRulePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getAutoApproveRuleGid() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.autoApproveRuleGid.convertFromDB(this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleGid(String autoApproveRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleGid;
            AutoApproveRuleData data = this.getData();
            this.autoApproveRuleGid = AutoApproveRuleColumns.autoApproveRuleGid.convertToDB(autoApproveRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleGid", String.class, oldValue, this.autoApproveRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAutoApproveRuleXid() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.autoApproveRuleXid.convertFromDB(this.autoApproveRuleXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAutoApproveRuleXid(String autoApproveRuleXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.autoApproveRuleXid;
            AutoApproveRuleData data = this.getData();
            this.autoApproveRuleXid = AutoApproveRuleColumns.autoApproveRuleXid.convertToDB(autoApproveRuleXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "autoApproveRuleXid", String.class, oldValue, this.autoApproveRuleXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            AutoApproveRuleData data = this.getData();
            this.description = AutoApproveRuleColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefaultCostPercentAbove() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defaultCostPercentAbove.convertFromDB(this.defaultCostPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultCostPercentAbove(Double defaultCostPercentAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultCostPercentAbove;
            AutoApproveRuleData data = this.getData();
            this.defaultCostPercentAbove = AutoApproveRuleColumns.defaultCostPercentAbove.convertToDB(defaultCostPercentAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultCostPercentAbove", Double.class, oldValue, this.defaultCostPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefaultCostPercentBelow() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defaultCostPercentBelow.convertFromDB(this.defaultCostPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultCostPercentBelow(Double defaultCostPercentBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultCostPercentBelow;
            AutoApproveRuleData data = this.getData();
            this.defaultCostPercentBelow = AutoApproveRuleColumns.defaultCostPercentBelow.convertToDB(defaultCostPercentBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultCostPercentBelow", Double.class, oldValue, this.defaultCostPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefaultWeightPercentAbove() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defaultWeightPercentAbove.convertFromDB(this.defaultWeightPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultWeightPercentAbove(Double defaultWeightPercentAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultWeightPercentAbove;
            AutoApproveRuleData data = this.getData();
            this.defaultWeightPercentAbove = AutoApproveRuleColumns.defaultWeightPercentAbove.convertToDB(defaultWeightPercentAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultWeightPercentAbove", Double.class, oldValue, this.defaultWeightPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefaultWeightPercentBelow() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defaultWeightPercentBelow.convertFromDB(this.defaultWeightPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultWeightPercentBelow(Double defaultWeightPercentBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultWeightPercentBelow;
            AutoApproveRuleData data = this.getData();
            this.defaultWeightPercentBelow = AutoApproveRuleColumns.defaultWeightPercentBelow.convertToDB(defaultWeightPercentBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultWeightPercentBelow", Double.class, oldValue, this.defaultWeightPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getDefCostAbove() throws GLException {
        try {
            return (Currency)AutoApproveRuleColumns.defCostAbove.convertFromDB(this.defCostAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefCostAbove(Currency defCostAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defCostAbove;
            AutoApproveRuleData data = this.getData();
            this.defCostAbove = AutoApproveRuleColumns.defCostAbove.convertToDB(defCostAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defCostAbove", Currency.class, oldValue, this.defCostAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getDefCostBelow() throws GLException {
        try {
            return (Currency)AutoApproveRuleColumns.defCostBelow.convertFromDB(this.defCostBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefCostBelow(Currency defCostBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defCostBelow;
            AutoApproveRuleData data = this.getData();
            this.defCostBelow = AutoApproveRuleColumns.defCostBelow.convertToDB(defCostBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defCostBelow", Currency.class, oldValue, this.defCostBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getDefWeightAbove() throws GLException {
        try {
            return (Weight)AutoApproveRuleColumns.defWeightAbove.convertFromDB(this.defWeightAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefWeightAbove(Weight defWeightAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defWeightAbove;
            AutoApproveRuleData data = this.getData();
            this.defWeightAbove = AutoApproveRuleColumns.defWeightAbove.convertToDB(defWeightAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defWeightAbove", Weight.class, oldValue, this.defWeightAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Weight getDefWeightBelow() throws GLException {
        try {
            return (Weight)AutoApproveRuleColumns.defWeightBelow.convertFromDB(this.defWeightBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefWeightBelow(Weight defWeightBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defWeightBelow;
            AutoApproveRuleData data = this.getData();
            this.defWeightBelow = AutoApproveRuleColumns.defWeightBelow.convertToDB(defWeightBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defWeightBelow", Weight.class, oldValue, this.defWeightBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getLocationProfileGid() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.locationProfileGid.convertFromDB(this.locationProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLocationProfileGid(String locationProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.locationProfileGid;
            AutoApproveRuleData data = this.getData();
            this.locationProfileGid = AutoApproveRuleColumns.locationProfileGid.convertToDB(locationProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "locationProfileGid", String.class, oldValue, this.locationProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getApplyCostRule() throws GLException {
        try {
            return (Boolean)AutoApproveRuleColumns.applyCostRule.convertFromDB(this.applyCostRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApplyCostRule(Boolean applyCostRule) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.applyCostRule;
            AutoApproveRuleData data = this.getData();
            this.applyCostRule = AutoApproveRuleColumns.applyCostRule.convertToDB(applyCostRule);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "applyCostRule", Boolean.class, oldValue, this.applyCostRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getApplyWeightRule() throws GLException {
        try {
            return (Boolean)AutoApproveRuleColumns.applyWeightRule.convertFromDB(this.applyWeightRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApplyWeightRule(Boolean applyWeightRule) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.applyWeightRule;
            AutoApproveRuleData data = this.getData();
            this.applyWeightRule = AutoApproveRuleColumns.applyWeightRule.convertToDB(applyWeightRule);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "applyWeightRule", Boolean.class, oldValue, this.applyWeightRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getRuleType() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.ruleType.convertFromDB(this.ruleType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRuleType(String ruleType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.ruleType;
            AutoApproveRuleData data = this.getData();
            this.ruleType = AutoApproveRuleColumns.ruleType.convertToDB(ruleType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "ruleType", String.class, oldValue, this.ruleType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getApprovePassThroughInv() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.approvePassThroughInv.convertFromDB(this.approvePassThroughInv);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApprovePassThroughInv(String approvePassThroughInv) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.approvePassThroughInv;
            AutoApproveRuleData data = this.getData();
            this.approvePassThroughInv = AutoApproveRuleColumns.approvePassThroughInv.convertToDB(approvePassThroughInv);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "approvePassThroughInv", String.class, oldValue, this.approvePassThroughInv);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AutoApproveRuleData data = this.getData();
            this.domainName = AutoApproveRuleColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getModeProfileGid() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.modeProfileGid.convertFromDB(this.modeProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setModeProfileGid(String modeProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.modeProfileGid;
            AutoApproveRuleData data = this.getData();
            this.modeProfileGid = AutoApproveRuleColumns.modeProfileGid.convertToDB(modeProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "modeProfileGid", String.class, oldValue, this.modeProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getMaxAmtOverInvoiceCost() throws GLException {
        try {
            return (Currency)AutoApproveRuleColumns.maxAmtOverInvoiceCost.convertFromDB(this.maxAmtOverInvoiceCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setMaxAmtOverInvoiceCost(Currency maxAmtOverInvoiceCost) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.maxAmtOverInvoiceCost;
            AutoApproveRuleData data = this.getData();
            this.maxAmtOverInvoiceCost = AutoApproveRuleColumns.maxAmtOverInvoiceCost.convertToDB(maxAmtOverInvoiceCost);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "maxAmtOverInvoiceCost", Currency.class, oldValue, this.maxAmtOverInvoiceCost);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getApplyDeviationRule() throws GLException {
        try {
            return (Boolean)AutoApproveRuleColumns.applyDeviationRule.convertFromDB(this.applyDeviationRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setApplyDeviationRule(Boolean applyDeviationRule) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.applyDeviationRule;
            AutoApproveRuleData data = this.getData();
            this.applyDeviationRule = AutoApproveRuleColumns.applyDeviationRule.convertToDB(applyDeviationRule);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "applyDeviationRule", Boolean.class, oldValue, this.applyDeviationRule);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getDefDeviationCostBelow() throws GLException {
        try {
            return (Currency)AutoApproveRuleColumns.defDeviationCostBelow.convertFromDB(this.defDeviationCostBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefDeviationCostBelow(Currency defDeviationCostBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defDeviationCostBelow;
            AutoApproveRuleData data = this.getData();
            this.defDeviationCostBelow = AutoApproveRuleColumns.defDeviationCostBelow.convertToDB(defDeviationCostBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defDeviationCostBelow", Currency.class, oldValue, this.defDeviationCostBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getDefDeviationCostAbove() throws GLException {
        try {
            return (Currency)AutoApproveRuleColumns.defDeviationCostAbove.convertFromDB(this.defDeviationCostAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefDeviationCostAbove(Currency defDeviationCostAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defDeviationCostAbove;
            AutoApproveRuleData data = this.getData();
            this.defDeviationCostAbove = AutoApproveRuleColumns.defDeviationCostAbove.convertToDB(defDeviationCostAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defDeviationCostAbove", Currency.class, oldValue, this.defDeviationCostAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDefDeviationAggType() throws GLException {
        try {
            return (String)AutoApproveRuleColumns.defDeviationAggType.convertFromDB(this.defDeviationAggType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefDeviationAggType(String defDeviationAggType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defDeviationAggType;
            AutoApproveRuleData data = this.getData();
            this.defDeviationAggType = AutoApproveRuleColumns.defDeviationAggType.convertToDB(defDeviationAggType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defDeviationAggType", String.class, oldValue, this.defDeviationAggType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefDeviationPercentAbove() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defDeviationPercentAbove.convertFromDB(this.defDeviationPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefDeviationPercentAbove(Double defDeviationPercentAbove) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defDeviationPercentAbove;
            AutoApproveRuleData data = this.getData();
            this.defDeviationPercentAbove = AutoApproveRuleColumns.defDeviationPercentAbove.convertToDB(defDeviationPercentAbove);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defDeviationPercentAbove", Double.class, oldValue, this.defDeviationPercentAbove);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getDefDeviationPercentBelow() throws GLException {
        try {
            return (Double)AutoApproveRuleColumns.defDeviationPercentBelow.convertFromDB(this.defDeviationPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefDeviationPercentBelow(Double defDeviationPercentBelow) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defDeviationPercentBelow;
            AutoApproveRuleData data = this.getData();
            this.defDeviationPercentBelow = AutoApproveRuleColumns.defDeviationPercentBelow.convertToDB(defDeviationPercentBelow);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defDeviationPercentBelow", Double.class, oldValue, this.defDeviationPercentBelow);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getEffectiveDate() throws GLException {
        try {
            return (LocalDate)AutoApproveRuleColumns.effectiveDate.convertFromDB(this.effectiveDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEffectiveDate(LocalDate effectiveDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.effectiveDate;
            AutoApproveRuleData data = this.getData();
            this.effectiveDate = AutoApproveRuleColumns.effectiveDate.convertToDB(effectiveDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "effectiveDate", LocalDate.class, oldValue, this.effectiveDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExpirationDate() throws GLException {
        try {
            return (LocalDate)AutoApproveRuleColumns.expirationDate.convertFromDB(this.expirationDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExpirationDate(LocalDate expirationDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.expirationDate;
            AutoApproveRuleData data = this.getData();
            this.expirationDate = AutoApproveRuleColumns.expirationDate.convertToDB(expirationDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "expirationDate", LocalDate.class, oldValue, this.expirationDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
