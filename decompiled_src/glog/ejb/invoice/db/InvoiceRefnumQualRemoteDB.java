/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRefnumQualData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceRefnumQualRemoteDB
extends EJBObject {
    public InvoiceRefnumQualData getData() throws RemoteException, GLException;

    public void setData(InvoiceRefnumQualData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceRefnumQualXid() throws RemoteException, GLException;

    public void setInvoiceRefnumQualXid(String var1) throws RemoteException, GLException;

    public String getInvoiceRefnumDesc() throws RemoteException, GLException;

    public void setInvoiceRefnumDesc(String var1) throws RemoteException, GLException;

    public String getDefaultRefnumBnTypeGid() throws RemoteException, GLException;

    public void setDefaultRefnumBnTypeGid(String var1) throws RemoteException, GLException;

    public String getUpdateFlag() throws RemoteException, GLException;

    public void setUpdateFlag(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
