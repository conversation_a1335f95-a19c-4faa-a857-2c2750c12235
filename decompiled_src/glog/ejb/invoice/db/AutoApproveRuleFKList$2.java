/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class AutoApproveRuleFKList.2
implements Fk {
    AutoApproveRuleFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AutoApproveRuleData";
    }

    @Override
    public String getPkField() {
        return "modeProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.modeprofile.db";
    }

    @Override
    public String getFkDataClass() {
        return "ModeProfileData";
    }

    @Override
    public String getFkDataField() {
        return "modeProfileGid";
    }
}
