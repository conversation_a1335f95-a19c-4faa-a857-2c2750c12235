/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherStatusData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface VoucherStatusRemoteDB
extends EJBObject {
    public VoucherStatusData getData() throws RemoteException, GLException;

    public void setData(VoucherStatusData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getVoucherGid() throws RemoteException, GLException;

    public void setVoucherGid(String var1) throws RemoteException, GLException;

    public String getStatusTypeGid() throws RemoteException, GLException;

    public void setStatusTypeGid(String var1) throws RemoteException, GLException;

    public String getStatusValueGid() throws RemoteException, GLException;

    public void setStatusValueGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
