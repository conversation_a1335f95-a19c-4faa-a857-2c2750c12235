/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRulePK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AutoApproveRuleData
extends BeanData {
    public String autoApproveRuleGid;
    public String autoApproveRuleXid;
    public String description;
    public Double defaultCostPercentAbove;
    public Double defaultCostPercentBelow;
    public Double defaultWeightPercentAbove;
    public Double defaultWeightPercentBelow;
    public Currency defCostAbove;
    public Currency defCostBelow;
    public Weight defWeightAbove;
    public Weight defWeightBelow;
    public String locationProfileGid;
    public Boolean applyCostRule = Boolean.valueOf("true");
    public Boolean applyWeightRule = Boolean.valueOf("true");
    public String ruleType = String.valueOf("NON-CONSOLIDATED");
    public String approvePassThroughInv = String.valueOf("N");
    public String domainName;
    public String modeProfileGid;
    public Currency maxAmtOverInvoiceCost;
    public Boolean applyDeviationRule = Boolean.valueOf("false");
    public Currency defDeviationCostBelow;
    public Currency defDeviationCostAbove;
    public String defDeviationAggType;
    public Double defDeviationPercentAbove;
    public Double defDeviationPercentBelow;
    public LocalDate effectiveDate;
    public LocalDate expirationDate;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field autoApproveRuleGidField = beanDataFields[0];
    public static Field autoApproveRuleXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field defaultCostPercentAboveField = beanDataFields[3];
    public static Field defaultCostPercentBelowField = beanDataFields[4];
    public static Field defaultWeightPercentAboveField = beanDataFields[5];
    public static Field defaultWeightPercentBelowField = beanDataFields[6];
    public static Field defCostAboveField = beanDataFields[7];
    public static Field defCostBelowField = beanDataFields[8];
    public static Field defWeightAboveField = beanDataFields[9];
    public static Field defWeightBelowField = beanDataFields[10];
    public static Field locationProfileGidField = beanDataFields[11];
    public static Field applyCostRuleField = beanDataFields[12];
    public static Field applyWeightRuleField = beanDataFields[13];
    public static Field ruleTypeField = beanDataFields[14];
    public static Field approvePassThroughInvField = beanDataFields[15];
    public static Field domainNameField = beanDataFields[16];
    public static Field modeProfileGidField = beanDataFields[17];
    public static Field maxAmtOverInvoiceCostField = beanDataFields[18];
    public static Field applyDeviationRuleField = beanDataFields[19];
    public static Field defDeviationCostBelowField = beanDataFields[20];
    public static Field defDeviationCostAboveField = beanDataFields[21];
    public static Field defDeviationAggTypeField = beanDataFields[22];
    public static Field defDeviationPercentAboveField = beanDataFields[23];
    public static Field defDeviationPercentBelowField = beanDataFields[24];
    public static Field effectiveDateField = beanDataFields[25];
    public static Field expirationDateField = beanDataFields[26];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AutoApproveRuleData() {
    }

    public AutoApproveRuleData(AutoApproveRuleData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAutoApproveRulePK();
    }

    @Legacy
    public AutoApproveRulePK getAutoApproveRulePK() {
        if (this.autoApproveRuleGid == null) {
            return null;
        }
        return new AutoApproveRulePK(this.autoApproveRuleGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAutoApproveRulePK((AutoApproveRulePK)pk);
    }

    @Legacy
    public void setAutoApproveRulePK(AutoApproveRulePK pk) {
        this.autoApproveRuleGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.AutoApproveRuleQueryGen";
    }

    public static AutoApproveRuleData load(Connection conn, AutoApproveRulePK pk) throws GLException {
        return (AutoApproveRuleData)AutoApproveRuleData.load(conn, pk, AutoApproveRuleData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AutoApproveRuleData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleData.load(conn, whereClause, prepareArguments, fetchSize, AutoApproveRuleData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AutoApproveRuleData.class);
    }
}
