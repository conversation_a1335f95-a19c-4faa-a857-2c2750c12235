/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class CommercialInvoiceChargeFKList.2
implements Fk {
    CommercialInvoiceChargeFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "CommercialInvoiceChargeData";
    }

    @Override
    public String getPkField() {
        return "commercialInvChargeCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "CommercialInvChargeCodeData";
    }

    @Override
    public String getFkDataField() {
        return "commercialInvChargeCodeGid";
    }
}
