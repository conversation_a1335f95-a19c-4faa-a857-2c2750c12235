/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineAppTolShipmentRefnumColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class LineAppTolShipmentRefnumPK
extends Pk {
    public Object lineApproveToleranceGid;
    public Object refnumSequence;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public LineAppTolShipmentRefnumPK() {
    }

    public LineAppTolShipmentRefnumPK(String lineApproveToleranceGid, Integer refnumSequence) {
        this.lineApproveToleranceGid = this.notNull(LineAppTolShipmentRefnumColumns.lineApproveToleranceGid.convertToDB(lineApproveToleranceGid), "lineApproveToleranceGid");
        this.refnumSequence = this.notNull(LineAppTolShipmentRefnumColumns.refnumSequence.convertToDB(refnumSequence), "refnumSequence");
    }

    public LineAppTolShipmentRefnumPK(int dummy, Object lineApproveToleranceGid, Object refnumSequence) {
        this(dummy, lineApproveToleranceGid, refnumSequence, null);
    }

    public LineAppTolShipmentRefnumPK(int dummy, Object lineApproveToleranceGid, Object refnumSequence, Object transaction) {
        this.lineApproveToleranceGid = lineApproveToleranceGid;
        this.refnumSequence = refnumSequence;
        this.transaction = transaction;
    }

    public LineAppTolShipmentRefnumPK(LineAppTolShipmentRefnumPK otherPk, Object transaction) {
        this.lineApproveToleranceGid = otherPk.lineApproveToleranceGid;
        this.refnumSequence = otherPk.refnumSequence;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.lineApproveToleranceGid != null ? String.valueOf(this.lineApproveToleranceGid) : "") + " " + (this.refnumSequence != null ? String.valueOf(this.refnumSequence) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.lineApproveToleranceGid != null ? String.valueOf(this.lineApproveToleranceGid) : "") + "|" + (this.refnumSequence != null ? String.valueOf(this.refnumSequence) : "");
    }

    public static LineAppTolShipmentRefnumPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new LineAppTolShipmentRefnumPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public static LineAppTolShipmentRefnumPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new LineAppTolShipmentRefnumPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.lineApproveToleranceGid.hashCode() + this.refnumSequence.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof LineAppTolShipmentRefnumPK)) {
            return false;
        }
        LineAppTolShipmentRefnumPK otherPk = (LineAppTolShipmentRefnumPK)other;
        return Functions.equals(otherPk.lineApproveToleranceGid, this.lineApproveToleranceGid) && Functions.equals(otherPk.refnumSequence, this.refnumSequence) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.LineAppTolShipmentRefnumHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.lineApproveToleranceGid;
            }
            case 1: {
                return this.refnumSequence;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return LineAppTolShipmentRefnumColumns.lineApproveToleranceGid.convertFromDB(this.lineApproveToleranceGid);
            }
            case 1: {
                return LineAppTolShipmentRefnumColumns.refnumSequence.convertFromDB(this.refnumSequence);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return LineAppTolShipmentRefnumPK.class;
        }

        @Override
        public final String getEntity() {
            return "LineAppTolShipmentRefnum";
        }

        @Override
        public final String getTableName() {
            return "line_app_tol_shipment_refnum";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"line_approve_tolerance_gid", "refnum_sequence"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            LineAppTolShipmentRefnumPK result = new LineAppTolShipmentRefnumPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
