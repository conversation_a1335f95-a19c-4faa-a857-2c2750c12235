/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceShipmentPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceShipmentData
extends BeanData {
    public String invoiceGid;
    public Long sequenceNo;
    public String shipmentGid;
    public String domainName;
    public String sShipUnitGid;
    public Long sShipUnitLineNo;
    public String trackingNumber;
    public Long lineitemSeqNo;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceShipmentData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field sequenceNoField = beanDataFields[1];
    public static Field shipmentGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];
    public static Field sShipUnitGidField = beanDataFields[4];
    public static Field sShipUnitLineNoField = beanDataFields[5];
    public static Field trackingNumberField = beanDataFields[6];
    public static Field lineitemSeqNoField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceShipmentData() {
    }

    public InvoiceShipmentData(InvoiceShipmentData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceShipmentPK();
    }

    @Legacy
    public InvoiceShipmentPK getInvoiceShipmentPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.sequenceNo == null) {
            return null;
        }
        return new InvoiceShipmentPK(this.invoiceGid, this.sequenceNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceShipmentPK((InvoiceShipmentPK)pk);
    }

    @Legacy
    public void setInvoiceShipmentPK(InvoiceShipmentPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.sequenceNo = (Long)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceShipmentQueryGen";
    }

    public static InvoiceShipmentData load(Connection conn, InvoiceShipmentPK pk) throws GLException {
        return (InvoiceShipmentData)InvoiceShipmentData.load(conn, pk, InvoiceShipmentData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceShipmentData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceShipmentData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceShipmentData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceShipmentData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceShipmentData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceShipmentData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceShipmentData.class);
    }
}
