/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlLookupKeyDColumns;
import glog.ejb.invoice.db.GlLookupKeyDData;
import glog.ejb.invoice.db.GlLookupKeyDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlLookupKeyDBeanDB
extends BeanManagedEntityBean {
    public Object glLookupKeyGid;
    public Object rank;
    public Object glKeyComponentGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlLookupKeyDPK pk;
    protected transient GlLookupKeyDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlLookupKeyDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlLookupKeyDPK.Callback theCall = new GlLookupKeyDPK.Callback();

    public GlLookupKeyDBeanDB() {
        super(false);
    }

    public GlLookupKeyDPK ejbCreate(GlLookupKeyDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(GlLookupKeyDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlLookupKeyDPK ejbFindByPrimaryKey(GlLookupKeyDPK pk) throws FinderException {
        return (GlLookupKeyDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlLookupKeyDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlLookupKeyDPK> v = new Vector<GlLookupKeyDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlLookupKeyDPK pk, GlLookupKeyDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlLookupKeyDPK pk, GlLookupKeyDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlLookupKeyDPK pk, GlLookupKeyDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (GlLookupKeyDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlLookupKeyDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlLookupKeyDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlLookupKeyDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlLookupKeyDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlLookupKeyDPK pk = (GlLookupKeyDPK)genericPk;
        this.glLookupKeyGid = pk.glLookupKeyGid;
        this.rank = pk.rank;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlLookupKeyDPK pk = new GlLookupKeyDPK(0, this.glLookupKeyGid, this.rank, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlLookupKeyD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlLookupKeyDData getData() throws GLException {
        try {
            GlLookupKeyDData retval = new GlLookupKeyDData();
            retval.getFromBean(this, GlLookupKeyDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlLookupKeyDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlLookupKeyDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlLookupKeyDData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlLookupKeyDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlLookupKeyDData.class;
    }

    @Override
    public Class getPkClass() {
        return GlLookupKeyDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlLookupKeyGid() throws GLException {
        try {
            return (String)GlLookupKeyDColumns.glLookupKeyGid.convertFromDB(this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlLookupKeyGid(String glLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glLookupKeyGid;
            GlLookupKeyDData data = this.getData();
            this.glLookupKeyGid = GlLookupKeyDColumns.glLookupKeyGid.convertToDB(glLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glLookupKeyGid", String.class, oldValue, this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Double getRank() throws GLException {
        try {
            return (Double)GlLookupKeyDColumns.rank.convertFromDB(this.rank);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRank(Double rank) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.rank;
            GlLookupKeyDData data = this.getData();
            this.rank = GlLookupKeyDColumns.rank.convertToDB(rank);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "rank", Double.class, oldValue, this.rank);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlKeyComponentGid() throws GLException {
        try {
            return (String)GlLookupKeyDColumns.glKeyComponentGid.convertFromDB(this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlKeyComponentGid(String glKeyComponentGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glKeyComponentGid;
            GlLookupKeyDData data = this.getData();
            this.glKeyComponentGid = GlLookupKeyDColumns.glKeyComponentGid.convertToDB(glKeyComponentGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glKeyComponentGid", String.class, oldValue, this.glKeyComponentGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlLookupKeyDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlLookupKeyDData data = this.getData();
            this.domainName = GlLookupKeyDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
