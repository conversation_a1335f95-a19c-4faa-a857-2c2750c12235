/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRemarkPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceRemarkData
extends BeanData {
    public String invoiceGid;
    public Integer remarkSeqNo;
    public String remarkQualIdentifier;
    public String remarkText;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRemarkData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field remarkSeqNoField = beanDataFields[1];
    public static Field remarkQualIdentifierField = beanDataFields[2];
    public static Field remarkTextField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceRemarkData() {
    }

    public InvoiceRemarkData(InvoiceRemarkData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceRemarkPK();
    }

    @Legacy
    public InvoiceRemarkPK getInvoiceRemarkPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.remarkSeqNo == null) {
            return null;
        }
        return new InvoiceRemarkPK(this.invoiceGid, this.remarkSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceRemarkPK((InvoiceRemarkPK)pk);
    }

    @Legacy
    public void setInvoiceRemarkPK(InvoiceRemarkPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.remarkSeqNo = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceRemarkQueryGen";
    }

    public static InvoiceRemarkData load(Connection conn, InvoiceRemarkPK pk) throws GLException {
        return (InvoiceRemarkData)InvoiceRemarkData.load(conn, pk, InvoiceRemarkData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceRemarkData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceRemarkData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRemarkData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceRemarkData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceRemarkData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRemarkData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceRemarkData.class);
    }
}
