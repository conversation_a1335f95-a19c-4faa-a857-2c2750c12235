/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class GeneralLedgerCodeColumns {
    public static SqlColumn generalLedgerGid = new SqlColumn(String.class, "general_ledger_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn generalLedgerXid = new SqlColumn(String.class, "general_ledger_xid", 101, 0, 1, null, 1, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 101, 0, 0, null, 2, null);
    public static SqlColumn isActive = new SqlColumn(Boolean.class, "is_active", 1, 0, 1, Boolean.valueOf("false"), 3, null);
    public static SqlColumn buyOrderGlLookupKeyGid = new SqlColumn(String.class, "buy_order_gl_lookup_key_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn sellOrderGlLookupKeyGid = new SqlColumn(String.class, "sell_order_gl_lookup_key_gid", 101, 0, 0, null, 5, null);
    public static SqlColumn buyShipGlLookupKeyGid = new SqlColumn(String.class, "buy_ship_gl_lookup_key_gid", 101, 0, 0, null, 6, null);
    public static SqlColumn sellShipGlLookupKeyGid = new SqlColumn(String.class, "sell_ship_gl_lookup_key_gid", 101, 0, 0, null, 7, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 8, null);
}
