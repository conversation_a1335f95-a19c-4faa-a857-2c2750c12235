/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceSummaryDetailColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceSummaryDetailPK
extends Pk {
    public Object invoiceGid;
    public Object lineitemSeqNo;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceSummaryDetailPK() {
    }

    public InvoiceSummaryDetailPK(String invoiceGid, Integer lineitemSeqNo) {
        this.invoiceGid = this.notNull(InvoiceSummaryDetailColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.lineitemSeqNo = this.notNull(InvoiceSummaryDetailColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
    }

    public InvoiceSummaryDetailPK(int dummy, Object invoiceGid, Object lineitemSeqNo) {
        this(dummy, invoiceGid, lineitemSeqNo, null);
    }

    public InvoiceSummaryDetailPK(int dummy, Object invoiceGid, Object lineitemSeqNo, Object transaction) {
        this.invoiceGid = invoiceGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.transaction = transaction;
    }

    public InvoiceSummaryDetailPK(InvoiceSummaryDetailPK otherPk, Object transaction) {
        this.invoiceGid = otherPk.invoiceGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "");
    }

    public static InvoiceSummaryDetailPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceSummaryDetailPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public static InvoiceSummaryDetailPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceSummaryDetailPK(gids[0], Integer.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.invoiceGid.hashCode() + this.lineitemSeqNo.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceSummaryDetailPK)) {
            return false;
        }
        InvoiceSummaryDetailPK otherPk = (InvoiceSummaryDetailPK)other;
        return Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceSummaryDetailHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceGid;
            }
            case 1: {
                return this.lineitemSeqNo;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceSummaryDetailColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 1: {
                return InvoiceSummaryDetailColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceSummaryDetailPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceSummaryDetail";
        }

        @Override
        public final String getTableName() {
            return "invoice_summary_detail";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_gid", "lineitem_seq_no"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceSummaryDetailPK result = new InvoiceSummaryDetailPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
