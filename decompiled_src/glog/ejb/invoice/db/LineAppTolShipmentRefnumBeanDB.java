/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.LineAppTolShipmentRefnumColumns;
import glog.ejb.invoice.db.LineAppTolShipmentRefnumData;
import glog.ejb.invoice.db.LineAppTolShipmentRefnumPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class LineAppTolShipmentRefnumBeanDB
extends BeanManagedEntityBean {
    public Object lineApproveToleranceGid;
    public Object refnumSequence;
    public Object shipmentRefnumQualGid;
    public Object shipmentRefnumValue;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient LineAppTolShipmentRefnumPK pk;
    protected transient LineAppTolShipmentRefnumData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineAppTolShipmentRefnumBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static LineAppTolShipmentRefnumPK.Callback theCall = new LineAppTolShipmentRefnumPK.Callback();

    public LineAppTolShipmentRefnumBeanDB() {
        super(false);
    }

    public LineAppTolShipmentRefnumPK ejbCreate(LineAppTolShipmentRefnumData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(LineAppTolShipmentRefnumData data) throws CreateException {
        this.ejbPostCreator();
    }

    public LineAppTolShipmentRefnumPK ejbFindByPrimaryKey(LineAppTolShipmentRefnumPK pk) throws FinderException {
        return (LineAppTolShipmentRefnumPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(LineAppTolShipmentRefnumPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<LineAppTolShipmentRefnumPK> v = new Vector<LineAppTolShipmentRefnumPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(LineAppTolShipmentRefnumPK pk, LineAppTolShipmentRefnumData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(LineAppTolShipmentRefnumPK pk, LineAppTolShipmentRefnumData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(LineAppTolShipmentRefnumPK pk, LineAppTolShipmentRefnumData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (LineAppTolShipmentRefnumPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(LineAppTolShipmentRefnumColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(LineAppTolShipmentRefnumColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(LineAppTolShipmentRefnumColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(LineAppTolShipmentRefnumColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        LineAppTolShipmentRefnumPK pk = (LineAppTolShipmentRefnumPK)genericPk;
        this.lineApproveToleranceGid = pk.lineApproveToleranceGid;
        this.refnumSequence = pk.refnumSequence;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        LineAppTolShipmentRefnumPK pk = new LineAppTolShipmentRefnumPK(0, this.lineApproveToleranceGid, this.refnumSequence, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.LineAppTolShipmentRefnum";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public LineAppTolShipmentRefnumData getData() throws GLException {
        try {
            LineAppTolShipmentRefnumData retval = new LineAppTolShipmentRefnumData();
            retval.getFromBean(this, LineAppTolShipmentRefnumColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(LineAppTolShipmentRefnumData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(LineAppTolShipmentRefnumData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            LineAppTolShipmentRefnumData oldData = modified ? this.getData() : null;
            data.setToBean(this, LineAppTolShipmentRefnumColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return LineAppTolShipmentRefnumData.class;
    }

    @Override
    public Class getPkClass() {
        return LineAppTolShipmentRefnumPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getLineApproveToleranceGid() throws GLException {
        try {
            return (String)LineAppTolShipmentRefnumColumns.lineApproveToleranceGid.convertFromDB(this.lineApproveToleranceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineApproveToleranceGid(String lineApproveToleranceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineApproveToleranceGid;
            LineAppTolShipmentRefnumData data = this.getData();
            this.lineApproveToleranceGid = LineAppTolShipmentRefnumColumns.lineApproveToleranceGid.convertToDB(lineApproveToleranceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineApproveToleranceGid", String.class, oldValue, this.lineApproveToleranceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getRefnumSequence() throws GLException {
        try {
            return (Integer)LineAppTolShipmentRefnumColumns.refnumSequence.convertFromDB(this.refnumSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setRefnumSequence(Integer refnumSequence) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.refnumSequence;
            LineAppTolShipmentRefnumData data = this.getData();
            this.refnumSequence = LineAppTolShipmentRefnumColumns.refnumSequence.convertToDB(refnumSequence);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "refnumSequence", Integer.class, oldValue, this.refnumSequence);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getShipmentRefnumQualGid() throws GLException {
        try {
            return (String)LineAppTolShipmentRefnumColumns.shipmentRefnumQualGid.convertFromDB(this.shipmentRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setShipmentRefnumQualGid(String shipmentRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.shipmentRefnumQualGid;
            LineAppTolShipmentRefnumData data = this.getData();
            this.shipmentRefnumQualGid = LineAppTolShipmentRefnumColumns.shipmentRefnumQualGid.convertToDB(shipmentRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "shipmentRefnumQualGid", String.class, oldValue, this.shipmentRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getShipmentRefnumValue() throws GLException {
        try {
            return (String)LineAppTolShipmentRefnumColumns.shipmentRefnumValue.convertFromDB(this.shipmentRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setShipmentRefnumValue(String shipmentRefnumValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.shipmentRefnumValue;
            LineAppTolShipmentRefnumData data = this.getData();
            this.shipmentRefnumValue = LineAppTolShipmentRefnumColumns.shipmentRefnumValue.convertToDB(shipmentRefnumValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "shipmentRefnumValue", String.class, oldValue, this.shipmentRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)LineAppTolShipmentRefnumColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            LineAppTolShipmentRefnumData data = this.getData();
            this.domainName = LineAppTolShipmentRefnumColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
