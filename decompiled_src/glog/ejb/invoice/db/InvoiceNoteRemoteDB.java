/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceNoteData;
import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceNoteRemoteDB
extends EJBObject {
    public InvoiceNoteData getData() throws RemoteException, GLException;

    public void setData(InvoiceNoteData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteEx<PERSON>, <PERSON>LEx<PERSON>;

    public String getInvoiceGid() throws RemoteException, <PERSON>LException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Long getInvoiceNoteSeqNo() throws RemoteException, GLException;

    public void setInvoiceNoteSeqNo(Long var1) throws RemoteException, GLException;

    public LocalTimestamp getTimestamp() throws RemoteException, GLException;

    public void setTimestamp(LocalTimestamp var1) throws RemoteException, GLException;

    public String getSummary() throws RemoteException, GLException;

    public void setSummary(String var1) throws RemoteException, GLException;

    public String getNote() throws RemoteException, GLException;

    public void setNote(String var1) throws RemoteException, GLException;

    public Boolean getIsSystemGenerated() throws RemoteException, GLException;

    public void setIsSystemGenerated(Boolean var1) throws RemoteException, GLException;

    public String getEnteredBy() throws RemoteException, GLException;

    public void setEnteredBy(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
