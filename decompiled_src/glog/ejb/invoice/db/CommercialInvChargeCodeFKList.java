/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class CommercialInvChargeCodeFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }
}
