/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.VoucherRefnumColumns;
import glog.ejb.invoice.db.VoucherRefnumData;
import glog.ejb.invoice.db.VoucherRefnumPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class VoucherRefnumBeanDB
extends BeanManagedEntityBean {
    public Object voucherGid;
    public Object voucherRefnumQualGid;
    public Object voucherRefnumValue;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient VoucherRefnumPK pk;
    protected transient VoucherRefnumData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherRefnumBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static VoucherRefnumPK.Callback theCall = new VoucherRefnumPK.Callback();

    public VoucherRefnumBeanDB() {
        super(false);
    }

    public VoucherRefnumPK ejbCreate(VoucherRefnumData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(VoucherRefnumData data) throws CreateException {
        this.ejbPostCreator();
    }

    public VoucherRefnumPK ejbFindByPrimaryKey(VoucherRefnumPK pk) throws FinderException {
        return (VoucherRefnumPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(VoucherRefnumPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<VoucherRefnumPK> v = new Vector<VoucherRefnumPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(VoucherRefnumPK pk, VoucherRefnumData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(VoucherRefnumPK pk, VoucherRefnumData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(VoucherRefnumPK pk, VoucherRefnumData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (VoucherRefnumPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(VoucherRefnumColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(VoucherRefnumColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(VoucherRefnumColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(VoucherRefnumColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        VoucherRefnumPK pk = (VoucherRefnumPK)genericPk;
        this.voucherGid = pk.voucherGid;
        this.voucherRefnumQualGid = pk.voucherRefnumQualGid;
        this.voucherRefnumValue = pk.voucherRefnumValue;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        VoucherRefnumPK pk = new VoucherRefnumPK(0, this.voucherGid, this.voucherRefnumQualGid, this.voucherRefnumValue, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.VoucherRefnum";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public VoucherRefnumData getData() throws GLException {
        try {
            VoucherRefnumData retval = new VoucherRefnumData();
            retval.getFromBean(this, VoucherRefnumColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(VoucherRefnumData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(VoucherRefnumData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            VoucherRefnumData oldData = modified ? this.getData() : null;
            data.setToBean(this, VoucherRefnumColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return VoucherRefnumData.class;
    }

    @Override
    public Class getPkClass() {
        return VoucherRefnumPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getVoucherGid() throws GLException {
        try {
            return (String)VoucherRefnumColumns.voucherGid.convertFromDB(this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherGid(String voucherGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherGid;
            VoucherRefnumData data = this.getData();
            this.voucherGid = VoucherRefnumColumns.voucherGid.convertToDB(voucherGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherGid", String.class, oldValue, this.voucherGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVoucherRefnumQualGid() throws GLException {
        try {
            return (String)VoucherRefnumColumns.voucherRefnumQualGid.convertFromDB(this.voucherRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherRefnumQualGid(String voucherRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherRefnumQualGid;
            VoucherRefnumData data = this.getData();
            this.voucherRefnumQualGid = VoucherRefnumColumns.voucherRefnumQualGid.convertToDB(voucherRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherRefnumQualGid", String.class, oldValue, this.voucherRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getVoucherRefnumValue() throws GLException {
        try {
            return (String)VoucherRefnumColumns.voucherRefnumValue.convertFromDB(this.voucherRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setVoucherRefnumValue(String voucherRefnumValue) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.voucherRefnumValue;
            VoucherRefnumData data = this.getData();
            this.voucherRefnumValue = VoucherRefnumColumns.voucherRefnumValue.convertToDB(voucherRefnumValue);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "voucherRefnumValue", String.class, oldValue, this.voucherRefnumValue);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)VoucherRefnumColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            VoucherRefnumData data = this.getData();
            this.domainName = VoucherRefnumColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
