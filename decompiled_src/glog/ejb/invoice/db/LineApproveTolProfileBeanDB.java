/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.LineApproveTolProfileColumns;
import glog.ejb.invoice.db.LineApproveTolProfileData;
import glog.ejb.invoice.db.LineApproveTolProfilePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class LineApproveTolProfileBeanDB
extends BeanManagedEntityBean {
    public Object lineApproveTolProfileGid;
    public Object lineApproveTolProfileXid;
    public Object description;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient LineApproveTolProfilePK pk;
    protected transient LineApproveTolProfileData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineApproveTolProfileBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static LineApproveTolProfilePK.Callback theCall = new LineApproveTolProfilePK.Callback();

    public LineApproveTolProfileBeanDB() {
        super(false);
    }

    public LineApproveTolProfilePK ejbCreate(LineApproveTolProfileData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected LineApproveTolProfilePK newPK() throws GLException {
        return LineApproveTolProfilePK.newPK(this.getDomainName(), this.getLineApproveTolProfileXid(), this.getConnection());
    }

    public void ejbPostCreate(LineApproveTolProfileData data) throws CreateException {
        this.ejbPostCreator();
    }

    public LineApproveTolProfilePK ejbFindByPrimaryKey(LineApproveTolProfilePK pk) throws FinderException {
        return (LineApproveTolProfilePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(LineApproveTolProfilePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<LineApproveTolProfilePK> v = new Vector<LineApproveTolProfilePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(LineApproveTolProfilePK pk, LineApproveTolProfileData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(LineApproveTolProfilePK pk, LineApproveTolProfileData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(LineApproveTolProfilePK pk, LineApproveTolProfileData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(LineApproveTolProfileColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(LineApproveTolProfileColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(LineApproveTolProfileColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(LineApproveTolProfileColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        LineApproveTolProfilePK pk = (LineApproveTolProfilePK)genericPk;
        this.lineApproveTolProfileGid = pk.lineApproveTolProfileGid;
        this.domainName = pk.domainName;
        this.lineApproveTolProfileXid = pk.lineApproveTolProfileXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        LineApproveTolProfilePK pk = new LineApproveTolProfilePK(0, this.lineApproveTolProfileGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.LineApproveTolProfile";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public LineApproveTolProfileData getData() throws GLException {
        try {
            LineApproveTolProfileData retval = new LineApproveTolProfileData();
            retval.getFromBean(this, LineApproveTolProfileColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(LineApproveTolProfileData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(LineApproveTolProfileData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            LineApproveTolProfileData oldData = modified ? this.getData() : null;
            data.setToBean(this, LineApproveTolProfileColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return LineApproveTolProfileData.class;
    }

    @Override
    public Class getPkClass() {
        return LineApproveTolProfilePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getLineApproveTolProfileGid() throws GLException {
        try {
            return (String)LineApproveTolProfileColumns.lineApproveTolProfileGid.convertFromDB(this.lineApproveTolProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineApproveTolProfileGid(String lineApproveTolProfileGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineApproveTolProfileGid;
            LineApproveTolProfileData data = this.getData();
            this.lineApproveTolProfileGid = LineApproveTolProfileColumns.lineApproveTolProfileGid.convertToDB(lineApproveTolProfileGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineApproveTolProfileGid", String.class, oldValue, this.lineApproveTolProfileGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getLineApproveTolProfileXid() throws GLException {
        try {
            return (String)LineApproveTolProfileColumns.lineApproveTolProfileXid.convertFromDB(this.lineApproveTolProfileXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineApproveTolProfileXid(String lineApproveTolProfileXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineApproveTolProfileXid;
            LineApproveTolProfileData data = this.getData();
            this.lineApproveTolProfileXid = LineApproveTolProfileColumns.lineApproveTolProfileXid.convertToDB(lineApproveTolProfileXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineApproveTolProfileXid", String.class, oldValue, this.lineApproveTolProfileXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)LineApproveTolProfileColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            LineApproveTolProfileData data = this.getData();
            this.description = LineApproveTolProfileColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)LineApproveTolProfileColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            LineApproveTolProfileData data = this.getData();
            this.domainName = LineApproveTolProfileColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
