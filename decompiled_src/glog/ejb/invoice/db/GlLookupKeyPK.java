/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlLookupKeyColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class GlLookupKeyPK
extends Pk {
    public Object glLookupKeyGid;
    public transient Object glLookupKeyXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public GlLookupKeyPK() {
    }

    public GlLookupKeyPK(String glLookupKeyGid) {
        this.glLookupKeyGid = this.notNull(GlLookupKeyColumns.glLookupKeyGid.convertToDB(glLookupKeyGid), "glLookupKeyGid");
    }

    public GlLookupKeyPK(String domainName, String glLookupKeyXid) {
        this.domainName = domainName;
        this.glLookupKeyXid = glLookupKeyXid;
        this.glLookupKeyGid = GlLookupKeyPK.concatForGid(domainName, glLookupKeyXid);
    }

    public GlLookupKeyPK(int dummy, Object glLookupKeyGid) {
        this(dummy, glLookupKeyGid, null);
    }

    public GlLookupKeyPK(int dummy, Object glLookupKeyGid, Object transaction) {
        this.glLookupKeyGid = glLookupKeyGid;
        this.transaction = transaction;
    }

    public GlLookupKeyPK(GlLookupKeyPK otherPk, Object transaction) {
        this.glLookupKeyGid = otherPk.glLookupKeyGid;
        this.glLookupKeyXid = otherPk.glLookupKeyXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.glLookupKeyGid != null ? String.valueOf(this.glLookupKeyGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.glLookupKeyGid != null ? String.valueOf(this.glLookupKeyGid) : "";
    }

    public static GlLookupKeyPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new GlLookupKeyPK(gids[0]) : null;
    }

    public static GlLookupKeyPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new GlLookupKeyPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.glLookupKeyGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof GlLookupKeyPK)) {
            return false;
        }
        GlLookupKeyPK otherPk = (GlLookupKeyPK)other;
        return Functions.equals(otherPk.glLookupKeyGid, this.glLookupKeyGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.GlLookupKeyHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.glLookupKeyGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return GlLookupKeyColumns.glLookupKeyGid.convertFromDB(this.glLookupKeyGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GlLookupKeyPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("GlLookupKeyPK requires a non-null XID to generate a GID");
            }
            GlLookupKeyPK glLookupKeyPK = new GlLookupKeyPK(domain, xid);
            return glLookupKeyPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static GlLookupKeyPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            GlLookupKeyPK glLookupKeyPK = GlLookupKeyPK.newPK(domainName, xid, connection);
            return glLookupKeyPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return GlLookupKeyPK.class;
        }

        @Override
        public final String getEntity() {
            return "GlLookupKey";
        }

        @Override
        public final String getTableName() {
            return "gl_lookup_key";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"gl_lookup_key_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            GlLookupKeyPK result = new GlLookupKeyPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
