/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceShipmentData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceShipmentRemoteDB
extends EJBObject {
    public InvoiceShipmentData getData() throws RemoteException, GLException;

    public void setData(InvoiceShipmentData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getInvoiceGid() throws RemoteException, GLException;

    public void setInvoiceGid(String var1) throws RemoteException, GLException;

    public Long getSequenceNo() throws RemoteException, GLException;

    public void setSequenceNo(Long var1) throws RemoteException, GLException;

    public String getShipmentGid() throws RemoteException, GLException;

    public void setShipmentGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getSShipUnitGid() throws RemoteException, GLException;

    public void setSShipUnitGid(String var1) throws RemoteException, GLException;

    public Long getSShipUnitLineNo() throws RemoteException, GLException;

    public void setSShipUnitLineNo(Long var1) throws RemoteException, GLException;

    public String getTrackingNumber() throws RemoteException, GLException;

    public void setTrackingNumber(String var1) throws RemoteException, GLException;

    public Long getLineitemSeqNo() throws RemoteException, GLException;

    public void setLineitemSeqNo(Long var1) throws RemoteException, GLException;
}
