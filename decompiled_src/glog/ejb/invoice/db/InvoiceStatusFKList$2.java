/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceStatusFKList.2
implements Fk {
    InvoiceStatusFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceStatusData";
    }

    @Override
    public String getPkField() {
        return "statusValueGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "StatusValueData";
    }

    @Override
    public String getFkDataField() {
        return "statusValueGid";
    }
}
