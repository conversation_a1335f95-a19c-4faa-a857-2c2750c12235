/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;
import glog.util.uom.data.Weight;

public class AutoApproveRuleDetailColumns {
    public static SqlColumn sequence = new SqlColumn(Integer.class, "sequence", 8, 0, 1, null, 0, null);
    public static SqlColumn autoApproveRuleGid = new SqlColumn(String.class, "auto_approve_rule_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn type = new SqlColumn(String.class, "type", 1, 0, 1, null, 2, null);
    public static SqlColumn allowablePercentAbove = new SqlColumn(Double.class, "allowable_percent_above", 22, 3, 0, null, 3, null);
    public static SqlColumn allowablePercentBelow = new SqlColumn(Double.class, "allowable_percent_below", 22, 3, 0, null, 4, null);
    public static SqlColumn approveToAmount = new SqlColumn(Currency.class, "approve_to_amount", 22, 2, 0, null, 5, "approve_to_amount_base");
    public static SqlColumn approveToWeight = new SqlColumn(Weight.class, "approve_to_weight", 8, 2, 0, null, 6, "approve_to_weight_base");
    public static SqlColumn allowCurrAbove = new SqlColumn(Currency.class, "allow_curr_above", 22, 2, 0, null, 7, "allow_curr_above_base");
    public static SqlColumn allowCurrBelow = new SqlColumn(Currency.class, "allow_curr_below", 22, 2, 0, null, 8, "allow_curr_below_base");
    public static SqlColumn allowWeightAbove = new SqlColumn(Weight.class, "allow_weight_above", 22, 2, 0, null, 9, "allow_weight_above_base");
    public static SqlColumn allowWeightBelow = new SqlColumn(Weight.class, "allow_weight_below", 22, 2, 0, null, 10, "allow_weight_below_base");
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 11, null);
}
