/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherStatusFKList.3
implements Fk {
    VoucherStatusFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherStatusData";
    }

    @Override
    public String getPkField() {
        return "voucherGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "VoucherData";
    }

    @Override
    public String getFkDataField() {
        return "voucherGid";
    }
}
