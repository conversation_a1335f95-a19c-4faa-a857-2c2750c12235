/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceLineitemRefnumQualColumns;
import glog.ejb.invoice.db.InvoiceLineitemRefnumQualData;
import glog.ejb.invoice.db.InvoiceLineitemRefnumQualPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceLineitemRefnumQualBeanDB
extends BeanManagedEntityBean {
    public Object invoiceLiRefnumQualGid;
    public Object invoiceLiRefnumQualXid;
    public Object invoiceLiRefnumDesc;
    public Object defaultRefnumBnTypeGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceLineitemRefnumQualPK pk;
    protected transient InvoiceLineitemRefnumQualData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLineitemRefnumQualBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceLineitemRefnumQualPK.Callback theCall = new InvoiceLineitemRefnumQualPK.Callback();

    public InvoiceLineitemRefnumQualBeanDB() {
        super(false);
    }

    public InvoiceLineitemRefnumQualPK ejbCreate(InvoiceLineitemRefnumQualData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected InvoiceLineitemRefnumQualPK newPK() throws GLException {
        return InvoiceLineitemRefnumQualPK.newPK(this.getDomainName(), this.getInvoiceLiRefnumQualXid(), this.getConnection());
    }

    public void ejbPostCreate(InvoiceLineitemRefnumQualData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceLineitemRefnumQualPK ejbFindByPrimaryKey(InvoiceLineitemRefnumQualPK pk) throws FinderException {
        return (InvoiceLineitemRefnumQualPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceLineitemRefnumQualPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceLineitemRefnumQualPK> v = new Vector<InvoiceLineitemRefnumQualPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceLineitemRefnumQualPK pk, InvoiceLineitemRefnumQualData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceLineitemRefnumQualPK pk, InvoiceLineitemRefnumQualData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceLineitemRefnumQualPK pk, InvoiceLineitemRefnumQualData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceLineitemRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceLineitemRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceLineitemRefnumQualColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceLineitemRefnumQualColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceLineitemRefnumQualPK pk = (InvoiceLineitemRefnumQualPK)genericPk;
        this.invoiceLiRefnumQualGid = pk.invoiceLiRefnumQualGid;
        this.domainName = pk.domainName;
        this.invoiceLiRefnumQualXid = pk.invoiceLiRefnumQualXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceLineitemRefnumQualPK pk = new InvoiceLineitemRefnumQualPK(0, this.invoiceLiRefnumQualGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceLineitemRefnumQual";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceLineitemRefnumQualData getData() throws GLException {
        try {
            InvoiceLineitemRefnumQualData retval = new InvoiceLineitemRefnumQualData();
            retval.getFromBean(this, InvoiceLineitemRefnumQualColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceLineitemRefnumQualData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceLineitemRefnumQualData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceLineitemRefnumQualData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceLineitemRefnumQualColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceLineitemRefnumQualData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceLineitemRefnumQualPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceLiRefnumQualGid() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualGid.convertFromDB(this.invoiceLiRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceLiRefnumQualGid(String invoiceLiRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceLiRefnumQualGid;
            InvoiceLineitemRefnumQualData data = this.getData();
            this.invoiceLiRefnumQualGid = InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualGid.convertToDB(invoiceLiRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceLiRefnumQualGid", String.class, oldValue, this.invoiceLiRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceLiRefnumQualXid() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualXid.convertFromDB(this.invoiceLiRefnumQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceLiRefnumQualXid(String invoiceLiRefnumQualXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceLiRefnumQualXid;
            InvoiceLineitemRefnumQualData data = this.getData();
            this.invoiceLiRefnumQualXid = InvoiceLineitemRefnumQualColumns.invoiceLiRefnumQualXid.convertToDB(invoiceLiRefnumQualXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceLiRefnumQualXid", String.class, oldValue, this.invoiceLiRefnumQualXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceLiRefnumDesc() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumQualColumns.invoiceLiRefnumDesc.convertFromDB(this.invoiceLiRefnumDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceLiRefnumDesc(String invoiceLiRefnumDesc) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceLiRefnumDesc;
            InvoiceLineitemRefnumQualData data = this.getData();
            this.invoiceLiRefnumDesc = InvoiceLineitemRefnumQualColumns.invoiceLiRefnumDesc.convertToDB(invoiceLiRefnumDesc);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceLiRefnumDesc", String.class, oldValue, this.invoiceLiRefnumDesc);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDefaultRefnumBnTypeGid() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumQualColumns.defaultRefnumBnTypeGid.convertFromDB(this.defaultRefnumBnTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDefaultRefnumBnTypeGid(String defaultRefnumBnTypeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.defaultRefnumBnTypeGid;
            InvoiceLineitemRefnumQualData data = this.getData();
            this.defaultRefnumBnTypeGid = InvoiceLineitemRefnumQualColumns.defaultRefnumBnTypeGid.convertToDB(defaultRefnumBnTypeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "defaultRefnumBnTypeGid", String.class, oldValue, this.defaultRefnumBnTypeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumQualColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceLineitemRefnumQualData data = this.getData();
            this.domainName = InvoiceLineitemRefnumQualColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
