/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.GlLookupKeyColumns;
import glog.ejb.invoice.db.GlLookupKeyData;
import glog.ejb.invoice.db.GlLookupKeyPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class GlLookupKeyBeanDB
extends BeanManagedEntityBean {
    public Object glLookupKeyGid;
    public Object glLookupKeyXid;
    public Object isActive;
    public Object perspective;
    public Object glCodeAssignType;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient GlLookupKeyPK pk;
    protected transient GlLookupKeyData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlLookupKeyBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static GlLookupKeyPK.Callback theCall = new GlLookupKeyPK.Callback();

    public GlLookupKeyBeanDB() {
        super(false);
    }

    public GlLookupKeyPK ejbCreate(GlLookupKeyData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected GlLookupKeyPK newPK() throws GLException {
        return GlLookupKeyPK.newPK(this.getDomainName(), this.getGlLookupKeyXid(), this.getConnection());
    }

    public void ejbPostCreate(GlLookupKeyData data) throws CreateException {
        this.ejbPostCreator();
    }

    public GlLookupKeyPK ejbFindByPrimaryKey(GlLookupKeyPK pk) throws FinderException {
        return (GlLookupKeyPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(GlLookupKeyPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<GlLookupKeyPK> v = new Vector<GlLookupKeyPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(GlLookupKeyPK pk, GlLookupKeyData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(GlLookupKeyPK pk, GlLookupKeyData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(GlLookupKeyPK pk, GlLookupKeyData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(GlLookupKeyColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(GlLookupKeyColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(GlLookupKeyColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(GlLookupKeyColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        GlLookupKeyPK pk = (GlLookupKeyPK)genericPk;
        this.glLookupKeyGid = pk.glLookupKeyGid;
        this.domainName = pk.domainName;
        this.glLookupKeyXid = pk.glLookupKeyXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        GlLookupKeyPK pk = new GlLookupKeyPK(0, this.glLookupKeyGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.GlLookupKey";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public GlLookupKeyData getData() throws GLException {
        try {
            GlLookupKeyData retval = new GlLookupKeyData();
            retval.getFromBean(this, GlLookupKeyColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(GlLookupKeyData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(GlLookupKeyData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            GlLookupKeyData oldData = modified ? this.getData() : null;
            data.setToBean(this, GlLookupKeyColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return GlLookupKeyData.class;
    }

    @Override
    public Class getPkClass() {
        return GlLookupKeyPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getGlLookupKeyGid() throws GLException {
        try {
            return (String)GlLookupKeyColumns.glLookupKeyGid.convertFromDB(this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlLookupKeyGid(String glLookupKeyGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glLookupKeyGid;
            GlLookupKeyData data = this.getData();
            this.glLookupKeyGid = GlLookupKeyColumns.glLookupKeyGid.convertToDB(glLookupKeyGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glLookupKeyGid", String.class, oldValue, this.glLookupKeyGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlLookupKeyXid() throws GLException {
        try {
            return (String)GlLookupKeyColumns.glLookupKeyXid.convertFromDB(this.glLookupKeyXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlLookupKeyXid(String glLookupKeyXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glLookupKeyXid;
            GlLookupKeyData data = this.getData();
            this.glLookupKeyXid = GlLookupKeyColumns.glLookupKeyXid.convertToDB(glLookupKeyXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glLookupKeyXid", String.class, oldValue, this.glLookupKeyXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Boolean getIsActive() throws GLException {
        try {
            return (Boolean)GlLookupKeyColumns.isActive.convertFromDB(this.isActive);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setIsActive(Boolean isActive) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.isActive;
            GlLookupKeyData data = this.getData();
            this.isActive = GlLookupKeyColumns.isActive.convertToDB(isActive);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "isActive", Boolean.class, oldValue, this.isActive);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getPerspective() throws GLException {
        try {
            return (String)GlLookupKeyColumns.perspective.convertFromDB(this.perspective);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPerspective(String perspective) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.perspective;
            GlLookupKeyData data = this.getData();
            this.perspective = GlLookupKeyColumns.perspective.convertToDB(perspective);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "perspective", String.class, oldValue, this.perspective);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getGlCodeAssignType() throws GLException {
        try {
            return (String)GlLookupKeyColumns.glCodeAssignType.convertFromDB(this.glCodeAssignType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setGlCodeAssignType(String glCodeAssignType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.glCodeAssignType;
            GlLookupKeyData data = this.getData();
            this.glCodeAssignType = GlLookupKeyColumns.glCodeAssignType.convertToDB(glCodeAssignType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "glCodeAssignType", String.class, oldValue, this.glCodeAssignType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)GlLookupKeyColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            GlLookupKeyData data = this.getData();
            this.domainName = GlLookupKeyColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
