/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class VoucherPK
extends Pk {
    public Object voucherGid;
    public transient Object voucherXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public VoucherPK() {
    }

    public VoucherPK(String voucherGid) {
        this.voucherGid = this.notNull(VoucherColumns.voucherGid.convertToDB(voucherGid), "voucherGid");
    }

    public VoucherPK(String domainName, String voucherXid) {
        this.domainName = domainName;
        this.voucherXid = voucherXid;
        this.voucherGid = VoucherPK.concatForGid(domainName, voucherXid);
    }

    public VoucherPK(int dummy, Object voucherGid) {
        this(dummy, voucherGid, null);
    }

    public VoucherPK(int dummy, Object voucherGid, Object transaction) {
        this.voucherGid = voucherGid;
        this.transaction = transaction;
    }

    public VoucherPK(VoucherPK otherPk, Object transaction) {
        this.voucherGid = otherPk.voucherGid;
        this.voucherXid = otherPk.voucherXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.voucherGid != null ? String.valueOf(this.voucherGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.voucherGid != null ? String.valueOf(this.voucherGid) : "";
    }

    public static VoucherPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new VoucherPK(gids[0]) : null;
    }

    public static VoucherPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new VoucherPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.voucherGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof VoucherPK)) {
            return false;
        }
        VoucherPK otherPk = (VoucherPK)other;
        return Functions.equals(otherPk.voucherGid, this.voucherGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.VoucherHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.voucherGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return VoucherColumns.voucherGid.convertFromDB(this.voucherGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static VoucherPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("VoucherPK requires a non-null XID to generate a GID");
            }
            VoucherPK voucherPK = new VoucherPK(domain, xid);
            return voucherPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static VoucherPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            VoucherPK voucherPK = VoucherPK.newPK(domainName, xid, connection);
            return voucherPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return VoucherPK.class;
        }

        @Override
        public final String getEntity() {
            return "Voucher";
        }

        @Override
        public final String getTableName() {
            return "voucher";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"voucher_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            VoucherPK result = new VoucherPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
