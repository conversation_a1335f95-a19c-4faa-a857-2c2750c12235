/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceStopRefnumColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn stopSeqNo = new SqlColumn(String.class, "stop_seq_no", 101, 0, 1, null, 1, null);
    public static SqlColumn invoiceStopRefnumQualGid = new SqlColumn(String.class, "invoice_stop_refnum_qual_gid", 101, 0, 1, null, 2, null);
    public static SqlColumn referenceNumber = new SqlColumn(String.class, "reference_number", 240, 0, 1, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
