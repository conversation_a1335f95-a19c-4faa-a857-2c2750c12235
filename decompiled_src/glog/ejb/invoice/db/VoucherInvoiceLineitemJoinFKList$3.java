/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherInvoiceLineitemJoinFKList.3
implements Fk {
    VoucherInvoiceLineitemJoinFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherInvoiceLineitemJoinData";
    }

    @Override
    public String getPkField() {
        return "invoiceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceGid";
    }
}
