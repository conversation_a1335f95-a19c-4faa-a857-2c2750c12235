/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlKeyComponentTypeParamColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class GlKeyComponentTypeParamPK
extends Pk {
    public Object glKeyComponentTypeGid;
    public Object sequence;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public GlKeyComponentTypeParamPK() {
    }

    public GlKeyComponentTypeParamPK(String glKeyComponentTypeGid, Double sequence) {
        this.glKeyComponentTypeGid = this.notNull(GlKeyComponentTypeParamColumns.glKeyComponentTypeGid.convertToDB(glKeyComponentTypeGid), "glKeyComponentTypeGid");
        this.sequence = this.notNull(GlKeyComponentTypeParamColumns.sequence.convertToDB(sequence), "sequence");
    }

    public GlKeyComponentTypeParamPK(int dummy, Object glKeyComponentTypeGid, Object sequence) {
        this(dummy, glKeyComponentTypeGid, sequence, null);
    }

    public GlKeyComponentTypeParamPK(int dummy, Object glKeyComponentTypeGid, Object sequence, Object transaction) {
        this.glKeyComponentTypeGid = glKeyComponentTypeGid;
        this.sequence = sequence;
        this.transaction = transaction;
    }

    public GlKeyComponentTypeParamPK(GlKeyComponentTypeParamPK otherPk, Object transaction) {
        this.glKeyComponentTypeGid = otherPk.glKeyComponentTypeGid;
        this.sequence = otherPk.sequence;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.glKeyComponentTypeGid != null ? String.valueOf(this.glKeyComponentTypeGid) : "") + " " + (this.sequence != null ? String.valueOf(this.sequence) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.glKeyComponentTypeGid != null ? String.valueOf(this.glKeyComponentTypeGid) : "") + "|" + (this.sequence != null ? String.valueOf(this.sequence) : "");
    }

    public static GlKeyComponentTypeParamPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new GlKeyComponentTypeParamPK(gids[0], Double.valueOf(gids[1])) : null;
    }

    public static GlKeyComponentTypeParamPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new GlKeyComponentTypeParamPK(gids[0], Double.valueOf(gids[1])) : null;
    }

    public int hashCode() {
        return this.glKeyComponentTypeGid.hashCode() + this.sequence.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof GlKeyComponentTypeParamPK)) {
            return false;
        }
        GlKeyComponentTypeParamPK otherPk = (GlKeyComponentTypeParamPK)other;
        return Functions.equals(otherPk.glKeyComponentTypeGid, this.glKeyComponentTypeGid) && Functions.equals(otherPk.sequence, this.sequence) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.GlKeyComponentTypeParamHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.glKeyComponentTypeGid;
            }
            case 1: {
                return this.sequence;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return GlKeyComponentTypeParamColumns.glKeyComponentTypeGid.convertFromDB(this.glKeyComponentTypeGid);
            }
            case 1: {
                return GlKeyComponentTypeParamColumns.sequence.convertFromDB(this.sequence);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return GlKeyComponentTypeParamPK.class;
        }

        @Override
        public final String getEntity() {
            return "GlKeyComponentTypeParam";
        }

        @Override
        public final String getTableName() {
            return "gl_key_component_type_param";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"gl_key_component_type_gid", "sequence"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            GlKeyComponentTypeParamPK result = new GlKeyComponentTypeParamPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
