/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class InvoiceEquipmentSealColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn seqNumber = new SqlColumn(Long.class, "seq_number", 10, 0, 1, null, 1, null);
    public static SqlColumn sealSequence = new SqlColumn(Integer.class, "seal_sequence", 8, 0, 1, null, 2, null);
    public static SqlColumn sealNumber = new SqlColumn(String.class, "seal_number", 30, 0, 1, null, 3, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 4, null);
}
