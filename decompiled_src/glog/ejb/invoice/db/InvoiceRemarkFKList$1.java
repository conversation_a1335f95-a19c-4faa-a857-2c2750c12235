/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceRemarkFKList.1
implements Fk {
    InvoiceRemarkFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceRemarkData";
    }

    @Override
    public String getPkField() {
        return "invoiceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceGid";
    }
}
