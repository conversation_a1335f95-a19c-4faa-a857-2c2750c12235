/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleDetailPK;
import glog.util.Functions;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Weight;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AutoApproveRuleDetailData
extends BeanData {
    public Integer sequence;
    public String autoApproveRuleGid;
    public String type;
    public Double allowablePercentAbove;
    public Double allowablePercentBelow;
    public Currency approveToAmount;
    public Weight approveToWeight;
    public Currency allowCurrAbove;
    public Currency allowCurrBelow;
    public Weight allowWeightAbove;
    public Weight allowWeightBelow;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleDetailData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field sequenceField = beanDataFields[0];
    public static Field autoApproveRuleGidField = beanDataFields[1];
    public static Field typeField = beanDataFields[2];
    public static Field allowablePercentAboveField = beanDataFields[3];
    public static Field allowablePercentBelowField = beanDataFields[4];
    public static Field approveToAmountField = beanDataFields[5];
    public static Field approveToWeightField = beanDataFields[6];
    public static Field allowCurrAboveField = beanDataFields[7];
    public static Field allowCurrBelowField = beanDataFields[8];
    public static Field allowWeightAboveField = beanDataFields[9];
    public static Field allowWeightBelowField = beanDataFields[10];
    public static Field domainNameField = beanDataFields[11];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AutoApproveRuleDetailData() {
    }

    public AutoApproveRuleDetailData(AutoApproveRuleDetailData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAutoApproveRuleDetailPK();
    }

    @Legacy
    public AutoApproveRuleDetailPK getAutoApproveRuleDetailPK() {
        if (this.autoApproveRuleGid == null) {
            return null;
        }
        if (this.sequence == null) {
            return null;
        }
        return new AutoApproveRuleDetailPK(this.autoApproveRuleGid, this.sequence);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAutoApproveRuleDetailPK((AutoApproveRuleDetailPK)pk);
    }

    @Legacy
    public void setAutoApproveRuleDetailPK(AutoApproveRuleDetailPK pk) {
        this.autoApproveRuleGid = (String)pk.getAppValue(0);
        this.sequence = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.AutoApproveRuleDetailQueryGen";
    }

    public static AutoApproveRuleDetailData load(Connection conn, AutoApproveRuleDetailPK pk) throws GLException {
        return (AutoApproveRuleDetailData)AutoApproveRuleDetailData.load(conn, pk, AutoApproveRuleDetailData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AutoApproveRuleDetailData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleDetailData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleDetailData.load(conn, whereClause, prepareArguments, fetchSize, AutoApproveRuleDetailData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleDetailData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleDetailData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AutoApproveRuleDetailData.class);
    }
}
