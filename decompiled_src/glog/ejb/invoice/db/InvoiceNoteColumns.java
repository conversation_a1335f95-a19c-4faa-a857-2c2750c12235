/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalTimestamp;
import glog.util.jdbc.SqlColumn;

public class InvoiceNoteColumns {
    public static SqlColumn invoiceGid = new SqlColumn(String.class, "invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn invoiceNoteSeqNo = new SqlColumn(Long.class, "invoice_note_seq_no", 10, 0, 1, null, 1, null);
    public static SqlColumn timestamp = new SqlColumn(LocalTimestamp.class, "timestamp", 7, 0, 1, null, 2, null);
    public static SqlColumn summary = new SqlColumn(String.class, "summary", 500, 0, 1, null, 3, null);
    public static SqlColumn note = new SqlColumn(String.class, "note", 2000, 0, 0, null, 4, null);
    public static SqlColumn isSystemGenerated = new SqlColumn(Boolean.class, "is_system_generated", 1, 0, 1, Boolean.valueOf("false"), 5, null);
    public static SqlColumn enteredBy = new SqlColumn(String.class, "entered_by", 50, 0, 0, null, 6, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, String.valueOf("PUBLIC"), 7, null);
}
