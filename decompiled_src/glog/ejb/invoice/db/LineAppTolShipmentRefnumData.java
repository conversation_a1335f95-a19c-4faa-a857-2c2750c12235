/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineAppTolShipmentRefnumPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class LineAppTolShipmentRefnumData
extends BeanData {
    public String lineApproveToleranceGid;
    public Integer refnumSequence;
    public String shipmentRefnumQualGid;
    public String shipmentRefnumValue;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(LineAppTolShipmentRefnumData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field lineApproveToleranceGidField = beanDataFields[0];
    public static Field refnumSequenceField = beanDataFields[1];
    public static Field shipmentRefnumQualGidField = beanDataFields[2];
    public static Field shipmentRefnumValueField = beanDataFields[3];
    public static Field domainNameField = beanDataFields[4];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public LineAppTolShipmentRefnumData() {
    }

    public LineAppTolShipmentRefnumData(LineAppTolShipmentRefnumData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getLineAppTolShipmentRefnumPK();
    }

    @Legacy
    public LineAppTolShipmentRefnumPK getLineAppTolShipmentRefnumPK() {
        if (this.lineApproveToleranceGid == null) {
            return null;
        }
        if (this.refnumSequence == null) {
            return null;
        }
        return new LineAppTolShipmentRefnumPK(this.lineApproveToleranceGid, this.refnumSequence);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setLineAppTolShipmentRefnumPK((LineAppTolShipmentRefnumPK)pk);
    }

    @Legacy
    public void setLineAppTolShipmentRefnumPK(LineAppTolShipmentRefnumPK pk) {
        this.lineApproveToleranceGid = (String)pk.getAppValue(0);
        this.refnumSequence = (Integer)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.LineAppTolShipmentRefnumQueryGen";
    }

    public static LineAppTolShipmentRefnumData load(Connection conn, LineAppTolShipmentRefnumPK pk) throws GLException {
        return (LineAppTolShipmentRefnumData)LineAppTolShipmentRefnumData.load(conn, pk, LineAppTolShipmentRefnumData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return LineAppTolShipmentRefnumData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return LineAppTolShipmentRefnumData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineAppTolShipmentRefnumData.load(conn, whereClause, prepareArguments, fetchSize, LineAppTolShipmentRefnumData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return LineAppTolShipmentRefnumData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return LineAppTolShipmentRefnumData.load(conn, fromWhere, alias, prepareArguments, fetchSize, LineAppTolShipmentRefnumData.class);
    }
}
