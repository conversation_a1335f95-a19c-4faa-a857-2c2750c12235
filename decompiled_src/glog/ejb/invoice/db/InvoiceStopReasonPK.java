/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopReasonColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class InvoiceStopReasonPK
extends Pk {
    public Object invoiceStopReasonGid;
    public transient Object invoiceStopReasonXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceStopReasonPK() {
    }

    public InvoiceStopReasonPK(String invoiceStopReasonGid) {
        this.invoiceStopReasonGid = this.notNull(InvoiceStopReasonColumns.invoiceStopReasonGid.convertToDB(invoiceStopReasonGid), "invoiceStopReasonGid");
    }

    public InvoiceStopReasonPK(String domainName, String invoiceStopReasonXid) {
        this.domainName = domainName;
        this.invoiceStopReasonXid = invoiceStopReasonXid;
        this.invoiceStopReasonGid = InvoiceStopReasonPK.concatForGid(domainName, invoiceStopReasonXid);
    }

    public InvoiceStopReasonPK(int dummy, Object invoiceStopReasonGid) {
        this(dummy, invoiceStopReasonGid, null);
    }

    public InvoiceStopReasonPK(int dummy, Object invoiceStopReasonGid, Object transaction) {
        this.invoiceStopReasonGid = invoiceStopReasonGid;
        this.transaction = transaction;
    }

    public InvoiceStopReasonPK(InvoiceStopReasonPK otherPk, Object transaction) {
        this.invoiceStopReasonGid = otherPk.invoiceStopReasonGid;
        this.invoiceStopReasonXid = otherPk.invoiceStopReasonXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.invoiceStopReasonGid != null ? String.valueOf(this.invoiceStopReasonGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.invoiceStopReasonGid != null ? String.valueOf(this.invoiceStopReasonGid) : "";
    }

    public static InvoiceStopReasonPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceStopReasonPK(gids[0]) : null;
    }

    public static InvoiceStopReasonPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceStopReasonPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.invoiceStopReasonGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceStopReasonPK)) {
            return false;
        }
        InvoiceStopReasonPK otherPk = (InvoiceStopReasonPK)other;
        return Functions.equals(otherPk.invoiceStopReasonGid, this.invoiceStopReasonGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceStopReasonHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.invoiceStopReasonGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceStopReasonColumns.invoiceStopReasonGid.convertFromDB(this.invoiceStopReasonGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceStopReasonPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("InvoiceStopReasonPK requires a non-null XID to generate a GID");
            }
            InvoiceStopReasonPK invoiceStopReasonPK = new InvoiceStopReasonPK(domain, xid);
            return invoiceStopReasonPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static InvoiceStopReasonPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            InvoiceStopReasonPK invoiceStopReasonPK = InvoiceStopReasonPK.newPK(domainName, xid, connection);
            return invoiceStopReasonPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceStopReasonPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceStopReason";
        }

        @Override
        public final String getTableName() {
            return "invoice_stop_reason";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"invoice_stop_reason_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceStopReasonPK result = new InvoiceStopReasonPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
