/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopReasonPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceStopReasonData
extends BeanData {
    public String invoiceStopReasonGid;
    public String invoiceStopReasonXid;
    public String invoiceStopReasonDesc;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStopReasonData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceStopReasonGidField = beanDataFields[0];
    public static Field invoiceStopReasonXidField = beanDataFields[1];
    public static Field invoiceStopReasonDescField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceStopReasonData() {
    }

    public InvoiceStopReasonData(InvoiceStopReasonData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceStopReasonPK();
    }

    @Legacy
    public InvoiceStopReasonPK getInvoiceStopReasonPK() {
        if (this.invoiceStopReasonGid == null) {
            return null;
        }
        return new InvoiceStopReasonPK(this.invoiceStopReasonGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceStopReasonPK((InvoiceStopReasonPK)pk);
    }

    @Legacy
    public void setInvoiceStopReasonPK(InvoiceStopReasonPK pk) {
        this.invoiceStopReasonGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceStopReasonQueryGen";
    }

    public static InvoiceStopReasonData load(Connection conn, InvoiceStopReasonPK pk) throws GLException {
        return (InvoiceStopReasonData)InvoiceStopReasonData.load(conn, pk, InvoiceStopReasonData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceStopReasonData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceStopReasonData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopReasonData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceStopReasonData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceStopReasonData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopReasonData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceStopReasonData.class);
    }
}
