/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.SqlColumn;

public class GlKeyComponentParamColumns {
    public static SqlColumn glKeyComponentGid = new SqlColumn(String.class, "gl_key_component_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn sequence = new SqlColumn(Double.class, "sequence", 22, 3, 1, null, 1, null);
    public static SqlColumn paramValue = new SqlColumn(String.class, "param_value", 101, 0, 0, null, 2, null);
    public static SqlColumn paramValueUomCode = new SqlColumn(String.class, "param_value_uom_code", 64, 0, 0, null, 3, null);
    public static SqlColumn glKeyComponentTypeGid = new SqlColumn(String.class, "gl_key_component_type_gid", 101, 0, 0, null, 4, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 5, null);
}
