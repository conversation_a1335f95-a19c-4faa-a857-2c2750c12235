/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.16
implements Fk {
    InvoiceFKList.16() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "userDefined5IconGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.icon.db";
    }

    @Override
    public String getFkDataClass() {
        return "IconData";
    }

    @Override
    public String getFkDataField() {
        return "iconGid";
    }
}
