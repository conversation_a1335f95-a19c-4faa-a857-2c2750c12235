/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemRemarkPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceLineitemRemarkData
extends BeanData {
    public String invoiceGid;
    public Integer lineitemSeqNo;
    public Integer remarkSeqNo;
    public String remarkQualIdentifier;
    public String remarkText;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLineitemRemarkData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field lineitemSeqNoField = beanDataFields[1];
    public static Field remarkSeqNoField = beanDataFields[2];
    public static Field remarkQualIdentifierField = beanDataFields[3];
    public static Field remarkTextField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceLineitemRemarkData() {
    }

    public InvoiceLineitemRemarkData(InvoiceLineitemRemarkData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceLineitemRemarkPK();
    }

    @Legacy
    public InvoiceLineitemRemarkPK getInvoiceLineitemRemarkPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.lineitemSeqNo == null) {
            return null;
        }
        if (this.remarkSeqNo == null) {
            return null;
        }
        return new InvoiceLineitemRemarkPK(this.invoiceGid, this.lineitemSeqNo, this.remarkSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceLineitemRemarkPK((InvoiceLineitemRemarkPK)pk);
    }

    @Legacy
    public void setInvoiceLineitemRemarkPK(InvoiceLineitemRemarkPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.lineitemSeqNo = (Integer)pk.getAppValue(1);
        this.remarkSeqNo = (Integer)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceLineitemRemarkQueryGen";
    }

    public static InvoiceLineitemRemarkData load(Connection conn, InvoiceLineitemRemarkPK pk) throws GLException {
        return (InvoiceLineitemRemarkData)InvoiceLineitemRemarkData.load(conn, pk, InvoiceLineitemRemarkData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceLineitemRemarkData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemRemarkData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemRemarkData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceLineitemRemarkData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceLineitemRemarkData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLineitemRemarkData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceLineitemRemarkData.class);
    }
}
