/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class LineApproveTolProfileDFKList.1
implements Fk {
    LineApproveTolProfileDFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "LineApproveTolProfileDData";
    }

    @Override
    public String getPkField() {
        return "lineApproveTolProfileGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "LineApproveTolProfileData";
    }

    @Override
    public String getFkDataField() {
        return "lineApproveTolProfileGid";
    }
}
