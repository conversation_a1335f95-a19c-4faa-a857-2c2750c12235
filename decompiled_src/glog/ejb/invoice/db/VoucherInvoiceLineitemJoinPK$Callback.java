/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class VoucherInvoiceLineitemJoinPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return VoucherInvoiceLineitemJoinPK.class;
    }

    @Override
    public final String getEntity() {
        return "VoucherInvoiceLineitemJoin";
    }

    @Override
    public final String getTableName() {
        return "voucher_invoice_lineitem_join";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"invoice_gid", "lineitem_seq_no", "voucher_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        VoucherInvoiceLineitemJoinPK result = new VoucherInvoiceLineitemJoinPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), transaction);
        return result;
    }
}
