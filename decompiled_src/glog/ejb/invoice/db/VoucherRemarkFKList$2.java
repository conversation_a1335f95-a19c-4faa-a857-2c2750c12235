/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class VoucherRemarkFKList.2
implements Fk {
    VoucherRemarkFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "VoucherRemarkData";
    }

    @Override
    public String getPkField() {
        return "remarkQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "RemarkQualData";
    }

    @Override
    public String getFkDataField() {
        return "remarkQualGid";
    }
}
