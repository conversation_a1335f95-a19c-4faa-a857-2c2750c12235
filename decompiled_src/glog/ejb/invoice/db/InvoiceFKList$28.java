/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceFKList.28
implements Fk {
    InvoiceFKList.28() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceData";
    }

    @Override
    public String getPkField() {
        return "invoiceServiceCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceServiceCodeData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceServiceCodeGid";
    }
}
