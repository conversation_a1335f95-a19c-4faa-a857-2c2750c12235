/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;

public class CommercialInvoiceColumns {
    public static SqlColumn commercialInvoiceGid = new SqlColumn(String.class, "commercial_invoice_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn commercialInvoiceXid = new SqlColumn(String.class, "commercial_invoice_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn commercialInvoiceType = new SqlColumn(String.class, "commercial_invoice_type", 101, 0, 0, String.valueOf("ORDER"), 2, null);
    public static SqlColumn invoiceDate = new SqlColumn(LocalDate.class, "invoice_date", 7, 0, 0, null, 3, null);
    public static SqlColumn exchangeRate = new SqlColumn(Double.class, "exchange_rate", 22, 3, 0, null, 4, null);
    public static SqlColumn exchangeToCurrencyGid = new SqlColumn(String.class, "exchange_to_currency_gid", 101, 0, 0, null, 5, null);
    public static SqlColumn productAmount = new SqlColumn(Currency.class, "product_amount", 22, 2, 1, Currency.valueOf("0"), 6, "product_amount_base");
    public static SqlColumn otherChargeAmount = new SqlColumn(Currency.class, "other_charge_amount", 22, 2, 0, null, 7, "other_charge_amount_base");
    public static SqlColumn isCalculateProductAmount = new SqlColumn(Boolean.class, "is_calculate_product_amount", 1, 0, 1, Boolean.valueOf("false"), 8, null);
    public static SqlColumn isCalculateTotalInvoiceAmt = new SqlColumn(Boolean.class, "is_calculate_total_invoice_amt", 1, 0, 0, Boolean.valueOf("false"), 9, null);
    public static SqlColumn totalInvoiceAmount = new SqlColumn(Currency.class, "total_invoice_amount", 22, 2, 1, Currency.valueOf("0"), 10, "total_invoice_amount_base");
    public static SqlColumn incoTermGid = new SqlColumn(String.class, "inco_term_gid", 101, 0, 0, null, 11, null);
    public static SqlColumn termLocationText = new SqlColumn(String.class, "term_location_text", 128, 0, 0, null, 12, null);
    public static SqlColumn finalIncoTermGid = new SqlColumn(String.class, "final_inco_term_gid", 101, 0, 0, null, 13, null);
    public static SqlColumn finalTextLocation = new SqlColumn(String.class, "final_text_location", 128, 0, 0, null, 14, null);
    public static SqlColumn commercialPaymentCodeGid = new SqlColumn(String.class, "commercial_payment_code_gid", 101, 0, 0, null, 15, null);
    public static SqlColumn exchangeRateDate = new SqlColumn(LocalDate.class, "exchange_rate_date", 7, 0, 0, null, 16, null);
    public static SqlColumn exchangeRateGid = new SqlColumn(String.class, "exchange_rate_gid", 101, 0, 0, null, 17, null);
    public static SqlColumn exchangeFromCurrencyGid = new SqlColumn(String.class, "exchange_from_currency_gid", 101, 0, 0, null, 18, null);
    public static SqlColumn isOverrideExchangeRate = new SqlColumn(Boolean.class, "is_override_exchange_rate", 1, 0, 1, Boolean.valueOf("false"), 19, null);
    public static SqlColumn commercialInvoiceNumber = new SqlColumn(String.class, "commercial_invoice_number", 101, 0, 0, null, 20, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 21, null);
}
