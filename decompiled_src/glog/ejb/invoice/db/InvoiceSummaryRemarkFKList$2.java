/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceSummaryRemarkFKList.2
implements Fk {
    InvoiceSummaryRemarkFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceSummaryRemarkData";
    }

    @Override
    public String getPkField() {
        return "invoiceSummarySeqNo";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceSummaryData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceSummarySeqNo";
    }
}
