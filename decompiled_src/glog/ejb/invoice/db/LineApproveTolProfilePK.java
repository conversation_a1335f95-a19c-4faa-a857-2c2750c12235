/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineApproveTolProfileColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class LineApproveTolProfilePK
extends Pk {
    public Object lineApproveTolProfileGid;
    public transient Object lineApproveTolProfileXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public LineApproveTolProfilePK() {
    }

    public LineApproveTolProfilePK(String lineApproveTolProfileGid) {
        this.lineApproveTolProfileGid = this.notNull(LineApproveTolProfileColumns.lineApproveTolProfileGid.convertToDB(lineApproveTolProfileGid), "lineApproveTolProfileGid");
    }

    public LineApproveTolProfilePK(String domainName, String lineApproveTolProfileXid) {
        this.domainName = domainName;
        this.lineApproveTolProfileXid = lineApproveTolProfileXid;
        this.lineApproveTolProfileGid = LineApproveTolProfilePK.concatForGid(domainName, lineApproveTolProfileXid);
    }

    public LineApproveTolProfilePK(int dummy, Object lineApproveTolProfileGid) {
        this(dummy, lineApproveTolProfileGid, null);
    }

    public LineApproveTolProfilePK(int dummy, Object lineApproveTolProfileGid, Object transaction) {
        this.lineApproveTolProfileGid = lineApproveTolProfileGid;
        this.transaction = transaction;
    }

    public LineApproveTolProfilePK(LineApproveTolProfilePK otherPk, Object transaction) {
        this.lineApproveTolProfileGid = otherPk.lineApproveTolProfileGid;
        this.lineApproveTolProfileXid = otherPk.lineApproveTolProfileXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.lineApproveTolProfileGid != null ? String.valueOf(this.lineApproveTolProfileGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.lineApproveTolProfileGid != null ? String.valueOf(this.lineApproveTolProfileGid) : "";
    }

    public static LineApproveTolProfilePK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new LineApproveTolProfilePK(gids[0]) : null;
    }

    public static LineApproveTolProfilePK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new LineApproveTolProfilePK(gids[0]) : null;
    }

    public int hashCode() {
        return this.lineApproveTolProfileGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof LineApproveTolProfilePK)) {
            return false;
        }
        LineApproveTolProfilePK otherPk = (LineApproveTolProfilePK)other;
        return Functions.equals(otherPk.lineApproveTolProfileGid, this.lineApproveTolProfileGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.LineApproveTolProfileHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.lineApproveTolProfileGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return LineApproveTolProfileColumns.lineApproveTolProfileGid.convertFromDB(this.lineApproveTolProfileGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static LineApproveTolProfilePK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("LineApproveTolProfilePK requires a non-null XID to generate a GID");
            }
            LineApproveTolProfilePK lineApproveTolProfilePK = new LineApproveTolProfilePK(domain, xid);
            return lineApproveTolProfilePK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static LineApproveTolProfilePK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            LineApproveTolProfilePK lineApproveTolProfilePK = LineApproveTolProfilePK.newPK(domainName, xid, connection);
            return lineApproveTolProfilePK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return LineApproveTolProfilePK.class;
        }

        @Override
        public final String getEntity() {
            return "LineApproveTolProfile";
        }

        @Override
        public final String getTableName() {
            return "line_approve_tol_profile";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"line_approve_tol_profile_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            LineApproveTolProfilePK result = new LineApproveTolProfilePK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
