/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.LineAppTolShipmentRefnumData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface LineAppTolShipmentRefnumRemoteDB
extends EJBObject {
    public LineAppTolShipmentRefnumData getData() throws RemoteException, GLException;

    public void setData(LineAppTolShipmentRefnumData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getLineApproveToleranceGid() throws RemoteException, GLException;

    public void setLineApproveToleranceGid(String var1) throws RemoteException, GLException;

    public Integer getRefnumSequence() throws RemoteException, GLException;

    public void setRefnumSequence(Integer var1) throws RemoteException, GLException;

    public String getShipmentRefnumQualGid() throws RemoteException, GLException;

    public void setShipmentRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getShipmentRefnumValue() throws RemoteException, GLException;

    public void setShipmentRefnumValue(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
