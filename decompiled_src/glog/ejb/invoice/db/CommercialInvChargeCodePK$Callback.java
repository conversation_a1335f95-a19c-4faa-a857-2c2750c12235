/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CommercialInvChargeCodePK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class CommercialInvChargeCodePK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return CommercialInvChargeCodePK.class;
    }

    @Override
    public final String getEntity() {
        return "CommercialInvChargeCode";
    }

    @Override
    public final String getTableName() {
        return "commercial_inv_charge_code";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"commercial_inv_charge_code_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        CommercialInvChargeCodePK result = new CommercialInvChargeCodePK(0, q.getObject(pkOffset + 0 + 1), transaction);
        return result;
    }
}
