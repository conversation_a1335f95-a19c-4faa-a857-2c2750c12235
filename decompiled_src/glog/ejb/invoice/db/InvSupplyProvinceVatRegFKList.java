/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class InvSupplyProvinceVatRegFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("invoiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvSupplyProvinceVatRegData";
            }

            @Override
            public String getPkField() {
                return "invoiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "InvoiceData";
            }

            @Override
            public String getFkDataField() {
                return "invoiceGid";
            }
        });
        fks.put("vatProvincialRegGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvSupplyProvinceVatRegData";
            }

            @Override
            public String getPkField() {
                return "vatProvincialRegGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.vat.db";
            }

            @Override
            public String getFkDataClass() {
                return "VatProvincialRegistrationData";
            }

            @Override
            public String getFkDataField() {
                return "vatProvincialRegGid";
            }
        });
        fks.put("countryCode3Gid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "InvSupplyProvinceVatRegData";
            }

            @Override
            public String getPkField() {
                return "countryCode3Gid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.location.db";
            }

            @Override
            public String getFkDataClass() {
                return "CountryCodeData";
            }

            @Override
            public String getFkDataField() {
                return "countryCode3Gid";
            }
        });
    }
}
