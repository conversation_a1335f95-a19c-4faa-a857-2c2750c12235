/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLiCommercialDataPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceLiCommercialDataData
extends BeanData {
    public String invoiceGid;
    public Integer lineitemSeqNo;
    public Integer commercialDataSeqNo;
    public Currency unitPrice;
    public String unitPriceQualifier;
    public Currency liTotalCommercialValue;
    public Long unitCount;
    public String packagingUnitGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLiCommercialDataData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field lineitemSeqNoField = beanDataFields[1];
    public static Field commercialDataSeqNoField = beanDataFields[2];
    public static Field unitPriceField = beanDataFields[3];
    public static Field unitPriceQualifierField = beanDataFields[4];
    public static Field liTotalCommercialValueField = beanDataFields[5];
    public static Field unitCountField = beanDataFields[6];
    public static Field packagingUnitGidField = beanDataFields[7];
    public static Field exchangeRateDateField = beanDataFields[8];
    public static Field exchangeRateGidField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceLiCommercialDataData() {
    }

    public InvoiceLiCommercialDataData(InvoiceLiCommercialDataData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceLiCommercialDataPK();
    }

    @Legacy
    public InvoiceLiCommercialDataPK getInvoiceLiCommercialDataPK() {
        if (this.commercialDataSeqNo == null) {
            return null;
        }
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.lineitemSeqNo == null) {
            return null;
        }
        return new InvoiceLiCommercialDataPK(this.commercialDataSeqNo, this.invoiceGid, this.lineitemSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceLiCommercialDataPK((InvoiceLiCommercialDataPK)pk);
    }

    @Legacy
    public void setInvoiceLiCommercialDataPK(InvoiceLiCommercialDataPK pk) {
        this.commercialDataSeqNo = (Integer)pk.getAppValue(0);
        this.invoiceGid = (String)pk.getAppValue(1);
        this.lineitemSeqNo = (Integer)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceLiCommercialDataQueryGen";
    }

    public static InvoiceLiCommercialDataData load(Connection conn, InvoiceLiCommercialDataPK pk) throws GLException {
        return (InvoiceLiCommercialDataData)InvoiceLiCommercialDataData.load(conn, pk, InvoiceLiCommercialDataData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceLiCommercialDataData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceLiCommercialDataData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLiCommercialDataData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceLiCommercialDataData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceLiCommercialDataData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceLiCommercialDataData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceLiCommercialDataData.class);
    }
}
