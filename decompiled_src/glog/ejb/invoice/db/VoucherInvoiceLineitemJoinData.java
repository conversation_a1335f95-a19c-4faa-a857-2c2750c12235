/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class VoucherInvoiceLineitemJoinData
extends BeanData {
    public String invoiceGid;
    public Integer lineitemSeqNo;
    public String voucherGid;
    public Currency amountPaid;
    public String adjustmentReasonGid;
    public LocalDate exchangeRateDate;
    public String exchangeRateGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(VoucherInvoiceLineitemJoinData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field lineitemSeqNoField = beanDataFields[1];
    public static Field voucherGidField = beanDataFields[2];
    public static Field amountPaidField = beanDataFields[3];
    public static Field adjustmentReasonGidField = beanDataFields[4];
    public static Field exchangeRateDateField = beanDataFields[5];
    public static Field exchangeRateGidField = beanDataFields[6];
    public static Field domainNameField = beanDataFields[7];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public VoucherInvoiceLineitemJoinData() {
    }

    public VoucherInvoiceLineitemJoinData(VoucherInvoiceLineitemJoinData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getVoucherInvoiceLineitemJoinPK();
    }

    @Legacy
    public VoucherInvoiceLineitemJoinPK getVoucherInvoiceLineitemJoinPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.lineitemSeqNo == null) {
            return null;
        }
        if (this.voucherGid == null) {
            return null;
        }
        return new VoucherInvoiceLineitemJoinPK(this.invoiceGid, this.lineitemSeqNo, this.voucherGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setVoucherInvoiceLineitemJoinPK((VoucherInvoiceLineitemJoinPK)pk);
    }

    @Legacy
    public void setVoucherInvoiceLineitemJoinPK(VoucherInvoiceLineitemJoinPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.lineitemSeqNo = (Integer)pk.getAppValue(1);
        this.voucherGid = (String)pk.getAppValue(2);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.VoucherInvoiceLineitemJoinQueryGen";
    }

    public static VoucherInvoiceLineitemJoinData load(Connection conn, VoucherInvoiceLineitemJoinPK pk) throws GLException {
        return (VoucherInvoiceLineitemJoinData)VoucherInvoiceLineitemJoinData.load(conn, pk, VoucherInvoiceLineitemJoinData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return VoucherInvoiceLineitemJoinData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return VoucherInvoiceLineitemJoinData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherInvoiceLineitemJoinData.load(conn, whereClause, prepareArguments, fetchSize, VoucherInvoiceLineitemJoinData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return VoucherInvoiceLineitemJoinData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return VoucherInvoiceLineitemJoinData.load(conn, fromWhere, alias, prepareArguments, fetchSize, VoucherInvoiceLineitemJoinData.class);
    }
}
