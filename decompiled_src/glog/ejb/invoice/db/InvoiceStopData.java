/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceStopPK;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceStopData
extends BeanData {
    public String invoiceGid;
    public String stopSeqNo;
    public String locationGid;
    public String locationRefnumQualGid;
    public String locationRefnum;
    public String invoiceStopReasonGid;
    public String equipmentPrefix;
    public String equipmentNumber;
    public LocalTimestamp startDate;
    public LocalTimestamp endDate;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceStopData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceGidField = beanDataFields[0];
    public static Field stopSeqNoField = beanDataFields[1];
    public static Field locationGidField = beanDataFields[2];
    public static Field locationRefnumQualGidField = beanDataFields[3];
    public static Field locationRefnumField = beanDataFields[4];
    public static Field invoiceStopReasonGidField = beanDataFields[5];
    public static Field equipmentPrefixField = beanDataFields[6];
    public static Field equipmentNumberField = beanDataFields[7];
    public static Field startDateField = beanDataFields[8];
    public static Field endDateField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceStopData() {
    }

    public InvoiceStopData(InvoiceStopData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceStopPK();
    }

    @Legacy
    public InvoiceStopPK getInvoiceStopPK() {
        if (this.invoiceGid == null) {
            return null;
        }
        if (this.stopSeqNo == null) {
            return null;
        }
        return new InvoiceStopPK(this.invoiceGid, this.stopSeqNo);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceStopPK((InvoiceStopPK)pk);
    }

    @Legacy
    public void setInvoiceStopPK(InvoiceStopPK pk) {
        this.invoiceGid = (String)pk.getAppValue(0);
        this.stopSeqNo = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceStopQueryGen";
    }

    public static InvoiceStopData load(Connection conn, InvoiceStopPK pk) throws GLException {
        return (InvoiceStopData)InvoiceStopData.load(conn, pk, InvoiceStopData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceStopData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceStopData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceStopData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceStopData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceStopData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceStopData.class);
    }
}
