/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class VoucherStatusFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("statusTypeGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "VoucherStatusData";
            }

            @Override
            public String getPkField() {
                return "statusTypeGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "StatusValueData";
            }

            @Override
            public String getFkDataField() {
                return "statusTypeGid";
            }
        });
        fks.put("statusValueGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "VoucherStatusData";
            }

            @Override
            public String getPkField() {
                return "statusValueGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "StatusValueData";
            }

            @Override
            public String getFkDataField() {
                return "statusValueGid";
            }
        });
        fks.put("voucherGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "VoucherStatusData";
            }

            @Override
            public String getPkField() {
                return "voucherGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "VoucherData";
            }

            @Override
            public String getFkDataField() {
                return "voucherGid";
            }
        });
    }
}
