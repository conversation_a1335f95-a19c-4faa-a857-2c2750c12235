/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemRefnumFKList.3
implements Fk {
    InvoiceLineitemRefnumFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemRefnumData";
    }

    @Override
    public String getPkField() {
        return "invoiceLiRefnumQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "InvoiceLineitemRefnumQualData";
    }

    @Override
    public String getFkDataField() {
        return "invoiceLiRefnumQualGid";
    }
}
