/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemRefnumQualData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface InvoiceLineitemRefnumQualRemoteDB
extends EJBObject {
    public InvoiceLineitemRefnumQualData getData() throws RemoteException, GLException;

    public void setData(InvoiceLineitemRefnumQualData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getInvoiceLiRefnumQualGid() throws RemoteException, GLException;

    public void setInvoiceLiRefnumQualGid(String var1) throws RemoteException, GLException;

    public String getInvoiceLiRefnumQualXid() throws RemoteException, GLException;

    public void setInvoiceLiRefnumQualXid(String var1) throws RemoteException, GLException;

    public String getInvoiceLiRefnumDesc() throws RemoteException, GLException;

    public void setInvoiceLiRefnumDesc(String var1) throws RemoteException, GLException;

    public String getDefaultRefnumBnTypeGid() throws RemoteException, GLException;

    public void setDefaultRefnumBnTypeGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
