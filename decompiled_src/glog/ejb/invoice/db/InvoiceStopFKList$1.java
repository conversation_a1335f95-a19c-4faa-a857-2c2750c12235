/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceStopFKList.1
implements Fk {
    InvoiceStopFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceStopData";
    }

    @Override
    public String getPkField() {
        return "locationRefnumQualGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.location.db";
    }

    @Override
    public String getFkDataClass() {
        return "LocationRefnumQualData";
    }

    @Override
    public String getFkDataField() {
        return "locationRefnumQualGid";
    }
}
