/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.CommercialInvoiceChargeColumns;
import glog.ejb.invoice.db.CommercialInvoiceChargeData;
import glog.ejb.invoice.db.CommercialInvoiceChargePK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class CommercialInvoiceChargeBeanDB
extends BeanManagedEntityBean {
    public Object commercialInvoiceGid;
    public Object sequenceNo;
    public Object chargeAmount;
    public Object chargeActivity;
    public Object commercialInvChargeCodeGid;
    public Object exchangeRateDate;
    public Object exchangeRateGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient CommercialInvoiceChargePK pk;
    protected transient CommercialInvoiceChargeData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(CommercialInvoiceChargeBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static CommercialInvoiceChargePK.Callback theCall = new CommercialInvoiceChargePK.Callback();

    public CommercialInvoiceChargeBeanDB() {
        super(false);
    }

    public CommercialInvoiceChargePK ejbCreate(CommercialInvoiceChargeData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(CommercialInvoiceChargeData data) throws CreateException {
        this.ejbPostCreator();
    }

    public CommercialInvoiceChargePK ejbFindByPrimaryKey(CommercialInvoiceChargePK pk) throws FinderException {
        return (CommercialInvoiceChargePK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(CommercialInvoiceChargePK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<CommercialInvoiceChargePK> v = new Vector<CommercialInvoiceChargePK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(CommercialInvoiceChargePK pk, CommercialInvoiceChargeData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(CommercialInvoiceChargePK pk, CommercialInvoiceChargeData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(CommercialInvoiceChargePK pk, CommercialInvoiceChargeData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (CommercialInvoiceChargePK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(CommercialInvoiceChargeColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(CommercialInvoiceChargeColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(CommercialInvoiceChargeColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(CommercialInvoiceChargeColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        CommercialInvoiceChargePK pk = (CommercialInvoiceChargePK)genericPk;
        this.commercialInvoiceGid = pk.commercialInvoiceGid;
        this.sequenceNo = pk.sequenceNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        CommercialInvoiceChargePK pk = new CommercialInvoiceChargePK(0, this.commercialInvoiceGid, this.sequenceNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.CommercialInvoiceCharge";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public CommercialInvoiceChargeData getData() throws GLException {
        try {
            CommercialInvoiceChargeData retval = new CommercialInvoiceChargeData();
            retval.getFromBean(this, CommercialInvoiceChargeColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(CommercialInvoiceChargeData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(CommercialInvoiceChargeData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            CommercialInvoiceChargeData oldData = modified ? this.getData() : null;
            data.setToBean(this, CommercialInvoiceChargeColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return CommercialInvoiceChargeData.class;
    }

    @Override
    public Class getPkClass() {
        return CommercialInvoiceChargePK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getCommercialInvoiceGid() throws GLException {
        try {
            return (String)CommercialInvoiceChargeColumns.commercialInvoiceGid.convertFromDB(this.commercialInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvoiceGid(String commercialInvoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvoiceGid;
            CommercialInvoiceChargeData data = this.getData();
            this.commercialInvoiceGid = CommercialInvoiceChargeColumns.commercialInvoiceGid.convertToDB(commercialInvoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvoiceGid", String.class, oldValue, this.commercialInvoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getSequenceNo() throws GLException {
        try {
            return (Integer)CommercialInvoiceChargeColumns.sequenceNo.convertFromDB(this.sequenceNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSequenceNo(Integer sequenceNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.sequenceNo;
            CommercialInvoiceChargeData data = this.getData();
            this.sequenceNo = CommercialInvoiceChargeColumns.sequenceNo.convertToDB(sequenceNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "sequenceNo", Integer.class, oldValue, this.sequenceNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getChargeAmount() throws GLException {
        try {
            return (Currency)CommercialInvoiceChargeColumns.chargeAmount.convertFromDB(this.chargeAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setChargeAmount(Currency chargeAmount) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.chargeAmount;
            CommercialInvoiceChargeData data = this.getData();
            this.chargeAmount = CommercialInvoiceChargeColumns.chargeAmount.convertToDB(chargeAmount);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "chargeAmount", Currency.class, oldValue, this.chargeAmount);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getChargeActivity() throws GLException {
        try {
            return (String)CommercialInvoiceChargeColumns.chargeActivity.convertFromDB(this.chargeActivity);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setChargeActivity(String chargeActivity) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.chargeActivity;
            CommercialInvoiceChargeData data = this.getData();
            this.chargeActivity = CommercialInvoiceChargeColumns.chargeActivity.convertToDB(chargeActivity);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "chargeActivity", String.class, oldValue, this.chargeActivity);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getCommercialInvChargeCodeGid() throws GLException {
        try {
            return (String)CommercialInvoiceChargeColumns.commercialInvChargeCodeGid.convertFromDB(this.commercialInvChargeCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCommercialInvChargeCodeGid(String commercialInvChargeCodeGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.commercialInvChargeCodeGid;
            CommercialInvoiceChargeData data = this.getData();
            this.commercialInvChargeCodeGid = CommercialInvoiceChargeColumns.commercialInvChargeCodeGid.convertToDB(commercialInvChargeCodeGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "commercialInvChargeCodeGid", String.class, oldValue, this.commercialInvChargeCodeGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalDate getExchangeRateDate() throws GLException {
        try {
            return (LocalDate)CommercialInvoiceChargeColumns.exchangeRateDate.convertFromDB(this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateDate(LocalDate exchangeRateDate) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateDate;
            CommercialInvoiceChargeData data = this.getData();
            this.exchangeRateDate = CommercialInvoiceChargeColumns.exchangeRateDate.convertToDB(exchangeRateDate);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateDate", LocalDate.class, oldValue, this.exchangeRateDate);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getExchangeRateGid() throws GLException {
        try {
            return (String)CommercialInvoiceChargeColumns.exchangeRateGid.convertFromDB(this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setExchangeRateGid(String exchangeRateGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.exchangeRateGid;
            CommercialInvoiceChargeData data = this.getData();
            this.exchangeRateGid = CommercialInvoiceChargeColumns.exchangeRateGid.convertToDB(exchangeRateGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "exchangeRateGid", String.class, oldValue, this.exchangeRateGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)CommercialInvoiceChargeColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            CommercialInvoiceChargeData data = this.getData();
            this.domainName = CommercialInvoiceChargeColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
