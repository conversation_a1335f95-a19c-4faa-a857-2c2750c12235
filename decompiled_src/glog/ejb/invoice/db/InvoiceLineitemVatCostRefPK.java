/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceLineitemVatCostRefColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class InvoiceLineitemVatCostRefPK
extends Pk {
    public Object costReferenceGid;
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object vatSeqno;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public InvoiceLineitemVatCostRefPK() {
    }

    public InvoiceLineitemVatCostRefPK(String costReferenceGid, String invoiceGid, Integer lineitemSeqNo, Integer vatSeqno) {
        this.costReferenceGid = this.notNull(InvoiceLineitemVatCostRefColumns.costReferenceGid.convertToDB(costReferenceGid), "costReferenceGid");
        this.invoiceGid = this.notNull(InvoiceLineitemVatCostRefColumns.invoiceGid.convertToDB(invoiceGid), "invoiceGid");
        this.lineitemSeqNo = this.notNull(InvoiceLineitemVatCostRefColumns.lineitemSeqNo.convertToDB(lineitemSeqNo), "lineitemSeqNo");
        this.vatSeqno = this.notNull(InvoiceLineitemVatCostRefColumns.vatSeqno.convertToDB(vatSeqno), "vatSeqno");
    }

    public InvoiceLineitemVatCostRefPK(int dummy, Object costReferenceGid, Object invoiceGid, Object lineitemSeqNo, Object vatSeqno) {
        this(dummy, costReferenceGid, invoiceGid, lineitemSeqNo, vatSeqno, null);
    }

    public InvoiceLineitemVatCostRefPK(int dummy, Object costReferenceGid, Object invoiceGid, Object lineitemSeqNo, Object vatSeqno, Object transaction) {
        this.costReferenceGid = costReferenceGid;
        this.invoiceGid = invoiceGid;
        this.lineitemSeqNo = lineitemSeqNo;
        this.vatSeqno = vatSeqno;
        this.transaction = transaction;
    }

    public InvoiceLineitemVatCostRefPK(InvoiceLineitemVatCostRefPK otherPk, Object transaction) {
        this.costReferenceGid = otherPk.costReferenceGid;
        this.invoiceGid = otherPk.invoiceGid;
        this.lineitemSeqNo = otherPk.lineitemSeqNo;
        this.vatSeqno = otherPk.vatSeqno;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.costReferenceGid != null ? String.valueOf(this.costReferenceGid) : "") + " " + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + " " + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "") + " " + (this.vatSeqno != null ? String.valueOf(this.vatSeqno) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.costReferenceGid != null ? String.valueOf(this.costReferenceGid) : "") + "|" + (this.invoiceGid != null ? String.valueOf(this.invoiceGid) : "") + "|" + (this.lineitemSeqNo != null ? String.valueOf(this.lineitemSeqNo) : "") + "|" + (this.vatSeqno != null ? String.valueOf(this.vatSeqno) : "");
    }

    public static InvoiceLineitemVatCostRefPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new InvoiceLineitemVatCostRefPK(gids[0], gids[1], Integer.valueOf(gids[2]), Integer.valueOf(gids[3])) : null;
    }

    public static InvoiceLineitemVatCostRefPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new InvoiceLineitemVatCostRefPK(gids[0], gids[1], Integer.valueOf(gids[2]), Integer.valueOf(gids[3])) : null;
    }

    public int hashCode() {
        return this.costReferenceGid.hashCode() + this.invoiceGid.hashCode() + this.lineitemSeqNo.hashCode() + this.vatSeqno.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof InvoiceLineitemVatCostRefPK)) {
            return false;
        }
        InvoiceLineitemVatCostRefPK otherPk = (InvoiceLineitemVatCostRefPK)other;
        return Functions.equals(otherPk.costReferenceGid, this.costReferenceGid) && Functions.equals(otherPk.invoiceGid, this.invoiceGid) && Functions.equals(otherPk.lineitemSeqNo, this.lineitemSeqNo) && Functions.equals(otherPk.vatSeqno, this.vatSeqno) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.invoice.InvoiceLineitemVatCostRefHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.costReferenceGid;
            }
            case 1: {
                return this.invoiceGid;
            }
            case 2: {
                return this.lineitemSeqNo;
            }
            case 3: {
                return this.vatSeqno;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return InvoiceLineitemVatCostRefColumns.costReferenceGid.convertFromDB(this.costReferenceGid);
            }
            case 1: {
                return InvoiceLineitemVatCostRefColumns.invoiceGid.convertFromDB(this.invoiceGid);
            }
            case 2: {
                return InvoiceLineitemVatCostRefColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
            }
            case 3: {
                return InvoiceLineitemVatCostRefColumns.vatSeqno.convertFromDB(this.vatSeqno);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return InvoiceLineitemVatCostRefPK.class;
        }

        @Override
        public final String getEntity() {
            return "InvoiceLineitemVatCostRef";
        }

        @Override
        public final String getTableName() {
            return "invoice_lineitem_vat_cost_ref";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"cost_reference_gid", "invoice_gid", "lineitem_seq_no", "vat_seqno"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            InvoiceLineitemVatCostRefPK result = new InvoiceLineitemVatCostRefPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), q.getObject(pkOffset + 2 + 1), q.getObject(pkOffset + 3 + 1), transaction);
            return result;
        }
    }
}
