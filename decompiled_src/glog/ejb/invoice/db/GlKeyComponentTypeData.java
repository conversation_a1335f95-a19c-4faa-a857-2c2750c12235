/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.GlKeyComponentTypePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class GlKeyComponentTypeData
extends BeanData {
    public String glKeyComponentTypeGid;
    public String glKeyComponentTypeXid;
    public String javaClass;
    public String gidQueryClass;
    public String glCodeAssignType = String.valueOf("O");
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(GlKeyComponentTypeData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field glKeyComponentTypeGidField = beanDataFields[0];
    public static Field glKeyComponentTypeXidField = beanDataFields[1];
    public static Field javaClassField = beanDataFields[2];
    public static Field gidQueryClassField = beanDataFields[3];
    public static Field glCodeAssignTypeField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public GlKeyComponentTypeData() {
    }

    public GlKeyComponentTypeData(GlKeyComponentTypeData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getGlKeyComponentTypePK();
    }

    @Legacy
    public GlKeyComponentTypePK getGlKeyComponentTypePK() {
        if (this.glKeyComponentTypeGid == null) {
            return null;
        }
        return new GlKeyComponentTypePK(this.glKeyComponentTypeGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setGlKeyComponentTypePK((GlKeyComponentTypePK)pk);
    }

    @Legacy
    public void setGlKeyComponentTypePK(GlKeyComponentTypePK pk) {
        this.glKeyComponentTypeGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.GlKeyComponentTypeQueryGen";
    }

    public static GlKeyComponentTypeData load(Connection conn, GlKeyComponentTypePK pk) throws GLException {
        return (GlKeyComponentTypeData)GlKeyComponentTypeData.load(conn, pk, GlKeyComponentTypeData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return GlKeyComponentTypeData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return GlKeyComponentTypeData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlKeyComponentTypeData.load(conn, whereClause, prepareArguments, fetchSize, GlKeyComponentTypeData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return GlKeyComponentTypeData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return GlKeyComponentTypeData.load(conn, fromWhere, alias, prepareArguments, fetchSize, GlKeyComponentTypeData.class);
    }
}
