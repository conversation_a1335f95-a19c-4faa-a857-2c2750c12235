/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.CommercialInvoiceData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface CommercialInvoiceRemoteDB
extends EJBObject {
    public CommercialInvoiceData getData() throws RemoteException, GLException;

    public void setData(CommercialInvoiceData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getCommercialInvoiceGid() throws RemoteException, GLException;

    public void setCommercialInvoiceGid(String var1) throws RemoteException, GLException;

    public String getCommercialInvoiceXid() throws RemoteException, GLException;

    public void setCommercialInvoiceXid(String var1) throws RemoteException, GLException;

    public String getCommercialInvoiceType() throws RemoteException, GLException;

    public void setCommercialInvoiceType(String var1) throws RemoteException, GLException;

    public LocalDate getInvoiceDate() throws RemoteException, GLException;

    public void setInvoiceDate(LocalDate var1) throws RemoteException, GLException;

    public Double getExchangeRate() throws RemoteException, GLException;

    public void setExchangeRate(Double var1) throws RemoteException, GLException;

    public String getExchangeToCurrencyGid() throws RemoteException, GLException;

    public void setExchangeToCurrencyGid(String var1) throws RemoteException, GLException;

    public Currency getProductAmount() throws RemoteException, GLException;

    public void setProductAmount(Currency var1) throws RemoteException, GLException;

    public Currency getOtherChargeAmount() throws RemoteException, GLException;

    public void setOtherChargeAmount(Currency var1) throws RemoteException, GLException;

    public Boolean getIsCalculateProductAmount() throws RemoteException, GLException;

    public void setIsCalculateProductAmount(Boolean var1) throws RemoteException, GLException;

    public Boolean getIsCalculateTotalInvoiceAmt() throws RemoteException, GLException;

    public void setIsCalculateTotalInvoiceAmt(Boolean var1) throws RemoteException, GLException;

    public Currency getTotalInvoiceAmount() throws RemoteException, GLException;

    public void setTotalInvoiceAmount(Currency var1) throws RemoteException, GLException;

    public String getIncoTermGid() throws RemoteException, GLException;

    public void setIncoTermGid(String var1) throws RemoteException, GLException;

    public String getTermLocationText() throws RemoteException, GLException;

    public void setTermLocationText(String var1) throws RemoteException, GLException;

    public String getFinalIncoTermGid() throws RemoteException, GLException;

    public void setFinalIncoTermGid(String var1) throws RemoteException, GLException;

    public String getFinalTextLocation() throws RemoteException, GLException;

    public void setFinalTextLocation(String var1) throws RemoteException, GLException;

    public String getCommercialPaymentCodeGid() throws RemoteException, GLException;

    public void setCommercialPaymentCodeGid(String var1) throws RemoteException, GLException;

    public LocalDate getExchangeRateDate() throws RemoteException, GLException;

    public void setExchangeRateDate(LocalDate var1) throws RemoteException, GLException;

    public String getExchangeRateGid() throws RemoteException, GLException;

    public void setExchangeRateGid(String var1) throws RemoteException, GLException;

    public String getExchangeFromCurrencyGid() throws RemoteException, GLException;

    public void setExchangeFromCurrencyGid(String var1) throws RemoteException, GLException;

    public Boolean getIsOverrideExchangeRate() throws RemoteException, GLException;

    public void setIsOverrideExchangeRate(Boolean var1) throws RemoteException, GLException;

    public String getCommercialInvoiceNumber() throws RemoteException, GLException;

    public void setCommercialInvoiceNumber(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
