/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceLineitemFKList.6
implements Fk {
    InvoiceLineitemFKList.6() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceLineitemData";
    }

    @Override
    public String getPkField() {
        return "vatCodeGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.vat.db";
    }

    @Override
    public String getFkDataClass() {
        return "VatCodeData";
    }

    @Override
    public String getFkDataField() {
        return "vatCodeGid";
    }
}
