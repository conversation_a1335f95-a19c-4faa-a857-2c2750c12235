/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class CommercialInvoiceChargeFKList.3
implements Fk {
    CommercialInvoiceChargeFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "CommercialInvoiceChargeData";
    }

    @Override
    public String getPkField() {
        return "commercialInvoiceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getFkDataClass() {
        return "CommercialInvoiceData";
    }

    @Override
    public String getFkDataField() {
        return "commercialInvoiceGid";
    }
}
