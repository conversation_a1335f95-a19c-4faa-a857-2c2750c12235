/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class AutoApproveRuleProfileDFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("autoApproveRuleGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleProfileDData";
            }

            @Override
            public String getPkField() {
                return "autoApproveRuleGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "AutoApproveRuleData";
            }

            @Override
            public String getFkDataField() {
                return "autoApproveRuleGid";
            }
        });
        fks.put("autoApproveRuleProfileGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getPkDataClass() {
                return "AutoApproveRuleProfileDData";
            }

            @Override
            public String getPkField() {
                return "autoApproveRuleProfileGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.invoice.db";
            }

            @Override
            public String getFkDataClass() {
                return "AutoApproveRuleProfileData";
            }

            @Override
            public String getFkDataField() {
                return "autoApproveRuleProfileGid";
            }
        });
    }
}
