/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.AutoApproveRuleProfilePK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AutoApproveRuleProfileData
extends BeanData {
    public String autoApproveRuleProfileGid;
    public String autoApproveRuleProfileXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AutoApproveRuleProfileData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field autoApproveRuleProfileGidField = beanDataFields[0];
    public static Field autoApproveRuleProfileXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AutoApproveRuleProfileData() {
    }

    public AutoApproveRuleProfileData(AutoApproveRuleProfileData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAutoApproveRuleProfilePK();
    }

    @Legacy
    public AutoApproveRuleProfilePK getAutoApproveRuleProfilePK() {
        if (this.autoApproveRuleProfileGid == null) {
            return null;
        }
        return new AutoApproveRuleProfilePK(this.autoApproveRuleProfileGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAutoApproveRuleProfilePK((AutoApproveRuleProfilePK)pk);
    }

    @Legacy
    public void setAutoApproveRuleProfilePK(AutoApproveRuleProfilePK pk) {
        this.autoApproveRuleProfileGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.AutoApproveRuleProfileQueryGen";
    }

    public static AutoApproveRuleProfileData load(Connection conn, AutoApproveRuleProfilePK pk) throws GLException {
        return (AutoApproveRuleProfileData)AutoApproveRuleProfileData.load(conn, pk, AutoApproveRuleProfileData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AutoApproveRuleProfileData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleProfileData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleProfileData.load(conn, whereClause, prepareArguments, fetchSize, AutoApproveRuleProfileData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AutoApproveRuleProfileData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AutoApproveRuleProfileData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AutoApproveRuleProfileData.class);
    }
}
