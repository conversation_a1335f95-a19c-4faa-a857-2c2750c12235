/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.invoice.db.InvoiceLineitemRefnumColumns;
import glog.ejb.invoice.db.InvoiceLineitemRefnumData;
import glog.ejb.invoice.db.InvoiceLineitemRefnumPK;
import glog.server.bngenerator.BNEngine;
import glog.server.bngenerator.BNNumber;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceLineitemRefnumBeanDB
extends BeanManagedEntityBean {
    public Object invoiceGid;
    public Object lineitemSeqNo;
    public Object invoiceLiRefnumQualGid;
    public Object referenceNumber;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient InvoiceLineitemRefnumPK pk;
    protected transient InvoiceLineitemRefnumData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceLineitemRefnumBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static InvoiceLineitemRefnumPK.Callback theCall = new InvoiceLineitemRefnumPK.Callback();

    public InvoiceLineitemRefnumBeanDB() {
        super(false);
    }

    public InvoiceLineitemRefnumPK ejbCreate(InvoiceLineitemRefnumData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(InvoiceLineitemRefnumData data) throws CreateException {
        this.ejbPostCreator();
    }

    public InvoiceLineitemRefnumPK ejbFindByPrimaryKey(InvoiceLineitemRefnumPK pk) throws FinderException {
        return (InvoiceLineitemRefnumPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(InvoiceLineitemRefnumPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<InvoiceLineitemRefnumPK> v = new Vector<InvoiceLineitemRefnumPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(InvoiceLineitemRefnumPK pk, InvoiceLineitemRefnumData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(InvoiceLineitemRefnumPK pk, InvoiceLineitemRefnumData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(InvoiceLineitemRefnumPK pk, InvoiceLineitemRefnumData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        if (this.referenceNumber == null) {
            BNNumber bnNumber = BNEngine.generateRefnum("invoice_li_refnum_qual_gid", (String)this.invoiceLiRefnumQualGid, "invoice_lineitem_refnum_qual", this.data, this.getConnection());
            this.referenceNumber = bnNumber.getValue();
        }
        this.pk = (InvoiceLineitemRefnumPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(InvoiceLineitemRefnumColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(InvoiceLineitemRefnumColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(InvoiceLineitemRefnumColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(InvoiceLineitemRefnumColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        InvoiceLineitemRefnumPK pk = (InvoiceLineitemRefnumPK)genericPk;
        this.invoiceGid = pk.invoiceGid;
        this.invoiceLiRefnumQualGid = pk.invoiceLiRefnumQualGid;
        this.lineitemSeqNo = pk.lineitemSeqNo;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        InvoiceLineitemRefnumPK pk = new InvoiceLineitemRefnumPK(0, this.invoiceGid, this.invoiceLiRefnumQualGid, this.lineitemSeqNo, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.InvoiceLineitemRefnum";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public InvoiceLineitemRefnumData getData() throws GLException {
        try {
            InvoiceLineitemRefnumData retval = new InvoiceLineitemRefnumData();
            retval.getFromBean(this, InvoiceLineitemRefnumColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(InvoiceLineitemRefnumData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(InvoiceLineitemRefnumData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            InvoiceLineitemRefnumData oldData = modified ? this.getData() : null;
            data.setToBean(this, InvoiceLineitemRefnumColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return InvoiceLineitemRefnumData.class;
    }

    @Override
    public Class getPkClass() {
        return InvoiceLineitemRefnumPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getInvoiceGid() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumColumns.invoiceGid.convertFromDB(this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceGid(String invoiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceGid;
            InvoiceLineitemRefnumData data = this.getData();
            this.invoiceGid = InvoiceLineitemRefnumColumns.invoiceGid.convertToDB(invoiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceGid", String.class, oldValue, this.invoiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getLineitemSeqNo() throws GLException {
        try {
            return (Integer)InvoiceLineitemRefnumColumns.lineitemSeqNo.convertFromDB(this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLineitemSeqNo(Integer lineitemSeqNo) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.lineitemSeqNo;
            InvoiceLineitemRefnumData data = this.getData();
            this.lineitemSeqNo = InvoiceLineitemRefnumColumns.lineitemSeqNo.convertToDB(lineitemSeqNo);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "lineitemSeqNo", Integer.class, oldValue, this.lineitemSeqNo);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getInvoiceLiRefnumQualGid() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumColumns.invoiceLiRefnumQualGid.convertFromDB(this.invoiceLiRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setInvoiceLiRefnumQualGid(String invoiceLiRefnumQualGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.invoiceLiRefnumQualGid;
            InvoiceLineitemRefnumData data = this.getData();
            this.invoiceLiRefnumQualGid = InvoiceLineitemRefnumColumns.invoiceLiRefnumQualGid.convertToDB(invoiceLiRefnumQualGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "invoiceLiRefnumQualGid", String.class, oldValue, this.invoiceLiRefnumQualGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getReferenceNumber() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumColumns.referenceNumber.convertFromDB(this.referenceNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setReferenceNumber(String referenceNumber) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.referenceNumber;
            InvoiceLineitemRefnumData data = this.getData();
            this.referenceNumber = InvoiceLineitemRefnumColumns.referenceNumber.convertToDB(referenceNumber);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "referenceNumber", String.class, oldValue, this.referenceNumber);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)InvoiceLineitemRefnumColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            InvoiceLineitemRefnumData data = this.getData();
            this.domainName = InvoiceLineitemRefnumColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
