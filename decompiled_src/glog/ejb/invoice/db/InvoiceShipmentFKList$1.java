/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.util.jdbc.Fk;

static final class InvoiceShipmentFKList.1
implements Fk {
    InvoiceShipmentFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.invoice.db";
    }

    @Override
    public String getPkDataClass() {
        return "InvoiceShipmentData";
    }

    @Override
    public String getPkField() {
        return "sShipUnitGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.shipment.db";
    }

    @Override
    public String getFkDataClass() {
        return "SShipUnitData";
    }

    @Override
    public String getFkDataField() {
        return "sShipUnitGid";
    }
}
