/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice.db;

import glog.ejb.invoice.db.InvoiceRefnumQualPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class InvoiceRefnumQualData
extends BeanData {
    public String invoiceRefnumQualGid;
    public String invoiceRefnumQualXid;
    public String invoiceRefnumDesc;
    public String defaultRefnumBnTypeGid;
    public String updateFlag;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(InvoiceRefnumQualData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field invoiceRefnumQualGidField = beanDataFields[0];
    public static Field invoiceRefnumQualXidField = beanDataFields[1];
    public static Field invoiceRefnumDescField = beanDataFields[2];
    public static Field defaultRefnumBnTypeGidField = beanDataFields[3];
    public static Field updateFlagField = beanDataFields[4];
    public static Field domainNameField = beanDataFields[5];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public InvoiceRefnumQualData() {
    }

    public InvoiceRefnumQualData(InvoiceRefnumQualData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getInvoiceRefnumQualPK();
    }

    @Legacy
    public InvoiceRefnumQualPK getInvoiceRefnumQualPK() {
        if (this.invoiceRefnumQualGid == null) {
            return null;
        }
        return new InvoiceRefnumQualPK(this.invoiceRefnumQualGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setInvoiceRefnumQualPK((InvoiceRefnumQualPK)pk);
    }

    @Legacy
    public void setInvoiceRefnumQualPK(InvoiceRefnumQualPK pk) {
        this.invoiceRefnumQualGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.invoice.gen.InvoiceRefnumQualQueryGen";
    }

    public static InvoiceRefnumQualData load(Connection conn, InvoiceRefnumQualPK pk) throws GLException {
        return (InvoiceRefnumQualData)InvoiceRefnumQualData.load(conn, pk, InvoiceRefnumQualData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return InvoiceRefnumQualData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return InvoiceRefnumQualData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRefnumQualData.load(conn, whereClause, prepareArguments, fetchSize, InvoiceRefnumQualData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return InvoiceRefnumQualData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return InvoiceRefnumQualData.load(conn, fromWhere, alias, prepareArguments, fetchSize, InvoiceRefnumQualData.class);
    }
}
