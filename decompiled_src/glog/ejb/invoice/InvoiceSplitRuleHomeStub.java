/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceSplitRule;
import glog.ejb.invoice.InvoiceSplitRuleBean;
import glog.ejb.invoice.InvoiceSplitRuleHome;
import glog.ejb.invoice.InvoiceSplitRuleStub;
import glog.ejb.invoice.db.InvoiceSplitRuleData;
import glog.ejb.invoice.db.InvoiceSplitRulePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class InvoiceSplitRuleHomeStub
implements InvoiceSplitRuleHome {
    static LRUCache cache = LocalEntityCache.register("InvoiceSplitRule");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public InvoiceSplitRule create(InvoiceSplitRuleData p1) throws CreateException, RemoteException {
        InvoiceSplitRuleStub invoiceSplitRuleStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "create", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            invoiceSplitRuleStub = new InvoiceSplitRuleStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return invoiceSplitRuleStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public InvoiceSplitRule findByPrimaryKey(InvoiceSplitRulePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceSplitRuleBean bean;
                ee.enterMethod("InvoiceSplitRuleHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (InvoiceSplitRuleBean)cache.get(p1)) != null) {
                    InvoiceSplitRuleStub invoiceSplitRuleStub = new InvoiceSplitRuleStub((EJBHome)this, bean);
                    return invoiceSplitRuleStub;
                }
                bean = new InvoiceSplitRuleBean();
                InvoiceSplitRulePK pk = bean.ejbFindByPrimaryKey(p1);
                InvoiceSplitRuleStub invoiceSplitRuleStub = new InvoiceSplitRuleStub((EJBHome)this, pk);
                return invoiceSplitRuleStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "findAll", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceSplitRuleStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "findAll", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceSplitRuleStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "findByPrimaryKeys", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceSplitRuleStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "findByPrimaryKeys", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceSplitRuleStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(InvoiceSplitRulePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceSplitRuleBean bean;
                ee.enterMethod("InvoiceSplitRuleHome", "findInCache", true);
                if (cache != null && (bean = (InvoiceSplitRuleBean)cache.get(p1)) != null) {
                    Vector<InvoiceSplitRulePK> v = new Vector<InvoiceSplitRulePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), InvoiceSplitRuleStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(InvoiceSplitRulePK p1, InvoiceSplitRuleData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "onCreate", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(InvoiceSplitRulePK p1, InvoiceSplitRuleData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "onUpdate", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(InvoiceSplitRulePK p1, InvoiceSplitRuleData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "onRemove", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "listBeansInCache", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "listLocks", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "unlock", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "getDataNoLock", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "getLockData", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "getMaxCacheSize", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceSplitRuleHome", "updateMaxCacheSize", true);
            InvoiceSplitRuleBean bean = new InvoiceSplitRuleBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new InvoiceSplitRuleStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
