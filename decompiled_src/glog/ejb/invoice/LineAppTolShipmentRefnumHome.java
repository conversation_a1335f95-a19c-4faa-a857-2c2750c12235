/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.LineAppTolShipmentRefnumHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface LineAppTolShipmentRefnumHome
extends LineAppTolShipmentRefnumHomeDB {
    public Enumeration findByLineApproveToleranceGid(String var1) throws FinderException, RemoteException;
}
