/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceEquipmentDetailPK;
import glog.ejb.invoice.db.InvoiceEquipmentSealBeanDB;
import glog.ejb.invoice.db.InvoicePK;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class InvoiceEquipmentSealBean
extends InvoiceEquipmentSealBeanDB {
    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    public Enumeration ejbFindByInvoiceEquipmentDetailPK(InvoiceEquipmentDetailPK equipDetailPK) throws FinderException {
        return this.ejbFind("invoice_gid=? and seq_number=?", new Object[]{equipDetailPK.invoiceGid, equipDetailPK.seqNumber});
    }
}
