/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoiceLineitemRemarkHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceLineitemRemarkHome
extends InvoiceLineitemRemarkHomeDB {
    public Enumeration findByInvoiceLineitemPK(InvoiceLineitemPK var1) throws FinderException, RemoteException;
}
