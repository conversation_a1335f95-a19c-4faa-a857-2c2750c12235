/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GlLookupKeyBean;
import glog.ejb.invoice.db.GlLookupKeyData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBContext;

public class GlLookupKeyServerSideEJBWrapper
extends GlLookupKeyBean {
    @Override
    public GlLookupKeyData getData() throws GLException {
        try {
            GlLookupKeyData glLookupKeyData = super.getData();
            return glLookupKeyData;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setData(GlLookupKeyData p1) throws GLException {
        try {
            super.setData(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setLinks(Vector p1, Vector p2) throws GLException {
        try {
            super.setLinks(p1, p2);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Pk getPK() {
        Pk pk = super.getPK();
        return pk;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() {
        super.markForCacheRemove();
    }

    @Override
    public void reload() throws GLException {
        try {
            super.reload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void unload() throws GLException {
        try {
            super.unload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Map verify() throws GLException {
        try {
            Map map = super.verify();
            return map;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getGlLookupKeyGid() throws GLException {
        try {
            String string = super.getGlLookupKeyGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setGlLookupKeyGid(String p1) throws GLException {
        try {
            super.setGlLookupKeyGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getGlLookupKeyXid() throws GLException {
        try {
            String string = super.getGlLookupKeyXid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setGlLookupKeyXid(String p1) throws GLException {
        try {
            super.setGlLookupKeyXid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Boolean getIsActive() throws GLException {
        try {
            Boolean bl = super.getIsActive();
            return bl;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setIsActive(Boolean p1) throws GLException {
        try {
            super.setIsActive(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getPerspective() throws GLException {
        try {
            String string = super.getPerspective();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setPerspective(String p1) throws GLException {
        try {
            super.setPerspective(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getGlCodeAssignType() throws GLException {
        try {
            String string = super.getGlCodeAssignType();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setGlCodeAssignType(String p1) throws GLException {
        try {
            super.setGlCodeAssignType(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDomainName() throws GLException {
        try {
            String string = super.getDomainName();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDomainName(String p1) throws GLException {
        try {
            super.setDomainName(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }
}
