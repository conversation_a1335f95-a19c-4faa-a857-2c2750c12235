/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.VoucherPK;
import glog.ejb.invoice.db.VoucherRemarkHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface VoucherRemarkHome
extends VoucherRemarkHomeDB {
    public Enumeration findByVoucherPK(VoucherPK var1) throws FinderException, RemoteException;
}
