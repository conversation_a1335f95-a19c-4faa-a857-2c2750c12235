/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.EntityContext
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GlKeyComponentParam;
import glog.ejb.invoice.GlKeyComponentParamBean;
import glog.ejb.invoice.GlKeyComponentParamHomeStub;
import glog.ejb.invoice.db.GlKeyComponentParamData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityContext;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.EntityContext;
import javax.ejb.Handle;
import javax.ejb.RemoveException;

public class GlKeyComponentParamStub
implements GlKeyComponentParam {
    private GlKeyComponentParamBean entityBean;
    private Pk pk;
    private EntityContext context;

    public GlKeyComponentParamStub(EJBHome ejbHome, GlKeyComponentParamBean bean) {
        this.entityBean = bean;
        this.pk = bean.getPK();
        this.context = new LocalEntityContext(ejbHome, this, this.pk);
    }

    public GlKeyComponentParamStub(EJBHome ejbHome, Pk pk) {
        this.pk = pk;
        this.context = new LocalEntityContext(ejbHome, this, pk);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public GlKeyComponentParamData getData() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getData", false);
            this.entityBean = this.loadOnDemand();
            GlKeyComponentParamData glKeyComponentParamData = this.entityBean.getData();
            return glKeyComponentParamData;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setData(GlKeyComponentParamData p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setData", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setData(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLinks(Vector p1, Vector p2) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setLinks", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLinks(p1, p2);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Pk getPK() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponentParam", "getPK", false);
                this.entityBean = this.loadOnDemand();
                Pk pk = this.entityBean.getPK();
                return pk;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("getPK", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponentParam", "markForCacheRemove", false);
                this.entityBean = this.loadOnDemand();
                this.entityBean.markForCacheRemove();
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("markForCacheRemove", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void reload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "reload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.reload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "unload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.unload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Map verify() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "verify", false);
            this.entityBean = this.loadOnDemand();
            Map map = this.entityBean.verify();
            return map;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlKeyComponentGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getGlKeyComponentGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlKeyComponentGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlKeyComponentGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setGlKeyComponentGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlKeyComponentGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Double getSequence() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getSequence", false);
            this.entityBean = this.loadOnDemand();
            Double d = this.entityBean.getSequence();
            return d;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setSequence(Double p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setSequence", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setSequence(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getParamValue() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getParamValue", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getParamValue();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setParamValue(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setParamValue", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setParamValue(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getParamValueUomCode() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getParamValueUomCode", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getParamValueUomCode();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setParamValueUomCode(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setParamValueUomCode", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setParamValueUomCode(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlKeyComponentTypeGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getGlKeyComponentTypeGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlKeyComponentTypeGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlKeyComponentTypeGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setGlKeyComponentTypeGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlKeyComponentTypeGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getDomainName() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "getDomainName", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getDomainName();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setDomainName(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponentParam", "setDomainName", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setDomainName(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    public void remove() throws RemoteException, RemoveException {
        try {
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponentParam", "remove", false);
                this.entityBean = this.loadOnDemand();
                this.remove(this.entityBean);
                this.entityBean = null;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                throw ex;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("remove", glex);
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() throws RemoteException {
        return this.getPK();
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    private void store(GlKeyComponentParamBean entityBean) throws RemoteException {
        if (GlKeyComponentParamHomeStub.cache != null) {
            GlKeyComponentParamHomeStub.cache.put(this.pk, entityBean);
        }
        entityBean.ejbStore();
    }

    private void remove(GlKeyComponentParamBean entityBean) throws RemoveException {
        if (GlKeyComponentParamHomeStub.cache != null) {
            GlKeyComponentParamHomeStub.cache.remove(this.pk);
        }
        entityBean.ejbRemove();
    }

    private GlKeyComponentParamBean loadOnDemand() throws RemoteException {
        if (this.entityBean == null && (GlKeyComponentParamHomeStub.cache == null || (this.entityBean = (GlKeyComponentParamBean)GlKeyComponentParamHomeStub.cache.get(this.pk)) == null)) {
            this.entityBean = new GlKeyComponentParamBean();
            this.entityBean.setPK(this.pk);
            this.entityBean.setEntityContext(this.context);
            this.entityBean.ejbLoad();
            if (GlKeyComponentParamHomeStub.cache != null) {
                GlKeyComponentParamHomeStub.cache.put(this.pk, this.entityBean);
            }
        }
        return this.entityBean;
    }
}
