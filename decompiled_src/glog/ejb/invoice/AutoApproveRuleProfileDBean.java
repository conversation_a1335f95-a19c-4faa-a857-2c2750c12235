/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.AutoApproveRuleProfileDBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class AutoApproveRuleProfileDBean
extends AutoApproveRuleProfileDBeanDB {
    public Enumeration ejbFindByAutoApproveRuleProfileGid(String ruleProfileGid) throws FinderException {
        return this.ejbFind("auto_approve_rule_profile_gid = ?", new Object[]{ruleProfileGid});
    }
}
