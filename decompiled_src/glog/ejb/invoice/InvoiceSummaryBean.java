/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.InvoiceSummaryBeanDB;
import glog.ejb.invoice.db.InvoiceSummaryData;
import glog.ejb.invoice.db.InvoiceSummaryPK;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import java.util.Enumeration;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceSummaryBean
extends InvoiceSummaryBeanDB {
    private static final String SELECT_SUMMARY_SEQ = "SELECT nvl(MAX(INVOICE_SUMMARY_SEQ_NO) , 0)+1 from INVOICE_SUMMARY WHERE INVOICE_GID = ?";

    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private Integer findNextSequence(String strInvoiceGid) throws GLException {
        SqlQuery sequenceQuery = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_SUMMARY_SEQ), new Object[]{strInvoiceGid});
        T2SharedConnection conn = this.getConnection();
        try {
            conn.open();
            sequenceQuery.open(conn.get());
            if (sequenceQuery.next()) {
                Integer n = new Integer(sequenceQuery.getInt(1));
                return n;
            }
        }
        finally {
            sequenceQuery.close();
            conn.close();
        }
        return null;
    }

    @Override
    public InvoiceSummaryPK ejbCreate(InvoiceSummaryData isData) throws CreateException {
        try {
            Integer nextSequence;
            if (isData.invoiceSummarySeqNo == null && (nextSequence = this.findNextSequence(isData.invoiceGid)) != null) {
                isData.invoiceSummarySeqNo = nextSequence;
            }
            return super.ejbCreate(isData);
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }
}
