/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemVatCostRefBeanDB;
import glog.ejb.invoice.db.InvoiceLineitemVatCostRefData;
import glog.ejb.invoice.db.InvoiceLineitemVatPK;
import glog.ejb.invoice.db.InvoicePK;
import glog.server.total.CostTotal;
import glog.util.exception.GLException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class InvoiceLineitemVatCostRefBean
extends InvoiceLineitemVatCostRefBeanDB {
    public Enumeration ejbFindByInvoiceLineitemVatPK(InvoiceLineitemVatPK invoiceLineitemVatPK) throws FinderException {
        return this.ejbFind("invoice_gid = ? AND lineitem_seq_no = ? AND vat_seqno = ?", new Object[]{invoiceLineitemVatPK.getDbValue(0), invoiceLineitemVatPK.getDbValue(1), invoiceLineitemVatPK.getDbValue(2)});
    }

    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.invoiceGid});
    }

    @Override
    protected boolean preCreate() throws GLException {
        boolean returnVal = super.preCreate();
        this.prePersist(true);
        return returnVal;
    }

    @Override
    protected boolean preStore() throws GLException {
        if (!super.preStore()) {
            return false;
        }
        this.prePersist(false);
        return true;
    }

    private boolean prePersist(boolean isCreate) throws GLException {
        try {
            InvoiceLineitemVatCostRefData newData = this.getData();
            if (newData.vatAmount != null) {
                CostTotal amtCostTot = new CostTotal();
                amtCostTot.add(newData.vatAmount);
                newData.vatAmount = amtCostTot.get();
            }
            this.setData(newData, false);
            return true;
        }
        catch (Exception e) {
            throw GLException.factory(e);
        }
    }
}
