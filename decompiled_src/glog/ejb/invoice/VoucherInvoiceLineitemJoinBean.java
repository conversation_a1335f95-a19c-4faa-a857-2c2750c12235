/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.VoucherInvoiceLineitemJoinBeanDB;
import glog.ejb.invoice.db.VoucherPK;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class VoucherInvoiceLineitemJoinBean
extends VoucherInvoiceLineitemJoinBeanDB {
    private static final String SELECT_BY_PARENT_INVOICE = "select j.invoice_gid, j.lineitem_seq_no, j.voucher_gid from voucher_invoice_lineitem_join j, voucher v where v.invoice_gid = ? and v.voucher_gid = j.voucher_gid";

    public Enumeration ejbFindByVoucherGid(String strVoucherGid) throws FinderException {
        return this.ejbFind("voucher_gid = ?", new Object[]{strVoucherGid});
    }

    public Enumeration ejbFindByVoucherPK(VoucherPK voucherPK) throws FinderException {
        return this.ejbFind("voucher_gid = ?", new Object[]{voucherPK.toString()});
    }

    public Enumeration ejbFindByInvoicePKOnVoucher(InvoicePK invoicePK) throws FinderException {
        return this.ejbFindSQL(SELECT_BY_PARENT_INVOICE, new Object[]{invoicePK.toString()});
    }

    public Enumeration ejbFindByInvoiceLineitemPK(InvoiceLineitemPK invoiceLineitemPK) throws FinderException {
        return this.ejbFind("invoice_gid = ? and lineitem_seq_no = ?", new Object[]{invoiceLineitemPK.invoiceGid, invoiceLineitemPK.lineitemSeqNo});
    }
}
