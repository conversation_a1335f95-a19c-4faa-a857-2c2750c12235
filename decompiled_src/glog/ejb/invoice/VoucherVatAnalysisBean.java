/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.VoucherPK;
import glog.ejb.invoice.db.VoucherVatAnalysisBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class VoucherVatAnalysisBean
extends VoucherVatAnalysisBeanDB {
    public Enumeration ejbFindByVoucherPK(VoucherPK voucherPK) throws FinderException {
        return this.ejbFind("voucher_gid = ?", new Object[]{voucherPK.toString()});
    }
}
