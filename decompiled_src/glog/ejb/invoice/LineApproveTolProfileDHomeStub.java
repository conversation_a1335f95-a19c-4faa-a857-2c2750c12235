/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.LineApproveTolProfileD;
import glog.ejb.invoice.LineApproveTolProfileDBean;
import glog.ejb.invoice.LineApproveTolProfileDHome;
import glog.ejb.invoice.LineApproveTolProfileDStub;
import glog.ejb.invoice.db.LineApproveTolProfileDData;
import glog.ejb.invoice.db.LineApproveTolProfileDPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class LineApproveTolProfileDHomeStub
implements LineApproveTolProfileDHome {
    static LRUCache cache = LocalEntityCache.register("LineApproveTolProfileD");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByLineApproveTolProfileGid(String p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "findByLineApproveTolProfileGid", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Enumeration e = bean.ejbFindByLineApproveTolProfileGid(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveTolProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByLineApproveTolProfileGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LineApproveTolProfileD create(LineApproveTolProfileDData p1) throws CreateException, RemoteException {
        LineApproveTolProfileDStub lineApproveTolProfileDStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "create", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            lineApproveTolProfileDStub = new LineApproveTolProfileDStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return lineApproveTolProfileDStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public LineApproveTolProfileD findByPrimaryKey(LineApproveTolProfileDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                LineApproveTolProfileDBean bean;
                ee.enterMethod("LineApproveTolProfileDHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (LineApproveTolProfileDBean)cache.get(p1)) != null) {
                    LineApproveTolProfileDStub lineApproveTolProfileDStub = new LineApproveTolProfileDStub((EJBHome)this, bean);
                    return lineApproveTolProfileDStub;
                }
                bean = new LineApproveTolProfileDBean();
                LineApproveTolProfileDPK pk = bean.ejbFindByPrimaryKey(p1);
                LineApproveTolProfileDStub lineApproveTolProfileDStub = new LineApproveTolProfileDStub((EJBHome)this, pk);
                return lineApproveTolProfileDStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "findAll", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveTolProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "findAll", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveTolProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "findByPrimaryKeys", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveTolProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "findByPrimaryKeys", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, LineApproveTolProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(LineApproveTolProfileDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                LineApproveTolProfileDBean bean;
                ee.enterMethod("LineApproveTolProfileDHome", "findInCache", true);
                if (cache != null && (bean = (LineApproveTolProfileDBean)cache.get(p1)) != null) {
                    Vector<LineApproveTolProfileDPK> v = new Vector<LineApproveTolProfileDPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), LineApproveTolProfileDStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(LineApproveTolProfileDPK p1, LineApproveTolProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "onCreate", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(LineApproveTolProfileDPK p1, LineApproveTolProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "onUpdate", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(LineApproveTolProfileDPK p1, LineApproveTolProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "onRemove", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "listBeansInCache", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "listLocks", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "unlock", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "getDataNoLock", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "getLockData", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "getMaxCacheSize", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("LineApproveTolProfileDHome", "updateMaxCacheSize", true);
            LineApproveTolProfileDBean bean = new LineApproveTolProfileDBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new LineApproveTolProfileDStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
