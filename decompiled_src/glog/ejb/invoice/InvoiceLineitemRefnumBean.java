/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoiceLineitemRefnumBeanDB;
import glog.ejb.invoice.db.InvoicePK;
import glog.util.OTMMessageFormat;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class InvoiceLineitemRefnumBean
extends InvoiceLineitemRefnumBeanDB {
    private static final String SQ = "'";

    public Enumeration ejbFindByInvoiceGidsAndQualifier(InvoicePK[] invoicePKs, String qualGid) throws FinderException {
        StringBuffer gidList = new StringBuffer();
        for (int i = 0; i < invoicePKs.length - 1; ++i) {
            gidList.append(SQ + invoicePKs[i].toString() + SQ + ",");
        }
        gidList.append(SQ + invoicePKs[invoicePKs.length - 1].toString() + SQ);
        String ejbFindClause = OTMMessageFormat.format("invoice_gid in ({0})", gidList);
        return this.ejbFind(ejbFindClause + " and INVOICE_LI_REFNUM_QUAL_GID=?", new Object[]{qualGid});
    }

    public Enumeration ejbFindByInvoiceLineitemPK(InvoiceLineitemPK invoiceLineitemPK) throws FinderException {
        return this.ejbFind("invoice_gid = ? and lineitem_seq_no = ?", new Object[]{invoiceLineitemPK.invoiceGid, invoiceLineitemPK.lineitemSeqNo});
    }
}
