/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.InvoiceSummaryRemarkBeanDB;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class InvoiceSummaryRemarkBean
extends InvoiceSummaryRemarkBeanDB {
    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }
}
