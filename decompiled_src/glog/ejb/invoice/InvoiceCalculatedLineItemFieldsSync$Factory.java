/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceCalculatedFieldsSync;
import glog.ejb.invoice.InvoiceCalculatedLineItemFieldsSync;
import glog.server.workflow.Topic;
import glog.util.CommandLine;
import glog.util.exception.GLException;

public static class InvoiceCalculatedLineItemFieldsSync.Factory
extends InvoiceCalculatedFieldsSync.Factory {
    public static InvoiceCalculatedLineItemFieldsSync.Factory theFactory = new InvoiceCalculatedLineItemFieldsSync.Factory();

    @Override
    public Topic newTopic(CommandLine commandLine) throws GLException {
        return new InvoiceCalculatedLineItemFieldsSync(commandLine);
    }

    @Override
    public String getDescriptionKey() {
        return "InvoiceCalculatedLineItemFieldsSync";
    }
}
