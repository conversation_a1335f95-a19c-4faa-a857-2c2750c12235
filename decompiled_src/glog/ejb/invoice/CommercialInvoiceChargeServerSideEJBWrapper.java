/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.ejb.invoice;

import glog.ejb.invoice.CommercialInvoiceChargeBean;
import glog.ejb.invoice.db.CommercialInvoiceChargeData;
import glog.util.LocalDate;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBContext;

public class CommercialInvoiceChargeServerSideEJBWrapper
extends CommercialInvoiceChargeBean {
    @Override
    public CommercialInvoiceChargeData getData() throws GLException {
        try {
            CommercialInvoiceChargeData commercialInvoiceChargeData = super.getData();
            return commercialInvoiceChargeData;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setData(CommercialInvoiceChargeData p1) throws GLException {
        try {
            super.setData(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setLinks(Vector p1, Vector p2) throws GLException {
        try {
            super.setLinks(p1, p2);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Pk getPK() {
        Pk pk = super.getPK();
        return pk;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() {
        super.markForCacheRemove();
    }

    @Override
    public void reload() throws GLException {
        try {
            super.reload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void unload() throws GLException {
        try {
            super.unload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Map verify() throws GLException {
        try {
            Map map = super.verify();
            return map;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getCommercialInvoiceGid() throws GLException {
        try {
            String string = super.getCommercialInvoiceGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setCommercialInvoiceGid(String p1) throws GLException {
        try {
            super.setCommercialInvoiceGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Integer getSequenceNo() throws GLException {
        try {
            Integer n = super.getSequenceNo();
            return n;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setSequenceNo(Integer p1) throws GLException {
        try {
            super.setSequenceNo(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Currency getChargeAmount() throws GLException {
        try {
            Currency currency = super.getChargeAmount();
            return currency;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setChargeAmount(Currency p1) throws GLException {
        try {
            super.setChargeAmount(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getChargeActivity() throws GLException {
        try {
            String string = super.getChargeActivity();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setChargeActivity(String p1) throws GLException {
        try {
            super.setChargeActivity(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getCommercialInvChargeCodeGid() throws GLException {
        try {
            String string = super.getCommercialInvChargeCodeGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setCommercialInvChargeCodeGid(String p1) throws GLException {
        try {
            super.setCommercialInvChargeCodeGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public LocalDate getExchangeRateDate() throws GLException {
        try {
            LocalDate localDate = super.getExchangeRateDate();
            return localDate;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateDate(LocalDate p1) throws GLException {
        try {
            super.setExchangeRateDate(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getExchangeRateGid() throws GLException {
        try {
            String string = super.getExchangeRateGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setExchangeRateGid(String p1) throws GLException {
        try {
            super.setExchangeRateGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDomainName() throws GLException {
        try {
            String string = super.getDomainName();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDomainName(String p1) throws GLException {
        try {
            super.setDomainName(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }
}
