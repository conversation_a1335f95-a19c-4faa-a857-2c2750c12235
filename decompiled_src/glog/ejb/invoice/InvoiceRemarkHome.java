/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.InvoiceRemarkHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceRemarkHome
extends InvoiceRemarkHomeDB {
    public Enumeration findByInvoicePK(InvoicePK var1) throws FinderException, RemoteException;
}
