/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoiceLineitemVatHomeDB;
import glog.ejb.invoice.db.InvoicePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceLineitemVatHome
extends InvoiceLineitemVatHomeDB {
    public Enumeration findByInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findByInvoiceLineitemPK(InvoiceLineitemPK var1) throws FinderException, RemoteException;
}
