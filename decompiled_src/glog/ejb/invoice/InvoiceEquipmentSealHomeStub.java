/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceEquipmentSeal;
import glog.ejb.invoice.InvoiceEquipmentSealBean;
import glog.ejb.invoice.InvoiceEquipmentSealHome;
import glog.ejb.invoice.InvoiceEquipmentSealStub;
import glog.ejb.invoice.db.InvoiceEquipmentDetailPK;
import glog.ejb.invoice.db.InvoiceEquipmentSealData;
import glog.ejb.invoice.db.InvoiceEquipmentSealPK;
import glog.ejb.invoice.db.InvoicePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class InvoiceEquipmentSealHomeStub
implements InvoiceEquipmentSealHome {
    static LRUCache cache = LocalEntityCache.register("InvoiceEquipmentSeal");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoicePK(InvoicePK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findByInvoicePK", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindByInvoicePK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoicePK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceEquipmentDetailPK(InvoiceEquipmentDetailPK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findByInvoiceEquipmentDetailPK", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindByInvoiceEquipmentDetailPK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceEquipmentDetailPK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public InvoiceEquipmentSeal create(InvoiceEquipmentSealData p1) throws CreateException, RemoteException {
        InvoiceEquipmentSealStub invoiceEquipmentSealStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "create", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            invoiceEquipmentSealStub = new InvoiceEquipmentSealStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return invoiceEquipmentSealStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public InvoiceEquipmentSeal findByPrimaryKey(InvoiceEquipmentSealPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceEquipmentSealBean bean;
                ee.enterMethod("InvoiceEquipmentSealHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (InvoiceEquipmentSealBean)cache.get(p1)) != null) {
                    InvoiceEquipmentSealStub invoiceEquipmentSealStub = new InvoiceEquipmentSealStub((EJBHome)this, bean);
                    return invoiceEquipmentSealStub;
                }
                bean = new InvoiceEquipmentSealBean();
                InvoiceEquipmentSealPK pk = bean.ejbFindByPrimaryKey(p1);
                InvoiceEquipmentSealStub invoiceEquipmentSealStub = new InvoiceEquipmentSealStub((EJBHome)this, pk);
                return invoiceEquipmentSealStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findAll", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findAll", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findByPrimaryKeys", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "findByPrimaryKeys", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentSealStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(InvoiceEquipmentSealPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceEquipmentSealBean bean;
                ee.enterMethod("InvoiceEquipmentSealHome", "findInCache", true);
                if (cache != null && (bean = (InvoiceEquipmentSealBean)cache.get(p1)) != null) {
                    Vector<InvoiceEquipmentSealPK> v = new Vector<InvoiceEquipmentSealPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), InvoiceEquipmentSealStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(InvoiceEquipmentSealPK p1, InvoiceEquipmentSealData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "onCreate", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(InvoiceEquipmentSealPK p1, InvoiceEquipmentSealData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "onUpdate", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(InvoiceEquipmentSealPK p1, InvoiceEquipmentSealData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "onRemove", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "listBeansInCache", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "listLocks", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "unlock", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "getDataNoLock", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "getLockData", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "getMaxCacheSize", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentSealHome", "updateMaxCacheSize", true);
            InvoiceEquipmentSealBean bean = new InvoiceEquipmentSealBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new InvoiceEquipmentSealStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
