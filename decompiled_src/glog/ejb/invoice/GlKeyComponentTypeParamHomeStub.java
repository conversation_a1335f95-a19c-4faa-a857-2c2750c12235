/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GlKeyComponentTypeParam;
import glog.ejb.invoice.GlKeyComponentTypeParamBean;
import glog.ejb.invoice.GlKeyComponentTypeParamHome;
import glog.ejb.invoice.GlKeyComponentTypeParamStub;
import glog.ejb.invoice.db.GlKeyComponentTypeParamData;
import glog.ejb.invoice.db.GlKeyComponentTypeParamPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class GlKeyComponentTypeParamHomeStub
implements GlKeyComponentTypeParamHome {
    static LRUCache cache = LocalEntityCache.register("GlKeyComponentTypeParam");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public GlKeyComponentTypeParam create(GlKeyComponentTypeParamData p1) throws CreateException, RemoteException {
        GlKeyComponentTypeParamStub glKeyComponentTypeParamStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "create", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            glKeyComponentTypeParamStub = new GlKeyComponentTypeParamStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return glKeyComponentTypeParamStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public GlKeyComponentTypeParam findByPrimaryKey(GlKeyComponentTypeParamPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                GlKeyComponentTypeParamBean bean;
                ee.enterMethod("GlKeyComponentTypeParamHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (GlKeyComponentTypeParamBean)cache.get(p1)) != null) {
                    GlKeyComponentTypeParamStub glKeyComponentTypeParamStub = new GlKeyComponentTypeParamStub((EJBHome)this, bean);
                    return glKeyComponentTypeParamStub;
                }
                bean = new GlKeyComponentTypeParamBean();
                GlKeyComponentTypeParamPK pk = bean.ejbFindByPrimaryKey(p1);
                GlKeyComponentTypeParamStub glKeyComponentTypeParamStub = new GlKeyComponentTypeParamStub((EJBHome)this, pk);
                return glKeyComponentTypeParamStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "findAll", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, GlKeyComponentTypeParamStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "findAll", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, GlKeyComponentTypeParamStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "findByPrimaryKeys", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, GlKeyComponentTypeParamStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "findByPrimaryKeys", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, GlKeyComponentTypeParamStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(GlKeyComponentTypeParamPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                GlKeyComponentTypeParamBean bean;
                ee.enterMethod("GlKeyComponentTypeParamHome", "findInCache", true);
                if (cache != null && (bean = (GlKeyComponentTypeParamBean)cache.get(p1)) != null) {
                    Vector<GlKeyComponentTypeParamPK> v = new Vector<GlKeyComponentTypeParamPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), GlKeyComponentTypeParamStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(GlKeyComponentTypeParamPK p1, GlKeyComponentTypeParamData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "onCreate", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(GlKeyComponentTypeParamPK p1, GlKeyComponentTypeParamData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "onUpdate", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(GlKeyComponentTypeParamPK p1, GlKeyComponentTypeParamData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "onRemove", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "listBeansInCache", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "listLocks", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "unlock", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "getDataNoLock", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "getLockData", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "getMaxCacheSize", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GlKeyComponentTypeParamHome", "updateMaxCacheSize", true);
            GlKeyComponentTypeParamBean bean = new GlKeyComponentTypeParamBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new GlKeyComponentTypeParamStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
