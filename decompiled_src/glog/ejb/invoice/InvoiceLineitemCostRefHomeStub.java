/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceLineitemCostRef;
import glog.ejb.invoice.InvoiceLineitemCostRefBean;
import glog.ejb.invoice.InvoiceLineitemCostRefHome;
import glog.ejb.invoice.InvoiceLineitemCostRefStub;
import glog.ejb.invoice.db.InvoiceLineitemCostRefData;
import glog.ejb.invoice.db.InvoiceLineitemCostRefPK;
import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoicePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class InvoiceLineitemCostRefHomeStub
implements InvoiceLineitemCostRefHome {
    static LRUCache cache = LocalEntityCache.register("InvoiceLineitemCostRef");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceLineitem(String p1, String p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByInvoiceLineitem", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByInvoiceLineitem(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceLineitem", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoicePK(InvoicePK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByInvoicePK", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByInvoicePK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoicePK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceGid(String p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByInvoiceGid", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByInvoiceGid(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceLineitemPK(InvoiceLineitemPK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByInvoiceLineitemPK", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByInvoiceLineitemPK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceLineitemPK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public InvoiceLineitemCostRef create(InvoiceLineitemCostRefData p1) throws CreateException, RemoteException {
        InvoiceLineitemCostRefStub invoiceLineitemCostRefStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "create", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            invoiceLineitemCostRefStub = new InvoiceLineitemCostRefStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return invoiceLineitemCostRefStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public InvoiceLineitemCostRef findByPrimaryKey(InvoiceLineitemCostRefPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceLineitemCostRefBean bean;
                ee.enterMethod("InvoiceLineitemCostRefHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (InvoiceLineitemCostRefBean)cache.get(p1)) != null) {
                    InvoiceLineitemCostRefStub invoiceLineitemCostRefStub = new InvoiceLineitemCostRefStub((EJBHome)this, bean);
                    return invoiceLineitemCostRefStub;
                }
                bean = new InvoiceLineitemCostRefBean();
                InvoiceLineitemCostRefPK pk = bean.ejbFindByPrimaryKey(p1);
                InvoiceLineitemCostRefStub invoiceLineitemCostRefStub = new InvoiceLineitemCostRefStub((EJBHome)this, pk);
                return invoiceLineitemCostRefStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findAll", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findAll", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByPrimaryKeys", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "findByPrimaryKeys", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceLineitemCostRefStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(InvoiceLineitemCostRefPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceLineitemCostRefBean bean;
                ee.enterMethod("InvoiceLineitemCostRefHome", "findInCache", true);
                if (cache != null && (bean = (InvoiceLineitemCostRefBean)cache.get(p1)) != null) {
                    Vector<InvoiceLineitemCostRefPK> v = new Vector<InvoiceLineitemCostRefPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), InvoiceLineitemCostRefStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(InvoiceLineitemCostRefPK p1, InvoiceLineitemCostRefData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "onCreate", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(InvoiceLineitemCostRefPK p1, InvoiceLineitemCostRefData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "onUpdate", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(InvoiceLineitemCostRefPK p1, InvoiceLineitemCostRefData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "onRemove", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "listBeansInCache", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "listLocks", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "unlock", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "getDataNoLock", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "getLockData", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "getMaxCacheSize", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceLineitemCostRefHome", "updateMaxCacheSize", true);
            InvoiceLineitemCostRefBean bean = new InvoiceLineitemCostRefBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new InvoiceLineitemCostRefStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
