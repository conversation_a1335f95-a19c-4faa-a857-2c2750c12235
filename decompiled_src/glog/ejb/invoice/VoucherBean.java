/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.allocation.AllocationBaseHome;
import glog.ejb.invoice.VoucherInvoiceLineitemJoinHome;
import glog.ejb.invoice.VoucherRefnumHome;
import glog.ejb.invoice.VoucherRemarkHome;
import glog.ejb.invoice.VoucherVatAnalysisHome;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.VoucherBeanDB;
import glog.ejb.invoice.db.VoucherPK;
import glog.ejb.shipment.ShipmentMatchedInvoiceCostsSync;
import glog.ejb.shipment.db.ShipmentPK;
import glog.server.bngenerator.BNEngine;
import glog.server.bngenerator.BNNumber;
import glog.server.bngenerator.BNType;
import glog.server.bngenerator.BNTypeContext;
import glog.server.bngenerator.contexts.BNEmptyContext;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.log.Log;
import glog.util.log.LogIds;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.NamingDirectory;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Enumeration;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.FinderException;

public class VoucherBean
extends VoucherBeanDB {
    private static final String SELECT_MATCHED_SHIPMENTS = "select shipment_gid from invoice_shipment where invoice_gid = ?";

    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    @Override
    protected VoucherPK newPK() throws GLException {
        String xid = this.getVoucherXid();
        String domainName = this.getDomainName();
        if (xid == null) {
            BNEmptyContext context = new BNEmptyContext();
            BNNumber xidNumber = BNEngine.generate(BNType.VOUCHER_XID, (BNTypeContext)context, domainName, this.getConnection(), 50);
            xid = xidNumber.getValue();
        }
        return new VoucherPK(domainName, xid);
    }

    @Override
    protected boolean preRemove() throws GLException {
        if (this.markedForCacheRemove) {
            return true;
        }
        VoucherPK voucherPK = (VoucherPK)this.getPK();
        VoucherBean.removeChildren(VoucherRefnumHome.class, "findByVoucherPK", new Class[]{VoucherPK.class}, new Object[]{voucherPK}, null, "VoucherRefnum", voucherPK);
        VoucherBean.removeChildren(VoucherRemarkHome.class, "findByVoucherPK", new Class[]{VoucherPK.class}, new Object[]{voucherPK}, null, "VoucherRemark", voucherPK);
        VoucherBean.removeChildren(VoucherVatAnalysisHome.class, "findByVoucherPK", new Class[]{VoucherPK.class}, new Object[]{voucherPK}, null, "VoucherVatAnalysis", voucherPK);
        VoucherBean.removeChildren(VoucherInvoiceLineitemJoinHome.class, "findByVoucherPK", new Class[]{VoucherPK.class}, new Object[]{voucherPK}, null, "VoucherInvoiceLineitemJoin", voucherPK);
        VoucherBean.removeChildren(AllocationBaseHome.class, "findByVoucherPK", new Class[]{VoucherPK.class}, new Object[]{voucherPK}, null, "AllocationBase", voucherPK);
        super.preRemove();
        return true;
    }

    public static void removeChildren(Class childHomeInterface, String finder, Class[] finderArgumentTypes, Object[] finderArguments, OnRemove onRemove, String childDescription, VoucherPK voucherPK) throws GLException {
        try {
            Method finderMethod = childHomeInterface.getMethod(finder, finderArgumentTypes);
            Field nameField = childHomeInterface.getField("NAME");
            NamingDirectory nd = NamingDirectory.get();
            try {
                EJBHome home = (EJBHome)nd.lookup((String)nameField.get(null));
                if (Log.idOn[LogIds.SETTLEMENT.index]) {
                    Log.logID(LogIds.SETTLEMENT, "Remove {0} from VoucherBean - Voucher: {1}", childDescription, voucherPK);
                }
                Enumeration e = (Enumeration)finderMethod.invoke((Object)home, finderArguments);
                while (e.hasMoreElements()) {
                    EJBObject bean = (EJBObject)e.nextElement();
                    if (onRemove != null) {
                        onRemove.callback(bean);
                    }
                    bean.remove();
                }
            }
            catch (InvocationTargetException ite) {
                if (ite.getTargetException() instanceof FinderNoMoreRecords) {
                    return;
                }
                throw ite;
            }
            finally {
                nd.release();
            }
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(new GLException.CausedBy("cause.VoucherBean.0001", null, new Object[][]{{"childDescription", childDescription}, {"voucherPK", voucherPK}}), t);
        }
    }

    @Override
    protected void postStore() throws GLException {
        super.postStore();
        this.registerShipmentInvoiceCostFieldsSync();
    }

    @Override
    protected void postCreate() throws GLException {
        super.postCreate();
        this.registerShipmentInvoiceCostFieldsSync();
    }

    private void registerShipmentInvoiceCostFieldsSync() throws GLException {
        SqlQuery sqlQuery = null;
        T2SharedConnection connection = this.getConnection();
        try {
            sqlQuery = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_MATCHED_SHIPMENTS), new Object[]{this.getInvoiceGid()});
            connection.open();
            sqlQuery.open(connection.get());
            while (sqlQuery.next()) {
                String shipmentGid = sqlQuery.getString("shipment_gid");
                if (shipmentGid == null) continue;
                Log.logID(LogIds.SETTLEMENT, "publish ShipmentMatchedInvoiceCostsSync for shipment {0}", shipmentGid);
                ShipmentMatchedInvoiceCostsSync.register(new ShipmentPK(shipmentGid));
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
        finally {
            if (sqlQuery != null) {
                sqlQuery.close();
            }
            if (connection != null) {
                connection.close();
            }
        }
    }

    public static interface OnRemove {
        public void callback(EJBObject var1) throws GLException;
    }
}
