/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.AutoApproveRuleProfileD;
import glog.ejb.invoice.AutoApproveRuleProfileDBean;
import glog.ejb.invoice.AutoApproveRuleProfileDHome;
import glog.ejb.invoice.AutoApproveRuleProfileDStub;
import glog.ejb.invoice.db.AutoApproveRuleProfileDData;
import glog.ejb.invoice.db.AutoApproveRuleProfileDPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class AutoApproveRuleProfileDHomeStub
implements AutoApproveRuleProfileDHome {
    static LRUCache cache = LocalEntityCache.register("AutoApproveRuleProfileD");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByAutoApproveRuleProfileGid(String p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "findByAutoApproveRuleProfileGid", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Enumeration e = bean.ejbFindByAutoApproveRuleProfileGid(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByAutoApproveRuleProfileGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AutoApproveRuleProfileD create(AutoApproveRuleProfileDData p1) throws CreateException, RemoteException {
        AutoApproveRuleProfileDStub autoApproveRuleProfileDStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "create", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            autoApproveRuleProfileDStub = new AutoApproveRuleProfileDStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return autoApproveRuleProfileDStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public AutoApproveRuleProfileD findByPrimaryKey(AutoApproveRuleProfileDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleProfileDBean bean;
                ee.enterMethod("AutoApproveRuleProfileDHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (AutoApproveRuleProfileDBean)cache.get(p1)) != null) {
                    AutoApproveRuleProfileDStub autoApproveRuleProfileDStub = new AutoApproveRuleProfileDStub((EJBHome)this, bean);
                    return autoApproveRuleProfileDStub;
                }
                bean = new AutoApproveRuleProfileDBean();
                AutoApproveRuleProfileDPK pk = bean.ejbFindByPrimaryKey(p1);
                AutoApproveRuleProfileDStub autoApproveRuleProfileDStub = new AutoApproveRuleProfileDStub((EJBHome)this, pk);
                return autoApproveRuleProfileDStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "findAll", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "findAll", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "findByPrimaryKeys", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "findByPrimaryKeys", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(AutoApproveRuleProfileDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleProfileDBean bean;
                ee.enterMethod("AutoApproveRuleProfileDHome", "findInCache", true);
                if (cache != null && (bean = (AutoApproveRuleProfileDBean)cache.get(p1)) != null) {
                    Vector<AutoApproveRuleProfileDPK> v = new Vector<AutoApproveRuleProfileDPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), AutoApproveRuleProfileDStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(AutoApproveRuleProfileDPK p1, AutoApproveRuleProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "onCreate", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(AutoApproveRuleProfileDPK p1, AutoApproveRuleProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "onUpdate", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(AutoApproveRuleProfileDPK p1, AutoApproveRuleProfileDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "onRemove", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "listBeansInCache", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "listLocks", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "unlock", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "getDataNoLock", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "getLockData", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "getMaxCacheSize", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileDHome", "updateMaxCacheSize", true);
            AutoApproveRuleProfileDBean bean = new AutoApproveRuleProfileDBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new AutoApproveRuleProfileDStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
