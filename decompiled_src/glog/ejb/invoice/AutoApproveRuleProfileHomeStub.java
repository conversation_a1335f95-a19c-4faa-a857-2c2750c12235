/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.AutoApproveRuleProfile;
import glog.ejb.invoice.AutoApproveRuleProfileBean;
import glog.ejb.invoice.AutoApproveRuleProfileHome;
import glog.ejb.invoice.AutoApproveRuleProfileStub;
import glog.ejb.invoice.db.AutoApproveRuleProfileData;
import glog.ejb.invoice.db.AutoApproveRuleProfilePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class AutoApproveRuleProfileHomeStub
implements AutoApproveRuleProfileHome {
    static LRUCache cache = LocalEntityCache.register("AutoApproveRuleProfile");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AutoApproveRuleProfile create(AutoApproveRuleProfileData p1) throws CreateException, RemoteException {
        AutoApproveRuleProfileStub autoApproveRuleProfileStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "create", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            autoApproveRuleProfileStub = new AutoApproveRuleProfileStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return autoApproveRuleProfileStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public AutoApproveRuleProfile findByPrimaryKey(AutoApproveRuleProfilePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleProfileBean bean;
                ee.enterMethod("AutoApproveRuleProfileHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (AutoApproveRuleProfileBean)cache.get(p1)) != null) {
                    AutoApproveRuleProfileStub autoApproveRuleProfileStub = new AutoApproveRuleProfileStub((EJBHome)this, bean);
                    return autoApproveRuleProfileStub;
                }
                bean = new AutoApproveRuleProfileBean();
                AutoApproveRuleProfilePK pk = bean.ejbFindByPrimaryKey(p1);
                AutoApproveRuleProfileStub autoApproveRuleProfileStub = new AutoApproveRuleProfileStub((EJBHome)this, pk);
                return autoApproveRuleProfileStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "findAll", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "findAll", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "findByPrimaryKeys", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "findByPrimaryKeys", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleProfileStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(AutoApproveRuleProfilePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleProfileBean bean;
                ee.enterMethod("AutoApproveRuleProfileHome", "findInCache", true);
                if (cache != null && (bean = (AutoApproveRuleProfileBean)cache.get(p1)) != null) {
                    Vector<AutoApproveRuleProfilePK> v = new Vector<AutoApproveRuleProfilePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), AutoApproveRuleProfileStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(AutoApproveRuleProfilePK p1, AutoApproveRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "onCreate", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(AutoApproveRuleProfilePK p1, AutoApproveRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "onUpdate", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(AutoApproveRuleProfilePK p1, AutoApproveRuleProfileData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "onRemove", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "listBeansInCache", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "listLocks", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "unlock", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "getDataNoLock", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "getLockData", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "getMaxCacheSize", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleProfileHome", "updateMaxCacheSize", true);
            AutoApproveRuleProfileBean bean = new AutoApproveRuleProfileBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new AutoApproveRuleProfileStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
