/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceShipmentBeanDB;
import glog.ejb.invoice.db.InvoiceShipmentData;
import glog.ejb.invoice.db.InvoiceShipmentPK;
import glog.ejb.shipment.ShipmentMatchedInvoiceCostsSync;
import glog.ejb.shipment.db.SShipUnitLinePK;
import glog.ejb.shipment.db.SShipUnitPK;
import glog.ejb.shipment.db.ShipmentPK;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.log.Log;
import glog.util.log.LogIds;
import java.util.Enumeration;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class InvoiceShipmentBean
extends InvoiceShipmentBeanDB {
    private static final String SELECT_INVOICE_SHIPMENT_SEQUENCE = "SELECT NVL( MAX ( SEQUENCE_NO ) , 0 ) + 1 FROM INVOICE_SHIPMENT WHERE INVOICE_GID = ?";
    private static final String[] types = new String[]{"ORDER RELEASE", "ORDER BASE", "JOB"};
    private static final String SELECT_MATCHED_SHIPMENTS = "select shipment_gid from invoice_shipment where invoice_gid = ?";

    public Enumeration ejbFindByInvoiceGid(String strInvoiceGid) throws FinderException {
        return this.ejbFind("invoice_gid like ?", new Object[]{strInvoiceGid});
    }

    public Enumeration ejbFindByShipmentGid(String shipmentGid) throws FinderException {
        return this.ejbFind("shipment_gid = ?", new Object[]{shipmentGid});
    }

    public Enumeration ejbFindByInvoiceShipmentGid(String invoiceGid, String shipmentGid) throws FinderException {
        return this.ejbFind("invoice_gid = ? and shipment_gid = ?", new Object[]{invoiceGid, shipmentGid});
    }

    public Enumeration ejbFindBySShipUnitPK(SShipUnitPK sShipUnitPK) throws FinderException {
        return this.ejbFind("S_SHIP_UNIT_GID = ?", new Object[]{sShipUnitPK.sShipUnitGid});
    }

    public Enumeration ejbFindBySShipUnitLinePK(SShipUnitLinePK sShipUnitLinePK) throws FinderException {
        return this.ejbFind("S_SHIP_UNIT_GID = ? and S_SHIP_UNIT_LINE_NO = ?", new Object[]{sShipUnitLinePK.sShipUnitGid, sShipUnitLinePK.sShipUnitLineNo});
    }

    @Override
    protected void postCreate() throws GLException {
        this.registerShipmentInvoiceCostFieldsSync();
        super.postCreate();
    }

    @Override
    protected boolean preRemove() throws GLException {
        this.registerShipmentInvoiceCostFieldsSync();
        return super.preRemove();
    }

    private void registerShipmentInvoiceCostFieldsSync() throws GLException {
        SqlQuery q = null;
        T2SharedConnection conn = this.getConnection();
        try {
            q = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_MATCHED_SHIPMENTS), new Object[]{this.getInvoiceGid()}, "InvoiceShipmentBean.registerShipmentInvoiceCostFieldsSync");
            conn.open();
            q.open(conn.get());
            while (q.next()) {
                String shipmentGid = q.getString("shipment_gid");
                if (shipmentGid == null) continue;
                Log.logID(LogIds.SETTLEMENT, "publish ShipmentMatchedInvoiceCostsSync for shipment {0}", shipmentGid);
                ShipmentMatchedInvoiceCostsSync.register(new ShipmentPK(shipmentGid));
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
        finally {
            if (q != null) {
                q.close();
            }
            if (conn != null) {
                conn.close();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private Long findNextInvoiceShipmentNo(String invoiceGid) throws GLException {
        SqlQuery sequenceQuery = new SqlQuery((QueryFilter)NoQueryFilter.get(SELECT_INVOICE_SHIPMENT_SEQUENCE), new Object[]{invoiceGid});
        T2SharedConnection conn = this.getConnection();
        try {
            conn.open();
            sequenceQuery.open(conn.get());
            if (sequenceQuery.next()) {
                Long l = new Long(sequenceQuery.getString(1));
                return l;
            }
        }
        finally {
            sequenceQuery.close();
            conn.close();
        }
        return null;
    }

    @Override
    public InvoiceShipmentPK ejbCreate(InvoiceShipmentData invoiceShipmentData) throws CreateException {
        try {
            Long nextSequence;
            if (invoiceShipmentData.sequenceNo == null && null != (nextSequence = this.findNextInvoiceShipmentNo(invoiceShipmentData.invoiceGid))) {
                invoiceShipmentData.sequenceNo = nextSequence;
            }
            return super.ejbCreate(invoiceShipmentData);
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }
}
