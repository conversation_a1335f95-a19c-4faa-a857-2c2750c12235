/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.LineApproveToleranceDetailHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface LineApproveToleranceDetailHome
extends LineApproveToleranceDetailHomeDB {
    public Enumeration findByLineApproveToleranceGid(String var1) throws FinderException, RemoteException;
}
