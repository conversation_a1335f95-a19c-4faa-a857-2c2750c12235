/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceRefnum;
import glog.ejb.invoice.InvoiceRefnumBean;
import glog.ejb.invoice.InvoiceRefnumHome;
import glog.ejb.invoice.InvoiceRefnumStub;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.InvoiceRefnumData;
import glog.ejb.invoice.db.InvoiceRefnumPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class InvoiceRefnumHomeStub
implements InvoiceRefnumHome {
    static LRUCache cache = LocalEntityCache.register("InvoiceRefnum");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoicePK(InvoicePK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findByInvoicePK", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindByInvoicePK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoicePK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceRefnumGid(String p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findByInvoiceRefnumGid", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindByInvoiceRefnumGid(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceRefnumGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoiceRefnumGidAndQualifierGid(String p1, String p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findByInvoiceRefnumGidAndQualifierGid", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindByInvoiceRefnumGidAndQualifierGid(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoiceRefnumGidAndQualifierGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public InvoiceRefnum create(InvoiceRefnumData p1) throws CreateException, RemoteException {
        InvoiceRefnumStub invoiceRefnumStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "create", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            invoiceRefnumStub = new InvoiceRefnumStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return invoiceRefnumStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public InvoiceRefnum findByPrimaryKey(InvoiceRefnumPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceRefnumBean bean;
                ee.enterMethod("InvoiceRefnumHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (InvoiceRefnumBean)cache.get(p1)) != null) {
                    InvoiceRefnumStub invoiceRefnumStub = new InvoiceRefnumStub((EJBHome)this, bean);
                    return invoiceRefnumStub;
                }
                bean = new InvoiceRefnumBean();
                InvoiceRefnumPK pk = bean.ejbFindByPrimaryKey(p1);
                InvoiceRefnumStub invoiceRefnumStub = new InvoiceRefnumStub((EJBHome)this, pk);
                return invoiceRefnumStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findAll", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findAll", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findByPrimaryKeys", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "findByPrimaryKeys", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceRefnumStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(InvoiceRefnumPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceRefnumBean bean;
                ee.enterMethod("InvoiceRefnumHome", "findInCache", true);
                if (cache != null && (bean = (InvoiceRefnumBean)cache.get(p1)) != null) {
                    Vector<InvoiceRefnumPK> v = new Vector<InvoiceRefnumPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), InvoiceRefnumStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(InvoiceRefnumPK p1, InvoiceRefnumData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "onCreate", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(InvoiceRefnumPK p1, InvoiceRefnumData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "onUpdate", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(InvoiceRefnumPK p1, InvoiceRefnumData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "onRemove", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "listBeansInCache", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "listLocks", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "unlock", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "getDataNoLock", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "getLockData", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "getMaxCacheSize", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceRefnumHome", "updateMaxCacheSize", true);
            InvoiceRefnumBean bean = new InvoiceRefnumBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new InvoiceRefnumStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
