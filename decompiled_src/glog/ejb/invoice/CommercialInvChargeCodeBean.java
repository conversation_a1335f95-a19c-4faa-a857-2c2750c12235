/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.CommercialInvChargeCodeBeanDB;
import glog.ejb.invoice.db.CommercialInvChargeCodePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class CommercialInvChargeCodeBean
extends CommercialInvChargeCodeBeanDB {
    public Enumeration ejbFindByCommercialInvChargeCodePK(CommercialInvChargeCodePK commercialInvChargeCodePK) throws FinderException, RemoteException {
        return this.ejbFind("commercial_inv_charge_code_gid = ?", new Object[]{commercialInvChargeCodePK.toString()});
    }
}
