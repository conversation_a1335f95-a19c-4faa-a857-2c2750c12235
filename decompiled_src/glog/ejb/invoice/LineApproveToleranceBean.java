/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice;

import glog.business.invoice.LineApprovalToleranceEngine;
import glog.ejb.invoice.db.LineApproveToleranceBeanDB;
import glog.ejb.invoice.db.LineApproveTolerancePK;
import glog.util.exception.GLException;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.PkAccessor;

public class LineApproveToleranceBean
extends LineApproveToleranceBeanDB {
    @Override
    protected void onUpdate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onUpdate(pk, data);
        LineApprovalToleranceEngine.clearRuleCache(this.getRuleGid(pk));
    }

    @Override
    protected void onRemove(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onRemove(pk, data);
        LineApprovalToleranceEngine.clearRuleCache(this.getRuleGid(pk));
    }

    private String getRuleGid(PkAccessor pk) {
        LineApproveTolerancePK rulePK = (LineApproveTolerancePK)pk.get(this);
        return (String)rulePK.lineApproveToleranceGid;
    }

    @Override
    protected boolean preRemove() throws GLException {
        if (this.markedForCacheRemove) {
            return true;
        }
        this.removeAllChildren();
        return super.preRemove();
    }

    private void removeAllChildren() throws GLException {
        LineApproveTolerancePK lineApproveTolerancePK = (LineApproveTolerancePK)this.getPK();
        LineApproveToleranceBean.remove("ejb.LineApproveToleranceDetail", "findByLineApproveToleranceGid", new Class[]{String.class}, new Object[]{lineApproveTolerancePK.toString()});
        LineApproveToleranceBean.remove("ejb.LineAppTolShipmentRefnum", "findByLineApproveToleranceGid", new Class[]{String.class}, new Object[]{lineApproveTolerancePK.toString()});
        LineApproveToleranceBean.remove("ejb.LineAppTolInvoiceRefnum", "findByLineApproveToleranceGid", new Class[]{String.class}, new Object[]{lineApproveTolerancePK.toString()});
    }
}
