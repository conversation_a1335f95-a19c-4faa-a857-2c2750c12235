/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceHomeDB;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.job.db.JobPK;
import glog.ejb.order.db.OrderReleasePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceHome
extends InvoiceHomeDB {
    public Enumeration findInvoiceByOrderRelease(OrderReleasePK var1) throws FinderException, RemoteException;

    public Enumeration findBillsByOrderRelease(OrderReleasePK var1) throws FinderException, RemoteException;

    public Enumeration findInvoiceByParentInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findBillsByJobPK(JobPK var1) throws FinderException, RemoteException;
}
