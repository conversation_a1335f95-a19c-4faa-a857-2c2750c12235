/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.CommercialInvoiceCharge;
import glog.ejb.invoice.CommercialInvoiceChargeBean;
import glog.ejb.invoice.CommercialInvoiceChargeHome;
import glog.ejb.invoice.CommercialInvoiceChargeStub;
import glog.ejb.invoice.db.CommercialInvoiceChargeData;
import glog.ejb.invoice.db.CommercialInvoiceChargePK;
import glog.ejb.invoice.db.CommercialInvoicePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class CommercialInvoiceChargeHomeStub
implements CommercialInvoiceChargeHome {
    static LRUCache cache = LocalEntityCache.register("CommercialInvoiceCharge");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByCommercialInvoicePK(CommercialInvoicePK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "findByCommercialInvoicePK", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Enumeration e = bean.ejbFindByCommercialInvoicePK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, CommercialInvoiceChargeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByCommercialInvoicePK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public CommercialInvoiceCharge create(CommercialInvoiceChargeData p1) throws CreateException, RemoteException {
        CommercialInvoiceChargeStub commercialInvoiceChargeStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "create", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            commercialInvoiceChargeStub = new CommercialInvoiceChargeStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return commercialInvoiceChargeStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public CommercialInvoiceCharge findByPrimaryKey(CommercialInvoiceChargePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                CommercialInvoiceChargeBean bean;
                ee.enterMethod("CommercialInvoiceChargeHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (CommercialInvoiceChargeBean)cache.get(p1)) != null) {
                    CommercialInvoiceChargeStub commercialInvoiceChargeStub = new CommercialInvoiceChargeStub((EJBHome)this, bean);
                    return commercialInvoiceChargeStub;
                }
                bean = new CommercialInvoiceChargeBean();
                CommercialInvoiceChargePK pk = bean.ejbFindByPrimaryKey(p1);
                CommercialInvoiceChargeStub commercialInvoiceChargeStub = new CommercialInvoiceChargeStub((EJBHome)this, pk);
                return commercialInvoiceChargeStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "findAll", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, CommercialInvoiceChargeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "findAll", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, CommercialInvoiceChargeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "findByPrimaryKeys", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, CommercialInvoiceChargeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "findByPrimaryKeys", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, CommercialInvoiceChargeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(CommercialInvoiceChargePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                CommercialInvoiceChargeBean bean;
                ee.enterMethod("CommercialInvoiceChargeHome", "findInCache", true);
                if (cache != null && (bean = (CommercialInvoiceChargeBean)cache.get(p1)) != null) {
                    Vector<CommercialInvoiceChargePK> v = new Vector<CommercialInvoiceChargePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), CommercialInvoiceChargeStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(CommercialInvoiceChargePK p1, CommercialInvoiceChargeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "onCreate", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(CommercialInvoiceChargePK p1, CommercialInvoiceChargeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "onUpdate", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(CommercialInvoiceChargePK p1, CommercialInvoiceChargeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "onRemove", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "listBeansInCache", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "listLocks", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "unlock", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "getDataNoLock", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "getLockData", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "getMaxCacheSize", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("CommercialInvoiceChargeHome", "updateMaxCacheSize", true);
            CommercialInvoiceChargeBean bean = new CommercialInvoiceChargeBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new CommercialInvoiceChargeStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
