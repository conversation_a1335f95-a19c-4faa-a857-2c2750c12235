/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceEquipmentDetail;
import glog.ejb.invoice.InvoiceEquipmentDetailBean;
import glog.ejb.invoice.InvoiceEquipmentDetailHome;
import glog.ejb.invoice.InvoiceEquipmentDetailStub;
import glog.ejb.invoice.db.InvoiceEquipmentDetailData;
import glog.ejb.invoice.db.InvoiceEquipmentDetailPK;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.shipment.db.SEquipmentPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class InvoiceEquipmentDetailHomeStub
implements InvoiceEquipmentDetailHome {
    static LRUCache cache = LocalEntityCache.register("InvoiceEquipmentDetail");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByInvoicePK(InvoicePK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findByInvoicePK", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindByInvoicePK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByInvoicePK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findBySEquipmentPK(SEquipmentPK p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findBySEquipmentPK", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindBySEquipmentPK(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findBySEquipmentPK", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public InvoiceEquipmentDetail create(InvoiceEquipmentDetailData p1) throws CreateException, RemoteException {
        InvoiceEquipmentDetailStub invoiceEquipmentDetailStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "create", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            invoiceEquipmentDetailStub = new InvoiceEquipmentDetailStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return invoiceEquipmentDetailStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public InvoiceEquipmentDetail findByPrimaryKey(InvoiceEquipmentDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceEquipmentDetailBean bean;
                ee.enterMethod("InvoiceEquipmentDetailHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (InvoiceEquipmentDetailBean)cache.get(p1)) != null) {
                    InvoiceEquipmentDetailStub invoiceEquipmentDetailStub = new InvoiceEquipmentDetailStub((EJBHome)this, bean);
                    return invoiceEquipmentDetailStub;
                }
                bean = new InvoiceEquipmentDetailBean();
                InvoiceEquipmentDetailPK pk = bean.ejbFindByPrimaryKey(p1);
                InvoiceEquipmentDetailStub invoiceEquipmentDetailStub = new InvoiceEquipmentDetailStub((EJBHome)this, pk);
                return invoiceEquipmentDetailStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findAll", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findAll", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findByPrimaryKeys", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "findByPrimaryKeys", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, InvoiceEquipmentDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(InvoiceEquipmentDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                InvoiceEquipmentDetailBean bean;
                ee.enterMethod("InvoiceEquipmentDetailHome", "findInCache", true);
                if (cache != null && (bean = (InvoiceEquipmentDetailBean)cache.get(p1)) != null) {
                    Vector<InvoiceEquipmentDetailPK> v = new Vector<InvoiceEquipmentDetailPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), InvoiceEquipmentDetailStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(InvoiceEquipmentDetailPK p1, InvoiceEquipmentDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "onCreate", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(InvoiceEquipmentDetailPK p1, InvoiceEquipmentDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "onUpdate", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(InvoiceEquipmentDetailPK p1, InvoiceEquipmentDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "onRemove", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "listBeansInCache", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "listLocks", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "unlock", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "getDataNoLock", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "getLockData", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "getMaxCacheSize", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("InvoiceEquipmentDetailHome", "updateMaxCacheSize", true);
            InvoiceEquipmentDetailBean bean = new InvoiceEquipmentDetailBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new InvoiceEquipmentDetailStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
