/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.GlLookupKeyDBeanDB;
import glog.util.OTMMessageFormat;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.log.Log;
import glog.util.log.LogIds;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class GlLookupKeyDBean
extends GlLookupKeyDBeanDB {
    private static final String SQL_COUNT_GL_CODES_FOR_KEY = "select count(*) from general_ledger_code where (buy_order_gl_lookup_key_gid = {0} or \t\tsell_order_gl_lookup_key_gid = {0} or \t\tbuy_ship_gl_lookup_key_gid = {0} or \t\tsell_ship_gl_lookup_key_gid = {0}) and is_active = {1}";
    private static final String SQ = "'";
    private static final String Y = "Y";

    public Enumeration ejbFindByGlLookupKeyGid(String keyGid) throws FinderException {
        return this.ejbFind("gl_lookup_key_gid = ?", new Object[]{keyGid});
    }

    @Override
    protected boolean preStore() throws GLException {
        if (!super.preStore()) {
            return false;
        }
        return this.prePersist();
    }

    @Override
    protected boolean preCreate() throws GLException {
        if (!super.preCreate()) {
            return false;
        }
        return this.prePersist();
    }

    @Override
    protected boolean preRemove() throws GLException {
        if (this.markedForCacheRemove) {
            return true;
        }
        if (!super.preRemove()) {
            return false;
        }
        return this.prePersist();
    }

    private boolean prePersist() throws GLException {
        String sqlString = OTMMessageFormat.format(SQL_COUNT_GL_CODES_FOR_KEY, SQ + this.getGlLookupKeyGid() + SQ, "'Y'");
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlString), null, "GlLookupKeyBean::onUpdate");
        T2SharedConnection connection = this.getConnection();
        try {
            connection.open();
            sql.open(connection.get());
            if (sql.next() && sql.getInt(1) != 0) {
                if (Log.idOn[LogIds.SETTLEMENT.index]) {
                    Log.logID(LogIds.SETTLEMENT, Log.ERROR, "General ledger key: {0} cannot be modified because it has associated general ledger codes that are marked active. Any general ledger codes that use this key must be marked not active and eventually should be deleted before modifying this general ledger key.", this.getGlLookupKeyGid());
                }
                throw GLException.factory((Cause)new GLException.CausedBy("cause.GlLookupKeyDBean.0001", null, new Object[][]{{"glLookupKeyGid", this.getGlLookupKeyGid()}}), null);
            }
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
        finally {
            sql.close();
            connection.close();
        }
        return true;
    }
}
