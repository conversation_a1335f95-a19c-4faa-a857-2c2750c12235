/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.invoice.db.InvoiceRefnumBeanDB;
import glog.ejb.invoice.db.InvoiceRefnumPK;
import glog.util.exception.Cause;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.remote.GLNoMoreRecords;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.FinderException;

public class InvoiceRefnumBean
extends InvoiceRefnumBeanDB {
    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{invoicePK.getDbValue(0)});
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Enumeration ejbFindByInvoiceRefnumGid(String invoiceGid) throws FinderException {
        Enumeration enumeration;
        String sqlText = "select invoice_gid,invoice_refnum_qual_gid, invoice_refnum_value from invoice_refnum where invoice_gid = ?";
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlText), new Object[]{invoiceGid}, "InvoiceRefnumBean::ejbFindByObLineRefnumGid");
        T2SharedConnection conn = this.getConnection();
        Vector<InvoiceRefnumPK> v = new Vector<InvoiceRefnumPK>();
        try {
            conn.open();
            sql.open(conn.get());
            while (sql.next()) {
                v.add(new InvoiceRefnumPK(1, sql.getObject(1), sql.getObject(2), sql.getObject(3)));
            }
            if (v.size() == 0) {
                throw new GLNoMoreRecords(this);
            }
            enumeration = v.elements();
        }
        catch (Throwable throwable) {
            try {
                sql.close();
                conn.close();
                throw throwable;
            }
            catch (Throwable t) {
                throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.InvoiceRefnumBean.0001", null, new Object[][]{{"invoiceGid", invoiceGid}}), t));
            }
        }
        sql.close();
        conn.close();
        return enumeration;
    }

    public Enumeration ejbFindByInvoiceRefnumGidAndQualifierGid(String invoiceGid, String refnumQualGid) throws FinderException {
        return this.ejbFind("invoice_gid = ? and invoice_refnum_qual_gid = ? ", new Object[]{invoiceGid, refnumQualGid});
    }
}
