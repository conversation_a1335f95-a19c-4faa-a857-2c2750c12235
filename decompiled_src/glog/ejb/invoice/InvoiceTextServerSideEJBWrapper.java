/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBContext
 */
package glog.ejb.invoice;

import glog.ejb.invoice.InvoiceTextBean;
import glog.ejb.invoice.db.InvoiceTextData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBContext;

public class InvoiceTextServerSideEJBWrapper
extends InvoiceTextBean {
    @Override
    public InvoiceTextData getData() throws GLException {
        try {
            InvoiceTextData invoiceTextData = super.getData();
            return invoiceTextData;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setData(InvoiceTextData p1) throws GLException {
        try {
            super.setData(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setLinks(Vector p1, Vector p2) throws GLException {
        try {
            super.setLinks(p1, p2);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Pk getPK() {
        Pk pk = super.getPK();
        return pk;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() {
        super.markForCacheRemove();
    }

    @Override
    public void reload() throws GLException {
        try {
            super.reload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void unload() throws GLException {
        try {
            super.unload();
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public Map verify() throws GLException {
        try {
            Map map = super.verify();
            return map;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getInvoiceGid() throws GLException {
        try {
            String string = super.getInvoiceGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setInvoiceGid(String p1) throws GLException {
        try {
            super.setInvoiceGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getTextTemplateGid() throws GLException {
        try {
            String string = super.getTextTemplateGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setTextTemplateGid(String p1) throws GLException {
        try {
            super.setTextTemplateGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDocumentDefGid() throws GLException {
        try {
            String string = super.getDocumentDefGid();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDocumentDefGid(String p1) throws GLException {
        try {
            super.setDocumentDefGid(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getTextOverride() throws GLException {
        try {
            String string = super.getTextOverride();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setTextOverride(String p1) throws GLException {
        try {
            super.setTextOverride(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public String getDomainName() throws GLException {
        try {
            String string = super.getDomainName();
            return string;
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable throwable) {
                // empty catch block
            }
            throw exc2;
        }
    }

    @Override
    public void setDomainName(String p1) throws GLException {
        try {
            super.setDomainName(p1);
        }
        catch (GLException exc2) {
            EJBContext context = (EJBContext)this.getContext();
            try {
                if (context != null) {
                    context.setRollbackOnly();
                }
            }
            catch (Throwable t) {
                // empty catch block
            }
            throw exc2;
        }
    }
}
