/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.CommercialInvoiceChargeHomeDB;
import glog.ejb.invoice.db.CommercialInvoicePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface CommercialInvoiceChargeHome
extends CommercialInvoiceChargeHomeDB {
    public Enumeration findByCommercialInvoicePK(CommercialInvoicePK var1) throws FinderException, RemoteException;
}
