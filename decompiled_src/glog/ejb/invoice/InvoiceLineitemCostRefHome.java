/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLineitemCostRefHomeDB;
import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoicePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceLineitemCostRefHome
extends InvoiceLineitemCostRefHomeDB {
    public Enumeration findByInvoiceLineitem(String var1, String var2) throws FinderException, RemoteException;

    public Enumeration findByInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findByInvoiceGid(String var1) throws FinderException, RemoteException;

    public Enumeration findByInvoiceLineitemPK(InvoiceLineitemPK var1) throws FinderException, RemoteException;
}
