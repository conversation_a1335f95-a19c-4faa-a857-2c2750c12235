/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GeneralLedgerCode;
import glog.ejb.invoice.GeneralLedgerCodeBean;
import glog.ejb.invoice.GeneralLedgerCodeHome;
import glog.ejb.invoice.GeneralLedgerCodeStub;
import glog.ejb.invoice.db.GeneralLedgerCodeData;
import glog.ejb.invoice.db.GeneralLedgerCodePK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class GeneralLedgerCodeHomeStub
implements GeneralLedgerCodeHome {
    static LRUCache cache = LocalEntityCache.register("GeneralLedgerCode");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public GeneralLedgerCode create(GeneralLedgerCodeData p1) throws CreateException, RemoteException {
        GeneralLedgerCodeStub generalLedgerCodeStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "create", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            generalLedgerCodeStub = new GeneralLedgerCodeStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return generalLedgerCodeStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public GeneralLedgerCode findByPrimaryKey(GeneralLedgerCodePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                GeneralLedgerCodeBean bean;
                ee.enterMethod("GeneralLedgerCodeHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (GeneralLedgerCodeBean)cache.get(p1)) != null) {
                    GeneralLedgerCodeStub generalLedgerCodeStub = new GeneralLedgerCodeStub((EJBHome)this, bean);
                    return generalLedgerCodeStub;
                }
                bean = new GeneralLedgerCodeBean();
                GeneralLedgerCodePK pk = bean.ejbFindByPrimaryKey(p1);
                GeneralLedgerCodeStub generalLedgerCodeStub = new GeneralLedgerCodeStub((EJBHome)this, pk);
                return generalLedgerCodeStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "findAll", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, GeneralLedgerCodeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "findAll", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, GeneralLedgerCodeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "findByPrimaryKeys", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, GeneralLedgerCodeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "findByPrimaryKeys", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, GeneralLedgerCodeStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(GeneralLedgerCodePK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                GeneralLedgerCodeBean bean;
                ee.enterMethod("GeneralLedgerCodeHome", "findInCache", true);
                if (cache != null && (bean = (GeneralLedgerCodeBean)cache.get(p1)) != null) {
                    Vector<GeneralLedgerCodePK> v = new Vector<GeneralLedgerCodePK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), GeneralLedgerCodeStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(GeneralLedgerCodePK p1, GeneralLedgerCodeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "onCreate", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(GeneralLedgerCodePK p1, GeneralLedgerCodeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "onUpdate", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(GeneralLedgerCodePK p1, GeneralLedgerCodeData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "onRemove", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "listBeansInCache", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "listLocks", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "unlock", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "getDataNoLock", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "getLockData", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "getMaxCacheSize", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("GeneralLedgerCodeHome", "updateMaxCacheSize", true);
            GeneralLedgerCodeBean bean = new GeneralLedgerCodeBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new GeneralLedgerCodeStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
