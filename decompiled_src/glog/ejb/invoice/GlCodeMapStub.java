/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.EntityContext
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GlCodeMap;
import glog.ejb.invoice.GlCodeMapBean;
import glog.ejb.invoice.GlCodeMapHomeStub;
import glog.ejb.invoice.db.GlCodeMapData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityContext;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.EntityContext;
import javax.ejb.Handle;
import javax.ejb.RemoveException;

public class GlCodeMapStub
implements GlCodeMap {
    private GlCodeMapBean entityBean;
    private Pk pk;
    private EntityContext context;

    public GlCodeMapStub(EJBHome ejbHome, GlCodeMapBean bean) {
        this.entityBean = bean;
        this.pk = bean.getPK();
        this.context = new LocalEntityContext(ejbHome, this, this.pk);
    }

    public GlCodeMapStub(EJBHome ejbHome, Pk pk) {
        this.pk = pk;
        this.context = new LocalEntityContext(ejbHome, this, pk);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public GlCodeMapData getData() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "getData", false);
            this.entityBean = this.loadOnDemand();
            GlCodeMapData glCodeMapData = this.entityBean.getData();
            return glCodeMapData;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setData(GlCodeMapData p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setData", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setData(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLinks(Vector p1, Vector p2) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setLinks", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLinks(p1, p2);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Pk getPK() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlCodeMap", "getPK", false);
                this.entityBean = this.loadOnDemand();
                Pk pk = this.entityBean.getPK();
                return pk;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("getPK", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlCodeMap", "markForCacheRemove", false);
                this.entityBean = this.loadOnDemand();
                this.entityBean.markForCacheRemove();
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("markForCacheRemove", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void reload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "reload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.reload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "unload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.unload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Map verify() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "verify", false);
            this.entityBean = this.loadOnDemand();
            Map map = this.entityBean.verify();
            return map;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getKeyValue() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "getKeyValue", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getKeyValue();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setKeyValue(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setKeyValue", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setKeyValue(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getDomainName() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "getDomainName", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getDomainName();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setDomainName(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setDomainName", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setDomainName(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlLookupKeyGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "getGlLookupKeyGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlLookupKeyGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlLookupKeyGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setGlLookupKeyGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlLookupKeyGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGeneralLedgerGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "getGeneralLedgerGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGeneralLedgerGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGeneralLedgerGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlCodeMap", "setGeneralLedgerGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGeneralLedgerGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    public void remove() throws RemoteException, RemoveException {
        try {
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlCodeMap", "remove", false);
                this.entityBean = this.loadOnDemand();
                this.remove(this.entityBean);
                this.entityBean = null;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                throw ex;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("remove", glex);
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() throws RemoteException {
        return this.getPK();
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    private void store(GlCodeMapBean entityBean) throws RemoteException {
        if (GlCodeMapHomeStub.cache != null) {
            GlCodeMapHomeStub.cache.put(this.pk, entityBean);
        }
        entityBean.ejbStore();
    }

    private void remove(GlCodeMapBean entityBean) throws RemoveException {
        if (GlCodeMapHomeStub.cache != null) {
            GlCodeMapHomeStub.cache.remove(this.pk);
        }
        entityBean.ejbRemove();
    }

    private GlCodeMapBean loadOnDemand() throws RemoteException {
        if (this.entityBean == null && (GlCodeMapHomeStub.cache == null || (this.entityBean = (GlCodeMapBean)GlCodeMapHomeStub.cache.get(this.pk)) == null)) {
            this.entityBean = new GlCodeMapBean();
            this.entityBean.setPK(this.pk);
            this.entityBean.setEntityContext(this.context);
            this.entityBean.ejbLoad();
            if (GlCodeMapHomeStub.cache != null) {
                GlCodeMapHomeStub.cache.put(this.pk, this.entityBean);
            }
        }
        return this.entityBean;
    }
}
