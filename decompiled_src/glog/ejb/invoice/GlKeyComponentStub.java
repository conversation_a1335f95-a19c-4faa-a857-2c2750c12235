/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.EntityContext
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.GlKeyComponent;
import glog.ejb.invoice.GlKeyComponentBean;
import glog.ejb.invoice.GlKeyComponentHomeStub;
import glog.ejb.invoice.db.GlKeyComponentData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityContext;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.EntityContext;
import javax.ejb.Handle;
import javax.ejb.RemoveException;

public class GlKeyComponentStub
implements GlKeyComponent {
    private GlKeyComponentBean entityBean;
    private Pk pk;
    private EntityContext context;

    public GlKeyComponentStub(EJBHome ejbHome, GlKeyComponentBean bean) {
        this.entityBean = bean;
        this.pk = bean.getPK();
        this.context = new LocalEntityContext(ejbHome, this, this.pk);
    }

    public GlKeyComponentStub(EJBHome ejbHome, Pk pk) {
        this.pk = pk;
        this.context = new LocalEntityContext(ejbHome, this, pk);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public GlKeyComponentData getData() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "getData", false);
            this.entityBean = this.loadOnDemand();
            GlKeyComponentData glKeyComponentData = this.entityBean.getData();
            return glKeyComponentData;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setData(GlKeyComponentData p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setData", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setData(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setLinks(Vector p1, Vector p2) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setLinks", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setLinks(p1, p2);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Pk getPK() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponent", "getPK", false);
                this.entityBean = this.loadOnDemand();
                Pk pk = this.entityBean.getPK();
                return pk;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("getPK", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void markForCacheRemove() throws RemoteException {
        try {
            boolean rollback = false;
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponent", "markForCacheRemove", false);
                this.entityBean = this.loadOnDemand();
                this.entityBean.markForCacheRemove();
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                rollback = true;
                throw ex;
            }
            finally {
                try {
                    if (!rollback && this.entityBean.isModified()) {
                        this.store(this.entityBean);
                    }
                }
                finally {
                    ee.exitMethod();
                }
            }
        }
        catch (GLException glex) {
            throw new RemoteException("markForCacheRemove", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void reload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "reload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.reload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unload() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "unload", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.unload();
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Map verify() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "verify", false);
            this.entityBean = this.loadOnDemand();
            Map map = this.entityBean.verify();
            return map;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlKeyComponentGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "getGlKeyComponentGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlKeyComponentGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlKeyComponentGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setGlKeyComponentGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlKeyComponentGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlKeyComponentXid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "getGlKeyComponentXid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlKeyComponentXid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlKeyComponentXid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setGlKeyComponentXid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlKeyComponentXid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getGlKeyComponentTypeGid() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "getGlKeyComponentTypeGid", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getGlKeyComponentTypeGid();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setGlKeyComponentTypeGid(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setGlKeyComponentTypeGid", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setGlKeyComponentTypeGid(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public String getDomainName() throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "getDomainName", false);
            this.entityBean = this.loadOnDemand();
            String string = this.entityBean.getDomainName();
            return string;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void setDomainName(String p1) throws RemoteException, GLException {
        boolean rollback = false;
        EntityEnterExit ee = new EntityEnterExit(this.pk);
        try {
            ee.enterMethod("GlKeyComponent", "setDomainName", false);
            this.entityBean = this.loadOnDemand();
            this.entityBean.setDomainName(p1);
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            rollback = true;
            throw ex;
        }
        finally {
            try {
                if (!rollback && this.entityBean.isModified()) {
                    this.store(this.entityBean);
                }
            }
            finally {
                ee.exitMethod();
            }
        }
    }

    public void remove() throws RemoteException, RemoveException {
        try {
            EntityEnterExit ee = new EntityEnterExit(this.pk);
            try {
                ee.enterMethod("GlKeyComponent", "remove", false);
                this.entityBean = this.loadOnDemand();
                this.remove(this.entityBean);
                this.entityBean = null;
            }
            catch (GLException ex) {
                this.context.setRollbackOnly();
                throw ex;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("remove", glex);
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() throws RemoteException {
        return this.getPK();
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    private void store(GlKeyComponentBean entityBean) throws RemoteException {
        if (GlKeyComponentHomeStub.cache != null) {
            GlKeyComponentHomeStub.cache.put(this.pk, entityBean);
        }
        entityBean.ejbStore();
    }

    private void remove(GlKeyComponentBean entityBean) throws RemoveException {
        if (GlKeyComponentHomeStub.cache != null) {
            GlKeyComponentHomeStub.cache.remove(this.pk);
        }
        entityBean.ejbRemove();
    }

    private GlKeyComponentBean loadOnDemand() throws RemoteException {
        if (this.entityBean == null && (GlKeyComponentHomeStub.cache == null || (this.entityBean = (GlKeyComponentBean)GlKeyComponentHomeStub.cache.get(this.pk)) == null)) {
            this.entityBean = new GlKeyComponentBean();
            this.entityBean.setPK(this.pk);
            this.entityBean.setEntityContext(this.context);
            this.entityBean.ejbLoad();
            if (GlKeyComponentHomeStub.cache != null) {
                GlKeyComponentHomeStub.cache.put(this.pk, this.entityBean);
            }
        }
        return this.entityBean;
    }
}
