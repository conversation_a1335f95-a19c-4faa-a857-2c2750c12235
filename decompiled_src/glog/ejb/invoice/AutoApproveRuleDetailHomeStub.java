/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.AutoApproveRuleDetail;
import glog.ejb.invoice.AutoApproveRuleDetailBean;
import glog.ejb.invoice.AutoApproveRuleDetailHome;
import glog.ejb.invoice.AutoApproveRuleDetailStub;
import glog.ejb.invoice.db.AutoApproveRuleDetailData;
import glog.ejb.invoice.db.AutoApproveRuleDetailPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class AutoApproveRuleDetailHomeStub
implements AutoApproveRuleDetailHome {
    static LRUCache cache = LocalEntityCache.register("AutoApproveRuleDetail");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByAutoApproveRuleGid(String p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "findByAutoApproveRuleGid", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Enumeration e = bean.ejbFindByAutoApproveRuleGid(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByAutoApproveRuleGid", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public AutoApproveRuleDetail create(AutoApproveRuleDetailData p1) throws CreateException, RemoteException {
        AutoApproveRuleDetailStub autoApproveRuleDetailStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "create", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            autoApproveRuleDetailStub = new AutoApproveRuleDetailStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return autoApproveRuleDetailStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public AutoApproveRuleDetail findByPrimaryKey(AutoApproveRuleDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleDetailBean bean;
                ee.enterMethod("AutoApproveRuleDetailHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (AutoApproveRuleDetailBean)cache.get(p1)) != null) {
                    AutoApproveRuleDetailStub autoApproveRuleDetailStub = new AutoApproveRuleDetailStub((EJBHome)this, bean);
                    return autoApproveRuleDetailStub;
                }
                bean = new AutoApproveRuleDetailBean();
                AutoApproveRuleDetailPK pk = bean.ejbFindByPrimaryKey(p1);
                AutoApproveRuleDetailStub autoApproveRuleDetailStub = new AutoApproveRuleDetailStub((EJBHome)this, pk);
                return autoApproveRuleDetailStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "findAll", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "findAll", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "findByPrimaryKeys", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "findByPrimaryKeys", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, AutoApproveRuleDetailStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(AutoApproveRuleDetailPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                AutoApproveRuleDetailBean bean;
                ee.enterMethod("AutoApproveRuleDetailHome", "findInCache", true);
                if (cache != null && (bean = (AutoApproveRuleDetailBean)cache.get(p1)) != null) {
                    Vector<AutoApproveRuleDetailPK> v = new Vector<AutoApproveRuleDetailPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), AutoApproveRuleDetailStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(AutoApproveRuleDetailPK p1, AutoApproveRuleDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "onCreate", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(AutoApproveRuleDetailPK p1, AutoApproveRuleDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "onUpdate", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(AutoApproveRuleDetailPK p1, AutoApproveRuleDetailData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "onRemove", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "listBeansInCache", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "listLocks", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "unlock", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "getDataNoLock", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "getLockData", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "getMaxCacheSize", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("AutoApproveRuleDetailHome", "updateMaxCacheSize", true);
            AutoApproveRuleDetailBean bean = new AutoApproveRuleDetailBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new AutoApproveRuleDetailStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
