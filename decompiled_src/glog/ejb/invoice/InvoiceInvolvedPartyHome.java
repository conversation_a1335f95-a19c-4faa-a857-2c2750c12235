/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceInvolvedPartyHomeDB;
import glog.ejb.invoice.db.InvoicePK;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface InvoiceInvolvedPartyHome
extends InvoiceInvolvedPartyHomeDB {
    public Enumeration findByInvoicePK(InvoicePK var1) throws FinderException, RemoteException;

    public Enumeration findByInvoicePKAndQualGid(InvoicePK var1, String var2) throws FinderException, RemoteException;

    public Enumeration findByInvoiceGid(String var1) throws FinderException, RemoteException;

    public Enumeration findByInvoiceGidAndQualifierGid(String var1, String var2) throws FinderException, RemoteException;
}
