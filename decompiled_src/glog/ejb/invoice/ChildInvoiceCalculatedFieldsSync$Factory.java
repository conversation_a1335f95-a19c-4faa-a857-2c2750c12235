/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice;

import glog.ejb.invoice.ChildInvoiceCalculatedFieldsSync;
import glog.ejb.invoice.InvoiceCalculatedFieldsSync;
import glog.server.workflow.Topic;
import glog.util.CommandLine;
import glog.util.exception.GLException;

public static class ChildInvoiceCalculatedFieldsSync.Factory
extends InvoiceCalculatedFieldsSync.Factory {
    public static ChildInvoiceCalculatedFieldsSync.Factory theFactory = new ChildInvoiceCalculatedFieldsSync.Factory();

    @Override
    public Topic newTopic(CommandLine commandLine) throws GLException {
        return new ChildInvoiceCalculatedFieldsSync(commandLine);
    }

    @Override
    public String getDescriptionKey() {
        return "ChildInvoiceCalculatedFieldsSync";
    }
}
