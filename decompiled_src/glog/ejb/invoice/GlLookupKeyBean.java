/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.invoice;

import glog.business.generalledger.GLLookup;
import glog.ejb.invoice.GlLookupKeyD;
import glog.ejb.invoice.GlLookupKeyDHome;
import glog.ejb.invoice.db.GlLookupKeyBeanDB;
import glog.util.OTMMessageFormat;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.NamingDirectory;
import glog.util.remote.PkAccessor;
import java.util.Enumeration;

public class GlLookupKeyBean
extends GlLookupKeyBeanDB {
    private static final String SQL_COUNT_ACTIVE_KEYS = "select count(*) from gl_lookup_key where domain_name={0} and is_active={1} and perspective={2} and gl_code_assign_type={3}";
    private static final String SQ = "'";
    private static final String Y = "Y";

    @Override
    protected void onCreate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onCreate(pk, data);
        GLLookup.refreshLookupKeyCache();
    }

    @Override
    protected void onUpdate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onUpdate(pk, data);
        GLLookup.refreshLookupKeyCache();
    }

    @Override
    protected void onRemove(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onRemove(pk, data);
        GLLookup.refreshLookupKeyCache();
    }

    @Override
    protected boolean preCreate() throws GLException {
        if (!super.preCreate()) {
            return false;
        }
        if (!this.canPersist()) {
            String l_perspective = "Buy Side";
            String l_type = "Order";
            if (this.getPerspective().equals("S")) {
                l_perspective = "Sell Side";
            }
            if (this.getGlCodeAssignType().equals("S")) {
                l_type = "Shipment";
            }
            throw GLException.factory((Cause)new GLException.CausedBy("cause.business.GlLookupKey.0001", "solution.business.GlLookupKeyBean.0001", new Object[][]{{"perspective", l_perspective}, {"type", l_type}, {"domain", this.getDomainName()}}), null);
        }
        return true;
    }

    @Override
    protected boolean preStore() throws GLException {
        if (!super.preStore()) {
            return false;
        }
        if (!this.canPersist()) {
            String l_perspective = "Buy Side";
            String l_type = "Order";
            if (this.getPerspective().equals("S")) {
                l_perspective = "Sell Side";
            }
            if (this.getGlCodeAssignType().equals("S")) {
                l_type = "Shipment";
            }
            throw GLException.factory((Cause)new GLException.CausedBy("cause.business.GlLookupKey.0001", "solution.business.GlLookupKeyBean.0001", new Object[][]{{"perspective", l_perspective}, {"type", l_type}, {"domain", this.getDomainName()}}), null);
        }
        return true;
    }

    private boolean canPersist() throws GLException {
        if (this.getIsActive().booleanValue()) {
            String sqlString = OTMMessageFormat.format(SQL_COUNT_ACTIVE_KEYS, SQ + this.getDomainName() + SQ, "'Y'", SQ + this.getPerspective() + SQ, SQ + this.getGlCodeAssignType() + SQ);
            SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlString), null, "GlLookupKeyBean::canPersist");
            T2SharedConnection connection = this.getConnection();
            try {
                connection.open();
                sql.open(connection.get());
                if (sql.next() && sql.getInt(1) != 0) {
                    boolean bl = false;
                    return bl;
                }
            }
            catch (Throwable t) {
                throw GLException.ejbFactory(t);
            }
            finally {
                sql.close();
                connection.close();
            }
        }
        return true;
    }

    @Override
    protected boolean preRemove() throws GLException {
        if (this.markedForCacheRemove) {
            return true;
        }
        try {
            super.preRemove();
            GlLookupKeyDHome detailHome = (GlLookupKeyDHome)NamingDirectory.get().lookup("ejb.GlLookupKeyD");
            try {
                Enumeration eNum = detailHome.findByGlLookupKeyGid(this.getGlLookupKeyGid());
                while (eNum.hasMoreElements()) {
                    GlLookupKeyD detail = (GlLookupKeyD)eNum.nextElement();
                    detail.remove();
                }
            }
            catch (FinderNoMoreRecords finderNoMoreRecords) {
                // empty catch block
            }
            return true;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
