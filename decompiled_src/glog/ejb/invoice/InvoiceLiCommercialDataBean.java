/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.InvoiceLiCommercialDataBeanDB;
import glog.ejb.invoice.db.InvoiceLineitemPK;
import glog.ejb.invoice.db.InvoicePK;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class InvoiceLiCommercialDataBean
extends InvoiceLiCommercialDataBeanDB {
    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        return this.ejbFind("invoice_gid = ?", new Object[]{(String)invoicePK.invoiceGid});
    }

    public Enumeration ejbFindByInvoiceLineitemPK(InvoiceLineitemPK invoiceLineitemPK) throws FinderException {
        return this.ejbFind("invoice_gid = ? and lineitem_seq_no = ?", new Object[]{invoiceLineitemPK.invoiceGid, invoiceLineitemPK.lineitemSeqNo});
    }
}
