/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.AutoApproveRuleBeanDB;
import glog.ejb.invoice.db.AutoApproveRulePK;
import glog.ejb.invoice.db.InvoicePK;
import glog.ejb.location.db.ServprovPK;
import glog.util.exception.Cause;
import glog.util.exception.FinderExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.jdbc.noserver.SqlQuery;
import glog.util.remote.GLNoMoreRecords;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.FinderException;

public class AutoApproveRuleBean
extends AutoApproveRuleBeanDB {
    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Enumeration ejbFindByInvoicePK(InvoicePK invoicePK) throws FinderException {
        Enumeration enumeration;
        String sqlText = "SELECT s.auto_approve_rule_gid FROM invoice i, servprov_alias spa, servprov s WHERE i.invoice_gid = ? AND i.servprov_alias_qual_gid = spa.servprov_alias_qual_gid AND i.servprov_alias_value = spa.alias AND spa.servprov_gid = s.servprov_gid";
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlText), new Object[]{invoicePK.toString()}, "AutoApproveRuleBean::ejbFindByInvoicePK");
        T2SharedConnection conn = this.getConnection();
        Vector<AutoApproveRulePK> v = new Vector<AutoApproveRulePK>();
        try {
            conn.open();
            sql.open(conn.get());
            while (sql.next()) {
                v.add(new AutoApproveRulePK(1, sql.getObject(1)));
            }
            if (v.size() == 0) {
                throw new GLNoMoreRecords(this);
            }
            enumeration = v.elements();
        }
        catch (Throwable throwable) {
            try {
                sql.close();
                conn.close();
                throw throwable;
            }
            catch (Throwable t) {
                throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.AutoApproveRuleBean.0001", null, new Object[][]{{"invoicePK", invoicePK}}), t));
            }
        }
        sql.close();
        conn.close();
        return enumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Enumeration ejbFindByServprovPK(ServprovPK servprovPK) throws FinderException {
        Enumeration enumeration;
        String sqlText = "SELECT s.auto_approve_rule_gid FROM servprov s WHERE s.servprov_gid = ?";
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(sqlText), new Object[]{servprovPK.toString()}, "AutoApproveRuleBean::ejbFindByServprovPK");
        T2SharedConnection conn = this.getConnection();
        Vector<AutoApproveRulePK> v = new Vector<AutoApproveRulePK>();
        try {
            conn.open();
            sql.open(conn.get());
            while (sql.next()) {
                v.add(new AutoApproveRulePK(1, sql.getObject(1)));
            }
            if (v.size() == 0) {
                throw new GLNoMoreRecords(this);
            }
            enumeration = v.elements();
        }
        catch (Throwable throwable) {
            try {
                sql.close();
                conn.close();
                throw throwable;
            }
            catch (Throwable t) {
                throw FinderExceptionWrapper.ejbFactory(GLException.factory((Cause)new GLException.CausedBy("cause.AutoApproveRuleBean.0002", null, new Object[][]{{"servprovPK", servprovPK}}), t));
            }
        }
        sql.close();
        conn.close();
        return enumeration;
    }

    @Override
    protected boolean preCreate() throws GLException {
        boolean returnVal = super.preCreate();
        return returnVal;
    }

    @Override
    protected boolean preStore() throws GLException {
        return super.preStore();
    }
}
