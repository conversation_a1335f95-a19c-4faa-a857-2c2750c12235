/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.invoice;

import glog.ejb.invoice.db.LineAppTolInvoiceRefnumHomeDB;
import java.rmi.RemoteException;
import java.util.Enumeration;
import javax.ejb.FinderException;

public interface LineAppTolInvoiceRefnumHome
extends LineAppTolInvoiceRefnumHomeDB {
    public Enumeration findByLineApproveToleranceGid(String var1) throws FinderException, RemoteException;
}
