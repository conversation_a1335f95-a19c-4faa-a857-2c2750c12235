/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.FinderException
 */
package glog.ejb.bulkcm;

import glog.ejb.bulkcm.db.BulkCmBeanDB;
import glog.ejb.bulkplan.db.BulkPlanPK;
import java.util.Enumeration;
import javax.ejb.FinderException;

public class BulkCmBean
extends BulkCmBeanDB {
    public Enumeration ejbFindByBulkPlanPK(BulkPlanPK bulkPlanPK) throws FinderException {
        return this.ejbFind("bulk_plan_gid=?", new Object[]{bulkPlanPK.toString()});
    }
}
