/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.FinderException
 */
package glog.ejb.bulkcm.db;

import glog.ejb.bulkcm.BulkCm;
import glog.ejb.bulkcm.db.BulkCmData;
import glog.ejb.bulkcm.db.BulkCmPK;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.FinderException;

public interface BulkCmHomeDB
extends EJBHome {
    public static final String NAME = "ejb.BulkCm";
    public static final String dataClass = "glog.ejb.bulkcm.db.BulkCmData";
    public static final String primaryKeyClass = "glog.ejb.bulkcm.db.BulkCmPK";

    public BulkCm create(BulkCmData var1) throws CreateException, RemoteException;

    public BulkCm findByPrimaryKey(BulkCmPK var1) throws FinderException, RemoteException;

    public Enumeration findAll() throws FinderException, RemoteException;

    public Enumeration findAll(Object var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1) throws FinderException, RemoteException;

    public Enumeration findByPrimaryKeys(Vector var1, Object var2) throws FinderException, RemoteException;

    public Enumeration findInCache(BulkCmPK var1) throws FinderException, RemoteException;

    public void onCreate(BulkCmPK var1, BulkCmData var2) throws GLException, RemoteException;

    public void onUpdate(BulkCmPK var1, BulkCmData var2) throws GLException, RemoteException;

    public void onRemove(BulkCmPK var1, BulkCmData var2) throws GLException, RemoteException;

    public Collection listBeansInCache() throws GLException, RemoteException;

    public Collection listLocks() throws GLException, RemoteException;

    public void unlock(Pk var1) throws GLException, RemoteException;

    public BeanData getDataNoLock(Pk var1) throws GLException, RemoteException;

    public LockData getLockData(Pk var1) throws GLException, RemoteException;

    public int getMaxCacheSize() throws GLException, RemoteException;

    public void updateMaxCacheSize(int var1) throws GLException, RemoteException;
}
