/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.ejb.bulkcm.db.BulkCmColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class BulkCmPK
extends Pk {
    public Object bulkCmGid;
    public transient Object bulkCmXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public BulkCmPK() {
    }

    public BulkCmPK(String bulkCmGid) {
        this.bulkCmGid = this.notNull(BulkCmColumns.bulkCmGid.convertToDB(bulkCmGid), "bulkCmGid");
    }

    public BulkCmPK(String domainName, String bulkCmXid) {
        this.domainName = domainName;
        this.bulkCmXid = bulkCmXid;
        this.bulkCmGid = BulkCmPK.concatForGid(domainName, bulkCmXid);
    }

    public BulkCmPK(int dummy, Object bulkCmGid) {
        this(dummy, bulkCmGid, null);
    }

    public BulkCmPK(int dummy, Object bulkCmGid, Object transaction) {
        this.bulkCmGid = bulkCmGid;
        this.transaction = transaction;
    }

    public BulkCmPK(BulkCmPK otherPk, Object transaction) {
        this.bulkCmGid = otherPk.bulkCmGid;
        this.bulkCmXid = otherPk.bulkCmXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.bulkCmGid != null ? String.valueOf(this.bulkCmGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.bulkCmGid != null ? String.valueOf(this.bulkCmGid) : "";
    }

    public static BulkCmPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new BulkCmPK(gids[0]) : null;
    }

    public static BulkCmPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new BulkCmPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.bulkCmGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof BulkCmPK)) {
            return false;
        }
        BulkCmPK otherPk = (BulkCmPK)other;
        return Functions.equals(otherPk.bulkCmGid, this.bulkCmGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.bulkcm.BulkCmHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.bulkCmGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return BulkCmColumns.bulkCmGid.convertFromDB(this.bulkCmGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static BulkCmPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("BulkCmPK requires a non-null XID to generate a GID");
            }
            BulkCmPK bulkCmPK = new BulkCmPK(domain, xid);
            return bulkCmPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static BulkCmPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            BulkCmPK bulkCmPK = BulkCmPK.newPK(domainName, xid, connection);
            return bulkCmPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return BulkCmPK.class;
        }

        @Override
        public final String getEntity() {
            return "BulkCm";
        }

        @Override
        public final String getTableName() {
            return "bulk_cm";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"bulk_cm_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            BulkCmPK result = new BulkCmPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
