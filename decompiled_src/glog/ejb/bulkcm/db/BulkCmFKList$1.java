/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.util.jdbc.Fk;

static final class BulkCmFKList.1
implements Fk {
    BulkCmFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.bulkcm.db";
    }

    @Override
    public String getPkDataClass() {
        return "BulkCmData";
    }

    @Override
    public String getPkField() {
        return "bulkPlanGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.bulkplan.db";
    }

    @Override
    public String getFkDataClass() {
        return "BulkPlanData";
    }

    @Override
    public String getFkDataField() {
        return "bulkPlanGid";
    }
}
