/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class BulkCmFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("bulkPlanGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.bulkcm.db";
            }

            @Override
            public String getPkDataClass() {
                return "BulkCmData";
            }

            @Override
            public String getPkField() {
                return "bulkPlanGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.bulkplan.db";
            }

            @Override
            public String getFkDataClass() {
                return "BulkPlanData";
            }

            @Override
            public String getFkDataField() {
                return "bulkPlanGid";
            }
        });
        fks.put("totalCostAfterCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.bulkcm.db";
            }

            @Override
            public String getPkDataClass() {
                return "BulkCmData";
            }

            @Override
            public String getPkField() {
                return "totalCostAfterCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("totalCostBeforeCurrencyGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.bulkcm.db";
            }

            @Override
            public String getPkDataClass() {
                return "BulkCmData";
            }

            @Override
            public String getPkField() {
                return "totalCostBeforeCurrencyGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.currency.db";
            }

            @Override
            public String getFkDataClass() {
                return "CurrencyData";
            }

            @Override
            public String getFkDataField() {
                return "currencyGid";
            }
        });
        fks.put("appMachineGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.bulkcm.db";
            }

            @Override
            public String getPkDataClass() {
                return "BulkCmData";
            }

            @Override
            public String getPkField() {
                return "appMachineGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.appserver.db";
            }

            @Override
            public String getFkDataClass() {
                return "AppMachineData";
            }

            @Override
            public String getFkDataField() {
                return "appMachineGid";
            }
        });
    }
}
