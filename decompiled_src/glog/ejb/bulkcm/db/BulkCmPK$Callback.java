/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.ejb.bulkcm.db.BulkCmPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class BulkCmPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return BulkCmPK.class;
    }

    @Override
    public final String getEntity() {
        return "BulkCm";
    }

    @Override
    public final String getTableName() {
        return "bulk_cm";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"bulk_cm_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        BulkCmPK result = new BulkCmPK(0, q.getObject(pkOffset + 0 + 1), transaction);
        return result;
    }
}
