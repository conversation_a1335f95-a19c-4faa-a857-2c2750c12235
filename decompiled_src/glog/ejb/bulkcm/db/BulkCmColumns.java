/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.jdbc.SqlColumn;
import glog.util.uom.data.Distance;
import glog.util.uom.data.Duration;

public class BulkCmColumns {
    public static SqlColumn bulkCmGid = new SqlColumn(String.class, "bulk_cm_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn bulkCmXid = new SqlColumn(String.class, "bulk_cm_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn queryName = new SqlColumn(String.class, "query_name", 120, 0, 0, null, 2, null);
    public static SqlColumn startTime = new SqlColumn(LocalTimestamp.class, "start_time", 7, 0, 0, null, 3, null);
    public static SqlColumn endTime = new SqlColumn(LocalTimestamp.class, "end_time", 7, 0, 0, null, 4, null);
    public static SqlColumn numOfShipmentsSelected = new SqlColumn(Integer.class, "num_of_shipments_selected", 22, 0, 1, Integer.valueOf("0"), 5, null);
    public static SqlColumn numOfShipmentsLinked = new SqlColumn(Integer.class, "num_of_shipments_linked", 22, 0, 1, Integer.valueOf("0"), 6, null);
    public static SqlColumn logProcessId = new SqlColumn(Long.class, "log_process_id", 22, 0, 0, null, 7, null);
    public static SqlColumn appMachineGid = new SqlColumn(String.class, "app_machine_gid", 101, 0, 0, null, 8, null);
    public static SqlColumn bulkPlanGid = new SqlColumn(String.class, "bulk_plan_gid", 101, 0, 0, null, 9, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 10, null);
    public static SqlColumn planningParameterSetGid = new SqlColumn(String.class, "planning_parameter_set_gid", 101, 0, 1, String.valueOf("DEFAULT"), 11, null);
    public static SqlColumn numShipmentsConsidered = new SqlColumn(Integer.class, "num_shipments_considered", 22, 0, 1, Integer.valueOf("0"), 12, null);
    public static SqlColumn numOfCmCreated = new SqlColumn(Integer.class, "num_of_cm_created", 22, 0, 1, Integer.valueOf("0"), 13, null);
    public static SqlColumn totalCostBefore = new SqlColumn(Currency.class, "total_cost_before", 22, 2, 0, null, 14, "total_cost_before_base");
    public static SqlColumn totalCostAfter = new SqlColumn(Currency.class, "total_cost_after", 22, 2, 0, null, 15, "total_cost_after_base");
    public static SqlColumn totalEmptyDistance = new SqlColumn(Distance.class, "total_empty_distance", 22, 1, 0, null, 16, "total_empty_distance_base");
    public static SqlColumn totalLoadedDistance = new SqlColumn(Distance.class, "total_loaded_distance", 22, 1, 0, null, 17, "total_loaded_distance_base");
    public static SqlColumn totalDistanceBefore = new SqlColumn(Distance.class, "total_distance_before", 22, 1, 0, null, 18, "total_distance_before_base");
    public static SqlColumn totalDistanceAfter = new SqlColumn(Distance.class, "total_distance_after", 22, 1, 0, null, 19, "total_distance_after_base");
    public static SqlColumn cmWithTwoShipments = new SqlColumn(Integer.class, "cm_with_two_shipments", 22, 0, 0, null, 20, null);
    public static SqlColumn cmWithThreeShipments = new SqlColumn(Integer.class, "cm_with_three_shipments", 22, 0, 0, null, 21, null);
    public static SqlColumn cmWithFourShipments = new SqlColumn(Integer.class, "cm_with_four_shipments", 22, 0, 0, null, 22, null);
    public static SqlColumn cmWithFiveShipments = new SqlColumn(Integer.class, "cm_with_five_shipments", 22, 0, 0, null, 23, null);
    public static SqlColumn cmWithSixOrMoreShipments = new SqlColumn(Integer.class, "cm_with_six_or_more_shipments", 22, 0, 0, null, 24, null);
    public static SqlColumn state = new SqlColumn(String.class, "state", 50, 0, 1, String.valueOf("SUBMITTED"), 25, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 4000, 0, 0, null, 26, null);
    public static SqlColumn totalTimeForAllCm = new SqlColumn(Duration.class, "total_time_for_all_cm", 22, 0, 0, null, 27, "total_time_for_all_cm_base");
}
