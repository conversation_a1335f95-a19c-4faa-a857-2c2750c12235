/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.util.jdbc.Fk;

static final class BulkCmFKList.4
implements Fk {
    BulkCmFKList.4() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.bulkcm.db";
    }

    @Override
    public String getPkDataClass() {
        return "BulkCmData";
    }

    @Override
    public String getPkField() {
        return "appMachineGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.appserver.db";
    }

    @Override
    public String getFkDataClass() {
        return "AppMachineData";
    }

    @Override
    public String getFkDataField() {
        return "appMachineGid";
    }
}
