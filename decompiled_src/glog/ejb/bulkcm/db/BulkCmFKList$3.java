/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.util.jdbc.Fk;

static final class BulkCmFKList.3
implements Fk {
    BulkCmFKList.3() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.bulkcm.db";
    }

    @Override
    public String getPkDataClass() {
        return "BulkCmData";
    }

    @Override
    public String getPkField() {
        return "totalCostBeforeCurrencyGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.currency.db";
    }

    @Override
    public String getFkDataClass() {
        return "CurrencyData";
    }

    @Override
    public String getFkDataField() {
        return "currencyGid";
    }
}
