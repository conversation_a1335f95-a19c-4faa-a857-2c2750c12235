/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.bulkcm.db;

import glog.ejb.bulkcm.db.BulkCmPK;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import glog.util.uom.data.Distance;
import glog.util.uom.data.Duration;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class BulkCmData
extends BeanData {
    public String bulkCmGid;
    public String bulkCmXid;
    public String queryName;
    public LocalTimestamp startTime;
    public LocalTimestamp endTime;
    public Integer numOfShipmentsSelected = Integer.valueOf("0");
    public Integer numOfShipmentsLinked = Integer.valueOf("0");
    public Long logProcessId;
    public String appMachineGid;
    public String bulkPlanGid;
    public String domainName;
    public String planningParameterSetGid = String.valueOf("DEFAULT");
    public Integer numShipmentsConsidered = Integer.valueOf("0");
    public Integer numOfCmCreated = Integer.valueOf("0");
    public Currency totalCostBefore;
    public Currency totalCostAfter;
    public Distance totalEmptyDistance;
    public Distance totalLoadedDistance;
    public Distance totalDistanceBefore;
    public Distance totalDistanceAfter;
    public Integer cmWithTwoShipments;
    public Integer cmWithThreeShipments;
    public Integer cmWithFourShipments;
    public Integer cmWithFiveShipments;
    public Integer cmWithSixOrMoreShipments;
    public String state = String.valueOf("SUBMITTED");
    public String description;
    public Duration totalTimeForAllCm;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(BulkCmData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field bulkCmGidField = beanDataFields[0];
    public static Field bulkCmXidField = beanDataFields[1];
    public static Field queryNameField = beanDataFields[2];
    public static Field startTimeField = beanDataFields[3];
    public static Field endTimeField = beanDataFields[4];
    public static Field numOfShipmentsSelectedField = beanDataFields[5];
    public static Field numOfShipmentsLinkedField = beanDataFields[6];
    public static Field logProcessIdField = beanDataFields[7];
    public static Field appMachineGidField = beanDataFields[8];
    public static Field bulkPlanGidField = beanDataFields[9];
    public static Field domainNameField = beanDataFields[10];
    public static Field planningParameterSetGidField = beanDataFields[11];
    public static Field numShipmentsConsideredField = beanDataFields[12];
    public static Field numOfCmCreatedField = beanDataFields[13];
    public static Field totalCostBeforeField = beanDataFields[14];
    public static Field totalCostAfterField = beanDataFields[15];
    public static Field totalEmptyDistanceField = beanDataFields[16];
    public static Field totalLoadedDistanceField = beanDataFields[17];
    public static Field totalDistanceBeforeField = beanDataFields[18];
    public static Field totalDistanceAfterField = beanDataFields[19];
    public static Field cmWithTwoShipmentsField = beanDataFields[20];
    public static Field cmWithThreeShipmentsField = beanDataFields[21];
    public static Field cmWithFourShipmentsField = beanDataFields[22];
    public static Field cmWithFiveShipmentsField = beanDataFields[23];
    public static Field cmWithSixOrMoreShipmentsField = beanDataFields[24];
    public static Field stateField = beanDataFields[25];
    public static Field descriptionField = beanDataFields[26];
    public static Field totalTimeForAllCmField = beanDataFields[27];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public BulkCmData() {
    }

    public BulkCmData(BulkCmData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getBulkCmPK();
    }

    @Legacy
    public BulkCmPK getBulkCmPK() {
        if (this.bulkCmGid == null) {
            return null;
        }
        return new BulkCmPK(this.bulkCmGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setBulkCmPK((BulkCmPK)pk);
    }

    @Legacy
    public void setBulkCmPK(BulkCmPK pk) {
        this.bulkCmGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.bulkcm.gen.BulkCmQueryGen";
    }

    public static BulkCmData load(Connection conn, BulkCmPK pk) throws GLException {
        return (BulkCmData)BulkCmData.load(conn, pk, BulkCmData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return BulkCmData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return BulkCmData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return BulkCmData.load(conn, whereClause, prepareArguments, fetchSize, BulkCmData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return BulkCmData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return BulkCmData.load(conn, fromWhere, alias, prepareArguments, fetchSize, BulkCmData.class);
    }
}
