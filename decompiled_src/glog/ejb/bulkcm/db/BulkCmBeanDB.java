/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.bulkcm.db;

import glog.database.security.SecurityUtil;
import glog.ejb.bulkcm.db.BulkCmColumns;
import glog.ejb.bulkcm.db.BulkCmData;
import glog.ejb.bulkcm.db.BulkCmPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import glog.util.uom.data.Distance;
import glog.util.uom.data.Duration;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class BulkCmBeanDB
extends BeanManagedEntityBean {
    public Object bulkCmGid;
    public Object bulkCmXid;
    public Object queryName;
    public Object startTime;
    public Object endTime;
    public Object numOfShipmentsSelected;
    public Object numOfShipmentsLinked;
    public Object logProcessId;
    public Object appMachineGid;
    public Object bulkPlanGid;
    public Object domainName;
    public Object planningParameterSetGid;
    public Object numShipmentsConsidered;
    public Object numOfCmCreated;
    public Object totalCostBefore;
    public Object totalCostAfter;
    public Object totalEmptyDistance;
    public Object totalLoadedDistance;
    public Object totalDistanceBefore;
    public Object totalDistanceAfter;
    public Object cmWithTwoShipments;
    public Object cmWithThreeShipments;
    public Object cmWithFourShipments;
    public Object cmWithFiveShipments;
    public Object cmWithSixOrMoreShipments;
    public Object state;
    public Object description;
    public Object totalTimeForAllCm;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient BulkCmPK pk;
    protected transient BulkCmData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(BulkCmBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static BulkCmPK.Callback theCall = new BulkCmPK.Callback();

    public BulkCmBeanDB() {
        super(false);
    }

    public BulkCmPK ejbCreate(BulkCmData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected BulkCmPK newPK() throws GLException {
        return BulkCmPK.newPK(this.getDomainName(), this.getBulkCmXid(), this.getConnection());
    }

    public void ejbPostCreate(BulkCmData data) throws CreateException {
        this.ejbPostCreator();
    }

    public BulkCmPK ejbFindByPrimaryKey(BulkCmPK pk) throws FinderException {
        return (BulkCmPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(BulkCmPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<BulkCmPK> v = new Vector<BulkCmPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(BulkCmPK pk, BulkCmData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(BulkCmPK pk, BulkCmData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(BulkCmPK pk, BulkCmData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(BulkCmColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(BulkCmColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(BulkCmColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(BulkCmColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        BulkCmPK pk = (BulkCmPK)genericPk;
        this.bulkCmGid = pk.bulkCmGid;
        this.domainName = pk.domainName;
        this.bulkCmXid = pk.bulkCmXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        BulkCmPK pk = new BulkCmPK(0, this.bulkCmGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.BulkCm";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public BulkCmData getData() throws GLException {
        try {
            BulkCmData retval = new BulkCmData();
            retval.getFromBean(this, BulkCmColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(BulkCmData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(BulkCmData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            BulkCmData oldData = modified ? this.getData() : null;
            data.setToBean(this, BulkCmColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return BulkCmData.class;
    }

    @Override
    public Class getPkClass() {
        return BulkCmPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getBulkCmGid() throws GLException {
        try {
            return (String)BulkCmColumns.bulkCmGid.convertFromDB(this.bulkCmGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBulkCmGid(String bulkCmGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.bulkCmGid;
            BulkCmData data = this.getData();
            this.bulkCmGid = BulkCmColumns.bulkCmGid.convertToDB(bulkCmGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "bulkCmGid", String.class, oldValue, this.bulkCmGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getBulkCmXid() throws GLException {
        try {
            return (String)BulkCmColumns.bulkCmXid.convertFromDB(this.bulkCmXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBulkCmXid(String bulkCmXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.bulkCmXid;
            BulkCmData data = this.getData();
            this.bulkCmXid = BulkCmColumns.bulkCmXid.convertToDB(bulkCmXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "bulkCmXid", String.class, oldValue, this.bulkCmXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getQueryName() throws GLException {
        try {
            return (String)BulkCmColumns.queryName.convertFromDB(this.queryName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setQueryName(String queryName) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.queryName;
            BulkCmData data = this.getData();
            this.queryName = BulkCmColumns.queryName.convertToDB(queryName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "queryName", String.class, oldValue, this.queryName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalTimestamp getStartTime() throws GLException {
        try {
            return (LocalTimestamp)BulkCmColumns.startTime.convertFromDB(this.startTime);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setStartTime(LocalTimestamp startTime) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.startTime;
            BulkCmData data = this.getData();
            this.startTime = BulkCmColumns.startTime.convertToDB(startTime);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "startTime", LocalTimestamp.class, oldValue, this.startTime);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public LocalTimestamp getEndTime() throws GLException {
        try {
            return (LocalTimestamp)BulkCmColumns.endTime.convertFromDB(this.endTime);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setEndTime(LocalTimestamp endTime) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.endTime;
            BulkCmData data = this.getData();
            this.endTime = BulkCmColumns.endTime.convertToDB(endTime);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "endTime", LocalTimestamp.class, oldValue, this.endTime);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getNumOfShipmentsSelected() throws GLException {
        try {
            return (Integer)BulkCmColumns.numOfShipmentsSelected.convertFromDB(this.numOfShipmentsSelected);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setNumOfShipmentsSelected(Integer numOfShipmentsSelected) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.numOfShipmentsSelected;
            BulkCmData data = this.getData();
            this.numOfShipmentsSelected = BulkCmColumns.numOfShipmentsSelected.convertToDB(numOfShipmentsSelected);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "numOfShipmentsSelected", Integer.class, oldValue, this.numOfShipmentsSelected);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getNumOfShipmentsLinked() throws GLException {
        try {
            return (Integer)BulkCmColumns.numOfShipmentsLinked.convertFromDB(this.numOfShipmentsLinked);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setNumOfShipmentsLinked(Integer numOfShipmentsLinked) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.numOfShipmentsLinked;
            BulkCmData data = this.getData();
            this.numOfShipmentsLinked = BulkCmColumns.numOfShipmentsLinked.convertToDB(numOfShipmentsLinked);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "numOfShipmentsLinked", Integer.class, oldValue, this.numOfShipmentsLinked);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Long getLogProcessId() throws GLException {
        try {
            return (Long)BulkCmColumns.logProcessId.convertFromDB(this.logProcessId);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLogProcessId(Long logProcessId) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.logProcessId;
            BulkCmData data = this.getData();
            this.logProcessId = BulkCmColumns.logProcessId.convertToDB(logProcessId);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "logProcessId", Long.class, oldValue, this.logProcessId);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAppMachineGid() throws GLException {
        try {
            return (String)BulkCmColumns.appMachineGid.convertFromDB(this.appMachineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAppMachineGid(String appMachineGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.appMachineGid;
            BulkCmData data = this.getData();
            this.appMachineGid = BulkCmColumns.appMachineGid.convertToDB(appMachineGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "appMachineGid", String.class, oldValue, this.appMachineGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getBulkPlanGid() throws GLException {
        try {
            return (String)BulkCmColumns.bulkPlanGid.convertFromDB(this.bulkPlanGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setBulkPlanGid(String bulkPlanGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.bulkPlanGid;
            BulkCmData data = this.getData();
            this.bulkPlanGid = BulkCmColumns.bulkPlanGid.convertToDB(bulkPlanGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "bulkPlanGid", String.class, oldValue, this.bulkPlanGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)BulkCmColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            BulkCmData data = this.getData();
            this.domainName = BulkCmColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getPlanningParameterSetGid() throws GLException {
        try {
            return (String)BulkCmColumns.planningParameterSetGid.convertFromDB(this.planningParameterSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setPlanningParameterSetGid(String planningParameterSetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.planningParameterSetGid;
            BulkCmData data = this.getData();
            this.planningParameterSetGid = BulkCmColumns.planningParameterSetGid.convertToDB(planningParameterSetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "planningParameterSetGid", String.class, oldValue, this.planningParameterSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getNumShipmentsConsidered() throws GLException {
        try {
            return (Integer)BulkCmColumns.numShipmentsConsidered.convertFromDB(this.numShipmentsConsidered);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setNumShipmentsConsidered(Integer numShipmentsConsidered) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.numShipmentsConsidered;
            BulkCmData data = this.getData();
            this.numShipmentsConsidered = BulkCmColumns.numShipmentsConsidered.convertToDB(numShipmentsConsidered);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "numShipmentsConsidered", Integer.class, oldValue, this.numShipmentsConsidered);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getNumOfCmCreated() throws GLException {
        try {
            return (Integer)BulkCmColumns.numOfCmCreated.convertFromDB(this.numOfCmCreated);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setNumOfCmCreated(Integer numOfCmCreated) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.numOfCmCreated;
            BulkCmData data = this.getData();
            this.numOfCmCreated = BulkCmColumns.numOfCmCreated.convertToDB(numOfCmCreated);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "numOfCmCreated", Integer.class, oldValue, this.numOfCmCreated);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTotalCostBefore() throws GLException {
        try {
            return (Currency)BulkCmColumns.totalCostBefore.convertFromDB(this.totalCostBefore);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalCostBefore(Currency totalCostBefore) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalCostBefore;
            BulkCmData data = this.getData();
            this.totalCostBefore = BulkCmColumns.totalCostBefore.convertToDB(totalCostBefore);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalCostBefore", Currency.class, oldValue, this.totalCostBefore);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Currency getTotalCostAfter() throws GLException {
        try {
            return (Currency)BulkCmColumns.totalCostAfter.convertFromDB(this.totalCostAfter);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalCostAfter(Currency totalCostAfter) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalCostAfter;
            BulkCmData data = this.getData();
            this.totalCostAfter = BulkCmColumns.totalCostAfter.convertToDB(totalCostAfter);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalCostAfter", Currency.class, oldValue, this.totalCostAfter);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Distance getTotalEmptyDistance() throws GLException {
        try {
            return (Distance)BulkCmColumns.totalEmptyDistance.convertFromDB(this.totalEmptyDistance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalEmptyDistance(Distance totalEmptyDistance) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalEmptyDistance;
            BulkCmData data = this.getData();
            this.totalEmptyDistance = BulkCmColumns.totalEmptyDistance.convertToDB(totalEmptyDistance);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalEmptyDistance", Distance.class, oldValue, this.totalEmptyDistance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Distance getTotalLoadedDistance() throws GLException {
        try {
            return (Distance)BulkCmColumns.totalLoadedDistance.convertFromDB(this.totalLoadedDistance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalLoadedDistance(Distance totalLoadedDistance) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalLoadedDistance;
            BulkCmData data = this.getData();
            this.totalLoadedDistance = BulkCmColumns.totalLoadedDistance.convertToDB(totalLoadedDistance);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalLoadedDistance", Distance.class, oldValue, this.totalLoadedDistance);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Distance getTotalDistanceBefore() throws GLException {
        try {
            return (Distance)BulkCmColumns.totalDistanceBefore.convertFromDB(this.totalDistanceBefore);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalDistanceBefore(Distance totalDistanceBefore) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalDistanceBefore;
            BulkCmData data = this.getData();
            this.totalDistanceBefore = BulkCmColumns.totalDistanceBefore.convertToDB(totalDistanceBefore);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalDistanceBefore", Distance.class, oldValue, this.totalDistanceBefore);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Distance getTotalDistanceAfter() throws GLException {
        try {
            return (Distance)BulkCmColumns.totalDistanceAfter.convertFromDB(this.totalDistanceAfter);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalDistanceAfter(Distance totalDistanceAfter) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalDistanceAfter;
            BulkCmData data = this.getData();
            this.totalDistanceAfter = BulkCmColumns.totalDistanceAfter.convertToDB(totalDistanceAfter);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalDistanceAfter", Distance.class, oldValue, this.totalDistanceAfter);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCmWithTwoShipments() throws GLException {
        try {
            return (Integer)BulkCmColumns.cmWithTwoShipments.convertFromDB(this.cmWithTwoShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCmWithTwoShipments(Integer cmWithTwoShipments) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cmWithTwoShipments;
            BulkCmData data = this.getData();
            this.cmWithTwoShipments = BulkCmColumns.cmWithTwoShipments.convertToDB(cmWithTwoShipments);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cmWithTwoShipments", Integer.class, oldValue, this.cmWithTwoShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCmWithThreeShipments() throws GLException {
        try {
            return (Integer)BulkCmColumns.cmWithThreeShipments.convertFromDB(this.cmWithThreeShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCmWithThreeShipments(Integer cmWithThreeShipments) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cmWithThreeShipments;
            BulkCmData data = this.getData();
            this.cmWithThreeShipments = BulkCmColumns.cmWithThreeShipments.convertToDB(cmWithThreeShipments);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cmWithThreeShipments", Integer.class, oldValue, this.cmWithThreeShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCmWithFourShipments() throws GLException {
        try {
            return (Integer)BulkCmColumns.cmWithFourShipments.convertFromDB(this.cmWithFourShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCmWithFourShipments(Integer cmWithFourShipments) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cmWithFourShipments;
            BulkCmData data = this.getData();
            this.cmWithFourShipments = BulkCmColumns.cmWithFourShipments.convertToDB(cmWithFourShipments);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cmWithFourShipments", Integer.class, oldValue, this.cmWithFourShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCmWithFiveShipments() throws GLException {
        try {
            return (Integer)BulkCmColumns.cmWithFiveShipments.convertFromDB(this.cmWithFiveShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCmWithFiveShipments(Integer cmWithFiveShipments) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cmWithFiveShipments;
            BulkCmData data = this.getData();
            this.cmWithFiveShipments = BulkCmColumns.cmWithFiveShipments.convertToDB(cmWithFiveShipments);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cmWithFiveShipments", Integer.class, oldValue, this.cmWithFiveShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Integer getCmWithSixOrMoreShipments() throws GLException {
        try {
            return (Integer)BulkCmColumns.cmWithSixOrMoreShipments.convertFromDB(this.cmWithSixOrMoreShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setCmWithSixOrMoreShipments(Integer cmWithSixOrMoreShipments) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.cmWithSixOrMoreShipments;
            BulkCmData data = this.getData();
            this.cmWithSixOrMoreShipments = BulkCmColumns.cmWithSixOrMoreShipments.convertToDB(cmWithSixOrMoreShipments);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "cmWithSixOrMoreShipments", Integer.class, oldValue, this.cmWithSixOrMoreShipments);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getState() throws GLException {
        try {
            return (String)BulkCmColumns.state.convertFromDB(this.state);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setState(String state) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.state;
            BulkCmData data = this.getData();
            this.state = BulkCmColumns.state.convertToDB(state);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "state", String.class, oldValue, this.state);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)BulkCmColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            BulkCmData data = this.getData();
            this.description = BulkCmColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public Duration getTotalTimeForAllCm() throws GLException {
        try {
            return (Duration)BulkCmColumns.totalTimeForAllCm.convertFromDB(this.totalTimeForAllCm);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setTotalTimeForAllCm(Duration totalTimeForAllCm) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.totalTimeForAllCm;
            BulkCmData data = this.getData();
            this.totalTimeForAllCm = BulkCmColumns.totalTimeForAllCm.convertToDB(totalTimeForAllCm);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "totalTimeForAllCm", Duration.class, oldValue, this.totalTimeForAllCm);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
