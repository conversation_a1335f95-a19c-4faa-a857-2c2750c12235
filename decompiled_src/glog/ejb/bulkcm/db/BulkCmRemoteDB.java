/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.bulkcm.db;

import glog.ejb.bulkcm.db.BulkCmData;
import glog.util.LocalTimestamp;
import glog.util.currency.Currency;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.uom.data.Distance;
import glog.util.uom.data.Duration;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface BulkCmRemoteDB
extends EJBObject {
    public BulkCmData getData() throws RemoteException, GLException;

    public void setData(BulkCmData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws <PERSON>moteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws RemoteException, GLException;

    public void unload() throws RemoteException, GLException;

    public Map verify() throws RemoteException, GLException;

    public String getBulkCmGid() throws RemoteException, GLException;

    public void setBulkCmGid(String var1) throws RemoteException, GLException;

    public String getBulkCmXid() throws RemoteException, GLException;

    public void setBulkCmXid(String var1) throws RemoteException, GLException;

    public String getQueryName() throws RemoteException, GLException;

    public void setQueryName(String var1) throws RemoteException, GLException;

    public LocalTimestamp getStartTime() throws RemoteException, GLException;

    public void setStartTime(LocalTimestamp var1) throws RemoteException, GLException;

    public LocalTimestamp getEndTime() throws RemoteException, GLException;

    public void setEndTime(LocalTimestamp var1) throws RemoteException, GLException;

    public Integer getNumOfShipmentsSelected() throws RemoteException, GLException;

    public void setNumOfShipmentsSelected(Integer var1) throws RemoteException, GLException;

    public Integer getNumOfShipmentsLinked() throws RemoteException, GLException;

    public void setNumOfShipmentsLinked(Integer var1) throws RemoteException, GLException;

    public Long getLogProcessId() throws RemoteException, GLException;

    public void setLogProcessId(Long var1) throws RemoteException, GLException;

    public String getAppMachineGid() throws RemoteException, GLException;

    public void setAppMachineGid(String var1) throws RemoteException, GLException;

    public String getBulkPlanGid() throws RemoteException, GLException;

    public void setBulkPlanGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;

    public String getPlanningParameterSetGid() throws RemoteException, GLException;

    public void setPlanningParameterSetGid(String var1) throws RemoteException, GLException;

    public Integer getNumShipmentsConsidered() throws RemoteException, GLException;

    public void setNumShipmentsConsidered(Integer var1) throws RemoteException, GLException;

    public Integer getNumOfCmCreated() throws RemoteException, GLException;

    public void setNumOfCmCreated(Integer var1) throws RemoteException, GLException;

    public Currency getTotalCostBefore() throws RemoteException, GLException;

    public void setTotalCostBefore(Currency var1) throws RemoteException, GLException;

    public Currency getTotalCostAfter() throws RemoteException, GLException;

    public void setTotalCostAfter(Currency var1) throws RemoteException, GLException;

    public Distance getTotalEmptyDistance() throws RemoteException, GLException;

    public void setTotalEmptyDistance(Distance var1) throws RemoteException, GLException;

    public Distance getTotalLoadedDistance() throws RemoteException, GLException;

    public void setTotalLoadedDistance(Distance var1) throws RemoteException, GLException;

    public Distance getTotalDistanceBefore() throws RemoteException, GLException;

    public void setTotalDistanceBefore(Distance var1) throws RemoteException, GLException;

    public Distance getTotalDistanceAfter() throws RemoteException, GLException;

    public void setTotalDistanceAfter(Distance var1) throws RemoteException, GLException;

    public Integer getCmWithTwoShipments() throws RemoteException, GLException;

    public void setCmWithTwoShipments(Integer var1) throws RemoteException, GLException;

    public Integer getCmWithThreeShipments() throws RemoteException, GLException;

    public void setCmWithThreeShipments(Integer var1) throws RemoteException, GLException;

    public Integer getCmWithFourShipments() throws RemoteException, GLException;

    public void setCmWithFourShipments(Integer var1) throws RemoteException, GLException;

    public Integer getCmWithFiveShipments() throws RemoteException, GLException;

    public void setCmWithFiveShipments(Integer var1) throws RemoteException, GLException;

    public Integer getCmWithSixOrMoreShipments() throws RemoteException, GLException;

    public void setCmWithSixOrMoreShipments(Integer var1) throws RemoteException, GLException;

    public String getState() throws RemoteException, GLException;

    public void setState(String var1) throws RemoteException, GLException;

    public String getDescription() throws RemoteException, GLException;

    public void setDescription(String var1) throws RemoteException, GLException;

    public Duration getTotalTimeForAllCm() throws RemoteException, GLException;

    public void setTotalTimeForAllCm(Duration var1) throws RemoteException, GLException;
}
