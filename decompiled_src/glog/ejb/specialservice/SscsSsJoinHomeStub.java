/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.specialservice;

import glog.ejb.specialservice.SscsSsJoin;
import glog.ejb.specialservice.SscsSsJoinBean;
import glog.ejb.specialservice.SscsSsJoinHome;
import glog.ejb.specialservice.SscsSsJoinStub;
import glog.ejb.specialservice.db.SscsSsJoinData;
import glog.ejb.specialservice.db.SscsSsJoinPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class SscsSsJoinHomeStub
implements SscsSsJoinHome {
    static LRUCache cache = LocalEntityCache.register("SscsSsJoin");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public SscsSsJoin create(SscsSsJoinData p1) throws CreateException, RemoteException {
        SscsSsJoinStub sscsSsJoinStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "create", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            sscsSsJoinStub = new SscsSsJoinStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return sscsSsJoinStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public SscsSsJoin findByPrimaryKey(SscsSsJoinPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SscsSsJoinBean bean;
                ee.enterMethod("SscsSsJoinHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (SscsSsJoinBean)cache.get(p1)) != null) {
                    SscsSsJoinStub sscsSsJoinStub = new SscsSsJoinStub((EJBHome)this, bean);
                    return sscsSsJoinStub;
                }
                bean = new SscsSsJoinBean();
                SscsSsJoinPK pk = bean.ejbFindByPrimaryKey(p1);
                SscsSsJoinStub sscsSsJoinStub = new SscsSsJoinStub((EJBHome)this, pk);
                return sscsSsJoinStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "findAll", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, SscsSsJoinStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "findAll", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SscsSsJoinStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "findByPrimaryKeys", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SscsSsJoinStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "findByPrimaryKeys", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, SscsSsJoinStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(SscsSsJoinPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SscsSsJoinBean bean;
                ee.enterMethod("SscsSsJoinHome", "findInCache", true);
                if (cache != null && (bean = (SscsSsJoinBean)cache.get(p1)) != null) {
                    Vector<SscsSsJoinPK> v = new Vector<SscsSsJoinPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), SscsSsJoinStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(SscsSsJoinPK p1, SscsSsJoinData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "onCreate", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(SscsSsJoinPK p1, SscsSsJoinData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "onUpdate", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(SscsSsJoinPK p1, SscsSsJoinData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "onRemove", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "listBeansInCache", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "listLocks", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "unlock", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "getDataNoLock", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "getLockData", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "getMaxCacheSize", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SscsSsJoinHome", "updateMaxCacheSize", true);
            SscsSsJoinBean bean = new SscsSsJoinBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new SscsSsJoinStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
