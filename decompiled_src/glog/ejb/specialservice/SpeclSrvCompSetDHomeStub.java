/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.specialservice;

import glog.ejb.specialservice.SpeclSrvCompSetD;
import glog.ejb.specialservice.SpeclSrvCompSetDBean;
import glog.ejb.specialservice.SpeclSrvCompSetDHome;
import glog.ejb.specialservice.SpeclSrvCompSetDStub;
import glog.ejb.specialservice.db.SpeclSrvCompSetDData;
import glog.ejb.specialservice.db.SpeclSrvCompSetDPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class SpeclSrvCompSetDHomeStub
implements SpeclSrvCompSetDHome {
    static LRUCache cache = LocalEntityCache.register("SpeclSrvCompSetD");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public SpeclSrvCompSetD create(SpeclSrvCompSetDData p1) throws CreateException, RemoteException {
        SpeclSrvCompSetDStub speclSrvCompSetDStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "create", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            speclSrvCompSetDStub = new SpeclSrvCompSetDStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return speclSrvCompSetDStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public SpeclSrvCompSetD findByPrimaryKey(SpeclSrvCompSetDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvCompSetDBean bean;
                ee.enterMethod("SpeclSrvCompSetDHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (SpeclSrvCompSetDBean)cache.get(p1)) != null) {
                    SpeclSrvCompSetDStub speclSrvCompSetDStub = new SpeclSrvCompSetDStub((EJBHome)this, bean);
                    return speclSrvCompSetDStub;
                }
                bean = new SpeclSrvCompSetDBean();
                SpeclSrvCompSetDPK pk = bean.ejbFindByPrimaryKey(p1);
                SpeclSrvCompSetDStub speclSrvCompSetDStub = new SpeclSrvCompSetDStub((EJBHome)this, pk);
                return speclSrvCompSetDStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "findAll", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompSetDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "findAll", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompSetDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "findByPrimaryKeys", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompSetDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "findByPrimaryKeys", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompSetDStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(SpeclSrvCompSetDPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvCompSetDBean bean;
                ee.enterMethod("SpeclSrvCompSetDHome", "findInCache", true);
                if (cache != null && (bean = (SpeclSrvCompSetDBean)cache.get(p1)) != null) {
                    Vector<SpeclSrvCompSetDPK> v = new Vector<SpeclSrvCompSetDPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), SpeclSrvCompSetDStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(SpeclSrvCompSetDPK p1, SpeclSrvCompSetDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "onCreate", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(SpeclSrvCompSetDPK p1, SpeclSrvCompSetDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "onUpdate", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(SpeclSrvCompSetDPK p1, SpeclSrvCompSetDData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "onRemove", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "listBeansInCache", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "listLocks", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "unlock", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "getDataNoLock", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "getLockData", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "getMaxCacheSize", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompSetDHome", "updateMaxCacheSize", true);
            SpeclSrvCompSetDBean bean = new SpeclSrvCompSetDBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new SpeclSrvCompSetDStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
