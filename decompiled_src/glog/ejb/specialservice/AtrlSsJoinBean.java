/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice;

import glog.business.reference.TSpecialService;
import glog.ejb.specialservice.db.AtrlSsJoinBeanDB;
import glog.util.exception.GLException;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.PkAccessor;

public class AtrlSsJoinBean
extends AtrlSsJoinBeanDB {
    @Override
    protected void onCreate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onCreate(pk, data);
        TSpecialService.refreshLRU();
    }

    @Override
    protected void onUpdate(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onUpdate(pk, data);
        TSpecialService.refreshLRU();
    }

    @Override
    protected void onRemove(PkAccessor pk, BeanDataAccessor data) throws GLException {
        super.onRemove(pk, data);
        TSpecialService.refreshLRU();
    }
}
