/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.specialservice;

import glog.ejb.specialservice.SpeclSrvCompatibleSet;
import glog.ejb.specialservice.SpeclSrvCompatibleSetBean;
import glog.ejb.specialservice.SpeclSrvCompatibleSetHome;
import glog.ejb.specialservice.SpeclSrvCompatibleSetStub;
import glog.ejb.specialservice.db.SpeclSrvCompatibleSetData;
import glog.ejb.specialservice.db.SpeclSrvCompatibleSetPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class SpeclSrvCompatibleSetHomeStub
implements SpeclSrvCompatibleSetHome {
    static LRUCache cache = LocalEntityCache.register("SpeclSrvCompatibleSet");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public SpeclSrvCompatibleSet create(SpeclSrvCompatibleSetData p1) throws CreateException, RemoteException {
        SpeclSrvCompatibleSetStub speclSrvCompatibleSetStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "create", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            speclSrvCompatibleSetStub = new SpeclSrvCompatibleSetStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return speclSrvCompatibleSetStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public SpeclSrvCompatibleSet findByPrimaryKey(SpeclSrvCompatibleSetPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvCompatibleSetBean bean;
                ee.enterMethod("SpeclSrvCompatibleSetHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (SpeclSrvCompatibleSetBean)cache.get(p1)) != null) {
                    SpeclSrvCompatibleSetStub speclSrvCompatibleSetStub = new SpeclSrvCompatibleSetStub((EJBHome)this, bean);
                    return speclSrvCompatibleSetStub;
                }
                bean = new SpeclSrvCompatibleSetBean();
                SpeclSrvCompatibleSetPK pk = bean.ejbFindByPrimaryKey(p1);
                SpeclSrvCompatibleSetStub speclSrvCompatibleSetStub = new SpeclSrvCompatibleSetStub((EJBHome)this, pk);
                return speclSrvCompatibleSetStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "findAll", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompatibleSetStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "findAll", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompatibleSetStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "findByPrimaryKeys", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompatibleSetStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "findByPrimaryKeys", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvCompatibleSetStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(SpeclSrvCompatibleSetPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvCompatibleSetBean bean;
                ee.enterMethod("SpeclSrvCompatibleSetHome", "findInCache", true);
                if (cache != null && (bean = (SpeclSrvCompatibleSetBean)cache.get(p1)) != null) {
                    Vector<SpeclSrvCompatibleSetPK> v = new Vector<SpeclSrvCompatibleSetPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), SpeclSrvCompatibleSetStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(SpeclSrvCompatibleSetPK p1, SpeclSrvCompatibleSetData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "onCreate", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(SpeclSrvCompatibleSetPK p1, SpeclSrvCompatibleSetData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "onUpdate", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(SpeclSrvCompatibleSetPK p1, SpeclSrvCompatibleSetData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "onRemove", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "listBeansInCache", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "listLocks", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "unlock", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "getDataNoLock", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "getLockData", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "getMaxCacheSize", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvCompatibleSetHome", "updateMaxCacheSize", true);
            SpeclSrvCompatibleSetBean bean = new SpeclSrvCompatibleSetBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new SpeclSrvCompatibleSetStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
