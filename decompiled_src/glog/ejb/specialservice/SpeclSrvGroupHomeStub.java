/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBMetaData
 *  javax.ejb.FinderException
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 */
package glog.ejb.specialservice;

import glog.ejb.specialservice.SpeclSrvGroup;
import glog.ejb.specialservice.SpeclSrvGroupBean;
import glog.ejb.specialservice.SpeclSrvGroupHome;
import glog.ejb.specialservice.SpeclSrvGroupStub;
import glog.ejb.specialservice.db.SpeclSrvGroupData;
import glog.ejb.specialservice.db.SpeclSrvGroupPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.local.EntityEnterExit;
import glog.util.local.LocalEntityCache;
import glog.util.local.LocalEntityEnumeration;
import glog.util.remote.BeanData;
import glog.util.remote.LockData;
import java.rmi.RemoteException;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.EJBHome;
import javax.ejb.EJBMetaData;
import javax.ejb.FinderException;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;

public class SpeclSrvGroupHomeStub
implements SpeclSrvGroupHome {
    static LRUCache cache = LocalEntityCache.register("SpeclSrvGroup");

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public SpeclSrvGroup create(SpeclSrvGroupData p1) throws CreateException, RemoteException {
        SpeclSrvGroupStub speclSrvGroupStub;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "create", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbCreate(p1);
            if (cache != null) {
                cache.put(bean.getPK(), bean);
            }
            speclSrvGroupStub = new SpeclSrvGroupStub((EJBHome)this, bean);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("create", glex);
            }
        }
        ee.exitMethod();
        return speclSrvGroupStub;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public SpeclSrvGroup findByPrimaryKey(SpeclSrvGroupPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvGroupBean bean;
                ee.enterMethod("SpeclSrvGroupHome", "findByPrimaryKey", true);
                if (cache != null && (bean = (SpeclSrvGroupBean)cache.get(p1)) != null) {
                    SpeclSrvGroupStub speclSrvGroupStub = new SpeclSrvGroupStub((EJBHome)this, bean);
                    return speclSrvGroupStub;
                }
                bean = new SpeclSrvGroupBean();
                SpeclSrvGroupPK pk = bean.ejbFindByPrimaryKey(p1);
                SpeclSrvGroupStub speclSrvGroupStub = new SpeclSrvGroupStub((EJBHome)this, pk);
                return speclSrvGroupStub;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findByPrimaryKey", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll() throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "findAll", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Enumeration e = bean.ejbFindAll();
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvGroupStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findAll(Object p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "findAll", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Enumeration e = bean.ejbFindAll(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvGroupStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findAll", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "findByPrimaryKeys", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvGroupStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Enumeration findByPrimaryKeys(Vector p1, Object p2) throws FinderException, RemoteException {
        LocalEntityEnumeration localEntityEnumeration;
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "findByPrimaryKeys", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Enumeration e = bean.ejbFindByPrimaryKeys(p1, p2);
            localEntityEnumeration = new LocalEntityEnumeration(e, SpeclSrvGroupStub.class, this);
        }
        catch (Throwable throwable) {
            try {
                ee.exitMethod();
                throw throwable;
            }
            catch (GLException glex) {
                throw new RemoteException("findByPrimaryKeys", glex);
            }
        }
        ee.exitMethod();
        return localEntityEnumeration;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public Enumeration findInCache(SpeclSrvGroupPK p1) throws FinderException, RemoteException {
        try {
            EntityEnterExit ee = new EntityEnterExit();
            try {
                SpeclSrvGroupBean bean;
                ee.enterMethod("SpeclSrvGroupHome", "findInCache", true);
                if (cache != null && (bean = (SpeclSrvGroupBean)cache.get(p1)) != null) {
                    Vector<SpeclSrvGroupPK> v = new Vector<SpeclSrvGroupPK>();
                    v.add(p1);
                    LocalEntityEnumeration localEntityEnumeration = new LocalEntityEnumeration(v.elements(), SpeclSrvGroupStub.class, this);
                    return localEntityEnumeration;
                }
                Enumeration enumeration = Functions.EMPTY_ENUMERATION;
                return enumeration;
            }
            finally {
                ee.exitMethod();
            }
        }
        catch (GLException glex) {
            throw new RemoteException("findInCache", glex);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onCreate(SpeclSrvGroupPK p1, SpeclSrvGroupData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "onCreate", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbHomeOnCreate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onUpdate(SpeclSrvGroupPK p1, SpeclSrvGroupData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "onUpdate", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbHomeOnUpdate(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void onRemove(SpeclSrvGroupPK p1, SpeclSrvGroupData p2) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "onRemove", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbHomeOnRemove(p1, p2);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listBeansInCache() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "listBeansInCache", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Collection collection = bean.ejbHomeListBeansInCache();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public Collection listLocks() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "listLocks", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            Collection collection = bean.ejbHomeListLocks();
            return collection;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void unlock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "unlock", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbHomeUnlock(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public BeanData getDataNoLock(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "getDataNoLock", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            BeanData beanData = bean.ejbHomeGetDataNoLock(p1);
            return beanData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public LockData getLockData(Pk p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "getLockData", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            LockData lockData = bean.ejbHomeGetLockData(p1);
            return lockData;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public int getMaxCacheSize() throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "getMaxCacheSize", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            int n = bean.ejbHomeGetMaxCacheSize();
            return n;
        }
        finally {
            ee.exitMethod();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void updateMaxCacheSize(int p1) throws GLException, RemoteException {
        EntityEnterExit ee = new EntityEnterExit();
        try {
            ee.enterMethod("SpeclSrvGroupHome", "updateMaxCacheSize", true);
            SpeclSrvGroupBean bean = new SpeclSrvGroupBean();
            bean.ejbHomeUpdateMaxCacheSize(p1);
        }
        finally {
            ee.exitMethod();
        }
    }

    public void remove(Object o) throws RemoteException, RemoveException {
        new SpeclSrvGroupStub((EJBHome)this, (Pk)o).remove();
    }

    public EJBMetaData getEJBMetaData() {
        return null;
    }

    public HomeHandle getHomeHandle() {
        return null;
    }

    public void remove(Handle handle) {
    }
}
