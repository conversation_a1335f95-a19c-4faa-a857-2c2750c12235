/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.Fk;

static final class SscsSsJoinFKList.1
implements Fk {
    SscsSsJoinFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.specialservice.db";
    }

    @Override
    public String getPkDataClass() {
        return "SscsSsJoinData";
    }

    @Override
    public String getPkField() {
        return "speclSrvCompSetGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.specialservice.db";
    }

    @Override
    public String getFkDataClass() {
        return "SpeclSrvCompatibleSetData";
    }

    @Override
    public String getFkDataField() {
        return "speclSrvCompSetGid";
    }
}
