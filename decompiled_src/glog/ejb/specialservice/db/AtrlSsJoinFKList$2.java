/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.Fk;

static final class AtrlSsJoinFKList.2
implements Fk {
    AtrlSsJoinFKList.2() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.specialservice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AtrlSsJoinData";
    }

    @Override
    public String getPkField() {
        return "specialServiceGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.reference.db";
    }

    @Override
    public String getFkDataClass() {
        return "SpecialServiceData";
    }

    @Override
    public String getFkDataField() {
        return "specialServiceGid";
    }
}
