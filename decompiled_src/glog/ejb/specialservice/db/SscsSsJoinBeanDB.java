/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.specialservice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.specialservice.db.SscsSsJoinColumns;
import glog.ejb.specialservice.db.SscsSsJoinData;
import glog.ejb.specialservice.db.SscsSsJoinPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class SscsSsJoinBeanDB
extends BeanManagedEntityBean {
    public Object specialServiceGid;
    public Object objectType;
    public Object speclSrvCompSetGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient SscsSsJoinPK pk;
    protected transient SscsSsJoinData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SscsSsJoinBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static SscsSsJoinPK.Callback theCall = new SscsSsJoinPK.Callback();

    public SscsSsJoinBeanDB() {
        super(false);
    }

    public SscsSsJoinPK ejbCreate(SscsSsJoinData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(SscsSsJoinData data) throws CreateException {
        this.ejbPostCreator();
    }

    public SscsSsJoinPK ejbFindByPrimaryKey(SscsSsJoinPK pk) throws FinderException {
        return (SscsSsJoinPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(SscsSsJoinPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<SscsSsJoinPK> v = new Vector<SscsSsJoinPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(SscsSsJoinPK pk, SscsSsJoinData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(SscsSsJoinPK pk, SscsSsJoinData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(SscsSsJoinPK pk, SscsSsJoinData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (SscsSsJoinPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(SscsSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(SscsSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(SscsSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(SscsSsJoinColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        SscsSsJoinPK pk = (SscsSsJoinPK)genericPk;
        this.objectType = pk.objectType;
        this.specialServiceGid = pk.specialServiceGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        SscsSsJoinPK pk = new SscsSsJoinPK(0, this.objectType, this.specialServiceGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.SscsSsJoin";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public SscsSsJoinData getData() throws GLException {
        try {
            SscsSsJoinData retval = new SscsSsJoinData();
            retval.getFromBean(this, SscsSsJoinColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(SscsSsJoinData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(SscsSsJoinData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            SscsSsJoinData oldData = modified ? this.getData() : null;
            data.setToBean(this, SscsSsJoinColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return SscsSsJoinData.class;
    }

    @Override
    public Class getPkClass() {
        return SscsSsJoinPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getSpecialServiceGid() throws GLException {
        try {
            return (String)SscsSsJoinColumns.specialServiceGid.convertFromDB(this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpecialServiceGid(String specialServiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.specialServiceGid;
            SscsSsJoinData data = this.getData();
            this.specialServiceGid = SscsSsJoinColumns.specialServiceGid.convertToDB(specialServiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "specialServiceGid", String.class, oldValue, this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getObjectType() throws GLException {
        try {
            return (String)SscsSsJoinColumns.objectType.convertFromDB(this.objectType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setObjectType(String objectType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.objectType;
            SscsSsJoinData data = this.getData();
            this.objectType = SscsSsJoinColumns.objectType.convertToDB(objectType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "objectType", String.class, oldValue, this.objectType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSpeclSrvCompSetGid() throws GLException {
        try {
            return (String)SscsSsJoinColumns.speclSrvCompSetGid.convertFromDB(this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvCompSetGid(String speclSrvCompSetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvCompSetGid;
            SscsSsJoinData data = this.getData();
            this.speclSrvCompSetGid = SscsSsJoinColumns.speclSrvCompSetGid.convertToDB(speclSrvCompSetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvCompSetGid", String.class, oldValue, this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)SscsSsJoinColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            SscsSsJoinData data = this.getData();
            this.domainName = SscsSsJoinColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
