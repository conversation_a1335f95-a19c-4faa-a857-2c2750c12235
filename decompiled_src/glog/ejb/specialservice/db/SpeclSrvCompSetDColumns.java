/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.SqlColumn;

public class SpeclSrvCompSetDColumns {
    public static SqlColumn speclSrvCompSetGid = new SqlColumn(String.class, "specl_srv_comp_set_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn specialServiceGid = new SqlColumn(String.class, "special_service_gid", 101, 0, 1, null, 1, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 2, null);
}
