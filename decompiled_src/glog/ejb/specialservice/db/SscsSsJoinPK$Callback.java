/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SscsSsJoinPK;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

public static class SscsSsJoinPK.Callback
implements PkCallback {
    @Override
    public final Class<? extends Pk> getPKClass() {
        return SscsSsJoinPK.class;
    }

    @Override
    public final String getEntity() {
        return "SscsSsJoin";
    }

    @Override
    public final String getTableName() {
        return "sscs_ss_join";
    }

    @Override
    public final String[] getSelectList() {
        return new String[]{"object_type", "special_service_gid"};
    }

    @Override
    public final FkSpec[] getForeignKeySpecs() {
        return new FkSpec[0];
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
        return this.getPk(q, getFks, 0, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
        return this.getPk(q, getFks, 0, transaction);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
        return this.getPk(q, getFks, pkOffset, null);
    }

    @Override
    public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
        SscsSsJoinPK result = new SscsSsJoinPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
        return result;
    }
}
