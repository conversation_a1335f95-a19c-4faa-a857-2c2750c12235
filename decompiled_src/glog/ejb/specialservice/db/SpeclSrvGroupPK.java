/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvGroupColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class SpeclSrvGroupPK
extends Pk {
    public Object speclSrvGroupGid;
    public transient Object speclSrvGroupXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public SpeclSrvGroupPK() {
    }

    public SpeclSrvGroupPK(String speclSrvGroupGid) {
        this.speclSrvGroupGid = this.notNull(SpeclSrvGroupColumns.speclSrvGroupGid.convertToDB(speclSrvGroupGid), "speclSrvGroupGid");
    }

    public SpeclSrvGroupPK(String domainName, String speclSrvGroupXid) {
        this.domainName = domainName;
        this.speclSrvGroupXid = speclSrvGroupXid;
        this.speclSrvGroupGid = SpeclSrvGroupPK.concatForGid(domainName, speclSrvGroupXid);
    }

    public SpeclSrvGroupPK(int dummy, Object speclSrvGroupGid) {
        this(dummy, speclSrvGroupGid, null);
    }

    public SpeclSrvGroupPK(int dummy, Object speclSrvGroupGid, Object transaction) {
        this.speclSrvGroupGid = speclSrvGroupGid;
        this.transaction = transaction;
    }

    public SpeclSrvGroupPK(SpeclSrvGroupPK otherPk, Object transaction) {
        this.speclSrvGroupGid = otherPk.speclSrvGroupGid;
        this.speclSrvGroupXid = otherPk.speclSrvGroupXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.speclSrvGroupGid != null ? String.valueOf(this.speclSrvGroupGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.speclSrvGroupGid != null ? String.valueOf(this.speclSrvGroupGid) : "";
    }

    public static SpeclSrvGroupPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new SpeclSrvGroupPK(gids[0]) : null;
    }

    public static SpeclSrvGroupPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new SpeclSrvGroupPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.speclSrvGroupGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof SpeclSrvGroupPK)) {
            return false;
        }
        SpeclSrvGroupPK otherPk = (SpeclSrvGroupPK)other;
        return Functions.equals(otherPk.speclSrvGroupGid, this.speclSrvGroupGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.specialservice.SpeclSrvGroupHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.speclSrvGroupGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return SpeclSrvGroupColumns.speclSrvGroupGid.convertFromDB(this.speclSrvGroupGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static SpeclSrvGroupPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("SpeclSrvGroupPK requires a non-null XID to generate a GID");
            }
            SpeclSrvGroupPK speclSrvGroupPK = new SpeclSrvGroupPK(domain, xid);
            return speclSrvGroupPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static SpeclSrvGroupPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            SpeclSrvGroupPK speclSrvGroupPK = SpeclSrvGroupPK.newPK(domainName, xid, connection);
            return speclSrvGroupPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return SpeclSrvGroupPK.class;
        }

        @Override
        public final String getEntity() {
            return "SpeclSrvGroup";
        }

        @Override
        public final String getTableName() {
            return "specl_srv_group";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"specl_srv_group_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            SpeclSrvGroupPK result = new SpeclSrvGroupPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
