/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SscsSsJoinData;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import java.rmi.RemoteException;
import java.util.Map;
import java.util.Vector;
import javax.ejb.EJBObject;

public interface SscsSsJoinRemoteDB
extends EJBObject {
    public SscsSsJoinData getData() throws RemoteException, GLException;

    public void setData(SscsSsJoinData var1) throws RemoteException, GLException;

    public void setLinks(Vector var1, Vector var2) throws RemoteException, GLException;

    public Pk getPK() throws RemoteException;

    public void markForCacheRemove() throws RemoteException;

    public void reload() throws <PERSON>moteException, GLException;

    public void unload() throws <PERSON>moteException, GLException;

    public Map verify() throws <PERSON>moteException, GLException;

    public String getSpecialServiceGid() throws RemoteException, GLException;

    public void setSpecialServiceGid(String var1) throws RemoteException, GLException;

    public String getObjectType() throws RemoteException, GLException;

    public void setObjectType(String var1) throws RemoteException, GLException;

    public String getSpeclSrvCompSetGid() throws RemoteException, GLException;

    public void setSpeclSrvCompSetGid(String var1) throws RemoteException, GLException;

    public String getDomainName() throws RemoteException, GLException;

    public void setDomainName(String var1) throws RemoteException, GLException;
}
