/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.Fk;
import glog.util.jdbc.FkList;
import java.io.Serializable;
import java.util.Enumeration;
import java.util.Hashtable;

public class SscsSsJoinFKList
implements FkList,
Serializable {
    private static Hashtable fks = new Hashtable();

    @Override
    public int getFkCount() {
        return fks.size();
    }

    @Override
    public Fk getFk(String fieldName) {
        return (Fk)fks.get(fieldName);
    }

    @Override
    public Enumeration getFks() {
        return fks.elements();
    }

    static {
        fks.put("speclSrvCompSetGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.specialservice.db";
            }

            @Override
            public String getPkDataClass() {
                return "SscsSsJoinData";
            }

            @Override
            public String getPkField() {
                return "speclSrvCompSetGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.specialservice.db";
            }

            @Override
            public String getFkDataClass() {
                return "SpeclSrvCompatibleSetData";
            }

            @Override
            public String getFkDataField() {
                return "speclSrvCompSetGid";
            }
        });
        fks.put("specialServiceGid", new Fk(){

            @Override
            public String getPkDataClassPackage() {
                return "glog.ejb.specialservice.db";
            }

            @Override
            public String getPkDataClass() {
                return "SscsSsJoinData";
            }

            @Override
            public String getPkField() {
                return "specialServiceGid";
            }

            @Override
            public String getFkDataClassPackage() {
                return "glog.ejb.reference.db";
            }

            @Override
            public String getFkDataClass() {
                return "SpecialServiceData";
            }

            @Override
            public String getFkDataField() {
                return "specialServiceGid";
            }
        });
    }
}
