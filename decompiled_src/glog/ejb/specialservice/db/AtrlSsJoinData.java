/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.AtrlSsJoinPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class AtrlSsJoinData
extends BeanData {
    public String specialServiceGid;
    public String objectType;
    public String attributeRuleGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AtrlSsJoinData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field specialServiceGidField = beanDataFields[0];
    public static Field objectTypeField = beanDataFields[1];
    public static Field attributeRuleGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public AtrlSsJoinData() {
    }

    public AtrlSsJoinData(AtrlSsJoinData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getAtrlSsJoinPK();
    }

    @Legacy
    public AtrlSsJoinPK getAtrlSsJoinPK() {
        if (this.objectType == null) {
            return null;
        }
        if (this.specialServiceGid == null) {
            return null;
        }
        return new AtrlSsJoinPK(this.objectType, this.specialServiceGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setAtrlSsJoinPK((AtrlSsJoinPK)pk);
    }

    @Legacy
    public void setAtrlSsJoinPK(AtrlSsJoinPK pk) {
        this.objectType = (String)pk.getAppValue(0);
        this.specialServiceGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.specialservice.gen.AtrlSsJoinQueryGen";
    }

    public static AtrlSsJoinData load(Connection conn, AtrlSsJoinPK pk) throws GLException {
        return (AtrlSsJoinData)AtrlSsJoinData.load(conn, pk, AtrlSsJoinData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return AtrlSsJoinData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return AtrlSsJoinData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return AtrlSsJoinData.load(conn, whereClause, prepareArguments, fetchSize, AtrlSsJoinData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return AtrlSsJoinData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return AtrlSsJoinData.load(conn, fromWhere, alias, prepareArguments, fetchSize, AtrlSsJoinData.class);
    }
}
