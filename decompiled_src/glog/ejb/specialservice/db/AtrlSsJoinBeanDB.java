/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.specialservice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.specialservice.db.AtrlSsJoinColumns;
import glog.ejb.specialservice.db.AtrlSsJoinData;
import glog.ejb.specialservice.db.AtrlSsJoinPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class AtrlSsJoinBeanDB
extends BeanManagedEntityBean {
    public Object specialServiceGid;
    public Object objectType;
    public Object attributeRuleGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient AtrlSsJoinPK pk;
    protected transient AtrlSsJoinData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(AtrlSsJoinBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static AtrlSsJoinPK.Callback theCall = new AtrlSsJoinPK.Callback();

    public AtrlSsJoinBeanDB() {
        super(false);
    }

    public AtrlSsJoinPK ejbCreate(AtrlSsJoinData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(AtrlSsJoinData data) throws CreateException {
        this.ejbPostCreator();
    }

    public AtrlSsJoinPK ejbFindByPrimaryKey(AtrlSsJoinPK pk) throws FinderException {
        return (AtrlSsJoinPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(AtrlSsJoinPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<AtrlSsJoinPK> v = new Vector<AtrlSsJoinPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(AtrlSsJoinPK pk, AtrlSsJoinData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(AtrlSsJoinPK pk, AtrlSsJoinData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(AtrlSsJoinPK pk, AtrlSsJoinData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (AtrlSsJoinPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(AtrlSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(AtrlSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(AtrlSsJoinColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(AtrlSsJoinColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        AtrlSsJoinPK pk = (AtrlSsJoinPK)genericPk;
        this.objectType = pk.objectType;
        this.specialServiceGid = pk.specialServiceGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        AtrlSsJoinPK pk = new AtrlSsJoinPK(0, this.objectType, this.specialServiceGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.AtrlSsJoin";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public AtrlSsJoinData getData() throws GLException {
        try {
            AtrlSsJoinData retval = new AtrlSsJoinData();
            retval.getFromBean(this, AtrlSsJoinColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(AtrlSsJoinData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(AtrlSsJoinData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            AtrlSsJoinData oldData = modified ? this.getData() : null;
            data.setToBean(this, AtrlSsJoinColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return AtrlSsJoinData.class;
    }

    @Override
    public Class getPkClass() {
        return AtrlSsJoinPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getSpecialServiceGid() throws GLException {
        try {
            return (String)AtrlSsJoinColumns.specialServiceGid.convertFromDB(this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpecialServiceGid(String specialServiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.specialServiceGid;
            AtrlSsJoinData data = this.getData();
            this.specialServiceGid = AtrlSsJoinColumns.specialServiceGid.convertToDB(specialServiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "specialServiceGid", String.class, oldValue, this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getObjectType() throws GLException {
        try {
            return (String)AtrlSsJoinColumns.objectType.convertFromDB(this.objectType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setObjectType(String objectType) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.objectType;
            AtrlSsJoinData data = this.getData();
            this.objectType = AtrlSsJoinColumns.objectType.convertToDB(objectType);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "objectType", String.class, oldValue, this.objectType);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getAttributeRuleGid() throws GLException {
        try {
            return (String)AtrlSsJoinColumns.attributeRuleGid.convertFromDB(this.attributeRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setAttributeRuleGid(String attributeRuleGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.attributeRuleGid;
            AtrlSsJoinData data = this.getData();
            this.attributeRuleGid = AtrlSsJoinColumns.attributeRuleGid.convertToDB(attributeRuleGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "attributeRuleGid", String.class, oldValue, this.attributeRuleGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)AtrlSsJoinColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            AtrlSsJoinData data = this.getData();
            this.domainName = AtrlSsJoinColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
