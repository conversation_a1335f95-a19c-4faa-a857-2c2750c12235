/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvCompSetDColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class SpeclSrvCompSetDPK
extends Pk {
    public Object specialServiceGid;
    public Object speclSrvCompSetGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public SpeclSrvCompSetDPK() {
    }

    public SpeclSrvCompSetDPK(String specialServiceGid, String speclSrvCompSetGid) {
        this.specialServiceGid = this.notNull(SpeclSrvCompSetDColumns.specialServiceGid.convertToDB(specialServiceGid), "specialServiceGid");
        this.speclSrvCompSetGid = this.notNull(SpeclSrvCompSetDColumns.speclSrvCompSetGid.convertToDB(speclSrvCompSetGid), "speclSrvCompSetGid");
    }

    public SpeclSrvCompSetDPK(int dummy, Object specialServiceGid, Object speclSrvCompSetGid) {
        this(dummy, specialServiceGid, speclSrvCompSetGid, null);
    }

    public SpeclSrvCompSetDPK(int dummy, Object specialServiceGid, Object speclSrvCompSetGid, Object transaction) {
        this.specialServiceGid = specialServiceGid;
        this.speclSrvCompSetGid = speclSrvCompSetGid;
        this.transaction = transaction;
    }

    public SpeclSrvCompSetDPK(SpeclSrvCompSetDPK otherPk, Object transaction) {
        this.specialServiceGid = otherPk.specialServiceGid;
        this.speclSrvCompSetGid = otherPk.speclSrvCompSetGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.specialServiceGid != null ? String.valueOf(this.specialServiceGid) : "") + " " + (this.speclSrvCompSetGid != null ? String.valueOf(this.speclSrvCompSetGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.specialServiceGid != null ? String.valueOf(this.specialServiceGid) : "") + "|" + (this.speclSrvCompSetGid != null ? String.valueOf(this.speclSrvCompSetGid) : "");
    }

    public static SpeclSrvCompSetDPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new SpeclSrvCompSetDPK(gids[0], gids[1]) : null;
    }

    public static SpeclSrvCompSetDPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new SpeclSrvCompSetDPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.specialServiceGid.hashCode() + this.speclSrvCompSetGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof SpeclSrvCompSetDPK)) {
            return false;
        }
        SpeclSrvCompSetDPK otherPk = (SpeclSrvCompSetDPK)other;
        return Functions.equals(otherPk.specialServiceGid, this.specialServiceGid) && Functions.equals(otherPk.speclSrvCompSetGid, this.speclSrvCompSetGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.specialservice.SpeclSrvCompSetDHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.specialServiceGid;
            }
            case 1: {
                return this.speclSrvCompSetGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return SpeclSrvCompSetDColumns.specialServiceGid.convertFromDB(this.specialServiceGid);
            }
            case 1: {
                return SpeclSrvCompSetDColumns.speclSrvCompSetGid.convertFromDB(this.speclSrvCompSetGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return SpeclSrvCompSetDPK.class;
        }

        @Override
        public final String getEntity() {
            return "SpeclSrvCompSetD";
        }

        @Override
        public final String getTableName() {
            return "specl_srv_comp_set_d";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"special_service_gid", "specl_srv_comp_set_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            SpeclSrvCompSetDPK result = new SpeclSrvCompSetDPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
