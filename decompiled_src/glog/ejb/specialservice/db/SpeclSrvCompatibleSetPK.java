/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvCompatibleSetColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.datasource.DataFunction;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;

@AssertFieldsOnly
public class SpeclSrvCompatibleSetPK
extends Pk {
    public Object speclSrvCompSetGid;
    public transient Object speclSrvCompSetXid;
    public transient Object domainName;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public SpeclSrvCompatibleSetPK() {
    }

    public SpeclSrvCompatibleSetPK(String speclSrvCompSetGid) {
        this.speclSrvCompSetGid = this.notNull(SpeclSrvCompatibleSetColumns.speclSrvCompSetGid.convertToDB(speclSrvCompSetGid), "speclSrvCompSetGid");
    }

    public SpeclSrvCompatibleSetPK(String domainName, String speclSrvCompSetXid) {
        this.domainName = domainName;
        this.speclSrvCompSetXid = speclSrvCompSetXid;
        this.speclSrvCompSetGid = SpeclSrvCompatibleSetPK.concatForGid(domainName, speclSrvCompSetXid);
    }

    public SpeclSrvCompatibleSetPK(int dummy, Object speclSrvCompSetGid) {
        this(dummy, speclSrvCompSetGid, null);
    }

    public SpeclSrvCompatibleSetPK(int dummy, Object speclSrvCompSetGid, Object transaction) {
        this.speclSrvCompSetGid = speclSrvCompSetGid;
        this.transaction = transaction;
    }

    public SpeclSrvCompatibleSetPK(SpeclSrvCompatibleSetPK otherPk, Object transaction) {
        this.speclSrvCompSetGid = otherPk.speclSrvCompSetGid;
        this.speclSrvCompSetXid = otherPk.speclSrvCompSetXid;
        this.domainName = otherPk.domainName;
        this.transaction = transaction;
    }

    public final String toString() {
        return this.speclSrvCompSetGid != null ? String.valueOf(this.speclSrvCompSetGid) : "";
    }

    @Override
    public final String toNewInstanceString() {
        return this.speclSrvCompSetGid != null ? String.valueOf(this.speclSrvCompSetGid) : "";
    }

    public static SpeclSrvCompatibleSetPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new SpeclSrvCompatibleSetPK(gids[0]) : null;
    }

    public static SpeclSrvCompatibleSetPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new SpeclSrvCompatibleSetPK(gids[0]) : null;
    }

    public int hashCode() {
        return this.speclSrvCompSetGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof SpeclSrvCompatibleSetPK)) {
            return false;
        }
        SpeclSrvCompatibleSetPK otherPk = (SpeclSrvCompatibleSetPK)other;
        return Functions.equals(otherPk.speclSrvCompSetGid, this.speclSrvCompSetGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.specialservice.SpeclSrvCompatibleSetHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.speclSrvCompSetGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return SpeclSrvCompatibleSetColumns.speclSrvCompSetGid.convertFromDB(this.speclSrvCompSetGid);
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static SpeclSrvCompatibleSetPK newPK(String domain, String xid, T2SharedConnection conn) throws GLException {
        try {
            conn.open();
            if (xid == null) {
                throw new Error("SpeclSrvCompatibleSetPK requires a non-null XID to generate a GID");
            }
            SpeclSrvCompatibleSetPK speclSrvCompatibleSetPK = new SpeclSrvCompatibleSetPK(domain, xid);
            return speclSrvCompatibleSetPK;
        }
        finally {
            conn.close();
        }
    }

    private static String concatForGid(String domain, String xid) {
        return domain.equals("PUBLIC") ? xid : domain + "." + xid;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static SpeclSrvCompatibleSetPK newPK(String domainName, String xid) throws GLException {
        try (T2SharedConnection connection = new T2SharedConnection(DataFunction.EJB);){
            connection.open();
            SpeclSrvCompatibleSetPK speclSrvCompatibleSetPK = SpeclSrvCompatibleSetPK.newPK(domainName, xid, connection);
            return speclSrvCompatibleSetPK;
        }
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return SpeclSrvCompatibleSetPK.class;
        }

        @Override
        public final String getEntity() {
            return "SpeclSrvCompatibleSet";
        }

        @Override
        public final String getTableName() {
            return "specl_srv_compatible_set";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"specl_srv_comp_set_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            SpeclSrvCompatibleSetPK result = new SpeclSrvCompatibleSetPK(0, q.getObject(pkOffset + 0 + 1), transaction);
            return result;
        }
    }
}
