/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.specialservice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.specialservice.db.SpeclSrvCompSetDColumns;
import glog.ejb.specialservice.db.SpeclSrvCompSetDData;
import glog.ejb.specialservice.db.SpeclSrvCompSetDPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class SpeclSrvCompSetDBeanDB
extends BeanManagedEntityBean {
    public Object speclSrvCompSetGid;
    public Object specialServiceGid;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient SpeclSrvCompSetDPK pk;
    protected transient SpeclSrvCompSetDData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvCompSetDBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static SpeclSrvCompSetDPK.Callback theCall = new SpeclSrvCompSetDPK.Callback();

    public SpeclSrvCompSetDBeanDB() {
        super(false);
    }

    public SpeclSrvCompSetDPK ejbCreate(SpeclSrvCompSetDData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    public void ejbPostCreate(SpeclSrvCompSetDData data) throws CreateException {
        this.ejbPostCreator();
    }

    public SpeclSrvCompSetDPK ejbFindByPrimaryKey(SpeclSrvCompSetDPK pk) throws FinderException {
        return (SpeclSrvCompSetDPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(SpeclSrvCompSetDPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<SpeclSrvCompSetDPK> v = new Vector<SpeclSrvCompSetDPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(SpeclSrvCompSetDPK pk, SpeclSrvCompSetDData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(SpeclSrvCompSetDPK pk, SpeclSrvCompSetDData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(SpeclSrvCompSetDPK pk, SpeclSrvCompSetDData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = (SpeclSrvCompSetDPK)this.getPK();
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(SpeclSrvCompSetDColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(SpeclSrvCompSetDColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(SpeclSrvCompSetDColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(SpeclSrvCompSetDColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        SpeclSrvCompSetDPK pk = (SpeclSrvCompSetDPK)genericPk;
        this.specialServiceGid = pk.specialServiceGid;
        this.speclSrvCompSetGid = pk.speclSrvCompSetGid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        SpeclSrvCompSetDPK pk = new SpeclSrvCompSetDPK(0, this.specialServiceGid, this.speclSrvCompSetGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.SpeclSrvCompSetD";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public SpeclSrvCompSetDData getData() throws GLException {
        try {
            SpeclSrvCompSetDData retval = new SpeclSrvCompSetDData();
            retval.getFromBean(this, SpeclSrvCompSetDColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(SpeclSrvCompSetDData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(SpeclSrvCompSetDData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            SpeclSrvCompSetDData oldData = modified ? this.getData() : null;
            data.setToBean(this, SpeclSrvCompSetDColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return SpeclSrvCompSetDData.class;
    }

    @Override
    public Class getPkClass() {
        return SpeclSrvCompSetDPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getSpeclSrvCompSetGid() throws GLException {
        try {
            return (String)SpeclSrvCompSetDColumns.speclSrvCompSetGid.convertFromDB(this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvCompSetGid(String speclSrvCompSetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvCompSetGid;
            SpeclSrvCompSetDData data = this.getData();
            this.speclSrvCompSetGid = SpeclSrvCompSetDColumns.speclSrvCompSetGid.convertToDB(speclSrvCompSetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvCompSetGid", String.class, oldValue, this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSpecialServiceGid() throws GLException {
        try {
            return (String)SpeclSrvCompSetDColumns.specialServiceGid.convertFromDB(this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpecialServiceGid(String specialServiceGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.specialServiceGid;
            SpeclSrvCompSetDData data = this.getData();
            this.specialServiceGid = SpeclSrvCompSetDColumns.specialServiceGid.convertToDB(specialServiceGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "specialServiceGid", String.class, oldValue, this.specialServiceGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)SpeclSrvCompSetDColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            SpeclSrvCompSetDData data = this.getData();
            this.domainName = SpeclSrvCompSetDColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
