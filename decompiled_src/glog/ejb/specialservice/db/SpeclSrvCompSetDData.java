/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvCompSetDPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class SpeclSrvCompSetDData
extends BeanData {
    public String speclSrvCompSetGid;
    public String specialServiceGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvCompSetDData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field speclSrvCompSetGidField = beanDataFields[0];
    public static Field specialServiceGidField = beanDataFields[1];
    public static Field domainNameField = beanDataFields[2];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public SpeclSrvCompSetDData() {
    }

    public SpeclSrvCompSetDData(SpeclSrvCompSetDData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getSpeclSrvCompSetDPK();
    }

    @Legacy
    public SpeclSrvCompSetDPK getSpeclSrvCompSetDPK() {
        if (this.specialServiceGid == null) {
            return null;
        }
        if (this.speclSrvCompSetGid == null) {
            return null;
        }
        return new SpeclSrvCompSetDPK(this.specialServiceGid, this.speclSrvCompSetGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setSpeclSrvCompSetDPK((SpeclSrvCompSetDPK)pk);
    }

    @Legacy
    public void setSpeclSrvCompSetDPK(SpeclSrvCompSetDPK pk) {
        this.specialServiceGid = (String)pk.getAppValue(0);
        this.speclSrvCompSetGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.specialservice.gen.SpeclSrvCompSetDQueryGen";
    }

    public static SpeclSrvCompSetDData load(Connection conn, SpeclSrvCompSetDPK pk) throws GLException {
        return (SpeclSrvCompSetDData)SpeclSrvCompSetDData.load(conn, pk, SpeclSrvCompSetDData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return SpeclSrvCompSetDData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return SpeclSrvCompSetDData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvCompSetDData.load(conn, whereClause, prepareArguments, fetchSize, SpeclSrvCompSetDData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return SpeclSrvCompSetDData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvCompSetDData.load(conn, fromWhere, alias, prepareArguments, fetchSize, SpeclSrvCompSetDData.class);
    }
}
