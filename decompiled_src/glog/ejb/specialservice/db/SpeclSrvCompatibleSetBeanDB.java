/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.specialservice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.specialservice.db.SpeclSrvCompatibleSetColumns;
import glog.ejb.specialservice.db.SpeclSrvCompatibleSetData;
import glog.ejb.specialservice.db.SpeclSrvCompatibleSetPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class SpeclSrvCompatibleSetBeanDB
extends BeanManagedEntityBean {
    public Object speclSrvCompSetGid;
    public Object speclSrvCompSetXid;
    public Object description;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient SpeclSrvCompatibleSetPK pk;
    protected transient SpeclSrvCompatibleSetData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvCompatibleSetBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static SpeclSrvCompatibleSetPK.Callback theCall = new SpeclSrvCompatibleSetPK.Callback();

    public SpeclSrvCompatibleSetBeanDB() {
        super(false);
    }

    public SpeclSrvCompatibleSetPK ejbCreate(SpeclSrvCompatibleSetData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected SpeclSrvCompatibleSetPK newPK() throws GLException {
        return SpeclSrvCompatibleSetPK.newPK(this.getDomainName(), this.getSpeclSrvCompSetXid(), this.getConnection());
    }

    public void ejbPostCreate(SpeclSrvCompatibleSetData data) throws CreateException {
        this.ejbPostCreator();
    }

    public SpeclSrvCompatibleSetPK ejbFindByPrimaryKey(SpeclSrvCompatibleSetPK pk) throws FinderException {
        return (SpeclSrvCompatibleSetPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(SpeclSrvCompatibleSetPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<SpeclSrvCompatibleSetPK> v = new Vector<SpeclSrvCompatibleSetPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(SpeclSrvCompatibleSetPK pk, SpeclSrvCompatibleSetData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(SpeclSrvCompatibleSetPK pk, SpeclSrvCompatibleSetData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(SpeclSrvCompatibleSetPK pk, SpeclSrvCompatibleSetData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(SpeclSrvCompatibleSetColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(SpeclSrvCompatibleSetColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(SpeclSrvCompatibleSetColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(SpeclSrvCompatibleSetColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        SpeclSrvCompatibleSetPK pk = (SpeclSrvCompatibleSetPK)genericPk;
        this.speclSrvCompSetGid = pk.speclSrvCompSetGid;
        this.domainName = pk.domainName;
        this.speclSrvCompSetXid = pk.speclSrvCompSetXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        SpeclSrvCompatibleSetPK pk = new SpeclSrvCompatibleSetPK(0, this.speclSrvCompSetGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.SpeclSrvCompatibleSet";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public SpeclSrvCompatibleSetData getData() throws GLException {
        try {
            SpeclSrvCompatibleSetData retval = new SpeclSrvCompatibleSetData();
            retval.getFromBean(this, SpeclSrvCompatibleSetColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(SpeclSrvCompatibleSetData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(SpeclSrvCompatibleSetData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            SpeclSrvCompatibleSetData oldData = modified ? this.getData() : null;
            data.setToBean(this, SpeclSrvCompatibleSetColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return SpeclSrvCompatibleSetData.class;
    }

    @Override
    public Class getPkClass() {
        return SpeclSrvCompatibleSetPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getSpeclSrvCompSetGid() throws GLException {
        try {
            return (String)SpeclSrvCompatibleSetColumns.speclSrvCompSetGid.convertFromDB(this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvCompSetGid(String speclSrvCompSetGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvCompSetGid;
            SpeclSrvCompatibleSetData data = this.getData();
            this.speclSrvCompSetGid = SpeclSrvCompatibleSetColumns.speclSrvCompSetGid.convertToDB(speclSrvCompSetGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvCompSetGid", String.class, oldValue, this.speclSrvCompSetGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSpeclSrvCompSetXid() throws GLException {
        try {
            return (String)SpeclSrvCompatibleSetColumns.speclSrvCompSetXid.convertFromDB(this.speclSrvCompSetXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvCompSetXid(String speclSrvCompSetXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvCompSetXid;
            SpeclSrvCompatibleSetData data = this.getData();
            this.speclSrvCompSetXid = SpeclSrvCompatibleSetColumns.speclSrvCompSetXid.convertToDB(speclSrvCompSetXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvCompSetXid", String.class, oldValue, this.speclSrvCompSetXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)SpeclSrvCompatibleSetColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            SpeclSrvCompatibleSetData data = this.getData();
            this.description = SpeclSrvCompatibleSetColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)SpeclSrvCompatibleSetColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            SpeclSrvCompatibleSetData data = this.getData();
            this.domainName = SpeclSrvCompatibleSetColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
