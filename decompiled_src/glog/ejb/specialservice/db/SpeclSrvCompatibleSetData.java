/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvCompatibleSetPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class SpeclSrvCompatibleSetData
extends BeanData {
    public String speclSrvCompSetGid;
    public String speclSrvCompSetXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvCompatibleSetData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field speclSrvCompSetGidField = beanDataFields[0];
    public static Field speclSrvCompSetXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public SpeclSrvCompatibleSetData() {
    }

    public SpeclSrvCompatibleSetData(SpeclSrvCompatibleSetData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getSpeclSrvCompatibleSetPK();
    }

    @Legacy
    public SpeclSrvCompatibleSetPK getSpeclSrvCompatibleSetPK() {
        if (this.speclSrvCompSetGid == null) {
            return null;
        }
        return new SpeclSrvCompatibleSetPK(this.speclSrvCompSetGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setSpeclSrvCompatibleSetPK((SpeclSrvCompatibleSetPK)pk);
    }

    @Legacy
    public void setSpeclSrvCompatibleSetPK(SpeclSrvCompatibleSetPK pk) {
        this.speclSrvCompSetGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.specialservice.gen.SpeclSrvCompatibleSetQueryGen";
    }

    public static SpeclSrvCompatibleSetData load(Connection conn, SpeclSrvCompatibleSetPK pk) throws GLException {
        return (SpeclSrvCompatibleSetData)SpeclSrvCompatibleSetData.load(conn, pk, SpeclSrvCompatibleSetData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return SpeclSrvCompatibleSetData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return SpeclSrvCompatibleSetData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvCompatibleSetData.load(conn, whereClause, prepareArguments, fetchSize, SpeclSrvCompatibleSetData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return SpeclSrvCompatibleSetData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvCompatibleSetData.load(conn, fromWhere, alias, prepareArguments, fetchSize, SpeclSrvCompatibleSetData.class);
    }
}
