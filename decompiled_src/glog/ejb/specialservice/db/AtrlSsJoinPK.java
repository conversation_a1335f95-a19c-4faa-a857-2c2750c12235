/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.AtrlSsJoinColumns;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.FkSpec;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlQuery;

@AssertFieldsOnly
public class AtrlSsJoinPK
extends Pk {
    public Object objectType;
    public Object specialServiceGid;
    private static boolean homeClassInitialized;
    private static Class homeClass;
    private static PkCallback theCall;

    public AtrlSsJoinPK() {
    }

    public AtrlSsJoinPK(String objectType, String specialServiceGid) {
        this.objectType = this.notNull(AtrlSsJoinColumns.objectType.convertToDB(objectType), "objectType");
        this.specialServiceGid = this.notNull(AtrlSsJoinColumns.specialServiceGid.convertToDB(specialServiceGid), "specialServiceGid");
    }

    public AtrlSsJoinPK(int dummy, Object objectType, Object specialServiceGid) {
        this(dummy, objectType, specialServiceGid, null);
    }

    public AtrlSsJoinPK(int dummy, Object objectType, Object specialServiceGid, Object transaction) {
        this.objectType = objectType;
        this.specialServiceGid = specialServiceGid;
        this.transaction = transaction;
    }

    public AtrlSsJoinPK(AtrlSsJoinPK otherPk, Object transaction) {
        this.objectType = otherPk.objectType;
        this.specialServiceGid = otherPk.specialServiceGid;
        this.transaction = transaction;
    }

    public final String toString() {
        return (this.objectType != null ? String.valueOf(this.objectType) : "") + " " + (this.specialServiceGid != null ? String.valueOf(this.specialServiceGid) : "");
    }

    @Override
    public final String toNewInstanceString() {
        return (this.objectType != null ? String.valueOf(this.objectType) : "") + "|" + (this.specialServiceGid != null ? String.valueOf(this.specialServiceGid) : "");
    }

    public static AtrlSsJoinPK newInstanceFactory(String s) {
        String[] gids = s.split("\\|");
        return gids != null && gids.length > 0 ? new AtrlSsJoinPK(gids[0], gids[1]) : null;
    }

    public static AtrlSsJoinPK valueOf(String s) {
        String[] gids = Functions.splitGid(s);
        return gids != null && gids.length > 0 ? new AtrlSsJoinPK(gids[0], gids[1]) : null;
    }

    public int hashCode() {
        return this.objectType.hashCode() + this.specialServiceGid.hashCode();
    }

    public boolean equals(Object other) {
        if (other == null || !(other instanceof AtrlSsJoinPK)) {
            return false;
        }
        AtrlSsJoinPK otherPk = (AtrlSsJoinPK)other;
        return Functions.equals(otherPk.objectType, this.objectType) && Functions.equals(otherPk.specialServiceGid, this.specialServiceGid) && Functions.equals(otherPk.transaction, this.transaction);
    }

    @Override
    public Class getHomeInterface() {
        if (homeClassInitialized) {
            return homeClass;
        }
        try {
            homeClass = Class.forName("glog.ejb.specialservice.AtrlSsJoinHome");
        }
        catch (Exception exception) {
            // empty catch block
        }
        homeClassInitialized = true;
        return homeClass;
    }

    @Override
    public PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    @Legacy
    public final Object getTransaction() {
        return this.transaction;
    }

    @Legacy
    public final void setTransaction(Object transaction) {
        this.transaction = transaction;
    }

    @Override
    public final Object getDbValue(int index) {
        switch (index) {
            case 0: {
                return this.objectType;
            }
            case 1: {
                return this.specialServiceGid;
            }
        }
        return null;
    }

    @Override
    public final Object getAppValue(int index) {
        switch (index) {
            case 0: {
                return AtrlSsJoinColumns.objectType.convertFromDB(this.objectType);
            }
            case 1: {
                return AtrlSsJoinColumns.specialServiceGid.convertFromDB(this.specialServiceGid);
            }
        }
        return null;
    }

    static {
        theCall = new Callback();
    }

    public static class Callback
    implements PkCallback {
        @Override
        public final Class<? extends Pk> getPKClass() {
            return AtrlSsJoinPK.class;
        }

        @Override
        public final String getEntity() {
            return "AtrlSsJoin";
        }

        @Override
        public final String getTableName() {
            return "atrl_ss_join";
        }

        @Override
        public final String[] getSelectList() {
            return new String[]{"object_type", "special_service_gid"};
        }

        @Override
        public final FkSpec[] getForeignKeySpecs() {
            return new FkSpec[0];
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks) throws GLException {
            return this.getPk(q, getFks, 0, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, Object transaction) throws GLException {
            return this.getPk(q, getFks, 0, transaction);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset) throws GLException {
            return this.getPk(q, getFks, pkOffset, null);
        }

        @Override
        public Pk getPk(SqlQuery q, boolean getFks, int pkOffset, Object transaction) throws GLException {
            AtrlSsJoinPK result = new AtrlSsJoinPK(0, q.getObject(pkOffset + 0 + 1), q.getObject(pkOffset + 1 + 1), transaction);
            return result;
        }
    }
}
