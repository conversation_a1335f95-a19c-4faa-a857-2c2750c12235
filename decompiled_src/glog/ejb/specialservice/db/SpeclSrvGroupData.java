/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SpeclSrvGroupPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class SpeclSrvGroupData
extends BeanData {
    public String speclSrvGroupGid;
    public String speclSrvGroupXid;
    public String description;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvGroupData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field speclSrvGroupGidField = beanDataFields[0];
    public static Field speclSrvGroupXidField = beanDataFields[1];
    public static Field descriptionField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public SpeclSrvGroupData() {
    }

    public SpeclSrvGroupData(SpeclSrvGroupData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getSpeclSrvGroupPK();
    }

    @Legacy
    public SpeclSrvGroupPK getSpeclSrvGroupPK() {
        if (this.speclSrvGroupGid == null) {
            return null;
        }
        return new SpeclSrvGroupPK(this.speclSrvGroupGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setSpeclSrvGroupPK((SpeclSrvGroupPK)pk);
    }

    @Legacy
    public void setSpeclSrvGroupPK(SpeclSrvGroupPK pk) {
        this.speclSrvGroupGid = (String)pk.getAppValue(0);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.specialservice.gen.SpeclSrvGroupQueryGen";
    }

    public static SpeclSrvGroupData load(Connection conn, SpeclSrvGroupPK pk) throws GLException {
        return (SpeclSrvGroupData)SpeclSrvGroupData.load(conn, pk, SpeclSrvGroupData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return SpeclSrvGroupData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return SpeclSrvGroupData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvGroupData.load(conn, whereClause, prepareArguments, fetchSize, SpeclSrvGroupData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return SpeclSrvGroupData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return SpeclSrvGroupData.load(conn, fromWhere, alias, prepareArguments, fetchSize, SpeclSrvGroupData.class);
    }
}
