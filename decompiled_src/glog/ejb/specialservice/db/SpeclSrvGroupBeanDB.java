/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.FinderException
 */
package glog.ejb.specialservice.db;

import glog.database.security.SecurityUtil;
import glog.ejb.specialservice.db.SpeclSrvGroupColumns;
import glog.ejb.specialservice.db.SpeclSrvGroupData;
import glog.ejb.specialservice.db.SpeclSrvGroupPK;
import glog.server.compareengine.BeanDataProcessor;
import glog.util.Functions;
import glog.util.exception.CreateExceptionWrapper;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.jdbc.PkCallback;
import glog.util.jdbc.SqlStatement;
import glog.util.remote.BeanDataAccessor;
import glog.util.remote.BeanManagedEntityBean;
import glog.util.remote.PkAccessor;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;
import javax.ejb.CreateException;
import javax.ejb.FinderException;

public class SpeclSrvGroupBeanDB
extends BeanManagedEntityBean {
    public Object speclSrvGroupGid;
    public Object speclSrvGroupXid;
    public Object description;
    public Object domainName;
    private BeanDataProcessor beanDataProcessor = BeanDataProcessor.get();
    protected transient SpeclSrvGroupPK pk;
    protected transient SpeclSrvGroupData data;
    static Field[] beanFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SpeclSrvGroupBeanDB.class));
    static Set primaryKeyCache = Collections.synchronizedSet(new HashSet());
    private static SpeclSrvGroupPK.Callback theCall = new SpeclSrvGroupPK.Callback();

    public SpeclSrvGroupBeanDB() {
        super(false);
    }

    public SpeclSrvGroupPK ejbCreate(SpeclSrvGroupData data) throws CreateException {
        if (data.isEjbCreateSuppressed()) {
            return null;
        }
        try {
            this.reset();
            this.data = data;
            this.ejbCreator();
            this.beanDataProcessor.setDataInfo(this.pk, null, this.data, 0);
            return this.pk;
        }
        catch (Throwable t) {
            throw CreateExceptionWrapper.ejbFactory(t);
        }
    }

    protected SpeclSrvGroupPK newPK() throws GLException {
        return SpeclSrvGroupPK.newPK(this.getDomainName(), this.getSpeclSrvGroupXid(), this.getConnection());
    }

    public void ejbPostCreate(SpeclSrvGroupData data) throws CreateException {
        this.ejbPostCreator();
    }

    public SpeclSrvGroupPK ejbFindByPrimaryKey(SpeclSrvGroupPK pk) throws FinderException {
        return (SpeclSrvGroupPK)super.ejbFindByKey(pk, pk.getTransaction());
    }

    public Enumeration ejbFindInCache(SpeclSrvGroupPK pk) throws FinderException {
        if (primaryKeyCache.contains(pk)) {
            Vector<SpeclSrvGroupPK> v = new Vector<SpeclSrvGroupPK>();
            v.add(pk);
            return v.elements();
        }
        return Functions.EMPTY_ENUMERATION;
    }

    public void ejbHomeOnCreate(SpeclSrvGroupPK pk, SpeclSrvGroupData data) throws GLException {
        this.callOnCreate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnUpdate(SpeclSrvGroupPK pk, SpeclSrvGroupData data) throws GLException {
        this.callOnUpdate(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    public void ejbHomeOnRemove(SpeclSrvGroupPK pk, SpeclSrvGroupData data) throws GLException {
        this.callOnRemove(new PkAccessor(pk), new BeanDataAccessor(data));
    }

    @Override
    protected boolean preCreate() throws GLException {
        this.transaction = null;
        this.setData(this.data, false);
        this.pk = this.newPK();
        this.setPK(this.pk);
        return super.preCreate();
    }

    @Override
    protected SqlStatement getCreateStatement() throws GLException {
        return super.getCreateStatement(SpeclSrvGroupColumns.class);
    }

    @Override
    protected SqlStatement getLoadStatement() throws GLException {
        return super.getLoadStatement(SpeclSrvGroupColumns.class);
    }

    @Override
    protected SqlStatement getStoreStatement() throws GLException {
        return super.getStoreStatement(SpeclSrvGroupColumns.class);
    }

    @Override
    protected SqlStatement getRemoveStatement() throws GLException {
        return super.getRemoveStatement(SpeclSrvGroupColumns.class);
    }

    @Override
    protected PkCallback getPkCallback() {
        return theCall;
    }

    @Override
    public void setPK(Pk genericPk) {
        SpeclSrvGroupPK pk = (SpeclSrvGroupPK)genericPk;
        this.speclSrvGroupGid = pk.speclSrvGroupGid;
        this.domainName = pk.domainName;
        this.speclSrvGroupXid = pk.speclSrvGroupXid;
        this.transaction = pk.transaction;
    }

    @Override
    public Pk getPK() {
        SpeclSrvGroupPK pk = new SpeclSrvGroupPK(0, this.speclSrvGroupGid, this.transaction);
        return pk;
    }

    @Override
    public String getName() {
        return "ejb.SpeclSrvGroup";
    }

    @Override
    protected Field[] getBeanFields() {
        return beanFields;
    }

    @Override
    protected Set getPrimaryKeyCache() {
        return primaryKeyCache;
    }

    public SpeclSrvGroupData getData() throws GLException {
        try {
            SpeclSrvGroupData retval = new SpeclSrvGroupData();
            retval.getFromBean(this, SpeclSrvGroupColumns.class);
            return retval;
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setData(SpeclSrvGroupData data) throws GLException {
        try {
            this.setData(data, true);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setLinks(Vector pksToAdd, Vector pksToRemove) throws GLException {
    }

    protected void setData(SpeclSrvGroupData data, boolean modified) throws GLException {
        this.checkForReadOnly();
        try {
            if (data.domainName == null) {
                data.domainName = SecurityUtil.getCurrentDomain();
            }
            SpeclSrvGroupData oldData = modified ? this.getData() : null;
            data.setToBean(this, SpeclSrvGroupColumns.class);
            if (modified) {
                this.beanDataProcessor.setDataInfo(this.getPK(), oldData, data, 1);
            }
            if (modified) {
                this.setHeaderModified(true);
            }
            this.setModified(modified);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    @Override
    public Class getDataClass() {
        return SpeclSrvGroupData.class;
    }

    @Override
    public Class getPkClass() {
        return SpeclSrvGroupPK.class;
    }

    @Override
    protected boolean isRemote() {
        return this.isRemote((String)this.domainName);
    }

    @Override
    protected boolean preRemove() throws GLException {
        super.preRemove();
        this.beanDataProcessor.setDataInfo(this.getPK(), this.getData(), null, 2);
        this.removeDocuments();
        return true;
    }

    public String getSpeclSrvGroupGid() throws GLException {
        try {
            return (String)SpeclSrvGroupColumns.speclSrvGroupGid.convertFromDB(this.speclSrvGroupGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvGroupGid(String speclSrvGroupGid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvGroupGid;
            SpeclSrvGroupData data = this.getData();
            this.speclSrvGroupGid = SpeclSrvGroupColumns.speclSrvGroupGid.convertToDB(speclSrvGroupGid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvGroupGid", String.class, oldValue, this.speclSrvGroupGid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getSpeclSrvGroupXid() throws GLException {
        try {
            return (String)SpeclSrvGroupColumns.speclSrvGroupXid.convertFromDB(this.speclSrvGroupXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setSpeclSrvGroupXid(String speclSrvGroupXid) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.speclSrvGroupXid;
            SpeclSrvGroupData data = this.getData();
            this.speclSrvGroupXid = SpeclSrvGroupColumns.speclSrvGroupXid.convertToDB(speclSrvGroupXid);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "speclSrvGroupXid", String.class, oldValue, this.speclSrvGroupXid);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDescription() throws GLException {
        try {
            return (String)SpeclSrvGroupColumns.description.convertFromDB(this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDescription(String description) throws GLException {
        this.checkForReadOnly();
        try {
            Object oldValue = this.description;
            SpeclSrvGroupData data = this.getData();
            this.description = SpeclSrvGroupColumns.description.convertToDB(description);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "description", String.class, oldValue, this.description);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public String getDomainName() throws GLException {
        try {
            return (String)SpeclSrvGroupColumns.domainName.convertFromDB(this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }

    public void setDomainName(String domainName) throws GLException {
        this.checkForReadOnly();
        try {
            if (domainName == null) {
                domainName = SecurityUtil.getCurrentDomain();
            }
            Object oldValue = this.domainName;
            SpeclSrvGroupData data = this.getData();
            this.domainName = SpeclSrvGroupColumns.domainName.convertToDB(domainName);
            this.setHeaderModified(true);
            this.setModified(true);
            this.beanDataProcessor.setDataInfo(this.getPK(), data, 1, "domainName", String.class, oldValue, this.domainName);
        }
        catch (Throwable t) {
            throw GLException.ejbFactory(t);
        }
    }
}
