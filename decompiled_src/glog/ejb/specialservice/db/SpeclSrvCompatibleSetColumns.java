/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.SqlColumn;

public class SpeclSrvCompatibleSetColumns {
    public static SqlColumn speclSrvCompSetGid = new SqlColumn(String.class, "specl_srv_comp_set_gid", 101, 0, 1, null, 0, null);
    public static SqlColumn speclSrvCompSetXid = new SqlColumn(String.class, "specl_srv_comp_set_xid", 50, 0, 1, null, 1, null);
    public static SqlColumn description = new SqlColumn(String.class, "description", 50, 0, 0, null, 2, null);
    public static SqlColumn domainName = new SqlColumn(String.class, "domain_name", 50, 0, 1, null, 3, null);
}
