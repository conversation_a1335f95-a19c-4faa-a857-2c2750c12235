/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.util.jdbc.Fk;

static final class AtrlSsJoinFKList.1
implements Fk {
    AtrlSsJoinFKList.1() {
    }

    @Override
    public String getPkDataClassPackage() {
        return "glog.ejb.specialservice.db";
    }

    @Override
    public String getPkDataClass() {
        return "AtrlSsJoinData";
    }

    @Override
    public String getPkField() {
        return "attributeRuleGid";
    }

    @Override
    public String getFkDataClassPackage() {
        return "glog.ejb.agent.db";
    }

    @Override
    public String getFkDataClass() {
        return "AttributeRuleData";
    }

    @Override
    public String getFkDataField() {
        return "attributeRuleGid";
    }
}
