/*
 * Decompiled with CFR 0.152.
 */
package glog.ejb.specialservice.db;

import glog.ejb.specialservice.db.SscsSsJoinPK;
import glog.util.Functions;
import glog.util.data.AssertFieldsOnly;
import glog.util.data.Legacy;
import glog.util.exception.GLException;
import glog.util.jdbc.Pk;
import glog.util.remote.BeanData;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.util.List;
import java.util.Map;

@AssertFieldsOnly
public class SscsSsJoinData
extends BeanData {
    public String specialServiceGid;
    public String objectType;
    public String speclSrvCompSetGid;
    public String domainName;
    public static Field[] beanDataFields = Functions.getPublicMemberFields(Functions.getOrderedDeclaredFields(SscsSsJoinData.class));
    public static Map beanDataFieldMap = Functions.getFieldMap(beanDataFields);
    public static Field specialServiceGidField = beanDataFields[0];
    public static Field objectTypeField = beanDataFields[1];
    public static Field speclSrvCompSetGidField = beanDataFields[2];
    public static Field domainNameField = beanDataFields[3];

    @Override
    public Field[] getBeanDataFields() {
        return beanDataFields;
    }

    @Override
    public Map getBeanDataFieldMap() {
        return beanDataFieldMap;
    }

    public SscsSsJoinData() {
    }

    public SscsSsJoinData(SscsSsJoinData obj) {
        try {
            this.copy(obj);
        }
        catch (Throwable throwable) {
            // empty catch block
        }
    }

    @Override
    @Legacy
    public Pk getPk() {
        return this.getSscsSsJoinPK();
    }

    @Legacy
    public SscsSsJoinPK getSscsSsJoinPK() {
        if (this.objectType == null) {
            return null;
        }
        if (this.specialServiceGid == null) {
            return null;
        }
        return new SscsSsJoinPK(this.objectType, this.specialServiceGid);
    }

    @Override
    @Legacy
    public void setPk(Pk pk) {
        this.setSscsSsJoinPK((SscsSsJoinPK)pk);
    }

    @Legacy
    public void setSscsSsJoinPK(SscsSsJoinPK pk) {
        this.objectType = (String)pk.getAppValue(0);
        this.specialServiceGid = (String)pk.getAppValue(1);
    }

    @Override
    public String getQueryName() {
        return "glog.server.query.specialservice.gen.SscsSsJoinQueryGen";
    }

    public static SscsSsJoinData load(Connection conn, SscsSsJoinPK pk) throws GLException {
        return (SscsSsJoinData)SscsSsJoinData.load(conn, pk, SscsSsJoinData.class);
    }

    public static List load(Connection conn, String whereClause) throws GLException {
        return SscsSsJoinData.load(conn, whereClause, null);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments) throws GLException {
        return SscsSsJoinData.load(conn, whereClause, prepareArguments, 0);
    }

    public static List load(Connection conn, String whereClause, Object[] prepareArguments, int fetchSize) throws GLException {
        return SscsSsJoinData.load(conn, whereClause, prepareArguments, fetchSize, SscsSsJoinData.class);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments) throws GLException {
        return SscsSsJoinData.load(conn, fromWhere, alias, prepareArguments, 0);
    }

    public static List load(Connection conn, String fromWhere, String alias, Object[] prepareArguments, int fetchSize) throws GLException {
        return SscsSsJoinData.load(conn, fromWhere, alias, prepareArguments, fetchSize, SscsSsJoinData.class);
    }
}
