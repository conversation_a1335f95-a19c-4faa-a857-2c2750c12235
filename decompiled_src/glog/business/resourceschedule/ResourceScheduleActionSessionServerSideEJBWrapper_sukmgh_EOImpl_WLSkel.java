/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  weblogic.rmi.internal.Skeleton
 *  weblogic.rmi.spi.InboundRequest
 *  weblogic.rmi.spi.MsgInput
 *  weblogic.rmi.spi.OutboundResponse
 */
package glog.business.resourceschedule;

import glog.business.resourceschedule.ResourceScheduleActionSession;
import glog.util.LocalTimestamp;
import glog.webserver.umt.UserPreference;
import java.io.IOException;
import java.rmi.MarshalException;
import java.rmi.UnmarshalException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import weblogic.rmi.internal.Skeleton;
import weblogic.rmi.spi.InboundRequest;
import weblogic.rmi.spi.MsgInput;
import weblogic.rmi.spi.OutboundResponse;

public final class ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel
extends Skeleton {
    private static /* synthetic */ Class class$java$lang$Object;
    private static /* synthetic */ Class class$javax$ejb$EJBObject;
    private static /* synthetic */ Class class$java$util$HashMap;
    private static /* synthetic */ Class class$javax$ejb$EJBHome;
    private static /* synthetic */ Class class$javax$ejb$Handle;
    private static /* synthetic */ Class array$Ljava$lang$String;
    private static /* synthetic */ Class class$java$util$ArrayList;
    private static /* synthetic */ Class class$glog$util$LocalTimestamp;
    private static /* synthetic */ Class class$glog$webserver$umt$UserPreference;
    private static /* synthetic */ Class class$java$lang$String;
    private static /* synthetic */ Class array$Lglog$util$LocalTimestamp;

    public OutboundResponse invoke(int n, InboundRequest inboundRequest, OutboundResponse outboundResponse, Object object) throws Exception {
        switch (n) {
            case 0: {
                LocalTimestamp localTimestamp;
                LocalTimestamp localTimestamp2;
                String string;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    string = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    localTimestamp2 = (LocalTimestamp)msgInput.readObject(class$glog$util$LocalTimestamp == null ? (class$glog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.util.LocalTimestamp")) : class$glog$util$LocalTimestamp);
                    localTimestamp = (LocalTimestamp)msgInput.readObject(class$glog$util$LocalTimestamp == null ? (class$glog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.util.LocalTimestamp")) : class$glog$util$LocalTimestamp);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                HashMap<String, String> hashMap = ((ResourceScheduleActionSession)object).generateResSchInstance(string, localTimestamp2, localTimestamp);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(hashMap, class$java$util$HashMap == null ? (class$java$util$HashMap = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.util.HashMap")) : class$java$util$HashMap);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 1: {
                UserPreference userPreference;
                String[] stringArray;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    stringArray = (String[])msgInput.readObject(array$Ljava$lang$String == null ? (array$Ljava$lang$String = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("[Ljava.lang.String;")) : array$Ljava$lang$String);
                    userPreference = (UserPreference)msgInput.readObject(class$glog$webserver$umt$UserPreference == null ? (class$glog$webserver$umt$UserPreference = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.webserver.umt.UserPreference")) : class$glog$webserver$umt$UserPreference);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                ArrayList arrayList = ((ResourceScheduleActionSession)object).getAllResSchInstances(stringArray, userPreference);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)arrayList, class$java$util$ArrayList == null ? (class$java$util$ArrayList = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.util.ArrayList")) : class$java$util$ArrayList);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 2: {
                EJBHome eJBHome = ((EJBObject)object).getEJBHome();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)eJBHome, class$javax$ejb$EJBHome == null ? (class$javax$ejb$EJBHome = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("javax.ejb.EJBHome")) : class$javax$ejb$EJBHome);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 3: {
                LocalTimestamp localTimestamp;
                LocalTimestamp localTimestamp3;
                String string;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    string = (String)msgInput.readObject(class$java$lang$String == null ? (class$java$lang$String = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.lang.String")) : class$java$lang$String);
                    localTimestamp3 = (LocalTimestamp)msgInput.readObject(class$glog$util$LocalTimestamp == null ? (class$glog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.util.LocalTimestamp")) : class$glog$util$LocalTimestamp);
                    localTimestamp = (LocalTimestamp)msgInput.readObject(class$glog$util$LocalTimestamp == null ? (class$glog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.util.LocalTimestamp")) : class$glog$util$LocalTimestamp);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                HashMap<String, String> hashMap = ((ResourceScheduleActionSession)object).getExistingResSchInstances(string, localTimestamp3, localTimestamp);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(hashMap, class$java$util$HashMap == null ? (class$java$util$HashMap = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.util.HashMap")) : class$java$util$HashMap);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 4: {
                Handle handle = ((EJBObject)object).getHandle();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)handle, class$javax$ejb$Handle == null ? (class$javax$ejb$Handle = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("javax.ejb.Handle")) : class$javax$ejb$Handle);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 5: {
                Object object2 = ((EJBObject)object).getPrimaryKey();
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject(object2, class$java$lang$Object == null ? (class$java$lang$Object = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.lang.Object")) : class$java$lang$Object);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 6: {
                UserPreference userPreference;
                LocalTimestamp[] localTimestampArray;
                LocalTimestamp[] localTimestampArray2;
                String[] stringArray;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    stringArray = (String[])msgInput.readObject(array$Ljava$lang$String == null ? (array$Ljava$lang$String = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("[Ljava.lang.String;")) : array$Ljava$lang$String);
                    localTimestampArray2 = (LocalTimestamp[])msgInput.readObject(array$Lglog$util$LocalTimestamp == null ? (array$Lglog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("[Lglog.util.LocalTimestamp;")) : array$Lglog$util$LocalTimestamp);
                    localTimestampArray = (LocalTimestamp[])msgInput.readObject(array$Lglog$util$LocalTimestamp == null ? (array$Lglog$util$LocalTimestamp = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("[Lglog.util.LocalTimestamp;")) : array$Lglog$util$LocalTimestamp);
                    userPreference = (UserPreference)msgInput.readObject(class$glog$webserver$umt$UserPreference == null ? (class$glog$webserver$umt$UserPreference = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("glog.webserver.umt.UserPreference")) : class$glog$webserver$umt$UserPreference);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                ArrayList arrayList = ((ResourceScheduleActionSession)object).getResSchInstancesForGivenTimes(stringArray, localTimestampArray2, localTimestampArray, userPreference);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeObject((Object)arrayList, class$java$util$ArrayList == null ? (class$java$util$ArrayList = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("java.util.ArrayList")) : class$java$util$ArrayList);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 7: {
                EJBObject eJBObject;
                try {
                    MsgInput msgInput = inboundRequest.getMsgInput();
                    eJBObject = (EJBObject)msgInput.readObject(class$javax$ejb$EJBObject == null ? (class$javax$ejb$EJBObject = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl_WLSkel.class$("javax.ejb.EJBObject")) : class$javax$ejb$EJBObject);
                }
                catch (IOException iOException) {
                    throw new UnmarshalException("error unmarshalling arguments", iOException);
                }
                catch (ClassNotFoundException classNotFoundException) {
                    throw new UnmarshalException("error unmarshalling arguments", classNotFoundException);
                }
                boolean bl = ((EJBObject)object).isIdentical(eJBObject);
                this.associateResponseData(inboundRequest, outboundResponse);
                try {
                    outboundResponse.getMsgOutput().writeBoolean(bl);
                    break;
                }
                catch (IOException iOException) {
                    throw new MarshalException("error marshalling return", iOException);
                }
            }
            case 8: {
                ((EJBObject)object).remove();
                this.associateResponseData(inboundRequest, outboundResponse);
                break;
            }
            default: {
                throw new UnmarshalException("Method identifier [" + n + "] out of range");
            }
        }
        return outboundResponse;
    }

    static /* synthetic */ Class class$(String string) {
        try {
            return Class.forName(string);
        }
        catch (ClassNotFoundException classNotFoundException) {
            throw new NoClassDefFoundError(classNotFoundException.getMessage());
        }
    }

    public Object invoke(int n, Object[] objectArray, Object object) throws Exception {
        switch (n) {
            case 0: {
                return ((ResourceScheduleActionSession)object).generateResSchInstance((String)objectArray[0], (LocalTimestamp)objectArray[1], (LocalTimestamp)objectArray[2]);
            }
            case 1: {
                return ((ResourceScheduleActionSession)object).getAllResSchInstances((String[])objectArray[0], (UserPreference)objectArray[1]);
            }
            case 2: {
                return ((EJBObject)object).getEJBHome();
            }
            case 3: {
                return ((ResourceScheduleActionSession)object).getExistingResSchInstances((String)objectArray[0], (LocalTimestamp)objectArray[1], (LocalTimestamp)objectArray[2]);
            }
            case 4: {
                return ((EJBObject)object).getHandle();
            }
            case 5: {
                return ((EJBObject)object).getPrimaryKey();
            }
            case 6: {
                return ((ResourceScheduleActionSession)object).getResSchInstancesForGivenTimes((String[])objectArray[0], (LocalTimestamp[])objectArray[1], (LocalTimestamp[])objectArray[2], (UserPreference)objectArray[3]);
            }
            case 7: {
                return new Boolean(((EJBObject)object).isIdentical((EJBObject)objectArray[0]));
            }
            case 8: {
                ((EJBObject)object).remove();
                return null;
            }
        }
        throw new UnmarshalException("Method identifier [" + n + "] out of range");
    }
}
