/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.SessionContext
 */
package glog.business.resourceschedule;

import glog.business.resourceschedule.ResourceScheduleActionSession;
import glog.business.resourceschedule.ResourceScheduleActionSessionBean;
import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.util.local.LocalSessionContext;
import glog.util.local.SessionEnterExit;
import glog.webserver.umt.UserPreference;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.ejb.EJBHome;
import javax.ejb.EJBObject;
import javax.ejb.Handle;
import javax.ejb.SessionContext;

public class ResourceScheduleActionSessionStub
implements ResourceScheduleActionSession {
    private SessionContext context;

    public ResourceScheduleActionSessionStub(EJBHome home) {
        this.context = new LocalSessionContext(home, this);
    }

    @Override
    public HashMap<String, String> generateResSchInstance(String p1, LocalTimestamp p2, LocalTimestamp p3) throws GLException, RemoteException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ResourceScheduleActionSession", "generateResSchInstance");
            ResourceScheduleActionSessionBean bean = new ResourceScheduleActionSessionBean();
            bean.setSessionContext(this.context);
            HashMap<String, String> hashMap = bean.generateResSchInstance(p1, p2, p3);
            return hashMap;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public HashMap<String, String> getExistingResSchInstances(String p1, LocalTimestamp p2, LocalTimestamp p3) throws GLException, RemoteException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ResourceScheduleActionSession", "getExistingResSchInstances");
            ResourceScheduleActionSessionBean bean = new ResourceScheduleActionSessionBean();
            bean.setSessionContext(this.context);
            HashMap<String, String> hashMap = bean.getExistingResSchInstances(p1, p2, p3);
            return hashMap;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public ArrayList getAllResSchInstances(String[] p1, UserPreference p2) throws GLException, RemoteException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ResourceScheduleActionSession", "getAllResSchInstances");
            ResourceScheduleActionSessionBean bean = new ResourceScheduleActionSessionBean();
            bean.setSessionContext(this.context);
            ArrayList arrayList = bean.getAllResSchInstances(p1, p2);
            return arrayList;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    @Override
    public ArrayList getResSchInstancesForGivenTimes(String[] p1, LocalTimestamp[] p2, LocalTimestamp[] p3, UserPreference p4) throws GLException, RemoteException {
        SessionEnterExit ee = new SessionEnterExit();
        try {
            ee.enterMethod("ResourceScheduleActionSession", "getResSchInstancesForGivenTimes");
            ResourceScheduleActionSessionBean bean = new ResourceScheduleActionSessionBean();
            bean.setSessionContext(this.context);
            ArrayList arrayList = bean.getResSchInstancesForGivenTimes(p1, p2, p3, p4);
            return arrayList;
        }
        catch (GLException ex) {
            this.context.setRollbackOnly();
            throw ex;
        }
        finally {
            ee.exitMethod();
        }
    }

    public EJBHome getEJBHome() {
        return this.context.getEJBHome();
    }

    public Handle getHandle() {
        return null;
    }

    public Object getPrimaryKey() {
        return null;
    }

    public boolean isIdentical(EJBObject obj) {
        return obj == this;
    }

    public void remove() {
    }
}
