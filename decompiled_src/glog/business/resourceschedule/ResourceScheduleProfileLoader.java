/*
 * Decompiled with CFR 0.152.
 */
package glog.business.resourceschedule;

import glog.business.util.EntityLoader;
import glog.ejb.resourceschedule.db.ResourceScheduleProfileDData;
import glog.ejb.resourceschedule.db.ResourceScheduleProfileData;
import glog.util.exception.GLException;

public class ResourceScheduleProfileLoader
extends EntityLoader {
    public ResourceScheduleProfileLoader() throws GLException {
        super(ResourceScheduleProfileData.class, false);
        this.createChildLoaders();
    }

    private void createChildLoaders() throws GLException {
        EntityLoader ResourceScheduleProfileDLoader = new EntityLoader(this, ResourceScheduleProfileDData.class, 30);
    }
}
