/*
 * Decompiled with CFR 0.152.
 */
package glog.business.resourceschedule;

import glog.business.util.TObject;
import glog.ejb.resourceschedule.db.ResourceScheduleProfileDData;
import glog.ejb.resourceschedule.db.ResourceScheduleProfileDPK;

public class TResourceScheduleProfileD
extends TObject {
    public static final String BT_RESOURCE_SCHEDULE_PROFILE_D = "ResourceScheduleProfileDData";

    public TResourceScheduleProfileD(ResourceScheduleProfileDData data) {
        super(data);
    }

    public ResourceScheduleProfileDData getData() {
        return (ResourceScheduleProfileDData)this.getBeanData();
    }

    @Override
    public ResourceScheduleProfileDPK getPrimaryKey() {
        return (ResourceScheduleProfileDPK)this.getBeanData().getPk();
    }

    @Override
    public String getBusinessType() {
        return BT_RESOURCE_SCHEDULE_PROFILE_D;
    }

    public String getResourceScheduleGid() {
        return this.getData().resourceScheduleGid;
    }
}
