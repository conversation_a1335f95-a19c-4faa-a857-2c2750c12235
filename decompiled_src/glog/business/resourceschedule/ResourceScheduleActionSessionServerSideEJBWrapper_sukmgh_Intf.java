/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.SessionContext
 *  weblogic.ejb.container.interfaces.WLEnterpriseBean
 */
package glog.business.resourceschedule;

import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.webserver.umt.UserPreference;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.ejb.CreateException;
import javax.ejb.SessionContext;
import weblogic.ejb.container.interfaces.WLEnterpriseBean;

public interface ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf
extends WLEnterpriseBean {
    public void ejbCreate() throws CreateException;

    public void ejbRemove() throws RemoteException;

    public void ejbActivate() throws RemoteException;

    public T2SharedConnection getConnection() throws GLException;

    public SessionContext getSessionContext();

    public HashMap generateResSchInstance(String var1, LocalTimestamp var2, LocalTimestamp var3) throws GLException;

    public ArrayList getAllResSchInstances(String[] var1, UserPreference var2) throws GLException;

    public T2SharedConnection getConnectionRemote() throws RemoteException;

    public void ejbPassivate() throws RemoteException;

    public ArrayList getResSchInstancesForGivenTimes(String[] var1, LocalTimestamp[] var2, LocalTimestamp[] var3, UserPreference var4) throws GLException;

    public HashMap getExistingResSchInstances(String var1, LocalTimestamp var2, LocalTimestamp var3) throws GLException;

    public void setSessionContext(SessionContext var1);
}
