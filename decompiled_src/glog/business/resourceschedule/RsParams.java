/*
 * Decompiled with CFR 0.152.
 */
package glog.business.resourceschedule;

import glog.business.continuousmove.CmParams;
import glog.business.continuousmove.CmPlanningParameterData;
import glog.business.resourceschedule.RsPlanningParameterData;
import glog.util.exception.GLException;
import java.util.HashMap;
import java.util.Map;

public class RsParams
extends CmParams {
    Map<String, RsPlanningParameterData> lcgToPlanningParamMap = new HashMap<String, RsPlanningParameterData>();

    public RsParams(Map<String, RsPlanningParameterData> lcgToPlanningParamMap, boolean passHOSStateDownstream, RsPlanningParameterData rsPlanningParameterDataDefault) throws GLException {
        super(rsPlanningParameterDataDefault, false, passHOSStateDownstream, rsPlanningParameterDataDefault.getRsCostTolerance(), rsPlanningParameterDataDefault.isIgnoreRsRuleCheck(), rsPlanningParameterDataDefault.getCmMaxNumberOfSequences());
        this.lcgToPlanningParamMap = lcgToPlanningParamMap;
    }

    public Map<String, RsPlanningParameterData> getLcgToPlanningParamMap() {
        return this.lcgToPlanningParamMap;
    }

    public void setLcgToPlanningParamMap(Map<String, RsPlanningParameterData> lcgToPlanningParamMap) {
        this.lcgToPlanningParamMap = lcgToPlanningParamMap;
    }

    @Override
    public CmPlanningParameterData getPlanningParameter(String lcg) {
        return this.lcgToPlanningParamMap.get(lcg);
    }

    @Override
    public boolean getUseSimulationDrive() {
        return false;
    }
}
