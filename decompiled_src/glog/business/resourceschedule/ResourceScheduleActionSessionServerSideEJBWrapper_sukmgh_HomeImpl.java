/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.CreateException
 *  javax.ejb.EJBMetaData
 *  javax.ejb.Handle
 *  javax.ejb.HomeHandle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.StatelessEJBHome
 */
package glog.business.resourceschedule;

import glog.business.resourceschedule.ResourceScheduleActionSession;
import glog.business.resourceschedule.ResourceScheduleActionSessionHome;
import glog.business.resourceschedule.ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl;
import glog.business.resourceschedule.ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf;
import java.lang.reflect.Method;
import java.rmi.RemoteException;
import javax.ejb.CreateException;
import javax.ejb.EJBMetaData;
import javax.ejb.Handle;
import javax.ejb.HomeHandle;
import javax.ejb.RemoveException;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.StatelessEJBHome;

public final class ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_HomeImpl
extends StatelessEJBHome
implements ResourceScheduleActionSessionHome {
    private static final Method mth_ejbCreate;
    public MethodDescriptor md_ejbCreate;
    public MethodDescriptor md_getEJBMetaData;
    public MethodDescriptor md_getHomeHandle;
    public MethodDescriptor md_ejbRemove_O;
    public MethodDescriptor md_ejbRemove_javax_ejb_Handle;

    static {
        try {
            mth_ejbCreate = ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf.class.getMethod("ejbCreate", new Class[0]);
        }
        catch (Exception exception) {
            throw new AssertionError((Object)("Unable to find expected methods.  Please check your classpath for stale versions of your ejb classes and re-run weblogic.appc.\n If this is a java.io.FilePermission exception and you are running under JACC security, then check your security policy file.\n  Exception: '" + exception.getMessage() + "'"));
        }
    }

    public ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_HomeImpl() {
        super(ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl.class);
    }

    public ResourceScheduleActionSession create() throws RemoteException, CreateException {
        try {
            return (ResourceScheduleActionSession)super.create(this.md_ejbCreate);
        }
        catch (Exception exception) {
            if (exception instanceof RemoteException) {
                throw (RemoteException)exception;
            }
            if (exception instanceof CreateException) {
                throw (CreateException)exception;
            }
            if (exception instanceof RuntimeException && this.deploymentInfo.getExceptionInfo(this.md_ejbCreate.getMethod(), (Throwable)exception).isAppException()) {
                throw (RuntimeException)exception;
            }
            throw new CreateException("Error while creating bean: " + exception.toString());
        }
    }

    public EJBMetaData getEJBMetaData() throws RemoteException {
        return super.getEJBMetaData(this.md_getEJBMetaData);
    }

    public HomeHandle getHomeHandle() throws RemoteException {
        return super.getHomeHandle(this.md_getHomeHandle);
    }

    public void remove(Object object) throws RemoteException, RemoveException {
        super.remove(this.md_ejbRemove_O, object);
    }

    public void remove(Handle handle) throws RemoteException, RemoveException {
        super.remove(this.md_ejbRemove_javax_ejb_Handle, handle);
    }
}
