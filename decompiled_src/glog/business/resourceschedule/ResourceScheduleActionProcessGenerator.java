/*
 * Decompiled with CFR 0.152.
 */
package glog.business.resourceschedule;

import glog.business.session.ActionProcessGenerator;
import glog.business.session.PlanningProcess;
import glog.util.jdbc.Pk;

class ResourceScheduleActionProcessGenerator
extends ActionProcessGenerator {
    ResourceScheduleActionProcessGenerator() {
    }

    public static final PlanningProcess getProcess(Pk pk, String methodName) {
        return ResourceScheduleActionProcessGenerator.getProcess(pk, methodName, null);
    }

    protected static final PlanningProcess getProcess(Pk pk, String methodName, Object[] args) {
        return ResourceScheduleActionProcessGenerator.getProcess(new Pk[]{pk}, methodName, args);
    }

    protected static final PlanningProcess getProcess(Pk[] pks, String methodName) {
        return ResourceScheduleActionProcessGenerator.getProcess(pks, methodName, null);
    }

    protected static final PlanningProcess getProcess(Pk[] pks, String methodName, Object[] args) {
        return ResourceScheduleActionProcessGenerator.getProcess(pks, methodName, null, args);
    }

    protected static final PlanningProcess getProcess(Pk[] pks, String methodName, String suffix, Object[] args) {
        return ActionProcessGenerator.getProcessForPks(pks, "process.ResourceSchedule." + methodName, suffix, args);
    }
}
