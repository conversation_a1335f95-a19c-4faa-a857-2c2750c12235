/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBHome
 *  javax.ejb.EJBObject
 *  javax.ejb.Handle
 *  javax.ejb.RemoveException
 *  weblogic.ejb.EJBObject
 *  weblogic.ejb.container.interfaces.Invokable
 *  weblogic.ejb.container.internal.BaseRemoteObject
 *  weblogic.ejb.container.internal.InvocationWrapper
 *  weblogic.ejb.container.internal.MethodDescriptor
 *  weblogic.ejb.container.internal.SessionRemoteMethodInvoker
 *  weblogic.ejb.container.internal.StatelessEJBObject
 */
package glog.business.resourceschedule;

import glog.business.resourceschedule.ResourceScheduleActionSession;
import glog.business.resourceschedule.ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf;
import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.webserver.umt.UserPreference;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.ejb.EJBHome;
import javax.ejb.Handle;
import javax.ejb.RemoveException;
import weblogic.ejb.EJBObject;
import weblogic.ejb.container.interfaces.Invokable;
import weblogic.ejb.container.internal.BaseRemoteObject;
import weblogic.ejb.container.internal.InvocationWrapper;
import weblogic.ejb.container.internal.MethodDescriptor;
import weblogic.ejb.container.internal.SessionRemoteMethodInvoker;
import weblogic.ejb.container.internal.StatelessEJBObject;

public final class ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_EOImpl
extends StatelessEJBObject
implements ResourceScheduleActionSession,
EJBObject,
Invokable {
    public static MethodDescriptor md_eo_getAllResSchInstances_ASglog_webserver_umt_UserPreference;
    public static MethodDescriptor md_eo_getResSchInstancesForGivenTimes_ASAglog_util_LocalTimestampAglog_util_LocalTimestampglog_webserver_umt_UserPreference;
    public static MethodDescriptor md_eo_getExistingResSchInstances_Sglog_util_LocalTimestampglog_util_LocalTimestamp;
    public static MethodDescriptor md_eo_generateResSchInstance_Sglog_util_LocalTimestampglog_util_LocalTimestamp;
    public static MethodDescriptor md_eo_remove;
    public static MethodDescriptor md_eo_getEJBHome;
    public static MethodDescriptor md_eo_getHandle;
    public static MethodDescriptor md_eo_getPrimaryKey;
    public static MethodDescriptor md_eo_isIdentical_javax_ejb_EJBObject;

    public ArrayList getResSchInstancesForGivenTimes(String[] stringArray, LocalTimestamp[] localTimestampArray, LocalTimestamp[] localTimestampArray2, UserPreference userPreference) throws GLException, RemoteException {
        return (ArrayList)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_getResSchInstancesForGivenTimes_ASAglog_util_LocalTimestampAglog_util_LocalTimestampglog_webserver_umt_UserPreference), (Object[])new Object[]{stringArray, localTimestampArray, localTimestampArray2, userPreference}, (int)0);
    }

    public HashMap generateResSchInstance(String string, LocalTimestamp localTimestamp, LocalTimestamp localTimestamp2) throws GLException, RemoteException {
        return (HashMap)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_generateResSchInstance_Sglog_util_LocalTimestampglog_util_LocalTimestamp), (Object[])new Object[]{string, localTimestamp, localTimestamp2}, (int)1);
    }

    public ArrayList getAllResSchInstances(String[] stringArray, UserPreference userPreference) throws GLException, RemoteException {
        return (ArrayList)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_getAllResSchInstances_ASglog_webserver_umt_UserPreference), (Object[])new Object[]{stringArray, userPreference}, (int)2);
    }

    public HashMap getExistingResSchInstances(String string, LocalTimestamp localTimestamp, LocalTimestamp localTimestamp2) throws GLException, RemoteException {
        return (HashMap)SessionRemoteMethodInvoker.invoke((Invokable)this, (BaseRemoteObject)this, (InvocationWrapper)InvocationWrapper.newInstance((MethodDescriptor)md_eo_getExistingResSchInstances_Sglog_util_LocalTimestampglog_util_LocalTimestamp), (Object[])new Object[]{string, localTimestamp, localTimestamp2}, (int)3);
    }

    public Object __WL_invoke(Object object, Object[] objectArray, int n) throws Throwable {
        switch (n) {
            case 0: {
                return ((ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf)object).getResSchInstancesForGivenTimes((String[])objectArray[0], (LocalTimestamp[])objectArray[1], (LocalTimestamp[])objectArray[2], (UserPreference)objectArray[3]);
            }
            case 1: {
                return ((ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf)object).generateResSchInstance((String)objectArray[0], (LocalTimestamp)objectArray[1], (LocalTimestamp)objectArray[2]);
            }
            case 2: {
                return ((ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf)object).getAllResSchInstances((String[])objectArray[0], (UserPreference)objectArray[1]);
            }
            case 3: {
                return ((ResourceScheduleActionSessionServerSideEJBWrapper_sukmgh_Intf)object).getExistingResSchInstances((String)objectArray[0], (LocalTimestamp)objectArray[1], (LocalTimestamp)objectArray[2]);
            }
        }
        throw new IllegalArgumentException("No method found for index : " + n);
    }

    public void __WL_handleException(int n, Throwable throwable) throws Throwable {
        switch (n) {
            case 0: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 1: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 2: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            case 3: {
                if (!(throwable instanceof GLException)) break;
                throw throwable;
            }
            default: {
                throw new IllegalArgumentException("No method found for index : " + n);
            }
        }
    }

    public void remove() throws RemoveException, RemoteException {
        super.remove(md_eo_remove);
    }

    public EJBHome getEJBHome() throws RemoteException {
        return super.getEJBHome(md_eo_getEJBHome);
    }

    public Handle getHandle() throws RemoteException {
        return super.getHandle(md_eo_getHandle);
    }

    public Object getPrimaryKey() throws RemoteException {
        return super.getPrimaryKey(md_eo_getPrimaryKey);
    }

    public boolean isIdentical(javax.ejb.EJBObject eJBObject) throws RemoteException {
        return super.isIdentical(md_eo_isIdentical_javax_ejb_EJBObject, eJBObject);
    }

    public void operationsComplete() {
    }
}
