/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  javax.ejb.EJBObject
 */
package glog.business.resourceschedule;

import glog.util.LocalTimestamp;
import glog.util.exception.GLException;
import glog.webserver.umt.UserPreference;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import javax.ejb.EJBObject;

public interface ResourceScheduleActionSession
extends EJBObject {
    public HashMap<String, String> generateResSchInstance(String var1, LocalTimestamp var2, LocalTimestamp var3) throws GLException, RemoteException;

    public HashMap<String, String> getExistingResSchInstances(String var1, LocalTimestamp var2, LocalTimestamp var3) throws GLException, RemoteException;

    public ArrayList getAllResSchInstances(String[] var1, UserPreference var2) throws GLException, RemoteException;

    public ArrayList getResSchInstancesForGivenTimes(String[] var1, LocalTimestamp[] var2, LocalTimestamp[] var3, UserPreference var4) throws GLException, RemoteException;
}
