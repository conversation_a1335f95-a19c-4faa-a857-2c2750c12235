/*
 * Decompiled with CFR 0.152.
 */
package glog.business.compatibility;

import glog.business.action.shipment.ShipmentActionHelper;
import glog.business.compatibility.CompatibilityError;
import glog.business.consolidation.bulkplan.BundleRulesToBeIgnored;
import glog.business.consolidation.bulkplan.OrderMovementBundle;
import glog.business.consolidation.bulkplan.ShipmentOrderBundle;
import glog.business.consolidation.bulkplan.TOrderBundle;
import glog.business.fleetassignment.FleetAssignmentHelper;
import glog.business.order.TOrderMovement;
import glog.business.order.TOrderRelease;
import glog.business.service.BusinessService;
import glog.business.service.BusinessServiceFactory;
import glog.business.service.fleetassignment.DriverAssignmentShipmentService;
import glog.business.service.fleetassignment.EquipmentAssignmentShipmentService;
import glog.business.service.shipment.AbstractShipmentService;
import glog.business.service.shipment.CommodityCompatibilityShipmentService;
import glog.business.service.shipment.ShipmentServiceParams;
import glog.business.shipment.TSEquipment;
import glog.business.shipment.TSShipUnit;
import glog.business.shipment.TShipment;
import glog.business.shipmentstructure.ShipmentGraph;
import glog.business.util.PkProfile;
import glog.business.util.PkProfileHelper;
import glog.ejb.commodity.db.CommodityData;
import glog.ejb.order.db.OrderMovementPK;
import glog.ejb.order.db.OrderReleasePK;
import glog.ejb.reference.db.FeasibilityCodePK;
import glog.ejb.shipment.db.ShipmentPK;
import glog.ejb.transportmode.db.TransportModePK;
import glog.util.exception.GLException;
import glog.util.jdbc.SqlQuery;
import glog.util.jdbc.T2SharedConnection;
import glog.util.jdbc.noserver.NoQueryFilter;
import glog.util.jdbc.noserver.QueryFilter;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;

public class CompatibilityChecker {
    private static final LogId LOG_ALIAS = LogIds.PLANNING;
    public static final String FIND_ORDER_RELEASE_BY_SHIPMENT = " select /*+ NO_MERGE(m) */ distinct order_release_gid  from (select ssul.order_release_gid        from   shipment shp,               shipment_s_equipment_join ssej,               s_equipment_s_ship_unit_join sessuj,               s_ship_unit_line ssul        where  shp.shipment_gid = ssej.shipment_gid        and    ssej.s_equipment_gid = sessuj.s_equipment_gid        and    sessuj.s_ship_unit_gid = ssul.s_ship_unit_gid        and    ssul.order_release_gid is not null        and    shp.shipment_gid = ?) m ";
    private static String FIND_ORDER_MOVEMENT_BY_SHIPMENT = " select permanent_om_gid  from om_shipment_join  where shipment_gid = ? ";
    private TShipment tShipment;
    private ShipmentGraph shipmentGraph;
    private T2SharedConnection connection;

    public static CompatibilityError validateShipment(TShipment tShipment, BundleRulesToBeIgnored rulesToIgnore, T2SharedConnection connection) throws GLException {
        CompatibilityError error = CompatibilityChecker.validateBundles(tShipment, rulesToIgnore, connection);
        if (error != null) {
            return error;
        }
        error = CompatibilityChecker.validateCommodityConstraints(tShipment);
        if (error != null) {
            return error;
        }
        return null;
    }

    private CompatibilityError validateBundles() throws GLException {
        return CompatibilityChecker.validateBundles(this.tShipment, null, this.connection);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static CompatibilityError validateBundles(TShipment tShipment, BundleRulesToBeIgnored rulesToIgnore, T2SharedConnection connection) throws GLException {
        HashMap<String, TOrderBundle> orderReleaseToTOrderBundleMap = new HashMap<String, TOrderBundle>();
        HashMap<String, OrderMovementBundle> orderMovementToTOrderBundleMap = new HashMap<String, OrderMovementBundle>();
        ShipmentOrderBundle shipmentOrderBundle = null;
        SqlQuery sql = new SqlQuery((QueryFilter)NoQueryFilter.get(FIND_ORDER_MOVEMENT_BY_SHIPMENT), new Object[]{tShipment.getShipmentGid()}, null);
        ArrayList<OrderMovementPK> orderMovementPKList = new ArrayList<OrderMovementPK>();
        try {
            connection.open();
            sql.open(connection.get());
            while (sql.next()) {
                OrderMovementPK orderMovementPK = new OrderMovementPK(1, sql.getObject(1));
                if (orderMovementPKList.contains(orderMovementPK)) continue;
                orderMovementPKList.add(orderMovementPK);
            }
            for (int i = 0; i < orderMovementPKList.size(); ++i) {
                CompatibilityError error;
                OrderMovementPK orderMovementPK = (OrderMovementPK)orderMovementPKList.get(i);
                TOrderMovement tOrderMovement = TOrderMovement.load(orderMovementPK, false, connection);
                String orderMovementGid = (String)orderMovementPK.orderMovementGid;
                OrderMovementBundle orderMovementBundle = (OrderMovementBundle)orderMovementToTOrderBundleMap.get(orderMovementGid);
                if (orderMovementBundle == null) {
                    orderMovementBundle = new OrderMovementBundle(tOrderMovement, null, null, true);
                    error = orderMovementBundle.getCompatibilityError();
                    if (error != null) {
                        CompatibilityError compatibilityError = error;
                        return compatibilityError;
                    }
                    orderMovementToTOrderBundleMap.put(orderMovementGid, orderMovementBundle);
                }
                if (shipmentOrderBundle == null) {
                    shipmentOrderBundle = new ShipmentOrderBundle(orderMovementBundle, tShipment);
                    continue;
                }
                error = shipmentOrderBundle.addBundle(orderMovementBundle, new BundleRulesToBeIgnored());
                if (error == null) continue;
                CompatibilityError compatibilityError = error;
                return compatibilityError;
            }
        }
        finally {
            sql.close();
            connection.close();
        }
        sql = new SqlQuery((QueryFilter)NoQueryFilter.get(FIND_ORDER_RELEASE_BY_SHIPMENT), new Object[]{tShipment.getShipmentGid()}, null);
        ArrayList<OrderReleasePK> orderReleasePKList = new ArrayList<OrderReleasePK>();
        try {
            connection.open();
            sql.open(connection.get());
            while (sql.next()) {
                OrderReleasePK orderReleasePK = new OrderReleasePK(1, sql.getObject(1));
                if (orderReleasePKList.contains(orderReleasePK)) continue;
                orderReleasePKList.add(orderReleasePK);
            }
        }
        finally {
            sql.close();
            connection.close();
        }
        BundleRulesToBeIgnored orderRulesToBeIgnored = new BundleRulesToBeIgnored();
        orderRulesToBeIgnored.setSuppressExceptionForBundleCreateError(true);
        for (int i = 0; i < orderReleasePKList.size(); ++i) {
            CompatibilityError error;
            OrderReleasePK orderReleasePK = (OrderReleasePK)orderReleasePKList.get(i);
            TOrderRelease tOrderRelease = TOrderRelease.load(orderReleasePK);
            String orderReleaseGid = (String)orderReleasePK.orderReleaseGid;
            TOrderBundle tOrderBundle = (TOrderBundle)orderReleaseToTOrderBundleMap.get(orderReleaseGid);
            if (tOrderBundle == null) {
                tOrderBundle = new TOrderBundle(tOrderRelease, tShipment.getPerspective(), null, orderRulesToBeIgnored);
                error = tOrderBundle.getCompatibilityError();
                if (error != null) {
                    return error;
                }
                orderReleaseToTOrderBundleMap.put(orderReleaseGid, tOrderBundle);
            }
            if (shipmentOrderBundle == null) {
                shipmentOrderBundle = new ShipmentOrderBundle(tOrderBundle, tShipment);
                continue;
            }
            error = shipmentOrderBundle.addBundle(tOrderBundle, new BundleRulesToBeIgnored());
            if (error == null) continue;
            return error;
        }
        return null;
    }

    private static CompatibilityError validateCommodityConstraints(TShipment tShipment) throws GLException {
        TSEquipment[] tSEquipments = tShipment.getTSEquipments();
        if (tSEquipments == null || tSEquipments.length == 0) {
            if (Log.idOn[CompatibilityChecker.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, "CompatibilityChecker found no equipment, commodity validation passes");
            }
            return null;
        }
        for (int e = 0; e < tSEquipments.length; ++e) {
            TSEquipment tEquip = tSEquipments[e];
            TSShipUnit[] tSSUs = tEquip.getTSShipUnits();
            if (tSSUs == null || tSSUs.length == 0) continue;
            for (int su = 0; su < tSSUs.length; ++su) {
                Collection commodData = tSSUs[su].getCommodityData();
                if (commodData == null || commodData.isEmpty()) continue;
                for (CommodityData commod : commodData) {
                    TransportModePK modePK;
                    PkProfile modePkProfile;
                    PkProfile egPkProfile;
                    if (commod.reqEqpmtGroupProfileGid != null && !(egPkProfile = PkProfileHelper.getEquipmentGroupPkProfile(null, commod.reqEqpmtGroupProfileGid, false)).isCompatibleWith(tEquip.getEquipmentGroupPK())) {
                        return CompatibilityError.commodityNotCompatibleWithEquipment;
                    }
                    if (commod.modeProfileGid == null || (modePkProfile = PkProfileHelper.getTransportModeProfile(null, commod.modeProfileGid, false)).isCompatibleWith(modePK = tShipment.getTransportModePK())) continue;
                    return CompatibilityError.commodityNotCompatibleWithMode;
                }
            }
        }
        return null;
    }

    public CompatibilityChecker(ShipmentGraph shipGraph, TShipment tShip, T2SharedConnection conn) {
        this.tShipment = tShip;
        this.connection = conn;
        this.shipmentGraph = shipGraph;
    }

    public void validate() throws GLException {
        try {
            FeasibilityCodePK[] infeasibilityCodePks;
            this.tShipment.clearInfeasibilityCodes();
            try {
                String feasCode;
                CompatibilityError err = CompatibilityChecker.validateBundles(this.tShipment, null, this.connection);
                if (err != null && (feasCode = err.getFeasiblityCodeGid()) != null && !feasCode.equals("FEASIBLE")) {
                    this.tShipment.setInfeasibilityCode(err.getFeasiblityCodeGid());
                }
            }
            catch (GLException ex) {
                // empty catch block
            }
            try {
                infeasibilityCodePks = this.checkDriverAssignmentCompatibility();
                if (infeasibilityCodePks != null && infeasibilityCodePks.length > 0) {
                    this.updateShipmentFeasibilityCodes(infeasibilityCodePks);
                }
            }
            catch (GLException ex) {
                this.tShipment.setInfeasibilityCode("DRIVER_INFEASIBLE");
            }
            try {
                infeasibilityCodePks = this.checkEquipmentAssignmentCompatibility();
                if (infeasibilityCodePks != null && infeasibilityCodePks.length > 0) {
                    this.updateShipmentFeasibilityCodes(infeasibilityCodePks);
                }
            }
            catch (GLException ex) {
                this.tShipment.setInfeasibilityCode("EQUIPMENT_INFEASIBLE");
            }
            try {
                this.checkCommodityCompatibility();
            }
            catch (GLException ex) {
                this.tShipment.setInfeasibilityCode("RATE_INFEASIBLE");
            }
        }
        catch (Throwable t) {
            throw GLException.factory(t);
        }
    }

    private void updateShipmentFeasibilityCodes(FeasibilityCodePK[] infeasibilityCodePks) {
        for (int i = 0; i < infeasibilityCodePks.length; ++i) {
            String infeasibilityCodeGid = (String)infeasibilityCodePks[i].feasibilityCodeGid;
            if (infeasibilityCodeGid.equals("FEASIBLE")) continue;
            this.tShipment.setInfeasibilityCode(infeasibilityCodeGid);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private FeasibilityCodePK[] checkDriverAssignmentCompatibility() throws GLException {
        String statusValue = ShipmentActionHelper.getStatusValue(this.tShipment.getPrimaryKey(), "DRIVER ASSIGNMENT");
        if (statusValue != null && !statusValue.equals("DRIVER ASSIGNMENT_NOT STARTED") && !statusValue.equals("DRIVER ASSIGNMENT_UNASSIGNED")) {
            try (AbstractShipmentService svc = null;){
                svc = (DriverAssignmentShipmentService)this.getShipmentService(DriverAssignmentShipmentService.NAME, null, this.tShipment, this.tShipment.getPrimaryKey(), this.connection);
                FeasibilityCodePK[] feasibilityCodePKArray = ((DriverAssignmentShipmentService)svc).checkCompatibility();
                return feasibilityCodePKArray;
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private FeasibilityCodePK[] checkEquipmentAssignmentCompatibility() throws GLException {
        String statusValue = FleetAssignmentHelper.getEquipmentAssignmentStatus(this.tShipment);
        if (statusValue != null && !statusValue.equals("EQUIPMENT ASSIGNMENT_UNASSIGNED")) {
            try (AbstractShipmentService svc = null;){
                svc = (EquipmentAssignmentShipmentService)this.getShipmentService(EquipmentAssignmentShipmentService.NAME, null, this.tShipment, this.tShipment.getPrimaryKey(), this.connection);
                FeasibilityCodePK[] feasibilityCodePKArray = ((EquipmentAssignmentShipmentService)svc).checkCompatibility();
                return feasibilityCodePKArray;
            }
        }
        return null;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private void checkCommodityCompatibility() throws GLException {
        try (AbstractShipmentService svc = null;){
            svc = (CommodityCompatibilityShipmentService)this.getShipmentService(CommodityCompatibilityShipmentService.NAME, this.shipmentGraph, this.tShipment, this.tShipment.getPrimaryKey(), this.connection);
            ((CommodityCompatibilityShipmentService)svc).checkCommodityCompatibilities();
        }
    }

    protected final BusinessService getShipmentService(String serviceName, ShipmentGraph shipmentGraph, TShipment tShipment, ShipmentPK shipmentPK, T2SharedConnection connection) throws GLException {
        ShipmentServiceParams params = new ShipmentServiceParams(connection, shipmentGraph, tShipment, shipmentPK);
        return BusinessServiceFactory.getService(serviceName, params);
    }
}
