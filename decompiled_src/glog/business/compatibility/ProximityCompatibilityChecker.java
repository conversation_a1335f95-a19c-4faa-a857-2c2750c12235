/*
 * Decompiled with CFR 0.152.
 */
package glog.business.compatibility;

import glog.business.bundling.TimeWindow;
import glog.business.consolidation.bulkplan.ShipmentOrderBundle;
import glog.business.consolidation.multistop.PairableTShipment;
import glog.business.location.Address;
import glog.business.shipment.TShipment;
import glog.business.util.PlanningDistTimeFinder;
import glog.business.util.PlanningParameterHelper;
import glog.business.util.TLogicConfig;
import glog.ejb.rates.db.RateDistancePK;
import glog.optimization.resourceallocation.ItemCompatibilityChecker;
import glog.optimization.resourceallocation.ResAllocItem;
import glog.optimization.resourceallocation.ResAllocResource;
import glog.util.LocalTimestamp;
import glog.util.exception.Cause;
import glog.util.exception.GLException;
import glog.util.jdbc.T2SharedConnection;
import glog.util.uom.data.Distance;
import glog.util.uom.data.Duration;
import java.util.Collection;
import java.util.HashSet;

public class ProximityCompatibilityChecker
implements ItemCompatibilityChecker {
    private RateDistancePK rateDistancePK = null;
    private Distance maxDistanceBetweenPickups = null;
    private Distance maxDistanceBetweenDeliveries = null;
    private Duration maxTimeBetweenPickups = null;
    private Duration maxTimeBetweenDeliveries = null;
    private int maxStopsAllowed = 100;
    private int maxPickupStopsAllowed = 100;
    private int maxDeliveryStopsAllowed = 100;
    private T2SharedConnection connection = null;

    public ProximityCompatibilityChecker() throws GLException {
        TLogicConfig tLogicConfig = PlanningParameterHelper.getLogicConfig("MULTISTOP CONFIG ID");
        String rateDistanceGid = tLogicConfig.getString("MULTISTOP RATE DISTANCE ID");
        if (rateDistanceGid == null) {
            throw GLException.factory((Cause)new GLException.CausedBy("cause.MultiStopPlanningParameters.0001", null), null);
        }
        this.rateDistancePK = new RateDistancePK(rateDistanceGid);
        this.maxDistanceBetweenPickups = tLogicConfig.getDistance("MULTISTOP MAX DISTANCE BETWEEN PICKUPS");
        this.maxDistanceBetweenDeliveries = tLogicConfig.getDistance("MULTISTOP MAX DISTANCE BETWEEN DELIVERIES");
        this.maxTimeBetweenPickups = tLogicConfig.getDuration("MULTISTOP MAX TIME BETWEEN PICKUPS");
        this.maxTimeBetweenDeliveries = tLogicConfig.getDuration("MULTISTOP MAX TIME BETWEEN DELIVERIES");
        this.maxStopsAllowed = tLogicConfig.getIntValue("MAXIMUM STOPS ALLOWED");
        this.maxPickupStopsAllowed = tLogicConfig.getIntValue("MAXIMUM PICKUP STOPS ALLOWED");
        this.maxDeliveryStopsAllowed = tLogicConfig.getIntValue("MAXIMUM DELIVERY STOPS ALLOWED");
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     * Enabled aggressive block sorting
     * Enabled unnecessary exception pruning
     * Enabled aggressive exception aggregation
     */
    @Override
    public boolean isCompatible(ResAllocItem item1, ResAllocItem item2) {
        TShipment tShipment1 = ((PairableTShipment)item1.getReference()).getTShipment();
        TShipment tShipment2 = ((PairableTShipment)item2.getReference()).getTShipment();
        try {
            ShipmentOrderBundle bundle1 = tShipment1.getShipmentOrderBundle();
            ShipmentOrderBundle bundle2 = tShipment1.getShipmentOrderBundle();
            LocalTimestamp earlyPickup1 = bundle1.getEarlyPickup();
            LocalTimestamp latePickup1 = bundle1.getLatePickup();
            LocalTimestamp earlyDelivery1 = bundle1.getEarlyDelivery();
            LocalTimestamp lateDelivery1 = bundle1.getLateDelivery();
            LocalTimestamp earlyPickup2 = bundle2.getEarlyPickup();
            LocalTimestamp latePickup2 = bundle2.getLatePickup();
            LocalTimestamp earlyDelivery2 = bundle2.getEarlyDelivery();
            LocalTimestamp lateDelivery2 = bundle2.getLateDelivery();
            Address source1 = tShipment1.getSourceAddress();
            Address dest1 = tShipment1.getDestAddress();
            Address source2 = tShipment2.getSourceAddress();
            Address dest2 = tShipment2.getDestAddress();
            TimeWindow pickupTimeWindow1 = new TimeWindow(earlyPickup1, latePickup1);
            TimeWindow pickupTimeWindow2 = new TimeWindow(earlyPickup2, latePickup2);
            TimeWindow pickupTimeWindowOverlap = TimeWindow.getOverlap(pickupTimeWindow1, pickupTimeWindow2);
            if (source1.equals(source2) && pickupTimeWindowOverlap == null) {
                boolean bl = false;
                return bl;
            }
            TimeWindow deliveryTimeWindow1 = new TimeWindow(earlyDelivery1, lateDelivery1);
            TimeWindow deliveryTimeWindow2 = new TimeWindow(earlyDelivery2, lateDelivery2);
            TimeWindow deliveryTimeWindowOverlap = TimeWindow.getOverlap(deliveryTimeWindow1, deliveryTimeWindow2);
            if (dest1.equals(dest2) && deliveryTimeWindowOverlap == null) {
                boolean bl = false;
                return bl;
            }
            this.connection = T2SharedConnection.adhoc();
            this.connection.open();
            PlanningDistTimeFinder distTimeFinder = new PlanningDistTimeFinder(this.rateDistancePK, this.connection);
            Distance distance = null;
            if (source1.equals(source2) && !dest1.equals(dest2)) {
                distance = distTimeFinder.findDistance(dest1.getLocationGid(), dest2.getLocationGid());
                if (distance == null) return false;
                if (distance.compareTo(this.maxDistanceBetweenDeliveries) > 0) return false;
                if (earlyDelivery2.subtract(lateDelivery1).compareTo(this.maxTimeBetweenDeliveries) > 0) return false;
                if (earlyDelivery1.subtract(lateDelivery2).compareTo(this.maxTimeBetweenDeliveries) > 0) return false;
                boolean bl = true;
                return bl;
            }
            if (!source1.equals(source2) && dest1.equals(dest2)) {
                distance = distTimeFinder.findDistance(source1.getLocationGid(), source2.getLocationGid());
                if (distance == null) return false;
                if (distance.compareTo(this.maxDistanceBetweenPickups) > 0) return false;
                if (earlyPickup2.subtract(latePickup1).compareTo(this.maxTimeBetweenPickups) > 0) return false;
                if (earlyPickup1.subtract(latePickup2).compareTo(this.maxTimeBetweenPickups) > 0) return false;
                boolean bl = true;
                return bl;
            }
            if (source1.equals(source2) && dest1.equals(dest2)) {
                boolean bl = true;
                return bl;
            }
            boolean bl = false;
            return bl;
        }
        catch (GLException e) {
            return false;
        }
        finally {
            if (this.connection != null) {
                try {
                    this.connection.close();
                }
                catch (GLException e) {}
            }
        }
    }

    @Override
    public boolean isCompatibleInGroup(Collection<ResAllocItem> itemCollection, ResAllocItem item) {
        HashSet stopAddresses = new HashSet();
        HashSet<Address> pickupStopAddresses = new HashSet<Address>();
        HashSet<Address> dropoffStopAddresses = new HashSet<Address>();
        try {
            for (ResAllocItem packedItem : itemCollection) {
                TShipment packedTShipment = ((PairableTShipment)packedItem.getReference()).getTShipment();
                pickupStopAddresses.add(packedTShipment.getSourceAddress());
                dropoffStopAddresses.add(packedTShipment.getDestAddress());
            }
            TShipment tShipment = ((PairableTShipment)item.getReference()).getTShipment();
            pickupStopAddresses.add(tShipment.getSourceAddress());
            dropoffStopAddresses.add(tShipment.getDestAddress());
            stopAddresses.addAll(pickupStopAddresses);
            stopAddresses.addAll(dropoffStopAddresses);
        }
        catch (GLException e) {
            // empty catch block
        }
        if (stopAddresses.size() > this.maxStopsAllowed) {
            return false;
        }
        if (pickupStopAddresses.size() > this.maxPickupStopsAllowed) {
            return false;
        }
        return dropoffStopAddresses.size() <= this.maxDeliveryStopsAllowed;
    }

    @Override
    public boolean isSame(ResAllocItem item1, ResAllocItem item2) {
        return true;
    }

    @Override
    public boolean isLike(ResAllocItem item1, ResAllocItem item2) {
        return true;
    }

    @Override
    public boolean isTiHiCompatible(ResAllocItem item1, ResAllocItem item2, ResAllocResource resource) {
        return true;
    }
}
