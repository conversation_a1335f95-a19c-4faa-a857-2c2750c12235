/*
 * Decompiled with CFR 0.152.
 */
package glog.business.compatibility;

import glog.util.exception.Cause;
import glog.util.exception.ErrorCause;

public class CompatibilityError
extends ErrorCause {
    private String feasibilityCodeGid = "FEASIBLE";
    private static final String causeOrderAddToBundle = "cause.OrderAddToBundle";
    private static final String causeShipmentAddToBundle = "cause.ShipmentAddToBundle";
    private static final String causeOrderCreateBundle = "cause.OrderCreateBundle";
    private static final String causeOrderBaseCreateBundle = "cause.OrderBaseCreateBundle";
    private static final String causeOMCreateBundle = "cause.OMCreateBundle";
    private static final String causeOMAddToBundle = "cause.OMAddToBundle";
    private static final String cannotAssignDriverToShipment = "cause.cannotAssignDriverToShipment";
    private static final String causeDriveShipmentGraphFailed = "cause.DriveShipmentGraphFailed";
    public static CompatibilityError compartmentsUseOfIncompatible = new CompatibilityError("cause.compartmentsUseOfIncompatible", null, null);
    public static CompatibilityError compartmentsExceedMax = new CompatibilityError("cause.compartmentsExceedMax", null, new Object[]{"MAXIMUM NUMBER OF COMPARTMENTS PER BUNDLE"});
    public static CompatibilityError weightExceedsMaxBundleWeight = new CompatibilityError("cause.weightExceedsMaxBundleWeight", null, new Object[]{"MAXIMUM WEIGHT PER BUNDLE"});
    public static CompatibilityError volumeExceedsMaxBundleVolume = new CompatibilityError("cause.volumeExceedsMaxBundleVolume", null, new Object[]{"MAXIMUM VOLUME PER BUNDLE"});
    public static CompatibilityError omLocationSourceNotMatch = new CompatibilityError("cause.omLocationSourceNotMatch", null, null);
    public static CompatibilityError omLocationDestNotMatch = new CompatibilityError("cause.omLocationDestNotMatch", null, null);
    public static CompatibilityError orLocationSourceNotMatch = new CompatibilityError("cause.orLocationSourceNotMatch", null, null);
    public static CompatibilityError orLocationDestNotMatch = new CompatibilityError("cause.orLocationDestNotMatch", null, null);
    public static CompatibilityError loadPointNotMatch = new CompatibilityError("cause.loadPointNotMatch", null, null);
    public static CompatibilityError unloadPointNotMatch = new CompatibilityError("cause.unloadPointNotMatch", null, null);
    public static CompatibilityError planFromLocationNotMatch = new CompatibilityError("cause.planFromLocationNotMatch", null, null);
    public static CompatibilityError planToLocationNotMatch = new CompatibilityError("cause.planToLocationNotMatch", null, null);
    public static CompatibilityError planFromLoadPointNotMatch = new CompatibilityError("cause.planFromLoadPointNotMatch", null, null);
    public static CompatibilityError planToUnloadPointNotMatch = new CompatibilityError("cause.planToUnloadPointNotMatch", null, null);
    public static CompatibilityError portsOfLoadNoOverlap = new CompatibilityError("cause.portsOfLoadNoOverlap", null, null);
    public static CompatibilityError portsOfDischargeNoOverlap = new CompatibilityError("cause.portsOfDischargeNoOverlap", null, null);
    public static CompatibilityError portsOfLoadLocOverrideNotMatch = new CompatibilityError("cause.portsOfLoadLocOverrideNotMatch", null, null);
    public static CompatibilityError portsOfDischargeLocOverrideNotMatch = new CompatibilityError("cause.portsOfDischargeLocOverrideNotMatch", null, null);
    public static CompatibilityError primaryLegSourceNoOverlap = new CompatibilityError("cause.primaryLegSourceNoOverlap", null, null);
    public static CompatibilityError primaryLegDestNoOverlap = new CompatibilityError("cause.primaryLegDestNoOverlap", null, null);
    public static CompatibilityError primaryLegSourceLocOverrideNotMatch = new CompatibilityError("cause.primaryLegSourceLocOverrideNotMatch", null, null);
    public static CompatibilityError primaryLegDestLocOverrideNotMatch = new CompatibilityError("cause.primaryLegDestLocOverrideNotMatch", null, null);
    public static CompatibilityError timeWindowsNotCompatible = new CompatibilityError("TIME_INFEASIBLE", "cause.timeWindowsNotCompatible", null, null);
    public static CompatibilityError billToNotMatchForSell = new CompatibilityError("cause.billToNotMatchForSell", null, new Object[]{"BILL-TO"});
    public static CompatibilityError orderBaseNotMatchForSell = new CompatibilityError("cause.orderBaseNotMatchForSell", null, null);
    public static CompatibilityError payMethodNotMatch = new CompatibilityError("cause.payMethodNotMatch", null, null);
    public static CompatibilityError incoTermsNotMatch = new CompatibilityError("cause.incoTermsNotMatch", null, null);
    public static CompatibilityError termLocTextNotMatch = new CompatibilityError("cause.termLocTextNotMatch", null, null);
    public static CompatibilityError rateServicesNotCompatible = new CompatibilityError("cause.rateServicesNotCompatible", null, null);
    public static CompatibilityError servprovsNotCompatible = new CompatibilityError("cause.servprovsNotCompatible", null, null);
    public static CompatibilityError modesNotCompatible = new CompatibilityError("cause.modesNotCompatible", null, null);
    public static CompatibilityError equipmentNotCompatible = new CompatibilityError("cause.equipmentNotCompatible", null, null);
    public static CompatibilityError mustShipAlone = new CompatibilityError("cause.mustShipAlone", null, null);
    public static CompatibilityError buyRatesNotCompatible = new CompatibilityError("cause.buyRatesNotCompatible", null, null);
    public static CompatibilityError sellRatesNotCompatible = new CompatibilityError("cause.sellRatesNotCompatible", null, null);
    public static CompatibilityError shipWithGroupsNotCompatible = new CompatibilityError("cause.shipWithGroupsNotCompatible", null, null);
    public static CompatibilityError timeWindowEmphasisPastOnlyWithPast = new CompatibilityError("cause.timeWindowEmphasisPastOnlyWithPast", null, null);
    public static CompatibilityError temperatureMinMaxNotCompatible = new CompatibilityError("MIN_MAX_TEMPERATURES_INCOMPATIBLE", "cause.temperatureMinMaxNotCompatible", null, null);
    public static CompatibilityError commoditiesNotCompatible = new CompatibilityError("COMMODITIES_INCOMPATIBLE", "cause.commoditiesNotCompatible", null, null);
    public static CompatibilityError equipmentRefUnitGidsNotMatch = new CompatibilityError("cause.equipmentRefUnitGidsNotMatch", null, null);
    public static CompatibilityError ignoreLocationCalendarFlagsNotMatch = new CompatibilityError("cause.ignoreLocationCalendarFlagsNotMatch", null, null);
    public static CompatibilityError buyItinerariesNotCompatible = new CompatibilityError("cause.buyItinerariesNotCompatible", null, null);
    public static CompatibilityError sellItinerariesNotCompatible = new CompatibilityError("cause.sellItinerariesNotCompatible", null, null);
    public static CompatibilityError orderCountZero = new CompatibilityError("cause.orderCountZero", null, null);
    public static CompatibilityError sequenceTypeNotCompatible = new CompatibilityError("cause.sequenceTypeNotCompatible", null, null);
    public static CompatibilityError orEquipConsolTypesNotMatch = new CompatibilityError("cause.orEquipConsolTypesNotMatch", null, null);
    public static CompatibilityError mustShipDirectNotMatch = new CompatibilityError("cause.mustShipDirectNotMatch", null, null);
    public static CompatibilityError mustShipThruPoolNotMatch = new CompatibilityError("cause.mustShipThruPoolNotMatch", null, null);
    public static CompatibilityError mustShipThruXDockNotMatch = new CompatibilityError("cause.mustShipThruXDockNotMatch", null, null);
    public static CompatibilityError omOriginalLegNotSame = new CompatibilityError("cause.omOriginalLegNotSame", null, null);
    public static CompatibilityError timeWindowArrivalNotCompatible = new CompatibilityError("TIME_INFEASIBLE", "cause.timeWindowArrivalNotCompatible", null, null);
    public static CompatibilityError stopLocationsNotMatch = new CompatibilityError("cause.stopLocationsNotMatch", null, null);
    public static CompatibilityError orderHasNoLines = new CompatibilityError("cause.orderHasNoLines", null, null);
    public static CompatibilityError pickupRailCarrierNotCompatible = new CompatibilityError("cause.pickupRailCarrierNotCompatible", null, null);
    public static CompatibilityError deliveryRailCarrierNotCompatible = new CompatibilityError("cause.deliveryRailCarrierNotCompatible", null, null);
    public static CompatibilityError commodityNotCompatibleWithMode = new CompatibilityError("COMMODITIES_INCOMPATIBLE_WITH_MODE", "cause.commodityNotCompatibleWithMode", null, null);
    public static CompatibilityError commodityNotCompatibleWithEquipment = new CompatibilityError("COMMODITIES_INCOMPATIBLE_WITH_EQUIPMENT", "cause.commodityNotCompatibleWithEquipment", null, null);
    public static CompatibilityError priorityNotCompatible = new CompatibilityError("cause.priorityNotCompatible", null, null);
    public static CompatibilityError railRouteCodeIncompatible = new CompatibilityError("cause.railRouteCodeIncompatible", null, null);
    public static CompatibilityError sharedSEquipmentIncompatible = new CompatibilityError("cause.sharedSEquipmentIncompatible", null, null);
    public static CompatibilityError arbitraryIncompatible = new CompatibilityError("cause.arbitraryIncompatible", null, null);
    public static CompatibilityError driverAdvancedCompatibleRuleFailure = new CompatibilityError("cause.driverAdvancedCompatibleRuleFailure", null, null);
    public static CompatibilityError driverBasicCompatibleRuleFailure = new CompatibilityError("cause.driverBasicCompatibleRuleFailure", null, null);
    public static CompatibilityError driveShipmentGraphFailed = new CompatibilityError("cause.DriveShipmentGraphFailed", null, null);
    public static CompatibilityError generalBundlingError = new CompatibilityError("cause.bundlesIncompatible", null, null);

    private CompatibilityError(String causeGid, String solutionGid, Object[] args) {
        this(null, causeGid, solutionGid, args);
    }

    private CompatibilityError(String feasibilityCodeGid, String causeGid, String solutionGid, Object[] args) {
        super(causeGid, solutionGid, args);
        this.feasibilityCodeGid = feasibilityCodeGid;
    }

    public Cause[] getOrderAddToBundleCauses(String orGidUnableToAdd) {
        return this.getCauses(causeOrderAddToBundle, null, new Object[]{orGidUnableToAdd});
    }

    public Cause[] getShipmentAddToBundleCauses(String shipmentGidForTarget) {
        return this.getCauses(causeShipmentAddToBundle, null, new Object[]{shipmentGidForTarget});
    }

    public Cause[] getOrderCreateBundleCauses(String orGid) {
        return this.getCauses(causeOrderCreateBundle, null, new Object[]{orGid});
    }

    public Cause[] getOrderCreateBundleFromBaseCauses(String obGid) {
        return this.getCauses(causeOrderBaseCreateBundle, null, new Object[]{obGid});
    }

    public Cause[] getOMCreateBundleCauses(String omGid, String orGid) {
        return this.getCauses(causeOMCreateBundle, null, new Object[]{omGid, orGid});
    }

    public Cause[] getOMAddToBundleCauses(String omGid, String orGid) {
        return this.getCauses(causeOMAddToBundle, null, new Object[]{omGid, orGid});
    }

    public Cause[] getAssignDriverToShipmentCauses(String driverGidNotAssigned, String shipmentGid) {
        return this.getCauses(cannotAssignDriverToShipment, null, new Object[]{driverGidNotAssigned, shipmentGid});
    }

    public Cause[] getDriverShipmentGraphCauses(String feasibilityExplanation) {
        return this.getCauses(causeDriveShipmentGraphFailed, null, new Object[]{feasibilityExplanation});
    }

    public String getFeasiblityCodeGid() {
        return this.feasibilityCodeGid;
    }

    public static CompatibilityError locationsIncompatible(String locationGid1, String locationGid2) {
        return new CompatibilityError("LOCATIONS_INCOMPATIBLE", "cause.locationsIncompatible", null, new Object[]{locationGid1, locationGid2});
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("Cause: " + this.causeGid);
        return sb.toString();
    }
}
