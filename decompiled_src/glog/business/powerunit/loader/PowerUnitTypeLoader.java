/*
 * Decompiled with CFR 0.152.
 */
package glog.business.powerunit.loader;

import glog.business.util.EntityLoader;
import glog.ejb.powerunit.db.PowerUnitTypeData;
import glog.ejb.powerunit.db.PuTypeRemarkData;
import glog.ejb.powerunit.db.PuTypeSpecialServiceData;
import glog.util.exception.GLException;

public class PowerUnitTypeLoader
extends EntityLoader {
    public PowerUnitTypeLoader() throws GLException {
        super(PowerUnitTypeData.class, true);
        this.createChildLoaders();
    }

    private void createChildLoaders() throws GLException {
        new EntityLoader(this, PuTypeSpecialServiceData.class, 60);
        new EntityLoader(this, PuTypeRemarkData.class, 60);
    }
}
