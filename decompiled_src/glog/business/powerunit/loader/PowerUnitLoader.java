/*
 * Decompiled with CFR 0.152.
 */
package glog.business.powerunit.loader;

import glog.business.util.EntityLoader;
import glog.ejb.powerunit.db.PowerUnitData;
import glog.ejb.powerunit.db.PowerUnitRefnumData;
import glog.ejb.powerunit.db.PowerUnitRemarkData;
import glog.ejb.powerunit.db.PowerUnitSpecialServiceData;
import glog.ejb.powerunit.db.PowerUnitStatusData;
import glog.util.exception.GLException;

public class PowerUnitLoader
extends EntityLoader {
    public PowerUnitLoader() throws GLException {
        super(PowerUnitData.class, false);
        this.createChildLoaders();
    }

    private void createChildLoaders() throws GLException {
        new EntityLoader(this, PowerUnitSpecialServiceData.class, 60);
        new EntityLoader(this, PowerUnitRemarkData.class, 60);
        new EntityLoader(this, PowerUnitStatusData.class, 60);
        new EntityLoader(this, PowerUnitSpecialServiceData.class, 100);
        new EntityLoader(this, PowerUnitRefnumData.class, 100);
    }
}
