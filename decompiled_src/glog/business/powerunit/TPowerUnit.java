/*
 * Decompiled with CFR 0.152.
 */
package glog.business.powerunit;

import glog.business.driver.DriverPowerUnitEquipmentCacheManager;
import glog.business.powerunit.loader.PowerUnitLoader;
import glog.business.rule.asset.AssetRuleHelper;
import glog.business.util.TChildObject;
import glog.business.util.TChildren;
import glog.business.util.TRootObject;
import glog.ejb.driver.DriverPowerUnitJoin;
import glog.ejb.driver.DriverPowerUnitJoinHome;
import glog.ejb.powerunit.db.PowerUnitData;
import glog.ejb.powerunit.db.PowerUnitPK;
import glog.ejb.powerunit.db.PowerUnitRemarkData;
import glog.ejb.powerunit.db.PowerUnitSpecialServiceData;
import glog.ejb.reference.db.RemarkQualPK;
import glog.util.Functions;
import glog.util.LocalDate;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.genericcontainer.GenericContainer;
import glog.util.jdbc.T2SharedConnection;
import glog.util.log.Log;
import glog.util.log.LogId;
import glog.util.log.LogIds;
import glog.util.remote.FinderNoMoreRecords;
import glog.util.remote.NamingDirectory;
import glog.util.uom.data.Weight;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class TPowerUnit
extends TRootObject {
    private static final LogId LOG_ALIAS = LogIds.PLANNING;
    private static LRUCache cache = new LRUCache("TPowerUnitCache", "Business");
    private static final Class POWER_UNIT_TREE_LOADER = PowerUnitLoader.class;
    public static final String BT_POWER_UNIT = "PowerUnitData";
    public static final String BT_POWER_UNIT_TYPE = "PowerUnitTypeData";
    public static final String BT_POWER_UNIT_SPECIAL_SERVICE = "PowerUnitSpecialServiceData";
    public static final String BT_POWER_UNIT_REMARKS = "PowerUnitRemarkData";
    public static final String BT_POWER_UNIT_STATUS = "PowerUnitStatusData";
    public static final String BT_POWER_UNIT_REFNUM = "PowerUnitRefnumData";
    private static final Set validChildBusinessTypes = Functions.asSet(new String[]{"PowerUnitTypeData", "PowerUnitSpecialServiceData", "PowerUnitRemarkData", "PowerUnitStatusData", "PowerUnitRefnumData"});
    private HashSet powerUnitSpecServPKSet = null;
    private HashMap powerUnitRemarkMap = null;

    public TPowerUnit(PowerUnitData powerunitData) {
        super(powerunitData);
    }

    public TPowerUnit(PowerUnitData powerunitData, int dmlCode) {
        super(powerunitData, dmlCode);
    }

    public TPowerUnit(GenericContainer templateContainer, int loadMode, Set loadedBusinessTypes) throws GLException {
        super(templateContainer, loadMode, loadedBusinessTypes);
        this.refreshMarkedPeriods();
    }

    public static void refreshLRU() throws GLException {
        cache.clear();
    }

    public static void refreshLRU(PowerUnitPK puPK) throws GLException {
        cache.remove(puPK);
    }

    @Override
    protected Class getRootEntityLoaderClass() {
        return POWER_UNIT_TREE_LOADER;
    }

    @Override
    protected Set getValidChildBusinessTypes() {
        return validChildBusinessTypes;
    }

    @Override
    public String getBusinessType() {
        return BT_POWER_UNIT;
    }

    public PowerUnitData getData() {
        return (PowerUnitData)this.getBeanData();
    }

    public static TPowerUnit getInstance(T2SharedConnection conn, PowerUnitPK powerUnitPk, int loadMode) throws GLException {
        return (TPowerUnit)TRootObject.getInstance(conn, cache, BT_POWER_UNIT, powerUnitPk, loadMode, TPowerUnit.class, POWER_UNIT_TREE_LOADER);
    }

    public static Map getInstances(T2SharedConnection conn, PowerUnitPK[] powerUnitPks, int loadMode) throws GLException {
        return TRootObject.getInstances(conn, cache, BT_POWER_UNIT, powerUnitPks, loadMode, TPowerUnit.class, POWER_UNIT_TREE_LOADER);
    }

    public static Map getInstances(T2SharedConnection conn, String alias, String whereClause, Object[] prepareArguments, int loadMode) throws GLException {
        return TRootObject.getInstances(conn, cache, BT_POWER_UNIT, alias, whereClause, prepareArguments, loadMode, TPowerUnit.class, POWER_UNIT_TREE_LOADER);
    }

    public Set getPowerUnitSpecServPKSet(T2SharedConnection conn) throws GLException {
        if (this.powerUnitSpecServPKSet == null) {
            HashSet<String> tmpPowerUnitSpecServPKSet = new HashSet<String>();
            TChildren tPuSSChildren = this.getTChildren(conn, BT_POWER_UNIT_SPECIAL_SERVICE);
            Iterator itTPowerUnitSpecServ = tPuSSChildren.getTObjectIterator();
            while (itTPowerUnitSpecServ.hasNext()) {
                TChildObject tChild = (TChildObject)itTPowerUnitSpecServ.next();
                PowerUnitSpecialServiceData pussData = (PowerUnitSpecialServiceData)tChild.getBeanData();
                tmpPowerUnitSpecServPKSet.add(pussData.specialServiceGid);
            }
            this.powerUnitSpecServPKSet = tmpPowerUnitSpecServPKSet;
        }
        return this.powerUnitSpecServPKSet;
    }

    public Map getPowerUnitRemarkMap(T2SharedConnection conn) throws GLException {
        if (this.powerUnitRemarkMap == null) {
            HashMap tmpPowerUnitRemarkMap = new HashMap();
            TChildren tPuRMKChildren = this.getTChildren(conn, BT_POWER_UNIT_REMARKS);
            Iterator itTPowerUnitRmk = tPuRMKChildren.getTObjectIterator();
            while (itTPowerUnitRmk.hasNext()) {
                TChildObject tChild = (TChildObject)itTPowerUnitRmk.next();
                PowerUnitRemarkData purData = (PowerUnitRemarkData)tChild.getBeanData();
                String qualGid = purData.remarkQualGid;
                String qualText = purData.remarkText;
                RemarkQualPK rqPK = qualGid == null ? null : new RemarkQualPK(qualGid);
                ArrayList remarkValueList = (ArrayList)tmpPowerUnitRemarkMap.get(rqPK);
                if (remarkValueList == null) {
                    remarkValueList = new ArrayList();
                }
                AssetRuleHelper.addRemarkTextToRemarkValueList(remarkValueList, qualText, ",");
                tmpPowerUnitRemarkMap.put(rqPK, remarkValueList);
            }
            this.powerUnitRemarkMap = tmpPowerUnitRemarkMap;
        }
        return this.powerUnitRemarkMap;
    }

    public List getDrivers() throws GLException {
        ArrayList<String> powerUnits = new ArrayList<String>();
        if (Log.idOn[TPowerUnit.LOG_ALIAS.index]) {
            Log.logID(LOG_ALIAS, "Start getDrivers...");
        }
        NamingDirectory nd = null;
        PowerUnitData powerUnitData = null;
        try {
            powerUnitData = (PowerUnitData)this.getBeanData();
            nd = NamingDirectory.get();
            DriverPowerUnitJoinHome driverPowerUnitJoinHome = (DriverPowerUnitJoinHome)nd.lookup("ejb.DriverPowerUnitJoin");
            Enumeration driverPowerUnitJoinEnum = driverPowerUnitJoinHome.findByDriverGid(powerUnitData.powerUnitGid);
            while (driverPowerUnitJoinEnum.hasMoreElements()) {
                DriverPowerUnitJoin driverPowerUnitJoin = (DriverPowerUnitJoin)driverPowerUnitJoinEnum.nextElement();
                powerUnits.add(driverPowerUnitJoin.getDriverGid());
            }
            ArrayList<String> arrayList = powerUnits;
            return arrayList;
        }
        catch (FinderNoMoreRecords fnmr) {
            if (Log.idOn[TPowerUnit.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, "No DRIVERS found for power unit {0}", powerUnitData.powerUnitGid);
            }
            List list = null;
            return list;
        }
        catch (Exception e) {
            throw GLException.factory(e);
        }
        finally {
            if (nd != null) {
                nd.release();
            }
            if (Log.idOn[TPowerUnit.LOG_ALIAS.index]) {
                Log.logID(LOG_ALIAS, "End getDrivers...");
            }
        }
    }

    public String toString() {
        PowerUnitData powerUnitData = (PowerUnitData)this.getBeanData();
        StringBuffer sb = new StringBuffer("powerUnitGid: ");
        sb.append(powerUnitData.powerUnitGid);
        return sb.toString();
    }

    public PowerUnitPK getPowerUnitPK() {
        return (PowerUnitPK)((PowerUnitData)this.getBeanData()).getPk();
    }

    public String getPowerUnitGid() {
        return ((PowerUnitData)this.getBeanData()).powerUnitGid;
    }

    public String getPowerUnitTypeGid() {
        return ((PowerUnitData)this.getBeanData()).powerUnitTypeGid;
    }

    public String getPowerUnitNum() {
        return ((PowerUnitData)this.getBeanData()).powerUnitNum;
    }

    public LocalDate getDateBuilt() {
        return ((PowerUnitData)this.getBeanData()).dateBuilt;
    }

    public Weight getTareWeight() {
        return ((PowerUnitData)this.getBeanData()).tareWeight;
    }

    public String getDomicileCountryCodeGid() {
        return ((PowerUnitData)this.getBeanData()).domicileCountryCodeGid;
    }

    public String getParkLocationGid() {
        return ((PowerUnitData)this.getBeanData()).parkLocationGid;
    }

    public String getCorporationGid() {
        return ((PowerUnitData)this.getBeanData()).corporationGid;
    }

    public Boolean isActive() {
        return ((PowerUnitData)this.getBeanData()).isActive;
    }

    private void refreshMarkedPeriods() throws GLException {
        T2SharedConnection conn = T2SharedConnection.adhoc();
        DriverPowerUnitEquipmentCacheManager.loadCacheDataforPowerUnit(this.getPowerUnitGid(), conn);
        if (conn != null) {
            conn.close();
        }
    }
}
