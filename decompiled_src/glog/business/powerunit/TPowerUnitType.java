/*
 * Decompiled with CFR 0.152.
 */
package glog.business.powerunit;

import glog.business.powerunit.loader.PowerUnitTypeLoader;
import glog.business.rule.asset.AssetRuleHelper;
import glog.business.util.TChildObject;
import glog.business.util.TChildren;
import glog.business.util.TRootObject;
import glog.ejb.powerunit.db.PowerUnitTypeData;
import glog.ejb.powerunit.db.PowerUnitTypePK;
import glog.ejb.powerunit.db.PuTypeRemarkData;
import glog.ejb.powerunit.db.PuTypeSpecialServiceData;
import glog.ejb.reference.db.RemarkQualPK;
import glog.util.Functions;
import glog.util.cache.LRUCache;
import glog.util.exception.GLException;
import glog.util.genericcontainer.GenericContainer;
import glog.util.jdbc.T2SharedConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

public class TPowerUnitType
extends TRootObject {
    private static LRUCache cache = new LRUCache("TPowerUnitTypeCache", "Business");
    private static final Class POWER_UNIT_TYPE_TREE_LOADER = PowerUnitTypeLoader.class;
    public static final String BT_POWER_UNIT_TYPE = "PowerUnitTypeData";
    public static final String BT_POWER_UNIT_TYPE_SPECIAL_SERVICE = "PuTypeSpecialServiceData";
    public static final String BT_POWER_UNIT_TYPE_REMARKS = "PuTypeRemarkData";
    private static final Set validChildBusinessTypes = Functions.asSet(new String[]{"PuTypeSpecialServiceData", "PuTypeRemarkData"});
    private HashSet powerUnitTypeSpecServPKSet = null;
    private HashMap powerUnitTypeRemarkMap = null;

    public TPowerUnitType(GenericContainer templateContainer, int loadMode, Set loadedBusinessTypes) throws GLException {
        super(templateContainer, loadMode, loadedBusinessTypes);
    }

    public static void refreshLRU() throws GLException {
        cache.clear();
    }

    public static void refreshLRU(PowerUnitTypePK putPK) throws GLException {
        cache.remove(putPK);
    }

    @Override
    protected Class getRootEntityLoaderClass() {
        return POWER_UNIT_TYPE_TREE_LOADER;
    }

    @Override
    protected Set getValidChildBusinessTypes() {
        return validChildBusinessTypes;
    }

    @Override
    public String getBusinessType() {
        return BT_POWER_UNIT_TYPE;
    }

    public static TPowerUnitType getInstance(T2SharedConnection conn, PowerUnitTypePK powerUnitTypePk, int loadMode) throws GLException {
        return (TPowerUnitType)TRootObject.getInstance(conn, cache, BT_POWER_UNIT_TYPE, powerUnitTypePk, loadMode, TPowerUnitType.class, POWER_UNIT_TYPE_TREE_LOADER);
    }

    public static Map getInstances(T2SharedConnection conn, PowerUnitTypePK[] powerUnitTypePks, int loadMode) throws GLException {
        return TRootObject.getInstances(conn, cache, BT_POWER_UNIT_TYPE, powerUnitTypePks, loadMode, TPowerUnitType.class, POWER_UNIT_TYPE_TREE_LOADER);
    }

    public static Map getInstances(T2SharedConnection conn, String alias, String whereClause, Object[] prepareArguments, int loadMode) throws GLException {
        return TRootObject.getInstances(conn, cache, BT_POWER_UNIT_TYPE, alias, whereClause, prepareArguments, loadMode, TPowerUnitType.class, POWER_UNIT_TYPE_TREE_LOADER);
    }

    public Set getPowerUnitTypeSpecServPKSet(T2SharedConnection conn) throws GLException {
        if (this.powerUnitTypeSpecServPKSet == null) {
            HashSet<String> tmpPowerUnitTypeSpecServPKSet = new HashSet<String>();
            TChildren tPutSSChildren = this.getTChildren(conn, BT_POWER_UNIT_TYPE_SPECIAL_SERVICE);
            Iterator itTPowerUnitTypeSpecServ = tPutSSChildren.getTObjectIterator();
            while (itTPowerUnitTypeSpecServ.hasNext()) {
                TChildObject tChild = (TChildObject)itTPowerUnitTypeSpecServ.next();
                PuTypeSpecialServiceData putssData = (PuTypeSpecialServiceData)tChild.getBeanData();
                tmpPowerUnitTypeSpecServPKSet.add(putssData.specialServiceGid);
            }
            this.powerUnitTypeSpecServPKSet = tmpPowerUnitTypeSpecServPKSet;
        }
        return this.powerUnitTypeSpecServPKSet;
    }

    public Map getPowerUnitTypeRemarkMap(T2SharedConnection conn) throws GLException {
        if (this.powerUnitTypeRemarkMap == null) {
            HashMap tmpPowerUnitTypeRemarkMap = new HashMap();
            TChildren tPutRMKChildren = this.getTChildren(conn, BT_POWER_UNIT_TYPE_REMARKS);
            Iterator itTPowerUnitTypeRmk = tPutRMKChildren.getTObjectIterator();
            while (itTPowerUnitTypeRmk.hasNext()) {
                TChildObject tChild = (TChildObject)itTPowerUnitTypeRmk.next();
                PuTypeRemarkData putrData = (PuTypeRemarkData)tChild.getBeanData();
                String qualGid = putrData.remarkQualGid;
                String qualText = putrData.remarkText;
                RemarkQualPK rqPK = qualGid == null ? null : new RemarkQualPK(qualGid);
                ArrayList remarkValueList = (ArrayList)tmpPowerUnitTypeRemarkMap.get(rqPK);
                if (remarkValueList == null) {
                    remarkValueList = new ArrayList();
                }
                AssetRuleHelper.addRemarkTextToRemarkValueList(remarkValueList, qualText, ",");
                tmpPowerUnitTypeRemarkMap.put(rqPK, remarkValueList);
            }
            this.powerUnitTypeRemarkMap = tmpPowerUnitTypeRemarkMap;
        }
        return this.powerUnitTypeRemarkMap;
    }

    public String toString() {
        PowerUnitTypeData powerUnitTypeData = (PowerUnitTypeData)this.getBeanData();
        StringBuffer sb = new StringBuffer("powerUnitTypeGid: ");
        sb.append(powerUnitTypeData.powerUnitTypeGid);
        return sb.toString();
    }

    public PowerUnitTypePK getPowerUnitTypePK() {
        return (PowerUnitTypePK)((PowerUnitTypeData)this.getBeanData()).getPk();
    }

    public String getPowerUnitTypeGid() {
        return ((PowerUnitTypeData)this.getBeanData()).powerUnitTypeGid;
    }

    public String getDescription() {
        return ((PowerUnitTypeData)this.getBeanData()).description;
    }
}
