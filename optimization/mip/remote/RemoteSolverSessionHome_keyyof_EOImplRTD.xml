<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.optimization.mip.remote.RemoteSolverSessionHome_keyyof_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="openProblem(glog.optimization.mip.remote.RemoteSolver)"
    timeout="600000"
>
</method>
<method
    name="writeLPModel(glog.optimization.mip.remote.RemoteSolver,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="writeModel(glog.optimization.mip.remote.RemoteSolver,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="interruptProblem(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="removeProblem(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="modifyVariables(java.lang.Long,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="deleteVariables(java.lang.Long,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="modifyConstraints(java.lang.Long,java.util.Collection,int)"
    timeout="600000"
>
</method>
<method
    name="deleteConstraints(java.lang.Long,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="solve(glog.optimization.mip.remote.RemoteSolver,int)"
    timeout="600000"
>
</method>
<method
    name="closeProblem(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
