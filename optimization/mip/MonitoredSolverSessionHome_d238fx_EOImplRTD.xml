<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.optimization.mip.MonitoredSolverSessionHome_d238fx_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="resetStats()"
    timeout="10000"
>
</method>
<method
    name="test(java.util.Collection,java.util.Collection,int,boolean)"
    timeout="10000"
>
</method>
<method
    name="interruptProblem(java.lang.Long)"
    timeout="10000"
>
</method>
<method
    name="diagnoseXML(int)"
    timeout="10000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="10000"
>
</method>
<method
    name="getEJBHome()"
    timeout="10000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="10000"
>
</method>
<method
    name="remove()"
    timeout="10000"
>
</method>
<method
    name="getHandle()"
    timeout="10000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
