<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.crt.CRTUploadSessionHome_ibncb3_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getShipmentSetTypeofLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupXidGidMapForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isValidBid(glog.procurement.app.ejb.bid.db.PBidData,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneXidGidMapForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTransportModeXidGidMapForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBiddableAccessorialListForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAccessorialXidGidMap(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="saveBids()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="loadBidRoundTObject(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processBids(java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processBid(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBidRoundStatusAndDates(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getSourcingContactForServiceProvider(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadConfigGid(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistBids(java.util.HashMap,java.lang.String,boolean)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isProjectHasPriorBidRoundCheck(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getClosedPriorBidRounds(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAllowedBiddableLanesSet(java.util.Set,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getExistingPBidData(glog.procurement.app.ejb.bid.db.PBidData,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getServprovGidForLoginUser()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="processBidsAndAccessorials(java.lang.String,glog.procurement.util.vo.CRTUploadVO,java.util.ArrayList,java.util.HashMap,java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getXidGidMapForOceanLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneGidsForXids(java.util.Map,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTransportModeGidsForXids(java.util.Map,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupGidsForXids(java.util.Map,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRegionGidsForXids(java.util.Map,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getOceanBidCosts(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBidGid(glog.procurement.util.vo.CRTUploadVO)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneSummaryGidForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
