<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.crt.CRTGeneratorSessionHome_r3iaoh_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getListOfLanePKs(glog.procurement.web.action.bid.ReportDataVO)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getListOfLanes(glog.procurement.web.action.bid.ReportDataVO,glog.procurement.app.ejb.lanesummary.db.LaneSummaryStatPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadFieldsDataList(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCostParametersOnly(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCostParametersWithData(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCRTCostConditions(glog.procurement.app.ejb.bid.db.PCrtConfigData)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isProjectCapacityByCnt(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCrtConfigOfBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getIsShipmentCount(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadFieldBreakIds(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNewCrtLoadProcessXid()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNewRateLoadProcessXid()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateGeoCostData(glog.ejb.rates.db.RateGeoCostPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAccessorialCostData(glog.ejb.rates.db.AccessorialCostPK)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBidToRateLoadFieldGidMap(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadCostParametersWithData(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNMFCs(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkUnRatedBids(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkUnRatedBids(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="checkUnRatedBidsForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadDataDList(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadDBreaksList(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getDisplayNamesMap(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadFieldBreakIdsWithPKValues(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRateLoadDAndBreaks(java.util.Map)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteCRTAndRateLoadProcess(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRatedNMFCsMap(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNMFCStatsforLTL(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNMFCInformationForLane(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneInformationForBidding(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupsOnOceanLane(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getArbitararyLanesOnOceanLane(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getArbitraryLaneRegionsForOceanLane(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isValidBid(glog.procurement.app.ejb.bid.db.PBidData,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getExistingBid(glog.procurement.app.ejb.bid.db.PBidData,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="validServiceProviderForBidRound(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getProjectAttributes(glog.procurement.web.action.bid.ReportDataVO)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getInitialData(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRemarkQualAttributes(glog.util.jdbc.T2SharedConnection,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneAttributes(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getAccessorilDetailsforLanes(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getRateLoadConfigIdsForProject(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCRTConfigAttributes(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTimePeriodRangeForLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCRTAttributesColumnPositionMap(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLaneSummaryDetails(glog.procurement.web.action.bid.ReportDataVO)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getReportLaneDataDetails(glog.procurement.web.action.bid.ReportDataVO,glog.procurement.web.action.bid.LaneSummaryDetailsVO,java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getOceanShipmentSetTypeofLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetTypeofLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="laneSummaryOfBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getTransportModesForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupsForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPriorBidRoundsForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getServiceProvidersForBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getCRTConfigDetails(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNMFCsforLanes(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="projectOfBidRound(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getLocationGroupDetailsForLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNMFCInformation(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
