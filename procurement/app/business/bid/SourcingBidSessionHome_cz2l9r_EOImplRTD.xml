<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.bid.SourcingBidSessionHome_cz2l9r_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getLowestBidsInProject(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getXlanesForProject(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidsInBidRound(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="isRateOfferingHasCRTConfig(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isRateRecordHasCRTConfig(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBiddableBidRoundsList()"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetTypeForProject(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getXlanesForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetType(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidDocumentForBids(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetTypeForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
