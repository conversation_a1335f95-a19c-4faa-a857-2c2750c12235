<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.bid.SourcingScenarioPersistenceSessionServerSideEJBWrapper_ayoq37_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="persistCopySolutiondetails(java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRuleSetsForScenarios(java.util.List,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRuleSetsForRateZoneProfiles(java.util.List,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="updateRuleSetsForLanes(java.util.List,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistentSolutionDetails(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="setBidPublishStatus(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
