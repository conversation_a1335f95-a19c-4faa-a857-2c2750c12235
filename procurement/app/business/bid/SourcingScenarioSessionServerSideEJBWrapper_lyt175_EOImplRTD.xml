<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.bid.SourcingScenarioSessionServerSideEJBWrapper_lyt175_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getBidRoundGidForSolution(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getProjectIdForSolution(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getProcurementSolverStatus(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getSolutionSummaryDetail(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServiceProvidersForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSourcingContactForServiceProvider(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getRuleSetsForScenarios(java.util.List)"
    timeout="600000"
>
</method>
<method
    name="getRuleSetsForRateZoneProfiles(java.util.List)"
    timeout="600000"
>
</method>
<method
    name="getRuleSetsForLanes(java.util.List)"
    timeout="600000"
>
</method>
<method
    name="processAddBidsToSolution(java.lang.String,java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="setBidPublishStatus(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isValidBidToSolution(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="processCopySolutionDetails(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServprovContactsForScenario(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAwardedValues(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="solutionSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
