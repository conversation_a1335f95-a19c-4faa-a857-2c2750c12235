<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.bid.BidServiceSessionHome_psvywf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getLowestBids(glog.procurement.app.ejb.bid.db.PBidPK[])"
    timeout="600000"
>
</method>
<method
    name="preBidRemove(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="postBidRemove(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getNextBidSequence(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changePublishStatus(java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="changeBidRoundStatus(java.lang.Object[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changeBidInStatus(glog.procurement.app.ejb.bid.db.PBidPK[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="changeBidsCommitmentStatus(java.lang.String,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getBidDefaultValues(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServprovGid()"
    timeout="600000"
>
</method>
<method
    name="changeCommitmentStatus(glog.procurement.app.ejb.bid.db.PBidPK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="changePackageCommitmentStatus(glog.procurement.app.ejb.bid.db.PBidPackagePK[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="togglePublishStatus(glog.procurement.app.ejb.bid.db.PBidPK[])"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
