<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.project.BidRoundSessionHome_gdmcq9_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="getAllowedBiddableLanesSet(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyBidRound(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getserviceProvidersandContactsforBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSendInvitationDetailsOfBidRound(java.lang.String,java.lang.String,java.util.Collection)"
    timeout="600000"
>
</method>
<method
    name="sendEmail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="carrierResponseAnalysis(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isProjectHasPriorBidRoundCheck(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isProjectHasClosedPriorBidRounds(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isServProvHasPriorRoundBidsForLane(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isBidRoundOpen(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getProjectGidOfBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getStatusTypeValueGids()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
