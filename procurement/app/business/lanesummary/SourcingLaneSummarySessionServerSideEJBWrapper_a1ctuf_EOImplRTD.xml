<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.lanesummary.SourcingLaneSummarySessionServerSideEJBWrapper_a1ctuf_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="persistLaneSummary(java.util.HashMap,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="generateProvinceMatrixPersist(java.util.ArrayList,java.util.ArrayList,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistLaneSummaryWithVolTarget(java.util.HashMap,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="clearLaneSummary(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="caluclateLaneSummaryStatistic(java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persisstCompressDuplicateLanes(java.util.HashMap,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteEmptiedLanes(java.lang.String,java.util.ArrayList)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="autoCombinesLanesPersist(glog.procurement.app.business.util.SourcingActionResult)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistXlaneShipments(glog.procurement.app.business.util.SourcingActionResult)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
