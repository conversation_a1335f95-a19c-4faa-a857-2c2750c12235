<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.lanesummary.LaneSummaryBuildSessionServerSideEJBWrapper_1c5q27_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="bidsExist(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="compressDuplicateLanes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildLaneSummaryWithVolTarget(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildLaneSummary(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="autoCombineLanes(java.lang.String,double,double,glog.util.uom.data.Distance,glog.util.uom.data.Distance,int,java.util.ArrayList,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="checkPShipmentDesiredGeoHierarchy(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="fillMissingLanes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="generateProvinceMatrix(java.lang.String,java.lang.String,java.lang.Integer)"
    timeout="600000"
>
</method>
<method
    name="combineLanes(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
