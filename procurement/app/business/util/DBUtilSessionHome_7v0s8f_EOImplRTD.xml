<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.util.DBUtilSessionHome_7v0s8f_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="hasPermission(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="openConnection()"
    timeout="600000"
>
</method>
<method
    name="copyBid(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyShipmentSet(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="executeQuery(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="combineLanes(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calcLaneDistance(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calcShipmentDistance(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="fillShipmentSetDistance(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="grantBidRoundDocuments(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteOrphanedShipments()"
    timeout="600000"
>
</method>
<method
    name="autoCombineLanes(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="publishRates(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="genProvinceMatrix(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="grantAction(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="revokeAction(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="viewGrants(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="addPreBids(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deletePreBids(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="genInvitationToBidConstantPart(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="genInvitationToBid(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSourcingContactForServiceProvider(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyPShipment(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="pShipmentModification(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="nullOutDistanceForLane(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="nullOutLane(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="removeFromShipmentSet(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="addShipmentToShipmentSet(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="viewSolutionCostSavingsByLane(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copySolution(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="deleteEmptiedLanes(java.lang.String,java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="compressDuplicateLanes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="viewSolutionCostSavingsByLaneByCarrier(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="viewSolutionComparison(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLaneSummaryRows(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="checkProjectAssociationForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getProjectAssociationForLaneSummaryFlag(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUserDefault(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setUserDefault(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isProjectPriorBidRoundCheck(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLanesWithoutBid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServprovContactsForScenario(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAwardedValues(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="adjustShipmentVolumes(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBidsForScenario(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getCountOfPublishedBids(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getCountOfPublishedBids(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="checkPShipmentDesiredGeoHierarchy(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="getProjectGidOfBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getStatusTypeValueGids()"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetGids()"
    timeout="600000"
>
</method>
<method
    name="getAllLanesForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetGidForPShipment(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLaneSummaryGidForSolution(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getTotalShipmentsInLane(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBidsOfServiceProviderInBidRound(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServiceProviderInBidRoundWithBid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLanesWithBidInProject(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLanesWithBidInBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidDocumentForBidRound(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidDocumentForProject(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidDocumentForRelatedProjects(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getLowestBidDocumentForBids(java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="getLanesFromLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="pctStopOffs(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getIntervalCounts(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPLaneAttributes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="executeFunction(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="executeProcedure(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="saveCRTConfiuration(java.lang.String,java.util.ArrayList)"
    timeout="600000"
>
</method>
<method
    name="getSolverMaxtime(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServprovGidForLoginUser()"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupXidGidMapForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getTransportModeXidGidMapForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetGidForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentsInShipmentSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getConstraintSetValueMapForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentsForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="sendEmail(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Collection,boolean)"
    timeout="600000"
>
</method>
<method
    name="getArbitraryLaneGidFromXidForLane(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getUOMDetails(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isProjectCapacityByCountForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLaneSummaryGidForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="fillMissingLanes(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildShipmentSetLocally(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clearLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildLaneSummary(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="buildLaneSummaryWithVolumeTarget(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="calcLaneSummaryStat(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="clearReferenceBids(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="carrierResponseAnalysis(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServProvsWithoutBid(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="solutionSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSolutionInsertDate(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="findLowestBid(java.lang.String,java.lang.String,boolean)"
    timeout="600000"
>
</method>
<method
    name="describeLocation(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="describePostalCode(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="describeLane(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getCarrierResponseTemplateAlternateColumnNames(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="setBidPublishStatus(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getGidList(java.lang.String,java.lang.Object[])"
    timeout="600000"
>
</method>
<method
    name="copyProject(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyBidRound(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="closeConnection()"
    timeout="600000"
>
</method>
<method
    name="clearShipmentSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="populateReferenceBids(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAccessorialListForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBiddableAccessorialListForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getNonBiddableAccessorialListForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAccessorialDetailsForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getTransportModesForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupsForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getServiceProvidersForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getPriorBidRoundsForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getCRTConfigVOs(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAllUserDefinedLaneAttributes()"
    timeout="600000"
>
</method>
<method
    name="getTimePeriodRangeForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLaneInformationForBidding(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetTypeofLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGroupsOnOceanLane(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getArbitararyLanesOnOceanLane(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getEquipmentGidForOceanBid(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getProjectSpecialServiceBasis(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getArbitraryLaneRegionsForOceanLane(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getExistingBid(glog.procurement.app.ejb.bid.db.PBidData)"
    timeout="600000"
>
</method>
<method
    name="getXidGidMapForOceanLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getLaneXidGidMapForBidRound(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetType(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetTypeForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getAccessorialXidGidMap(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isValidBid(glog.procurement.app.ejb.bid.db.PBidData,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getNextLaneSummaryStatSequence()"
    timeout="600000"
>
</method>
<method
    name="getProcurementSolverStatus(java.lang.Long)"
    timeout="600000"
>
</method>
<method
    name="getLocationGroupDetailsForLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getOceanShipmentSetTypeofLaneSummary(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="isOceanBidCostValid(glog.procurement.app.ejb.bid.db.PBidData,java.util.Map)"
    timeout="600000"
>
</method>
<method
    name="getBidGid(glog.procurement.util.vo.CRTUploadVO)"
    timeout="600000"
>
</method>
<method
    name="getNMFCStatsforLTL(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getSolutionSummaryDetail(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="bidsExist(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getBidPackagesForUser()"
    timeout="600000"
>
</method>
<method
    name="genLinkToLaneSummaryResults(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
