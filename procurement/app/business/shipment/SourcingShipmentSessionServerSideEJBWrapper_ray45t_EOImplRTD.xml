<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.shipment.SourcingShipmentSessionServerSideEJBWrapper_ray45t_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="buildShipmentSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="assignShipmentSet(java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="copyShipmentstoShipmentSet(java.lang.String,java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="nullOutLane(java.util.List)"
    timeout="600000"
>
</method>
<method
    name="nullOutDistance(java.util.List)"
    timeout="600000"
>
</method>
<method
    name="fillShipmentSetDistance(java.lang.String,java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="getShipmentSetGidsForPShipment(java.lang.String[])"
    timeout="600000"
>
</method>
<method
    name="copyShipments(java.lang.String[],java.lang.String)"
    timeout="600000"
>
</method>
<method
    name="remove()"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
