<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE rmi PUBLIC "-//BEA Systems, Inc.//RMI Runtime DTD 1.0//EN" "rmi.dtd">
<rmi
    name="glog.procurement.app.business.shipment.SourcingShipmentPersistenceSessionServerSideEJBWrapper_brr2wv_EOImpl"
>
<cluster
    clusterable="true"
    propagate-environment="true"
>
</cluster>

<lifecycle
    dgc-policy="managed"
>
</lifecycle>

<method
    name="persistBuildShipmentSet(java.util.HashMap,java.lang.String,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistShipmentsAndDetails(java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="clearShipmentSet(java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="nullOutDistance(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="nullOutLane(java.util.List)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistShipmentSetD(java.util.ArrayList,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistShipmentsforDistance(java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="removeFromShipmentSet(java.lang.String[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="deleteAllUnassignedShipments()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="addToShipmentSet(java.lang.String[],java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistShipments(java.util.HashMap)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="persistModifiedShipments(java.util.List,java.lang.String)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getEJBHome()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="isIdentical(javax.ejb.EJBObject)"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getPrimaryKey()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="remove()"
    requires-transaction="true"
    timeout="600000"
>
</method>
<method
    name="getHandle()"
    requires-transaction="true"
    timeout="600000"
>
</method>

<security
    client-authentication="supported"
    client-cert-authentication="supported"
    identity-assertion="supported"
    confidentiality="supported"
    integrity="supported"
>
</security>

</rmi>
